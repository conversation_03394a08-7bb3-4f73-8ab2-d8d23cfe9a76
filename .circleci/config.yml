version: 2.1

# Define the jobs we want to run for this project
jobs:
  run-tests:
    docker:
      - image: cimg/node:20.18.1
      - image: mongo:6.0.11
      - image: redis:latest
      - image: nats-streaming:latest
    steps:
      - checkout
      - run: sudo apt-get update && sudo apt-get install curl gnupg -y
      - run: curl --location --silent https://dl-ssl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
      - run: sudo sh -c 'echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list'
      - run: sudo apt-get update
      - run: sudo apt-get install google-chrome-stable -y --no-install-recommends
      - run: sudo rm -rf /var/lib/apt/lists/*
      - run: yarn install --frozen-lock-file
      - run: yarn build
      - run: MONGODB_URI=mongodb://localhost:27017/test_catlog yarn db:seed
      - run: USE_CHANGE_STREAMS=false USE_MAILCHIMP_API=false USE_TWITTER_API=false USE_NOTIFICATIONS=false USE_RECAPTCHA=false yarn ci:test

  deploy-production-api:
    docker:
      - image: cimg/node:20.18.1
    steps:
      - checkout
      - setup_remote_docker:
          # version: 20.10.2
          docker_layer_caching: false
      - run: yarn ci:docker
      - run: ssh -oStrictHostKeyChecking=no -v $USER@$IP "bash /var/www/html/catlog-api/deploy.sh"

  deploy-beta-api:
    docker:
      - image: cimg/node:20.18.1
    steps:
      - checkout
      - setup_remote_docker:
          # version: 20.10.6
          docker_layer_caching: false
      - run: docker build -t myshopcatlog/catlog-api:beta .
      - run: docker login -u  $DOCKERHUB_USERNAME -p $DOCKERHUB_PASSWORD
      - run: docker push myshopcatlog/catlog-api:beta
      - run: ssh -oStrictHostKeyChecking=no -v $USER@$BETA_IP "bash ~/deployments/api-deploy.sh"

  staging-api:
    docker:
      - image: cimg/base:2021.04
    steps:
      - checkout
      - setup_remote_docker:
          # version: 20.10.2
          docker_layer_caching: false
      - run: docker build -t myshopcatlog/catlog-api:staging .
      - run: docker login -u  $DOCKERHUB_USERNAME -p $DOCKERHUB_PASSWORD
      - run: docker push myshopcatlog/catlog-api:staging
      - run: ssh -oStrictHostKeyChecking=no -v $USER@$IP "bash ~/deployments/api-deploy.sh"

  experiments-api:
    docker:
      - image: cimg/base:2021.04
    steps:
      - checkout
      - setup_remote_docker:
          docker_layer_caching: false
      - run: docker build -t myshopcatlog/catlog-api:experiments .
      - run: docker login -u  $DOCKERHUB_USERNAME -p $DOCKERHUB_PASSWORD
      - run: docker push myshopcatlog/catlog-api:experiments
      - run: sudo apt-get update && sudo apt-get install sshpass -y
      - run: sshpass -p "circleci" ssh -oStrictHostKeyChecking=no -v circleci@$EXPERIMENTS_IP "bash ~/deployments/api-deploy.sh" # I messed up the ssh keys somehow, this is the only solution I can think of at the moment

      # Orchestrate our job run sequence
workflows:
  build:
    jobs:
      - run-tests
      - deploy-production-api:
          requires:
            - run-tests
          filters:
            branches:
              only:
                - master
      - deploy-beta-api:
          requires:
            - run-tests
          filters:
            branches:
              only:
                - beta
      - staging-api:
          requires:
            - run-tests
          filters:
            branches:
              only:
                - develop
      - experiments-api:
          requires:
            - run-tests
          filters:
            branches:
              only:
                - experiments
# VS Code Extension Version: 1.5.1
