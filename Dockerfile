FROM node:20-alpine AS BUILD_IMAGE

WORKDIR /usr/src/app

# Install git and openssh
RUN apk add --no-cache git openssh

# Configure Git to use HTTPS instead of SSH
RUN git config --global url."https://github.com/".insteadOf "ssh://**************/"

COPY package.json yarn.lock ./
#COPY .env ./.env

COPY . .

ENV PATH /usr/src/app/node_modules/.bin:$PATH

RUN yarn install
RUN yarn build
RUN rm -rf node_modules
RUN yarn install --production

FROM zenika/alpine-chrome:with-node

WORKDIR /usr/src/app

ENV CHROME_BIN=/usr/bin/chromium-browser

COPY --from=BUILD_IMAGE /usr/src/app/dist ./dist
COPY --from=BUILD_IMAGE /usr/src/app/node_modules node_modules
COPY --from=BUILD_IMAGE /usr/src/app/package.json package.json

EXPOSE 4000

CMD [ "node", "dist/main" ]
