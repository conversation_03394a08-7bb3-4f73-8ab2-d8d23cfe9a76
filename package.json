{"name": "catlog-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "db:seed": "ts-node src/scripts/seed.script.ts", "local:setupdb": "ts-node src/scripts/setup-script.ts", "local:scaleseed": "ts-node src/scripts/scale-seed-script.ts", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "MONGODB_URI=mongodb://localhost:27017/test_catlog jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "MONGODB_URI=mongodb://localhost:27017/test_catlog node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --config jest-e2e.json --runInBand", "test:e2e": "MONGODB_URI=mongodb://localhost:27017/test_catlog jest --config ./jest-e2e.json", "ci:test": "yarn test; yarn test:e2e --ci --runInBand", "docker:build": "docker build -t sheghun/catlog-api:latest .", "docker:login": "docker login -u  $DOCKERHUB_USERNAME -p $DOCKERHUB_PASSWORD", "docker:push": "docker push sheghun/catlog-api:latest", "ci:docker": "npm run docker:build && npm run docker:login && npm run docker:push", "start:api": "unset PREFIX && . ~/.nvm/nvm.sh && nvm use 16.14.0 && nest start --watch", "start:emails": "cd src/react-email && npx email dev --port=3005"}, "dependencies": {"@getbrevo/brevo": "^2.0.0-beta.4", "@google-cloud/vision": "^4.1.0", "@mailchimp/mailchimp_marketing": "^3.0.75", "@nestjs/bull": "^0.6.0", "@nestjs/cli": "7.5.5", "@nestjs/common": "7.5.1", "@nestjs/config": "0.6.1", "@nestjs/core": "7.5.1", "@nestjs/jwt": "7.2.0", "@nestjs/microservices": "7.5.5", "@nestjs/mongoose": "7.1.2", "@nestjs/passport": "7.1.5", "@nestjs/platform-express": "7.5.1", "@nestjs/platform-socket.io": "^9.1.4", "@nestjs/schedule": "^1.0.2", "@nestjs/swagger": "4.7.5", "@nestjs/throttler": "^6.1.1", "@nestjs/websockets": "^9.1.4", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.54.0", "@opentelemetry/instrumentation-fs": "^0.19.0", "@opentelemetry/instrumentation-mongodb": "^0.51.0", "@opentelemetry/instrumentation-winston": "^0.43.0", "@opentelemetry/winston-transport": "^0.9.0", "@react-email/components": "^0.0.32", "@sentry/node": "^6.16.1", "@sentry/tracing": "^6.16.1", "@types/classnames": "^2.3.4", "@types/jsdom": "^16.2.14", "@types/mime-types": "^2.1.4", "@types/sharp": "0.29.3", "@types/socket.io": "^3.0.2", "JSONStream": "^1.3.5", "ansi-regex": "4.1.1", "apitoolkit-express": "^3.1.5", "async-mutex": "^0.4.0", "aws-sdk": "^2.982.0", "axios": "^0.24.0", "bcrypt": "5.0.0", "body-parser": "^1.20.0", "bull": "^4.8.5", "cache-manager": "3.4.0", "cache-manager-redis-store": "2.0.0", "class-transformer": "0.3.1", "class-validator": "0.12.2", "classnames": "^2.5.1", "cloudinary": "^2.2.0", "colorthief": "2.3.2", "cookie": "^0.5.0", "customerio-node": "^4.1.1", "dayjs": "^1.11.5", "dotenv": "10.0.0", "ejs": "^3.1.8", "event-stream": "^4.0.1", "faker": "^5.5.3", "firebase-admin": "^13.0.1", "form-data": "^4.0.1", "helmet": "^7.1.0", "image-downloader": "^4.3.0", "json-as-xlsx": "^2.5.6", "md5": "^2.3.0", "mime-types": "^3.0.1", "mobile-detect": "^1.4.5", "moment": "^2.29.1", "mongoose": "5.10", "mongoose-lean-virtuals": "^0.9.0", "mongoose-paginate-v2": "^1.6.0", "nats": "1.4.12", "nest-raven": "^8.0.0", "nest-winston": "^1.6.1", "node-vibrant": "^3.2.1-alpha.1", "nodejs-dna": "^1.0.3", "openai": "^3.3.0", "passport": "0.4.1", "passport-jwt": "4.0.0", "passport-local": "1.0.0", "phone": "^3.1.10", "puppeteer": "^18.2.1", "puppeteer-core": "^18.2.1", "react": "^18.3.1", "react-confetti": "^6.0.1", "react-dom": "^18.3.1", "reflect-metadata": "0.1.13", "resend": "^4.1.1", "rimraf": "3.0.2", "rxjs": "6.6.3", "sharp": "^0.33.5", "snakecase-keys": "^5.1.0", "swagger-ui-express": "4.1.5", "twitter-api-sdk": "^1.1.0", "twitter-api-v2": "^1.12.5", "uuid": "^9.0.0", "web-push": "^3.5.0", "winston": "^3.3.3", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/schematics": "^7.1.3", "@nestjs/testing": "^7.5.1", "@types/bcrypt": "^3.0.0", "@types/bull": "^3.15.9", "@types/cache-manager": "^3.4.0", "@types/cookie": "^0.5.1", "@types/cron": "^1.7.3", "@types/ejs": "^3.1.1", "@types/express": "^4.17.8", "@types/faker": "^5.5.8", "@types/jest": "^26.0.15", "@types/mailchimp__mailchimp_marketing": "^3.0.5", "@types/md5": "^2.3.2", "@types/mongoose": "5.10.2", "@types/mongoose-paginate-v2": "^1.3.10", "@types/multer": "^1.4.7", "@types/node": "^14.14.6", "@types/passport-jwt": "^3.0.3", "@types/passport-local": "^1.0.33", "@types/react": "^19.0.8", "@types/redis": "^4.0.11", "@types/supertest": "^2.0.10", "@types/uuid": "^9.0.2", "@types/web-push": "^3.3.2", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-prettier": "^3.1.4", "jest": "^29.7.0", "jest-puppeteer": "^6.1.1", "prettier": "^2.1.2", "react-email": "3.0.6", "spdy": "^4.0.2", "supertest": "^6.1.4", "ts-jest": "^29.2.4", "ts-loader": "^8.0.8", "ts-node": "^10.8.1", "tsconfig-paths": "^3.9.0", "twitter-lite": "^1.1.0", "typescript": "4.9"}}