export enum PLAN_TYPE {
  BASIC = 'BASIC',
  STARTER = 'STARTER',
  BUSINESS_PLUS = 'BUSINESS_PLUS',
  <PERSON><PERSON><PERSON><PERSON> = 'KITCHEN',
}

export class PLANS {
  static get TYPES() {
    return [this.BASIC, this.STARTER, this.BUSINESS_PLUS, this.K<PERSON>CH<PERSON>];
  }

  static BASIC = 'BASIC';
  static STARTER = 'STARTER';
  static BUSINESS_PLUS = 'BUSINESS_PLUS';
  static KITCHEN = 'KITCHEN';

  static MAX_UPLOADS = {
    STARTER: 10,
    BASIC: 100,
    BUSINESS_PLUS: Number.POSITIVE_INFINITY,
    KITCHEN: Number.POSITIVE_INFINITY,
  };
}

export enum PLAN_MAX_UPLOADABLE_PRODUCT {
  STARTER = 10,
  BASIC = 100,
  BUSINESS_PLUS = Number.POSITIVE_INFINITY,
  <PERSON><PERSON><PERSON><PERSON> = Number.POSITIVE_INFINITY,
}

export const PLAN_RANKING: Record<PLAN_TYPE, number> = {
  [PLAN_TYPE.STARTER]: 1,
  [PLAN_TYPE.BASIC]: 2,
  [PLAN_TYPE.BUSINESS_PLUS]: 3,
  [PLAN_TYPE.KITCHEN]: 4,
};
