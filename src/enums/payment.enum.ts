import { CURRENCIES } from '../modules/country/country.schema';
import { ACCOUNT_PROVIDERS } from '../modules/wallets/wallet.account.schema';

export enum PAYMENT_METHODS {
  PAYSTACK = 'PAYSTACK',
  TRF_MOMO = 'TRF_MOMO',
  TRANSFER = 'TRANSFER',
  MONNIFY_TRANSFER = 'MONNIFY_TRANSFER',
  ZILLA = 'ZILLA',
  DIRECT_TRANSFER = 'DIRECT_TRANSFER',
  MONO_DIRECT_PAY = 'MONO_DIRECT_PAY',
  THEPEER = 'THEPEER',
  WALLET = 'WALLET',
  UNKNOWN = 'UNKNOWN', //for receipts
  CATLOG_CREDIT = 'CATLOG_CREDITS',
  CARD = 'CARD',
  STARTBUTTON = 'STARTBUTTON',
  MOMO = 'MOMO',
  STRIPE = 'STRIPE',
  LEATHERBACK = 'LEATHERBACK',
  DIRECT_DEBIT = 'DIRECT_DEBIT',
}

export enum PAYMENT_PROVIDERS {
  BLOCHQ = 'BLOCHQ',
  MONNIFY = 'MONNIFY',
  FLUTTERWAVE = 'FLUTTERWAVE',
  KORAPAY = 'KORAPAY',
  PAYAZA = 'PAYAZA',
  SQUAD = 'SQUAD',
  ZEEPAY = 'ZEEPAY',
  SUDO = 'SUDO',
  SAME_AS_METHOD = 'SAME_AS_METHOD',
  // PAYSTACK = 'PAYSTACK',
}

export enum PAYMENT_STATUS {
  SUCCESS = 'SUCCESS',
  PENDING = 'PENDING',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED',
}

export enum SUBSCRIPTION_STATUS {
  ACTIVE = 'ACTIVE',
  IN_ACTIVE = 'INACTIVE',
}

export enum SUBSCRIPTION_CHANGE_TYPE {
  SIGNUP = 'SIGNUP',
  UPGRADE = 'UPGRADE',
  DOWNGRADE = 'DOWNGRADE',
  RENEWAL = 'RENEWAL',
  INTERVAL_CHANGE = 'INTERVAL_CHANGE',
}

export enum TOKEN_PURCHASE_METHOD {
  SUBSCRIPTION = 'SUBSCRIPTION',
  TOKEN_PURCHASE = 'TOKEN_PURCHASE',
}

export enum PAYMENT_TYPES {
  INVOICE = 'INVOICE',
  SUBSCRIPTION = 'SUBSCRIPTION',
  DELIVERY = 'DELIVERY',
  WALLET = 'WALLET',
  TOKEN_PURCHASE = 'TOKEN_PURCHASE',
  DOMAIN_PURCHASE = 'DOMAIN_PURCHASE',
  TEST = 'TEST',
}

export const MININUM_PAYABLE = {
  [CURRENCIES.NGN]: 250_00,
  [CURRENCIES.GHC]: 5_00,
  [CURRENCIES.KES]: 10_00,
  [CURRENCIES.ZAR]: 5_00,
};

export const TRANSFERS_PROVIDERS_PRIORITY = [
  { provider: PAYMENT_PROVIDERS.KORAPAY, priority: 1 },
  { provider: PAYMENT_PROVIDERS.FLUTTERWAVE, priority: 2 },
  { provider: PAYMENT_PROVIDERS.MONNIFY, priority: 3 },
  { provider: PAYMENT_PROVIDERS.PAYAZA, priority: 4 },
  { provider: PAYMENT_PROVIDERS.SUDO, priority: 5 },
  // { provider: PAYMENT_PROVIDERS.BLOCHQ, priority: 4 },
];

export enum MOBILE_MONEY_NETWORK {
  MTN = 'MTN',
  VODAFONE = 'Vodafone',
  AIRTELTIGO = 'AirtelTigo',
  ZEEPAY = 'Zeepay',
}

export const ZEEPAY_CHANNEL_MAP = {
  [MOBILE_MONEY_NETWORK.MTN]: '2',
  [MOBILE_MONEY_NETWORK.VODAFONE]: '3',
  [MOBILE_MONEY_NETWORK.AIRTELTIGO]: '4',
  [MOBILE_MONEY_NETWORK.ZEEPAY]: '5',
};

export const ACCOUNT_PROVIDER_TO_PAYMENT_PROVIDER_MAP = {
  [ACCOUNT_PROVIDERS.SQUAD]: PAYMENT_PROVIDERS.SQUAD,
  [ACCOUNT_PROVIDERS.PAYAZA]: PAYMENT_PROVIDERS.PAYAZA,
};
