export enum DELIVERY_PROVIDERS {
  SHIPBUBBLE = 'SHIPBU<PERSON><PERSON>',
  CHOWDECK = 'CHOWDECK',
  FEZ_DELIVERY = 'FEZ_DELIVERY',
  SHAQ_EXPRESS = 'SHAQ_EXPRESS',
}

export enum DELIVERY_SERVICE_TYPE {
  DROP_OFF = 'dropff',
  PICKUP = 'pickup',
}

export enum DELIVERY_STATUS {
  DRAFT = 'DRAFT',
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  PICKED_UP = 'PICKED_UP',
  IN_TRANSIT = 'IN_TRANSIT',
  AT_CUSTOMER_LOCATION = 'AT_CUSTOMER_LOCATION',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
}

export enum CHOWDECK_DELIVERY_EVENTS {
  ORDER_ASSIGNED = 'ORDER_ASSIGNED',
  ORDER_AWAITING_PICKUP = 'ORDER_AWAITING_PICKUP',
  ORDER_PICKED_UP = 'ORDER_PICKED_UP',
  ORDER_ARRIVED_AT_CUSTOMER_LOCATION = 'ORDER_ARRIVED_AT_CUSTOMER_LOCATION',
  ORDER_COMPLETE = 'ORDER_COMPLETE',
  ORDER_REJECTED = 'ORDER_REJECTED',
}

export const CHOWDECK_EVENTS_STATUS_MAP: { [key in CHOWDECK_DELIVERY_EVENTS]: DELIVERY_STATUS } = {
  [CHOWDECK_DELIVERY_EVENTS.ORDER_ASSIGNED]: DELIVERY_STATUS.CONFIRMED,
  [CHOWDECK_DELIVERY_EVENTS.ORDER_AWAITING_PICKUP]: DELIVERY_STATUS.CONFIRMED,
  [CHOWDECK_DELIVERY_EVENTS.ORDER_PICKED_UP]: DELIVERY_STATUS.PICKED_UP,
  [CHOWDECK_DELIVERY_EVENTS.ORDER_ARRIVED_AT_CUSTOMER_LOCATION]: DELIVERY_STATUS.AT_CUSTOMER_LOCATION,
  [CHOWDECK_DELIVERY_EVENTS.ORDER_COMPLETE]: DELIVERY_STATUS.COMPLETED,
  [CHOWDECK_DELIVERY_EVENTS.ORDER_REJECTED]: DELIVERY_STATUS.CANCELLED,
};

export enum FEZ_DELIVERY_STATUS {
  PENDING_PICK_UP = 'Pending Pick-Up',
  PICKED_UP = 'Picked-Up',
  DISPATCHED = 'Dispatched',
  DELIVERED = 'Delivered',
  CANCELLED = 'Cancelled',
}

export const FEZ_DELIVERY_EVENTS_STATUS_MAP: { [key: string]: DELIVERY_STATUS } = {
  'Pending Pick-Up': DELIVERY_STATUS.CONFIRMED,
  'Assigned To A Rider': DELIVERY_STATUS.CONFIRMED,
  'Picked-Up': DELIVERY_STATUS.PICKED_UP,
  Dispatched: DELIVERY_STATUS.IN_TRANSIT,
  Delivered: DELIVERY_STATUS.COMPLETED,
  Returned: DELIVERY_STATUS.CANCELLED,
};

export const SHAQ_EXPRESS_EVENTS_STATUS_MAP: { [key: string]: DELIVERY_STATUS } = {
  started: DELIVERY_STATUS.CONFIRMED,
  assigned: DELIVERY_STATUS.CONFIRMED,
  completed: DELIVERY_STATUS.COMPLETED,
  cancelled: DELIVERY_STATUS.CANCELLED,
  delivered: DELIVERY_STATUS.COMPLETED,
};
