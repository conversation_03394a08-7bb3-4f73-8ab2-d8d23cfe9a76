export enum WHATSAPP_BOT_STEPS {
  START = 'START',
  WELCOME_MESSAGE = 'WELCOME_MESSAGE',
  NEW_ORDER = 'NEW_ORDER',
  REPEAT_ORDER = 'REPEAT_ORDER',
  ORDER_SELECTION = 'ORDER_SELECTION',
  AUTO_SELECTION = 'AUTO_SELECTION',
  ORDERS_NOT_FOUND = 'ORDERS_NOT_FOUND',
  CONTACT_SUPPORT = 'CONTACT_SUPPORT',
  CHAT_INITIATION_EXISTING_CUSTOMER = 'CHAT_INITIATION_EXISTING_CUSTOMER',
  CHAT_INITIATION_NEW_CUSTOMER = 'CHAT_INITIATION_NEW_CUSTOMER',
  SEARCH_STORE_NEW_CUSTOMER = 'SEARCH_STORE_NEW_CUSTOMER',
  SEARCH_STORE = 'SEARCH_STORE',
  STORE_FETCH_NO_RESULTS = 'STORE_FETCH_NO_RESULTS',
  STORE_SEARCH_NO_RESULTS = 'STORE_SEARCH_NO_RESULTS',
  STORE_SEARCH_RESULTS = 'STORE_SEARCH_RESULTS',
  STORE_SEARCH_RESULTS_EXTRA = 'STORE_SEARCH_RESULTS_EXTRA',
  STORE_SEARCH_RESULTS_NO_EXTRA = 'STORE_SEARCH_RESULTS_NO_EXTRA',
  SAME_STORE_INITIATION = 'SAME_STORE_INITIATION',
  DIFFERENT_STORE_INITIATION = 'DIFFERENT_STORE_INITIATION',
  NAME = 'NAME',
  ENTER_NAME = 'ENTER_NAME',
  ITEMS_SELECTION = 'ITEMS_SELECTION',
  ITEMS_CONFIRMATION = 'ITEMS_CONFIRMATION',
  DELIVERY_METHOD_SELECTION = 'DELIVERY_METHOD_SELECTION',
  DELIVERY_INFO_METHOD_SELECTION = 'DELIVERY_INFO_METHOD_SELECTION',
  DELIVERY_INFO_CONFIRMATION = 'DELIVERY_INFO_CONFIRMATION',
  DELIVERY_INFO_FORM = 'DELIVERY_INFO_FORM',
  ALT_DELIVERY_INFO_FORM = 'ALT_DELIVERY_INFO_FORM',
  PICKUP_ADDRESS = 'PICKUP_ADDRESS',
  PAYMENT_METHOD_SELECTION = 'PAYMENT_METHOD_SELECTION',
  PAYMENT_DETAILS = 'PAYMENT_DETAILS',
  OTHER_PAYMENT_METHOD = 'OTHER_PAYMENT_METHOD',
  ORDER_SUCCESSFUL = 'ORDER_SUCCESSFUL',
  UNEXPECTED_RESPONSE = 'UNEXPECTED_RESPONSE',
  PAYMENT_REQUEST = 'PAYMENT_REQUEST',
  SELECT_BRANCH = 'SELECT_BRANCH',
  ORDER_CANCELLED = 'ORDER_CANCELLED',
  EDIT_ORDER = 'EDIT_ORDER',
  REQUEST_LOCATION = 'REQUEST_LOCATION',
  ENTER_COUPON = 'ENTER_COUPON',
  ADD_ORDER_NOTES = 'ADD_ORDER_NOTES',
  COUPON_VALIDATION_FAILED = 'COUPON_VALIDATION_FAILED',

  // SELLER STEPS
  ORDER_CONFIRMATION = 'ORDER_CONFIRMATION',
  ORDER_REJECTION_REASON = 'ORDER_REJECTION_REASON',

  // CHECK IN
  FEEDBACK = 'FEEDBACK',
  CHECK_IN_INITIATION = 'CHECK_IN_INITIATION',
}

export const LOCKED_STEPS = [
  WHATSAPP_BOT_STEPS.PAYMENT_REQUEST,
  WHATSAPP_BOT_STEPS.PAYMENT_DETAILS,
  WHATSAPP_BOT_STEPS.PAYMENT_METHOD_SELECTION,
  WHATSAPP_BOT_STEPS.OTHER_PAYMENT_METHOD,
  WHATSAPP_BOT_STEPS.ORDER_SUCCESSFUL,
  WHATSAPP_BOT_STEPS.WELCOME_MESSAGE,
];

export enum WHATSAPP_BOT_APP_EVENTS {
  RECEIEVED_CUSTOMER_MESSAGE = 'received customer message',
}

export enum WHATSAPP_BOT_EVENTS {
  INITIATED_CONVERSATION = 'initiated conversation',
  SELECTED_REPEAT_ORDER = 'selected repeat order',
  RECEIVED_PREVIOUS_ORDERS = 'sent previous orders',
  SELECTED_PREVIOUS_ORDER = 'selected previous order',
  RECIEVED_WELCOME_MESSAGE = 'sent welcome message',
  SELECTED_MAKE_NEW_ORDER = 'proceeded to make new order',
  RECEIVED_ITEM_SELECTION_LINK = 'requested items selection',
  SWITCHED_BRANCHES = 'switched branches',
  SELECTED_ITEMS = 'selected items',
  SELECTED_EDIT_ITEMS = 'selected edit items',
  RECIEVED_CONFIRM_SELECTION_REQUEST = 'requested selection confirmation',
  RECEIVED_DELIVERY_METHOD_REQUEST = 'requested delivery method',
  SELECTED_DELIVERY = 'selected delivery',
  SELECTED_PICKUP = 'selected pickup',
  RECEIVED_DELIVERY_INFO_FORM = 'sent delivery info form',
  FILLED_DELIVERY_INFO_FORM = 'filled delivery info form',
  RECIEVED_CONFIRM_DELIVERY_INFO_REQUEST = 'requested delivery info confirmation',
  SELECTED_EDIT_DELIVERY_INFO = 'selected edit delivery info',

  SELECTED_ADD_COUPON = 'selected add coupon',
  SELECTED_REMOVE_COUPON = 'selected remove coupon',

  SELECTED_PROCEED_TO_PAYMENT = 'selected proceed to payment',
  RECIEVED_ORDER_CONFIRMATION_REQUEST = 'requested order confirmation',
  // RECIEVED_ORDER_CONFIRMATION = 'received order confirmation',
  RECIEVED_PAYMENT_DETAILS = 'sent payment details',
  RECIEVED_PICKUP_ADDRESS = 'sent pickup address',
  SELECTED_CONTACT_SUPPORT = 'selected contact support',
  RECIEVED_SUPPORT_CONTACT = 'sent support contact',
  MADE_PAYMENT = 'successfully made payment',
  RECIEVED_PAYMENT_CONFIRMATION = 'sent payment confirmation',
  SELECTED_OTHER_PAYMENT_METHODS = 'selected other payment methods',
  ORDER_REJECTED = 'order was rejected by seller',
  ORDER_CONFIRMED = 'order was confirmed by seller',
  ORDER_EXPIRED = 'order was automatically canceled',
  RECEIVED_ORDER_EXPIRED = 'order was automatically cancelled',
  RECEIEVED_ORDER_REJECTED = 'order was rejected by seller',
  RECEIEVED_ORDER_CONFIRMED = 'order was confirmed by seller',
  RECIEVED_REJECTION_REASON = 'sent rejection reason',
  SWITCHED_STORES = 'switched stores',
  ENDED_CHAT = 'ended chat',
  EXPIRED_SESSION = 'session expired',
  WENT_BACK = 'proceeded to previous step',
  RESTARTED_CHAT = 'restarted chat',
}

export enum INTENTS {
  NONE = 'NONE',
  SEARCH_STORE = 'SEARCH_STORE',
  PLACE_ORDER = 'PLACE_ORDER',
  REPEAT_ORDER = 'REPEAT_ORDER',
}

export enum INTERACTIVE_MESSAGE_TYPES {
  BUTTON_REPLY = 'button_reply',
  LIST_REPLY = 'list_reply',
  NFM_REPLY = 'nfm_reply',
}

export enum SUPPORTED_PHONES {
  NG = '234',
  GH = '233',
}

export enum FLOWS {
  DELIVERY_INFO = 'DELIVERY_INFO',
  ALT_DELIVERY_INFO = 'ALT_DELIVERY_INFO',
  CONFIRM_ORDER = 'QUESTION_ONE',
  RATE = 'RATE',
}
