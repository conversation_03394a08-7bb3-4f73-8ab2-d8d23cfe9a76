import { COUNTRY_CODE } from '../modules/country/country.schema';

// 'NIN' | 'INTERNATIONAL_PASSPORT' | 'DRIVERS_LINCENSE' | 'VOTERS_CARD'

export enum VERIFICATION_METHODS {
  NIN = 'NIN',
  INTERNATIONAL_PASSPORT = 'INTERNATIONAL_PASSPORT',
  DRIVERS_LINCENSE = 'DRIVERS_LICENSE',
  VOTERS_CARD = 'VOTERS_CARD',
  SSNIT_ID = 'SSNIT_ID',
  NATIONAL_ID = 'NATIONAL_ID',
  GHANA_CARD = 'GHANA_CARD',
}

export const COUNTRY_VERIFICATION_METHODS: { [key: string]: VERIFICATION_METHODS[] } = {
  [COUNTRY_CODE.NG]: [VERIFICATION_METHODS.NIN],
  [COUNTRY_CODE.GH]: [
    VERIFICATION_METHODS.INTERNATIONAL_PASSPORT,
    VERIFICATION_METHODS.VOTERS_CARD,
    VERIFICATION_METHODS.DRIVERS_LINCENSE,
    VERIFICATION_METHODS.SSNIT_ID,
    VERIFICATION_METHODS.GHANA_CARD,
  ],
  [COUNTRY_CODE.KE]: [VERIFICATION_METHODS.NATIONAL_ID, VERIFICATION_METHODS.INTERNATIONAL_PASSPORT],
  [COUNTRY_CODE.ZA]: [VERIFICATION_METHODS.NATIONAL_ID],
};
