export default {
  mode: 'jit',
  purge: ['./src/**/*.{js,ts,jsx,tsx}'],
  darkMode: false, // or 'media' or 'class'
  safelist: ['text-xs', 'text-xxs', 'mr-1', 'ml-1', 'inline-block'],
  theme: {
    fontFamily: {
      body: [
        'Inter',
        '-apple-system',
        'BlinkMacSystemFont',
        'Roboto',
        'Helvetica Neue',
        'Helvetica',
        'Arial',
        'sans-serif',
      ],
      display: [
        'FH Oscar',
        'Inter',
        '-apple-system',
        'BlinkMacSystemFont',
        'Roboto',
        'Helvetica Neue',
        'Helvetica',
        'Arial',
        'sans-serif',
      ],
    },
    screens: {
      xs: '370px',
      sm: '545px',
      md: '800px',
      lg: '1024px',
      xl: '1280px',
      '2xl': '1536px',
    },
    extend: {
      outline: {
        primary: '1.5px solid #332089',
        grey: '1.5px solid rgba(229,229,229,0.3)',
      },
      opacity: {
        7: '0.07',
        8: '0.08',
      },
      borderRadius: {
        5: '0.3125rem',
        8: '0.5rem',
        10: '0.625rem',
        15: '0.9375rem',
        20: '1.25rem',
        30: '30px',
      },
      spacing: {
        0.75: '0.1875rem',
        1.25: '0.3125rem',
        1.75: '0.4375rem',
        3.75: '0.9375rem',
        4.5: '1.125rem',
        6.25: '1.5625rem',
        6.75: '1.6875rem',
        7: '1.75rem',
        7.5: '1.875rem',
        8.75: '2.1875rem',
        11.25: '2.8125rem',
        11.5: '2.875rem',
        12.5: '3.125rem',
        15: '3.75rem',
        16.25: '4.0625rem',
        17.5: '4.375rem',
        18: '4.5rem',
        22.5: '5.625rem',
        25: '6.25rem',
        30: '7.5rem',
      },
      fontSize: {
        '0.5xxs': '0.625rem',
        xxs: '0.6875rem',
        '1xs': '0.8125rem',
        '1sm': '0.9375rem',
        '2lg': '1.375rem',
        '3lg': '1.75rem',
        '4lg-small': '2rem',
        '4lg': '2.1875rem',
      },
      keyframes: {
        spin: {
          to: {
            transform: 'rotate(180deg)',
          },
        },
      },
      colors: {
        primary: {
          50: '#FCFBFF',
          80: '#FBFAFF',
          100: '#F1EEFF',
          300: '#5943DD',
          400: '#4B38B1',
          500: '#332089',
          700: '#2B1890',
          900: '#050537',
          pastel: '#F7F5FF',
        },
        success: '#72CF90',
        danger: {
          400: '#FF6767',
          500: '#FF426B',
          700: '#E22C43',
        },
        placeholder: '#8E8E8E',
        dark: '#656565',
        'black-placeholder': '#747478',
        'black-secondary': '#3E3E3E',
        black: '#292D32',
        'black-muted': '#656565',
        'black-300': '#192C48',
        'black-400': '#1E1E1E',
        'black-500': '#050929',
        grey: {
          fields: {
            100: '#F8F8F8',
            200: '#FAFAFA',
          },
          light: '#fcfcfb',
          border: '#E5E5E5',
          bg: '#FBFBFF',
          bgdark: '#FAFAFA',
          divider: '#F5F4F4',
          loader: '#F8F5F5',
          subtext: '#737576',
          header: '#FAFAFD',
          modal: '#F9F9F9',
          input: '#F7F5FF',
          'border-dark': '#D0D0D0',
          card: '#F3F6F9',
          outline: '#E0E0E0',
          ash: '#F2F2F2',
          smoke: '#F0F0F0',
          muted: '#C2C2C2',
        },
        accent: {
          500: '#F79B01',
          600: '#EC9504',
          700: '#D48706',
          yellow: {
            100: '#FFE1BB',
            300: '#FFC476',
            500: '#EF940F',
            700: '#965E23',
            900: '#7F4201',
            pastel: '#FEF6EB',
          },
          red: {
            100: '#FEB8DD',
            300: '#EA5E8B',
            500: '#BF0637',
            700: '#7B0827',
            900: '#55001F',
            pastel: '#FFEBF5',
          },
          green: {
            100: '#CAFFE9',
            300: '#73E8BB',
            500: '#39B588',
            700: '#18663C',
            900: '#014F28',
            pastel: '#EBFFF7',
          },
          orange: {
            100: '#FFC8B3',
            300: '#FF8159',
            500: '#F35508',
            700: '#A83508',
            900: '#661A00',
            pastel: '#FFF0EB',
          },
          purple: {
            500: '#332089',
          },
        },
      },
      gridTemplateColumns: {
        'dashboard-lg': 'minmax(265px, 20%) 1fr',
        'dashboard-md': '265px 1fr',
      },
      boxShadow: {
        card: '0 3px 25px rgba(0,0,0,0.03)',
        accent: '0px 3px 10px rgba(247, 155, 1, 0.1)',
        primary: '0px 22px 40px rgba(51, 32, 152, 0.1);',
        btn: '0px 4px 50px rgba(0,0,0,0.1);',
        icon1: '0px 10px 40px rgba(0, 0, 0, 0.1)',
        icon2: '0px 4px 50px rgba(0,0,0,0.03)',
        icon3: 'inset 0px 4px 10px rgba(253, 206, 218, 0.15)',
        pill: '0px 2px 25px rgba(0, 0, 0, 0.05);',
        'slider-handle': '0px 2px 10px rgba(0,0,0,0.25);',
      },
      animation: {
        'ping-once': 'ping 1s ease-in-out',
        'spin-once': 'spin 1s ease-in-out',
      },
    },
    backgroundImage: {
      'pattern-light': "url('/images/patterns/pattern-light.png')",
    },
  },
  variants: {
    extend: {},
  },
  plugins: [],
};
