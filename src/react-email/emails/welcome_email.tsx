import {
  Body,
  Column,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Preview,
  Row,
  Section,
  Text,
  Tailwind,
  Hr,
  Link,
} from "@react-email/components";
import EmailFonts from "./components/fonts";
import config from "../tailwind.config";
import EmailButton from "./components/button";
import EmailHeader from "./components/header";
import EmailFooter from "./components/footer";
import * as React from "react";

interface Props {
  name: string;
  preview_text: string;
}

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="welcome_email" isLight>
                Welcome <br /> Catlog {props.name}
              </EmailHeader>
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}!
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    We’re excited to have you onboard, and can’t wait to see you show the world all the cool stuff you
                    sell.
                  </Text>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base pt-2.5">
                    At Catlog, your business is our priority and we are invested in seeing it grow. We do this through
                    our platform and its amazing features that help you manage and grow your business. We really want to
                    see you succeed and will be rooting for you all the way. 🚀
                  </Text>
                  <Text className="font-body text-black text-sm sm:text-base font-semibold pt-2.5">
                    Here’s what you should do next:
                  </Text>
                  <ul className="list-disc">
                    <li className="font-body text-black-muted py-1.25">
                      Add your products. You can read a guide{" "}
                      <Link href="https://catlog.substack.com/p/how-to-upload-products-to-your-catlog?s=w" className="text-primary-500 font-body font-medium">
                        here
                      </Link>
                    </li>
                    <li className="font-body text-black-muted py-1.25">
                      Add your store link to your social profiles so customers can start ordering from you with ease
                    </li>
                    <li className="font-body text-black-muted py-1.25">
                      Verify your business so you can sell hitch-free{" "}
                    </li>
                  </ul>
                  <Text className="font-body text-black-muted pt-2.5 text-base ">
                    Also, Join our community on whatsapp so we could work together to help grow your business.
                  </Text>
                  <EmailButton href="https://catlog.shop/app/get-verified" className="my-6.25">
                    Join Community
                  </EmailButton>
                  <Text className="font-body text-black-muted py-1.25 text-base">
                    If you feel stuck while doing any of the above, please{" "}
                    <Link href="https://api.whatsapp.com/send/?phone=+2349031678578" className="text-primary-500 font-body font-medium">
                      send us a message on WhatsApp
                    </Link>{" "}
                    and we will be happy to help!
                  </Text>
                  <Text className="font-body text-black-muted pt-2.5 text-base">
                    Once again we are rooting for you and hope Catlog helps take your business to the next level.
                  </Text>
                  <Text className="font-body text-black-muted pt-2.5 text-base">
                    <span className="font-body text-black font-medium">Silas.</span>
                    <br />
                    For the Catlog Team.
                  </Text>
                </Column>
              </Row>
              <EmailFooter bannerType="light-purple" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: "John Doe",
} as Props;

export default EmailTemplate;
