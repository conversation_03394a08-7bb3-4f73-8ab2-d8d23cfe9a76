import {
  Body,
  Column,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Row,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
import config from '../tailwind.config';
import EmailButton from './components/button';
import EmailFonts from './components/fonts';
import EmailFooter from './components/footer';
import EmailHeader from './components/header';
import { EmailOrderData } from './utils/types';
import OrderDetails, { CustomerDetails, DeliveryDetails } from './components/orders/order-details';
import { normalizePhone } from './utils/functions';

// interface Props {
//   preview_text: string;
//   name: string;
//   order_info: {
//     customer_info: {
//       name: string;
//       phone: string;
//     };
//     order_link: string;
//     order_id: string;
//     order_status: string;
//     products: {
//       name: string;
//       price: string;
//       quantity: string;
//       image: string;
//     }[];
//     discount: string;
//     delivery_fee: string;
//     total: string;
//   };
//   delivery_info: {
//     delivery_method: string;
//     delivery_address: string;
//   };
// }

const EmailTemplate = (props: EmailOrderData) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}

        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="order_received">
                Your have <br /> a new order
              </EmailHeader>
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name},
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    You just received a new order from{' '}
                    <span className="text-black-secondary font-semibold font-body">
                      {props.order.customer_info.name}!
                    </span>{' '}
                    Here’s a summary of the Order:
                  </Text>

                  <OrderDetails {...props} />

                  {props.order?.customer_info && <CustomerDetails {...props} />}

                  {props.order?.delivery_address && <DeliveryDetails {...props} />}

                  <Text className="font-body text-grey-subtext">
                    If <span className="text-black-muted font-medium font-body">{props.order.customer_info.name}</span>{' '}
                    has not reached out to you yet, you can reach out to them on{' '}
                    <Link
                      href={`tel:${props.order.customer_info.phone.replace('+', '').split('-').join('')}`}
                      className="text-primary-500 font-medium font-body"
                    >
                      {normalizePhone(props.order.customer_info.phone)}
                    </Link>
                    . Click the button below to check it out and confirm the Order
                  </Text>

                  <EmailButton href={props.order.link} className="my-6.25">
                    Checkout & Confirm Order
                  </EmailButton>
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    If you have any questions or complaints regarding this email, please reach out to us on{' '}
                    <Link href="mailto:<EMAIL>" className="font-medium text-primary-500 font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-red" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  preview_text: 'Your order from TechGadgets is pending!',
  store_name: 'TechGadgets',
  name: 'Emily Johnson',
  // store_color: '#FF33A2',
  store_logo: null,
  order: {
    order_id: 'CLGO-6299282',
    link: 'https://example.com/orders/e349f216-48c5-4ef9-b712-45d0fee5b70d',
    order_status: 'Pending',
    products: [
      {
        name: 'Backpack',
        price: '261',
        quantity: '3',
        image: 'https://catlog-s3.s3.eu-west-2.amazonaws.com/ITEMS/4tpefumup.jpeg',
      },
      {
        name: 'Laptop',
        price: '486',
        quantity: 4,
        image: 'https://catlog-s3.s3.eu-west-2.amazonaws.com/ITEMS/r9xxull2i9.jpeg',
      },
      {
        name: 'Sneakers',
        price: '275',
        quantity: 4,
        image: 'https://catlog-s3.s3.eu-west-2.amazonaws.com/ITEMS/uv2h48dfy9.jpeg',
      },
      {
        name: 'Smartphone',
        price: '357',
        quantity: 4,
        image: 'https://catlog-s3.s3.eu-west-2.amazonaws.com/ITEMS/b3hbmxxxem.jpeg',
      },
    ],
    currency: 'NGN',
    total: '5255',
    delivery_method: 'Delivery',
    delivery_area: 'Lekki',
    delivery_address: 'No 2, Oba Dosumu Street, Lekki Phase 1, Lagos',
    fees: [
      {
        label: 'Coupon Code',
        amount: 19,
      },
    ],
    customer_info: {
      name: 'Emily Johnson',
      email: null,
      phone: '+234-80722696716',
    },
  },
} as EmailOrderData;

export default EmailTemplate;
