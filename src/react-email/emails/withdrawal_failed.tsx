import {
  Body,
  Column,
  Container,
  Head,
  <PERSON><PERSON>,
  Hr,
  Html,
  Link,
  Preview,
  Row,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";
import config from "../tailwind.config";
import EmailButton from "./components/button";
import EmailFonts from "./components/fonts";
import EmailFooter from "./components/footer";
import * as React from "react";
import EmailHeader from "./components/header";

interface Props {
  preview_text?: string;
  name: string;
  wallet_link: string;
  amount: string;
  remaining_balance: string;
}

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}

        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="withdrawal_failed" />
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}!
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Your withdrawal of{" "}
                    <span className="text-black-secondary font-semibold font-body">{props.amount}</span> from your
                    Catlog wallet failed and we've reversed the funds back to your wallet.
                    <br />
                    <br />
                    Your wallet balance is now{" "}
                    <span className="text-black-secondary font-semibold font-body">{props.remaining_balance}</span>
                  </Text>

                  <EmailButton href={props.wallet_link} className="my-6.25">
                    Manage Funds
                  </EmailButton>
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    If you have any issues with this reversal, please reach out to us on{" "}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-red" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: "John Doe",
  amount: "20",
  remaining_balance: "3000",
  wallet_link: "https://catlog.shop",
} as Props;

export default EmailTemplate;
