import {
  Body,
  Column,
  Container,
  Head,
  <PERSON>ing,
  Html,
  Img,
  Preview,
  Row,
  Section,
  Text,
  Tailwind,
  Hr,
  Link,
} from "@react-email/components";
import EmailFonts from "./components/fonts";
import config from "../tailwind.config";
import EmailButton from "./components/button";
import EmailHeader from "./components/header";
import EmailFooter from "./components/footer";
import * as React from "react";

interface Props {
  preview_text: string;
  name: string;
  source_amount: string;
  destination_currency: string;
  remaining_balance: string;
  wallet_link: string;
}

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="funds_reversed">
                <>
                  Conversion
                  <br />
                  to {props.destination_currency} Failed!
                </>
              </EmailHeader>
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}!
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Your conversion of{" "}
                    <span className="text-black-secondary font-semibold font-body">{props.source_amount}</span> to
                    <br />
                    <span className="text-black-secondary font-semibold font-body">
                      {" "}
                      {props.destination_currency}
                    </span>{" "}
                    failed and we've reversed the funds back to your wallet.
                  </Text>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Your wallet balance is now{" "}
                    <span className="text-black-secondary font-semibold font-body">{props.remaining_balance}</span>
                  </Text>
                  <EmailButton href={props.wallet_link} className="my-6.25">
                    Manage Funds
                  </EmailButton>
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    If you have any issues with this reversal, please reach out to us on{" "}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-red" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: "John Doe",
  source_amount: "NGN 100",
  destination_currency: "Euros",
  remaining_balance: "NGN 10000",
  wallet_link: "https://catlog.shop",
  preview_text: "Conversion Successful",
} as Props;
export default EmailTemplate;
