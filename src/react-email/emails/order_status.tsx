import {
  Body,
  Column,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Preview,
  Row,
  Section,
  Text,
  Tailwind,
  Hr,
  Link,
} from '@react-email/components';
import EmailFonts from './components/fonts';
import config from '../tailwind.config';
import EmailButton from './components/button';
import EmailHeader from './components/header';
import EmailFooter from './components/footer';
import * as React from 'react';
import { EmailOrderData } from './utils/types';
import EmailHeader2 from './components/header-2';
import { normalizePhone } from './utils/functions';
import OrderDetails, { DeliveryDetails } from './components/orders/order-details';

interface Props extends EmailOrderData {
  status_text: string;
}

const EmailTemplate = (props: Props) => {
  const previewText = `Verify your email address`;
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}

        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader2
                headerImage={props.store_logo}
                avatarPlaceholderChar={props.store_name[0]}
                textColor={props.store_color}
              >
                Your Order <br />
                {props.status_text} {props.order.order_status}
              </EmailHeader2>
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}!
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Your order{' '}
                    <span className="text-black-secondary font-semibold font-body">#{props.order.order_id}</span>{' '}
                    {props.status_text} {props.order.order_status}
                  </Text>

                  <OrderDetails {...props} />

                  {props.order?.delivery_address && <DeliveryDetails {...props} />}

                  <EmailButton
                    href={props.order.link}
                    style={{ backgroundColor: props.store_color }}
                    className="my-6.25"
                  >
                    View Full Order Details
                  </EmailButton>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    You will receive emails whenever the status of your order changes.
                  </Text>
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    If you have any issues with this order, please reach out to us on{' '}
                    <Link
                      href={`tel:${props.store_phone.replace('+', '').split('-').join('')}`}
                      style={{ color: props.store_color }}
                      className="font-body font-medium"
                    >
                      {normalizePhone(props.store_phone)}
                    </Link>
                  </Text>
                </Column>
              </Row>
              {/* <EmailFooter bannerType="light-red" /> */}
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  status_text: 'is',
  preview_text: 'Your order from TechGadgets is pending!',
  store_name: 'TechGadgets',
  name: 'Emily Johnson',
  store_color: '#FF33A2',
  store_logo: null,
  store_phone: '+234-80722696716',
  order: {
    order_id: 'CLGO-6299282',
    link: 'https://example.com/orders/e349f216-48c5-4ef9-b712-45d0fee5b70d',
    order_status: 'Pending',
    products: [
      {
        name: 'Backpack',
        price: '261',
        quantity: 1,
        image: 'https://catlog-s3.s3.eu-west-2.amazonaws.com/ITEMS/4tpefumup.jpeg',
      },
      {
        name: 'Laptop',
        price: '486',
        quantity: 4,
        image: 'https://catlog-s3.s3.eu-west-2.amazonaws.com/ITEMS/r9xxull2i9.jpeg',
      },
      {
        name: 'Sneakers',
        price: '275',
        quantity: 4,
        image: 'https://catlog-s3.s3.eu-west-2.amazonaws.com/ITEMS/uv2h48dfy9.jpeg',
      },
      {
        name: 'Smartphone',
        price: '357',
        quantity: 4,
        image: 'https://catlog-s3.s3.eu-west-2.amazonaws.com/ITEMS/b3hbmxxxem.jpeg',
      },
    ],
    currency: 'NGN',
    total: '5255',
    delivery_method: 'Delivery',
    delivery_area: 'Lekki',
    delivery_address: 'No 2, Oba Dosumu Street, Lekki Phase 1, Lagos',
    fees: [
      {
        label: 'Coupon Code',
        amount: 19,
      },
    ],
    customer_info: {
      name: 'Emily Johnson',
      email: null,
      phone: '+234-80722696716',
    },
  },
} as Props;

export default EmailTemplate;
