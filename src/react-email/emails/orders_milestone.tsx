import {
  Body,
  Column,
  Container,
  Head,
  <PERSON><PERSON>,
  Hr,
  Html,
  Link,
  Preview,
  Row,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
import config from '../tailwind.config';
import EmailFonts from './components/fonts';
import EmailFooter from './components/footer';
import * as React from 'react';
import EmailHeader from './components/header';
import EmailButton from './components/button';

interface Props {
  name: string;
  preview_text: string;
  value: string;
  currency: string;
}

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="orders_milestone">
                🎊 Congrats on <br /> hitting {props.value} <br /> orders!
              </EmailHeader>
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base pt-2.5">
                    Woo-hoo! Your store just hit {props.value} orders on Catlog! 🎉 It's time to do a happy dance!
                  </Text>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base pt-2.5">
                    We really love that your business is climbing new levels, so here’s a virtual fist bump for you 🤜🏽.
                  </Text>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base pt-2.5">
                    You're making waves, and we're here to cheer you on. If you ever need any assistance or have a
                    burning question, we've got your back.
                  </Text>

                  <Text className="font-body text-grey-subtext text-sm sm:text-base pt-2.5">
                    Here’s to more milestones 🥂
                  </Text>
                  <EmailButton className="my-7.5" href="https://app.catlog.shop/my-store/milestones">
                    Go to dashboard
                  </EmailButton>
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    Did you know that when you cross 1M, 5M, 10M & 15M in payments processed, we give you some money
                    back?
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-red" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: 'John Doe',
  value: '100000',
  currency: 'NGN',
} as Props;

export default EmailTemplate;
