import {
  Body,
  Column,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Row,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
import config from '../tailwind.config';
import EmailFonts from './components/fonts';
import EmailFooter from './components/footer';
import * as React from 'react';
import EmailHeader from './components/header';
import classNames from 'classnames';
import { chunkArray, millify, toCurrency, toTitleCase } from './utils/functions';
import { EmailSummaryData, EmailMetric, EmailMetricKey } from './utils/types';

const EmailTemplate = (props: EmailSummaryData) => {
  const storePerformance = chunkArray(props.store_performance, 2);
  const paymentsSummary = chunkArray(props.payments_summary, 2);

  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="weekly_monthly_summaries">
                <>
                  Your {props.summary_type}ly
                  <br />
                  Store Report
                </>
              </EmailHeader>
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Section>
                    <Heading as="h2" className="text-black text-xl sm:text-2xl">
                      Hi {props.name},
                    </Heading>
                    <Text className="font-body text-grey-subtext text-sm sm:text-base">
                      Here’s a quick overview of how{' '}
                      <span className="text-black font-body font-medium">{props.store_name}</span> performed over the
                      last {props.summary_type}.
                    </Text>

                    <Heading as="h2" className="text-black text-xl sm:text-3xl mb-0">
                      Performance Overview
                    </Heading>
                    <Text className="font-body text-grey-subtext text-sm sm:text-base !mt-1">
                      A snapshot of your store's key performance, including traffic, orders, and customer growth:
                    </Text>

                    <Row className="p-2.5 sm:p-3.75 bg bg-grey-fields-100 rounded-20 mb-5">
                      <Column className="overflow-hidden">
                        {storePerformance.map((performance, outerIndex) => (
                          <Section className={outerIndex > 0 ? 'mt-1' : ''} key={`performance-section-${outerIndex}`}>
                            <Row className="bg-white rounded-10">
                              {performance.map((metric, index) => (
                                <React.Fragment key={`performance-${metric.key}`}>
                                  {index === 1 && (
                                    <Column
                                      className="h-full bg-grey-fields-200 p-0.5"
                                      key={`separator-${metric.key}`}
                                    />
                                  )}
                                  <Column key={`column-${metric.key}`}>
                                    <MetricBox
                                      title={toTitleCase(metric.key)}
                                      metric={metric}
                                      icon={keyIcons[metric.key]}
                                    />
                                  </Column>
                                </React.Fragment>
                              ))}
                            </Row>
                          </Section>
                        ))}
                      </Column>
                    </Row>
                    <Heading as="h2" className="text-black text-xl sm:text-3xl capitalize mb-0">
                      Payments Collected
                    </Heading>
                    <Text className="font-body text-grey-subtext text-sm sm:text-base !mt-1">
                      Your store's financial performance across different currencies last {props.summary_type}:
                    </Text>

                    <Row className="p-2.5 sm:p-3.75 bg bg-grey-fields-100 rounded-20 mb-5">
                      <Column className="overflow-hidden">
                        {paymentsSummary.map((payments, outerIndex) => (
                          <Section className={outerIndex > 0 ? 'mt-1' : ''} key={`payments-section-${outerIndex}`}>
                            <Row className="bg-white rounded-10">
                              {payments.map((metric, index) => (
                                <React.Fragment key={`payments-${metric.key}`}>
                                  {index === 1 && (
                                    <Column
                                      className="h-full bg-grey-fields-200 p-0.5"
                                      key={`separator-${metric.key}`}
                                    />
                                  )}
                                  <Column key={`column-${metric.key}`}>
                                    <MetricBox
                                      title={metric.key}
                                      metric={{ ...metric, value: millify(Number(metric.value) / 100, metric.key) }}
                                      icon={keyIcons[metric.key]}
                                    />
                                  </Column>
                                </React.Fragment>
                              ))}
                            </Row>
                          </Section>
                        ))}
                      </Column>
                    </Row>

                    {props?.top_customers && props?.top_customers.length > 0 && (
                      <React.Fragment>
                        <Heading as="h2" className="text-black text-xl sm:text-3xl capitalize mb-0">
                          Top Customers
                        </Heading>
                        <Text className="font-body text-grey-subtext text-sm sm:text-base !mt-1">
                          Customers with the most orders last {props.summary_type}:
                        </Text>

                        <Section className="sm:p-5 p-3.75 bg bg-grey-fields-100 rounded-20">
                          {props.top_customers.map((c, i) => (
                            <Row key={`customer-${c.name}`}>
                              <Column align="left" width="50%">
                                <Img
                                  src={POSITION_BADGES[i + 1]}
                                  alt=""
                                  className="w-6.75 h-6.75 inline-block align-middle mr-1.25 sm:mr-2.5"
                                />
                                <Text className="inline-block h-full align-middle font-body text-black-muted text-sm sm:text-base !my-2.5">
                                  {c.name}
                                </Text>
                              </Column>
                              <Column align="right" width="50%">
                                <Text className="inline-block h-full align-middle font-body font-medium text-black text-1xs sm:text-sm !my-2.5">
                                  {c.orders} Orders
                                </Text>
                              </Column>
                            </Row>
                          ))}
                        </Section>
                      </React.Fragment>
                    )}

                    {props?.top_products && props?.top_products.length > 0 && (
                      <React.Fragment>
                        <Heading as="h2" className="text-black text-xl sm:text-3xl capitalize mb-0">
                          Top Products
                        </Heading>
                        <Text className="font-body text-grey-subtext text-sm sm:text-base -mt-1">
                          Your best-performing products based on orders:
                        </Text>

                        <Section className="m:p-5 p-3.75  bg bg-grey-fields-100 rounded-20">
                          {props.top_products.map((c, i) => (
                            <Row key={`product-${c.name}`}>
                              <Column align="left" width="50%">
                                <Img
                                  src={POSITION_BADGES[i + 1]}
                                  style={{ opacity: i > 2 ? 0 : 1 }}
                                  alt=""
                                  className="w-6.75 h-6.75 inline-block align-middle mr-1.25 sm:mr-2.5"
                                />
                                <Text className="inline-block h-full align-middle font-body text-black-muted text-sm sm:text-base !my-2.5">
                                  {c.name}
                                </Text>
                              </Column>
                              <Column align="right" width="50%">
                                <Text className="inline-block h-full align-middle font-body font-medium text-black text-1xs sm:text-sm !my-2.5">
                                  {c.orders} Orders
                                </Text>
                              </Column>
                            </Row>
                          ))}
                        </Section>
                      </React.Fragment>
                    )}

                    <Text className="inline-block h-full align-middle font-body  text-black text-sm sm:text-base">
                      Keep up the great work, and let’s aim for even better results next {props.summary_type}!
                    </Text>

                    <Row>
                      <Hr />
                      <Text className="font-body text-grey-subtext">
                        If you have any questions or complaints regarding this email, please reach out to us on{' '}
                        <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                          <EMAIL>
                        </Link>
                      </Text>
                      <Hr />
                    </Row>
                  </Section>
                </Column>
              </Row>
              <EmailFooter bannerType="light-green" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

const POSITION_BADGES = {
  1: 'https://res.cloudinary.com/catlog/image/upload/v1739444392/email-images/gold-medal.png',
  2: 'https://res.cloudinary.com/catlog/image/upload/v1739444406/email-images/silver-medal.png',
  3: 'https://res.cloudinary.com/catlog/image/upload/v1739444451/email-images/bronze-medal.png',
};

const keyIcons = {
  [EmailMetricKey.TOTAL_ORDERS]:
    'https://res.cloudinary.com/catlog/image/upload/v1739444056/email-images/Frame_1000001762.png',
  [EmailMetricKey.PAYMENT]:
    'https://res.cloudinary.com/catlog/image/upload/v1739444058/email-images/Frame_1000001763.png',
  [EmailMetricKey.TOTAL_STORE_VISITS]:
    'https://res.cloudinary.com/catlog/image/upload/v1739444060/email-images/Frame_1000001763-2.png',
  [EmailMetricKey.NEW_CUSTOMERS_COUNT]:
    'https://res.cloudinary.com/catlog/image/upload/v1739449451/email-images/store_visits.png',
  [EmailMetricKey.NGN]: 'https://res.cloudinary.com/catlog/image/upload/v1739444405/email-images/naira.png',
  [EmailMetricKey.GHS]: 'https://res.cloudinary.com/catlog/image/upload/v1739444384/email-images/cedis.png',
  [EmailMetricKey.USD]: 'https://res.cloudinary.com/catlog/image/upload/v1739444386/email-images/doll.png',
  [EmailMetricKey.GBP]: 'https://res.cloudinary.com/catlog/image/upload/v1739696605/email-images/pound.png',
  [EmailMetricKey.KES]: 'https://res.cloudinary.com/catlog/image/upload/v1739696605/email-images/shilling.png',
  [EmailMetricKey.ZAR]: 'https://res.cloudinary.com/catlog/image/upload/v1739696605/email-images/rand.png',
  [EmailMetricKey.CAD]: 'https://res.cloudinary.com/catlog/image/upload/v1739444396/email-images/doll.png',
};

EmailTemplate.PreviewProps = {
  name: 'John Doe',
  store_name: 'Test Store',
  summary_type: 'Month',
  store_performance: [
    {
      change: 2.5,
      value: 200,
      key: EmailMetricKey.TOTAL_ORDERS,
    },
    {
      change: 2,
      value: 200,
      key: EmailMetricKey.PAYMENT,
    },
    {
      change: 2,
      value: 200,
      key: EmailMetricKey.TOTAL_STORE_VISITS,
    },
    {
      change: 2,
      value: 200,
      key: EmailMetricKey.NEW_CUSTOMERS_COUNT,
    },
  ],
  payments_summary: [
    {
      change: 2,
      value: 34107,
      key: EmailMetricKey.GHS,
    },
    {
      change: 2,
      value: 34107,
      key: EmailMetricKey.USD,
    },
    {
      change: 2,
      value: 34107,
      key: EmailMetricKey.NGN,
    },
    {
      change: 2,
      value: 34107,
      key: EmailMetricKey.CAD,
    },
  ],
  top_customers: [
    {
      name: 'Babalola Iana',
      orders: 4344,
    },
    {
      name: 'Joseph James',
      orders: 434,
    },
    {
      name: 'Kayode Bulus',
      orders: 55,
    },
  ],
  top_products: [
    {
      name: 'Jeans',
      orders: 4344,
    },
    {
      name: 'Boots',
      orders: 434,
    },
    {
      name: 'Nice stuvs',
      orders: 55,
    },
    {
      name: 'Something',
      orders: 33,
    },
    {
      name: 'Something',
      orders: 20,
    },
  ],
} as EmailSummaryData;

export default EmailTemplate;

interface MetricProps {
  metric: EmailMetric;
  icon: string;
  title: string;
}

export const MetricBox: React.FC<MetricProps> = ({ metric, icon, title }) => {
  return (
    <Column valign="top" width="50%" className="py-5 pl-6.25 pr-3.75">
      <Section>
        <Row>
          <Column>
            <Img className="sm:w-12.5 sm:h-12.5 h-10 w-10" src={icon} alt="" />
          </Column>
          <Column>
            <Section className="">
              <Row className="bg-grey-fields-100 w-5 p-2.5 py-1.25 rounded-full ml-auto !mr-0">
                <Column className="80%">
                  <Text
                    className={classNames('m-0 text-xs sm:text-sm font-semibold font-body', {
                      [metric?.change > 0 ? 'text-accent-green-500' : 'text-accent-red-500']: true,
                    })}
                  >
                    {metric?.change}%
                  </Text>
                </Column>
                <Column width="20%">
                  <Img
                    className="sm:w-3.75 sm:h-3.75 w-2.5 h-2.5"
                    src={
                      metric?.change > 0
                        ? 'https://res.cloudinary.com/catlog/image/upload/v1739444403/email-images/Group_128.png'
                        : 'https://res.cloudinary.com/catlog/image/upload/v1739444647/email-images/red_arrow.png'
                    }
                  ></Img>
                </Column>
              </Row>
            </Section>
          </Column>
        </Row>
      </Section>
      <Text className="m-0 font-body text-black-placeholder text-xs sm:text-sm mt-3.75">{title}</Text>
      <Heading as="h3" className="text-black font-bold text-lg  sm:text-2xl !my-0 mt-1.25">
        {metric.value}
      </Heading>
    </Column>
  );
};
