import {
  Body,
  Column,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Preview,
  Row,
  Section,
  Text,
  Tailwind,
  Hr,
  Link,
} from '@react-email/components';
import EmailFonts from '../components/fonts';
import config from '../../tailwind.config';
import EmailButton from '../components/button';
import EmailHeader from '../components/header';
import EmailFooter from '../components/footer';
import * as React from 'react';

interface Props {
  name: string;
  customer_phone: string;
  customer_name: string;
  order_link: string;
  preview_text?: string;
}

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="order_received" />
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}!
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    You just received an order from{' '}
                    <span className="text-black-secondary font-semibold font-body">{props.customer_name}</span>, click
                    on the button below to check it out and confirm it.
                  </Text>

                  <EmailButton href={props.order_link} className="my-6.25">
                    Checkout Order
                  </EmailButton>
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    If <span className="text-black-muted font-medium font-body">{props.customer_name}</span> has not
                    reached out to you yet, you can reach out to them on{' '}
                    <Link href={`tel:${props.customer_phone}`} className="text-primary-500 font-medium font-body">
                      {props.customer_phone}
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-red" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: 'John Doe',
  customer_name: 'Bill Doe',
  customer_phone: '08012345678',
  order_link: 'https://catlog.shop',
} as Props;

export default EmailTemplate;
