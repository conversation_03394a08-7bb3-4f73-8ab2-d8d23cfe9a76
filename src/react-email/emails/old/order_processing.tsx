import {
  Body,
  Column,
  Container,
  Head,
  <PERSON>ing,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Row,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
import config from '../../tailwind.config';
import EmailButton from '../components/button';
import EmailFonts from '../components/fonts';
import EmailHeader2 from '../components/header-2';

interface Props {
  preview_text: string;
  store_name: string;
  name: string;
  store_color: string;
  store_logo: string;
  order_info: {
    order_id: string;
    order_link: string;
    order_status: string;
    products: {
      name: string;
      price: string;
      quantity: string;
      image: string;
    }[];
    discount: string;
    delivery_fee: string;
    total: string;
  };
  delivery_info: {
    delivery_method: string;
    delivery_address: string;
  };
}

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}

        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader2 headerImage={props.store_logo} textColor={props.store_color}>
                Your Order is Being <br /> Processed!
              </EmailHeader2>
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Holla {props.name},
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Your order{' '}
                    <span className="text-black-secondary font-semibold font-body">{props.order_info.order_id}</span>{' '}
                    with {props.store_name} is being prepared and will soon be ready.
                  </Text>

                  <Text className="text-grey-subtext font-body text-sm sm:text-base">
                    Here’s a summary of your Order:
                  </Text>
                  <Text className="inline-block h-full align-middle font-body font-medium  text-black text-sm sm:text-base">
                    Order Details
                  </Text>

                  <Section className="p-5 bg bg-grey-fields-100 rounded-20">
                    <Row>
                      <Column align="left" width="50%" className="">
                        <Text className="inline-block h-full align-middle font-body text-black-muted text-sm sm:text-base">
                          Order Number
                        </Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="inline-block h-full align-middle font-body font-medium  text-black text-sm sm:text-base">
                          {props.order_info.order_id}
                        </Text>
                      </Column>
                    </Row>

                    <Row>
                      <Column align="left" width="50%" className="">
                        <Text className="inline-block h-full align-middle font-body text-black-muted text-sm sm:text-base">
                          Status
                        </Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="inline-block h-full align-middle font-body font-medium  text-black text-sm sm:text-base">
                          {props.order_info.order_status}
                        </Text>
                      </Column>
                    </Row>
                  </Section>

                  <Text className="inline-block h-full align-middle font-body font-medium  text-black text-sm sm:text-base">
                    Payment Summary
                  </Text>

                  <Section className="p-5 bg bg-grey-fields-100 rounded-20">
                    {props.order_info.products.map((product, index) => (
                      <Row key={index}>
                        <Column align="left" width="50%" className="">
                          <Img src={product.image} alt="" className="w-6.75 h-6.75 inline-block align-middle mr-2.5" />
                          <Text className="inline-block h-full align-middle font-body text-black-muted text-sm sm:text-base">
                            {product.name} x {product.quantity}
                          </Text>
                        </Column>
                        <Column align="right" width="50%" className="">
                          <Text className="inline-block h-full align-middle font-body font-medium  text-black text-sm sm:text-base">
                            {product.price}
                          </Text>
                        </Column>
                      </Row>
                    ))}
                  </Section>

                  <Text className="inline-block h-full align-middle font-body font-medium  text-black text-sm sm:text-base">
                    Delivery Information
                  </Text>

                  <Section className="p-5 bg bg-grey-fields-100 rounded-20">
                    <Row>
                      <Column align="left" width="50%" className="">
                        <Text className="inline-block h-full align-middle font-body text-black-muted text-sm sm:text-base">
                          Delivery Method
                        </Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="inline-block h-full align-middle font-body font-medium  text-black text-sm sm:text-base">
                          {props.delivery_info.delivery_method}
                        </Text>
                      </Column>
                    </Row>

                    <Row>
                      <Column align="left" width="20%" className="">
                        <Text className="inline-block h-full align-middle font-body text-black-muted text-sm sm:text-base">
                          Delivery Address
                        </Text>
                      </Column>
                      <Column align="right" width="80%" className="">
                        <Text className="inline-block h-full align-middle font-body font-medium  text-black text-sm sm:text-base">
                          {props.delivery_info.delivery_address}
                        </Text>
                      </Column>
                    </Row>
                  </Section>

                  <EmailButton
                    href={props.order_info.order_link}
                    style={{ backgroundColor: props.store_color }}
                    className="my-6.25"
                  >
                    View full Order Information
                  </EmailButton>

                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    You will receive emails whenever {' '}
                    <span className="text-black-secondary font-semibold font-body" style={{ color: props.store_color }}>
                      {props.store_name}
                    </span>{' '}
                    updates the status of this order.
                  </Text>

                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    If you have any questions or complaints regarding this order, please reach out to us on{' '}
                    <Link
                      href="mailto:<EMAIL>"
                      style={{ color: props.store_color }}
                      className="font-medium font-body"
                    >
                      <EMAIL>
                    </Link>
                  </Text>
                </Column>
              </Row>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: 'John Doe',
  store_name: 'Catlog Shop',
  store_logo: 'https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/foot-banner-light-yellow.png',
  store_color: '#3D5541',
  order_info: {
    order_link: 'https://catlog.shop',
    order_id: 'CLGO03284',
    order_status: 'Processing',
    products: [
      {
        name: 'Item',
        price: 'NGN 100',
        quantity: '1',
        image: 'https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/foot-banner-light-yellow.png',
      },
      {
        name: 'Item2',
        price: 'NGN 200',
        quantity: '2',
        image: 'https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/foot-banner-light-yellow.png',
      },
    ],
    discount: 'NGN 0',
    delivery_fee: 'NGN 0',
    total: 'NGN 300',
  },
  delivery_info: {
    delivery_method: 'Delivery',
    delivery_address: '123 Main St, Anytown, ST 12345',
  },
} as Props;

export default EmailTemplate;
