import {
  Body,
  Column,
  Container,
  Head,
  <PERSON><PERSON>,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Row,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
import config from '../../tailwind.config';
import EmailFonts from '../components/fonts';
import EmailFooter from '../components/footer';
import * as React from 'react';
import EmailHeader from '../components/header';
import classNames from 'classnames';

type Metric = {
  value: number | string;
  change: number;
};

interface Props {
  preview_text: string;

  name: string;
  store_name: string;
  summary_type: string;

  store_performance: {
    total_orders: Metric;
    payment_count: Metric;
    total_store_visits: Metric;
    new_customers_count: Metric;
  };

  payments_summary: {
    NGN: Metric;
    CEDIS: Metric;
    USD: Metric;
    EUR: Metric;
  };

  top_customers: {
    name: string;
    orders: number;
  }[];

  top_products: {
    name: string;
    orders: number;
  }[];
}

const POSITION_BADGES = {
  1: 'https://res.cloudinary.com/catlog/image/upload/v1739444392/email-images/gold-medal.png',
  2: 'https://res.cloudinary.com/catlog/image/upload/v1739444406/email-images/silver-medal.png',
  3: 'https://res.cloudinary.com/catlog/image/upload/v1739444451/email-images/bronze-medal.png',
};

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="weekly_monthly_summaries">
                <>
                  Your {props.summary_type}ly
                  <br />
                  Catlog Store Report
                </>
              </EmailHeader>
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Section>
                    <Heading as="h2" className="text-black text-lg sm:text-xl">
                      Hi {props.name}!
                    </Heading>
                    <Text className="font-body text-grey-subtext text-sm sm:text-base">
                      We hope you've had a productive and rewarding {props.summary_type}! Here’s a quick overview of{' '}
                      <span className="text-black font-body font-medium">{props.store_name}</span>'s key performance
                      metrics for the past {props.summary_type}. Dive into the numbers and insights that have shaped
                      your store’s success.
                    </Text>

                    <Heading as="h2" className="text-black text-xl sm:text-3xl">
                      Store Performance Overview
                    </Heading>
                    <Text className="font-body text-grey-subtext text-sm sm:text-base">
                      A snapshot of your store's key performance indicators, including traffic, orders, and customer
                      growth:
                    </Text>

                    <Row className="p-2.5 sm:p-3.75 bg bg-grey-fields-100 rounded-20 mb-5">
                      <Column className="overflow-hidden">
                        <Section className="">
                          <Row className="bg-white rounded-10 ">
                            {/* metrics */}
                            <MetricBox
                              title="Total Orders"
                              metric={props.store_performance.total_orders}
                              icon="https://res.cloudinary.com/catlog/image/upload/v1739444056/email-images/Frame_1000001762.png"
                            />
                            <Column className="h-full bg-grey-fields-200 p-[0.5px]"></Column>
                            <MetricBox
                              title="No. of payments"
                              metric={props.store_performance.payment_count}
                              icon="https://res.cloudinary.com/catlog/image/upload/v1739444058/email-images/Frame_1000001763.png"
                            />
                          </Row>
                        </Section>
                        <Section className="mt-2.5">
                          <Row className="bg-white rounded-10 ">
                            {/* metrics */}
                            <MetricBox
                              title="Total Store Visits"
                              metric={props.store_performance.total_store_visits}
                              icon="https://res.cloudinary.com/catlog/image/upload/v1739449451/email-images/store_visits.png"
                            />
                            <Column className="h-full bg-grey-fields-200 p-[0.5px]"></Column>
                            <MetricBox
                              title="No. of New Customers"
                              metric={props.store_performance.new_customers_count}
                              icon="https://res.cloudinary.com/catlog/image/upload/v1739444060/email-images/Frame_1000001763-2.png"
                            />
                          </Row>
                        </Section>
                      </Column>
                    </Row>

                    <Heading as="h2" className="text-black text-xl sm:text-3xl capitalize">
                      Payments Received This {props.summary_type}
                    </Heading>
                    <Text className="font-body text-grey-subtext text-sm sm:text-base">
                      An overview of your store's financial performance across different currencies:
                    </Text>

                    <Row className="p-2.5 sm:p-3.75 bg bg-grey-fields-100 rounded-20 mb-5">
                      <Column className="overflow-hidden">
                        <Section className="">
                          <Row className="bg-white rounded-10 ">
                            {/* metrics */}
                            <MetricBox
                              title="Payments Received(NGN)"
                              metric={props.payments_summary.NGN}
                              icon="https://res.cloudinary.com/catlog/image/upload/v1739444405/email-images/naira.png"
                            />
                            <Column className="h-full bg-grey-fields-200 p-[0.5px]"></Column>
                            <MetricBox
                              title="Payments Received(Cedis)"
                              metric={props.payments_summary.CEDIS}
                              icon="https://res.cloudinary.com/catlog/image/upload/v1739444384/email-images/cedis.png"
                            />
                          </Row>
                        </Section>
                        <Section className="mt-2.5">
                          <Row className="bg-white rounded-10 ">
                            {/* metrics */}
                            <MetricBox
                              title="Payments Received(USD)"
                              metric={props.payments_summary.USD}
                              icon="https://res.cloudinary.com/catlog/image/upload/v1739444386/email-images/doll.png"
                            />
                            <Column className="h-full bg-grey-fields-200 p-[0.5px]"></Column>
                            <MetricBox
                              title="Payments Received(EUR)"
                              metric={props.payments_summary.EUR}
                              icon="https://res.cloudinary.com/catlog/image/upload/v1739444390/email-images/euros.png"
                            />
                          </Row>
                        </Section>
                      </Column>
                    </Row>

                    <Heading as="h2" className="text-black text-xl sm:text-3xl capitalize">
                      Your Top 3 Customers This {props.summary_type}
                    </Heading>
                    <Text className="font-body text-grey-subtext text-sm sm:text-base">
                      Your most valuable customers who contributed significantly to your store's growth:
                    </Text>

                    <Section className="sm:p-5 p-3.75 bg bg-grey-fields-100 rounded-20">
                      {props.top_customers.map((c, i) => (
                        <Row key={i}>
                          <Column align="left" width="50%" className="">
                            <Img
                              src={POSITION_BADGES[i + 1]}
                              alt=""
                              className="w-6.75 h-6.75 inline-block align-middle mr-1.25 sm:mr-2.5"
                            />
                            <Text className="inline-block h-full align-middle font-body text-black-muted text-sm sm:text-base">
                              {c.name}
                            </Text>
                          </Column>
                          <Column align="right" width="50%" className="">
                            <Text className="inline-block h-full align-middle font-body font-medium  text-black text-sm sm:text-base">
                              {c.orders} Orders
                            </Text>
                          </Column>
                        </Row>
                      ))}
                    </Section>

                    <Heading as="h2" className="text-black text-xl sm:text-3xl capitalize">
                      Your Top 5 Products This {props.summary_type}
                    </Heading>
                    <Text className="font-body text-grey-subtext text-sm sm:text-base">
                      Your best-performing products based on customer demand:
                    </Text>

                    <Section className="m:p-5 p-3.75  bg bg-grey-fields-100 rounded-20">
                      {props.top_products.map((c, i) => (
                        <Row key={i}>
                          <Column align="left" width="50%" className="">
                            <Img
                              src={POSITION_BADGES[i + 1]}
                              style={{ opacity: i > 2 ? 0 : 1 }}
                              alt=""
                              className="w-6.75 h-6.75 inline-block align-middle mr-1.25 sm:mr-2.5"
                            />
                            <Text className="inline-block h-full align-middle font-body text-black-muted text-sm sm:text-base">
                              {c.name}
                            </Text>
                          </Column>
                          <Column align="right" width="50%" className="">
                            <Text className="inline-block h-full align-middle font-body font-medium  text-black text-sm sm:text-base">
                              {c.orders} Orders
                            </Text>
                          </Column>
                        </Row>
                      ))}
                    </Section>

                    <Text className="inline-block h-full align-middle font-body  text-black text-sm sm:text-base">
                      Keep up the great work, and let’s aim for even greater success next {props.summary_type}!
                    </Text>

                    <Row>
                      <Hr />
                      <Text className="font-body text-grey-subtext">
                        If you have any questions or complaints regarding this email, please reach out to us on{' '}
                        <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                          <EMAIL>
                        </Link>
                      </Text>
                      <Hr />
                    </Row>
                  </Section>
                </Column>
              </Row>
              <EmailFooter bannerType="light-green" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: 'John Doe',
  store_name: 'Test Store',
  summary_type: 'Month',
  store_performance: {
    total_orders: {
      change: 2.5,
      value: 200,
    },
    payment_count: {
      change: 2,
      value: 200,
    },
    total_store_visits: {
      change: 2,
      value: 200,
    },
    new_customers_count: {
      change: 2,
      value: 200,
    },
  },
  payments_summary: {
    CEDIS: {
      change: 2,
      value: '₵ 34,107',
    },
    NGN: {
      change: 2,
      value: 'NGN 209,812',
    },
    USD: {
      change: 2,
      value: 'USD 209,812',
    },
    EUR: {
      change: 2,
      value: 'EUR 209,812',
    },
  },
  top_customers: [
    {
      name: 'Babalola Iana',
      orders: 4344,
    },
    {
      name: 'Joseph James',
      orders: 434,
    },
    {
      name: 'Kayode Bulus',
      orders: 55,
    },
  ],
  top_products: [
    {
      name: 'Jeans',
      orders: 4344,
    },
    {
      name: 'Boots',
      orders: 434,
    },
    {
      name: 'Nice stuvs',
      orders: 55,
    },
    {
      name: 'Something',
      orders: 33,
    },
    {
      name: 'Something',
      orders: 20,
    },
  ],
} as Props;

export default EmailTemplate;

interface MetricProps {
  metric: Metric;
  icon: string;
  title: string;
}
export const MetricBox: React.FC<MetricProps> = ({ metric, icon, title }) => {
  return (
    <>
      <Column valign="top" width="50%" className="py-5 pl-6.25 pr-3.75 ">
        <Section>
          <Row>
            <Column>
              <Img className="sm:w-12.5 sm:h-12.5 h-10 w-10" src={icon} alt="" />
            </Column>
            <Column>
              <Section className="">
                <Row className="bg-grey-fields-100 w-5 p-2.5 py-1.25 rounded-full">
                  <Column className="80%">
                    <Text
                      className={classNames('m-0 text-xs sm:text-sm font-semibold font-body', {
                        [metric.change >= 0 ? 'text-accent-green-500' : 'text-accent-red-500']: true,
                      })}
                    >
                      {metric.change}%
                    </Text>
                  </Column>
                  <Column width="20%">
                    <Img
                      className="sm:w-3.75 sm:h-3.75 w-2.5 h-2.5"
                      src={
                        metric.change >= 0
                          ? 'https://res.cloudinary.com/catlog/image/upload/v1739444403/email-images/Group_128.png'
                          : 'https://res.cloudinary.com/catlog/image/upload/v1739444647/email-images/red_arrow.png'
                      }
                    ></Img>
                  </Column>
                </Row>
              </Section>
            </Column>
          </Row>
        </Section>
        <Text className="m-0 font-body text-black-placeholder text-xs sm:text-sm mt-3.75">{title}</Text>
        <Heading as="h3" className="text-black font-bold text-lg  sm:text-2xl !my-0 mt-1.25">
          {metric.value}
        </Heading>
      </Column>
    </>
  );
};
