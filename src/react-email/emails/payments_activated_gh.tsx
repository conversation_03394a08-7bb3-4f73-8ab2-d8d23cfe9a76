import {
  Body,
  Column,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Preview,
  Row,
  Section,
  Text,
  Tailwind,
  Hr,
  Link,
} from "@react-email/components";
import EmailFonts from "./components/fonts";
import config from "../tailwind.config";
import EmailButton from "./components/button";
import EmailHeader from "./components/header";
import EmailFooter from "./components/footer";
import * as React from "react";

interface Props {
  preview_text: string;
  name: string;
  store_name: string;
  store_phone: string;
  total_amount: string;
  bank_name: string;
  bank_logo: string;
  account_name: string;
  app_link: string;
}

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}

        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="payments_activated" />
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}!
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Your KYC information has been approved for{" "}
                    <span className="text-black-secondary font-semibold font-body">{props.store_name}</span> and
                    payments has been activated for your store.
                  </Text>
                  <Text className="text-grey-subtext font-medium font-body text-sm sm:text-base">This means that:</Text>
                  <ul className="list-image-[url(https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/check.png)] ">
                    <li className="font-body text-black-muted py-1.25 text-sm sm:text-base">
                    You can now accept payments from cards and mobile money wallets
                    </li>
                    <li className="font-body text-black-muted py-1.25 text-sm sm:text-base">You can now create invoice links</li>
                    <li className="font-body text-black-muted py-1.25 text-sm sm:text-base">
                    You can now receive payments on your store
                    </li>
                  </ul>
                 
                  <Text className="text-grey-subtext font-body text-base">Time to start racking some coins.</Text>
                  <EmailButton href={props.app_link} className="my-6.25">
                  Try Out Payments
                  </EmailButton>
                  <Hr />
                  <Text className="font-body text-grey-subtext sm:text-1sm">
                    If you have any questions or complaints regarding this email, please reach out to us on{" "}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-green" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: "John Doe",
  store_name: "Catlog",
  store_phone: "***********",
  total_amount: "100",
  bank_name: "GTB",
  bank_logo: "https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/foot-banner-light-yellow.png",
  account_name: "John Doe Gt",
  app_link: "https://catlog.shop",
} as Props;

export default EmailTemplate;
