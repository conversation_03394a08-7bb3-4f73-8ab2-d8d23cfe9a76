import {
  Body,
  Column,
  Container,
  Head,
  <PERSON>ing,
  Hr,
  Html,
  Link,
  Preview,
  Row,
  Section,
  Tailwind,
  Text
} from "@react-email/components";
import config from "../tailwind.config";
import EmailButton from "./components/button";
import EmailFonts from "./components/fonts";
import EmailFooter from "./components/footer";
import * as React from "react";
import EmailHeader from "./components/header";

interface Props {
  preview_text: string;
  name: string;
  payment_amount: string;
  token_amount: string;
  tokens_balance: string;
  date: string;
  payment_method_name: string;
  cta_link: string;
}

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="extra_token_purchase" />
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Your payment of{" "}
                    <span className="text-black-secondary font-semibold font-body">{props.payment_amount}</span> was
                    successful and your token balance has been credited with{" "}
                    <span className="text-black-secondary font-semibold font-body">{props.token_amount}</span>
                  </Text>

                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Here’s a summary of the payment.
                  </Text>
                  <Section className="p-5 bg bg-grey-fields-100 rounded-20">
                    <Row>
                      <Column>
                        <Text className="m-0 font-body text-grey-subtext">Total amount</Text>
                      </Column>
                    </Row>
                    <Row>
                      <Column width="50%">
                        <Text className=" font-body text-black text-base sm:text-lg font-semibold">
                          {props.payment_amount}
                        </Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="inline-block h-full align-middle font-body text-black-muted text-base">
                          {props.payment_method_name}
                        </Text>
                      </Column>
                    </Row>
                    <Row>
                      <Hr />
                    </Row>
                    <Row className="py-2.5">
                      <Column width="50%">
                        <Text className=" font-body text-black-placeholder m-0">Tokens Bought</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="inline-block h-full align-middle font-body font-medium  text-black text-base m-0">
                          {props.token_amount}
                        </Text>
                      </Column>
                    </Row>
                    <Row className="py-2.5">
                      <Column width="50%">
                        <Text className=" font-body text-black-placeholder m-0">Tokens Balance</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="inline-block h-full align-middle font-body font-medium  text-black text-base m-0">
                          {props.tokens_balance}
                        </Text>
                      </Column>
                    </Row>
                    <Row className="py-2.5">
                      <Column width="50%">
                        <Text className=" font-body text-black-placeholder m-0">Date</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="inline-block h-full align-middle font-body font-medium  text-black text-base m-0">
                          {props.date}
                        </Text>
                      </Column>
                    </Row>
                  </Section>
                  <EmailButton href={props.cta_link} className="my-6.25">
                    Manage Chowbot Tokens
                  </EmailButton>
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    If you have any questions or complaints regarding this payment, please reach out to us on{" "}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-green" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: "John Doe",
  payment_amount: "100",
  payment_method_name: "GTB",
  date: "12/12/2022",
  cta_link: "https://catlog.shop",
  token_amount: "300",
  tokens_balance: "330",
} as Props;

export default EmailTemplate;
