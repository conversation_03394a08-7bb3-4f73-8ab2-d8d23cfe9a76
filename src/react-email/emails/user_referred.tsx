import {
  Body,
  Column,
  Container,
  Head,
  <PERSON><PERSON>,
  Hr,
  Html,
  Link,
  Preview,
  Row,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";
import config from "../tailwind.config";
import EmailFonts from "./components/fonts";
import EmailFooter from "./components/footer";
import * as React from "react";
import EmailHeader from "./components/header";
import EmailButton from "./components/button";

interface Props {
  name: string;
  preview_text: string;
  user_name: string;
}

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="user_referred"></EmailHeader>
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base pt-2.5">
                    <span className="text-black-secondary font-body font-medium">{props.user_name}</span> has signed up
                    to Catlog with your referral code. You will receive some Catlog Credits when they make their first
                    subscription payment.
                  </Text>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base pt-2.5">
                    We’ve also given them some Credits they can use to pay for their subscription.
                  </Text>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base pt-2.5">
                    Thank you for inviting your friends to Catlog. You rock!! 🥂
                  </Text>
                  <EmailButton className="my-7.5" href="https://catlog.shop/app/dashboard">
                    Go to dashboard
                  </EmailButton>
                  <Hr />
                  <Text className="font-body text-grey-subtext sm:text-1sm">
                    If you have any questions or complaints regarding this email, please reach out to us on{" "}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-yellow" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: "John Doe",
  user_name: "Jomo",
} as Props;

export default EmailTemplate;
