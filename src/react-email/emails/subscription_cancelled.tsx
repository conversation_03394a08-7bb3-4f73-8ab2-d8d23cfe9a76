import {
  Body,
  Column,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Preview,
  Row,
  Section,
  Text,
  Tailwind,
  Hr,
  Link,
} from '@react-email/components';
import EmailFonts from './components/fonts';
import config from '../tailwind.config';
import EmailButton from './components/button';
import EmailHeader from './components/header';
import EmailFooter from './components/footer';
import * as React from 'react';

interface Props {
  name: string;
  plan_name: string;
  new_store_slug: string;
  preview_text: string;
  downgrade_message_1: string;
  downgrade_message_2: string;
}

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="subscription_cancelled" />
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}!
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Your subscription to{' '}
                    <span className="text-black-secondary font-semibold font-body">{props.plan_name} plan</span> has
                    been cancelled.
                  </Text>
                  <Text className="text-grey-subtext font-medium font-body text-base">This means that:</Text>
                  <ul className="list-image-[url(https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/check.png)]">
                    <li className="font-body text-black-muted py-1.25">{props.downgrade_message_1}</li>
                    <li className="font-body text-black-muted py-1.25">{props.downgrade_message_2} </li>
                    {/* <li className="font-body text-black-muted py-1.25">{props.downgrade_message_3} </li> */}
                  </ul>
                  {/* <Text className="text-grey-subtext font-body text-base">Here’s your new store link:</Text>

                  <Text className="text-center py-5 font-semibold bg-grey-fields-100 rounded-20 text-primary-500 font-body text-base sm:text-lg">
                    https://catlog.shop/{props.new_store_slug}
                  </Text> */}
                  <Text className="font-body text-grey-subtext text-sm sm:text-base pt-2.5">
                    If you think you'll still love to continue using these features, please head over to your dashboard
                    to renew your subscription.
                  </Text>
                  <EmailButton href="https://catlog.shop/app/dashboard?renew_plan=true" className="my-6.25">
                    Renew Subscription
                  </EmailButton>
                  <Hr />
                  <Text className="font-body text-grey-subtext sm:text-1sm">
                    If you have any issues with this subscription, please reach out to us on{' '}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-red" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: 'John Doe',
  plan_name: 'Basic',
  new_store_slug: 'catlog',
  downgrade_message_1: 'This is a test message',
  downgrade_message_2: 'This is a test message',
} as Props;

export default EmailTemplate;
