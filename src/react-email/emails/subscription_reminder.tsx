import {
  Body,
  Column,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Preview,
  Row,
  Section,
  Text,
  Tailwind,
  Hr,
  Link,
} from '@react-email/components';
import EmailFonts from './components/fonts';
import config from '../tailwind.config';
import EmailButton from './components/button';
import EmailHeader from './components/header';
import EmailFooter from './components/footer';
import * as React from 'react';

interface Props {
  name: string;
  store_name: string;
  preview_text: string;
  plan_name: string;
  amount: string;
  next_payment_date: string;
  interval: string;
  dashboard_link: string;
  auto_renewal: string;
}

const SubscriptionReminderEmail = (props: Props) => {
  const previewText = props.preview_text || 'Your subscription will renew soon';
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="subscription_reminder" />
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}!
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    This is a friendly reminder that your{' '}
                    <span className="text-black-secondary font-semibold font-body">{props.plan_name} plan</span>{' '}
                    subscription will automatically renew on {props.next_payment_date}.
                  </Text>

                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Here are the details of your upcoming subscription renewal:
                  </Text>
                  <Section className="p-5 bg bg-grey-fields-100 rounded-20 mb-5">
                    <Row className="py-1.25">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Plan Name</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium text-black">
                          {props.plan_name}
                        </Text>
                      </Column>
                    </Row>

                    <Row className="py-1.25">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Interval</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium text-black">
                          {props.interval}
                        </Text>
                      </Column>
                    </Row>

                    <Row className="py-1.25">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Amount</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium text-black">
                          NGN {props.amount}
                        </Text>
                      </Column>
                    </Row>

                    <Row className="py-1.25">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Next payment date</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium text-black">
                          {props.next_payment_date}
                        </Text>
                      </Column>
                    </Row>
                  </Section>
                  <EmailButton href={props.dashboard_link} className="my-6.25">
                    Manage Subscription
                  </EmailButton>
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    {props.auto_renewal === 'On' ? (
                      <>
                        Your subscription is set to automatically renew. If you wish to cancel or modify your
                        subscription, please visit your account settings before the renewal date.
                      </>
                    ) : (
                      <>
                        Your auto-renewal is currently turned off. Your subscription will expire on{' '}
                        {props.next_payment_date} unless you manually renew it.
                      </>
                    )}
                  </Text>
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    If you have any questions about your subscription, please reach out to us on{' '}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-green" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

SubscriptionReminderEmail.PreviewProps = {
  name: 'John Doe',
  store_name: "John's Store",
  preview_text: 'Your subscription will renew soon',
  plan_name: 'Premium',
  amount: '15,000',
  next_payment_date: 'March 13, 2025',
  interval: 'month',
  dashboard_link: '#',
  auto_renewal: 'On',
} as Props;

export default SubscriptionReminderEmail;
