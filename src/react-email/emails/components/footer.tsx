import { Row, Column, Heading, Section, Img, Text } from '@react-email/components';
import * as React from 'react';

interface Props {
  bannerType: keyof typeof bannerImages;
}
const EmailFooter: React.FC<Props> = ({ bannerType }) => {
  return (
    <Row className="pt-6.25 px-[7%] sm:px-[12%]">
      <Column className="" width="50%">
        <Heading as="h3" className="text-primary-500 text-xl md:text-[25px]">
          Sell Easily On <br />
          Social Media
        </Heading>
        <Text className="font-body text-grey-subtext !mb-0">© {new Date().getFullYear()} Catlog Inc.</Text>
        <Section className="max-w-">
          <Row>
            <Text>
              {footerImages.map(({ link, image }, index) => (
                <a className="w-5 mr-3.75 sm:mr-5 inline-block" href={link} key={index}>
                  <Img src={image} alt="" className="w-6.75" />
                </a>
              ))}
            </Text>
          </Row>
        </Section>
      </Column>
      <Column width="[50%]">
        <Img className="w-full" src={bannerImages[bannerType]} />
      </Column>
    </Row>
  );
};
export default EmailFooter;

const bannerImages = {
  'light-purple':
    'https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/foot-banner-light-purple.png',
  'light-green': 'https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/foot-banner-light-green.png',
  'light-orange':
    'https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/foot-banner-light-orange.png',
  'light-yellow':
    'https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/foot-banner-light-yellow.png',
  'light-red': 'https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/foot-banner-light-red.png',
};

const footerImages = [
  {
    link: 'https://instagram.com/catlogshop',
    image: 'https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/instagram.png',
  },
  {
    link: 'https://twitter.com/catlogshop',
    image: 'https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/twitter.png',
  },
  {
    link: 'https://api.whatsapp.com/send/?phone=2347081606751',
    image: 'https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/whatsapp.png',
  },
  {
    link: 'https://www.youtube.com/@catlogshop',
    image: 'https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/youtube.png',
  },
];
