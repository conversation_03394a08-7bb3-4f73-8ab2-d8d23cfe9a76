import { Row, Column, Img, Heading, Section, Text } from '@react-email/components';
import * as React from 'react';

interface Props {
  children?: React.ReactNode;
  textColor?: string;
  headerImage?: string;
  avatarPlaceholderChar?: string;
}

const EmailHeader2: React.FC<Props> = ({ textColor, children, headerImage, avatarPlaceholderChar }) => {
  const overlayRgb = hexToRgb(textColor);
  const colorOverlay = `rgba(${overlayRgb.r}, ${overlayRgb.g}, ${overlayRgb.b}, 0.15)`;
  const whiteOverlay = `rgba(255, 255, 255, 0.6)`;
  return (
    <Row>
      <Column
        style={{
          background: `linear-gradient(${whiteOverlay}, ${whiteOverlay}),linear-gradient(${colorOverlay}, ${colorOverlay}),url('https://res.cloudinary.com/catlog/image/upload/v1739278956/email-images/Group.png')`,
          backgroundSize: 'cover',
          backgroundPosition: '30% 100%',
          backgroundRepeat: 'no-repeat',
        }}
        className="py-[20px] sm:py-[40px] px-[7%] sm:px-[8%] relative"
        align="center"
      >
        {headerImage && (
          <Img
            src={headerImage}
            alt=""
            className="h-12.5 w-12.5 sm:w-15 sm:h-15 rounded-full overflow-hidden mx-auto relative z-30 mb-[30px] sm:mb-[35px]"
          />
        )}

        {!headerImage && avatarPlaceholderChar && (
          <Text
            className="text-2xl sm:text-3xl bg-white px-6 py-4 rounded-full inline-table font-bold mx-auto mb-[20px] sm:mb-[25px]"
            style={{ color: textColor }}
          >
            {avatarPlaceholderChar}
          </Text>
        )}

        <Heading
          style={{ color: textColor }}
          as="h1"
          className={`text-3lg sm:text-[45px] text-center font-semibold relative z-20 mt-0`}
        >
          {children}
        </Heading>
      </Column>
    </Row>
  );
};
export default EmailHeader2;

function hexToRgb(hex) {
  var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
      }
    : null;
}
