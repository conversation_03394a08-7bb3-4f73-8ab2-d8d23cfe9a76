import { Button } from '@react-email/components';
import classNames from 'classnames';
import * as React from 'react';

interface Props {
  children?: React.ReactNode;
  className?: string;
  href: string;
  style?: React.CSSProperties;
}

const EmailButton: React.FC<Props> = ({ children, className, href, style }) => {
  return (
    <Button
      href={href}
      style={style}
      className={classNames(
        'bg-primary-500 py-4.5 px-6.25 text-white block text-center rounded-xl !font-body',
        className,
      )}
    >
      <span className="font-body font-medium sm:text-base text-1sm">{children}</span>
    </Button>
  );
};
export default EmailButton;
