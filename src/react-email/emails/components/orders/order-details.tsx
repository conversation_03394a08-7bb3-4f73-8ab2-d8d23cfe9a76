import React from 'react';
import {
  Body,
  Column,
  Container,
  Head,
  <PERSON>ing,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Row,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
import { normalizePhone, toCurrency } from '../../utils/functions';
import { EmailOrderData } from '../../utils/types';

const OrderDetails = (props: EmailOrderData) => {
  const order = props.order;

  return (
    <React.Fragment>
      <Text className="inline-block h-full align-middle font-display font-semibold  text-black text-base sm:text-lg">
        Order Details
      </Text>

      <Section className="px-5 py-3.5 bg bg-grey-fields-100 rounded-20">
        {!props.is_affiliate && (
          <>
            <Row>
              <Column align="left" width="50%" className="">
                <Text className="inline-block h-full align-middle font-body text-black-muted text-1xs sm:text-sm  !py-2.5 !my-0">
                  Status
                </Text>
              </Column>
              <Column align="right" width="50%" className="">
                <Text
                  className="inline-block h-full align-middle font-body font-medium  text-black text-xs !py-1 !my-0 px-3 bg-white rounded-30"
                  style={{ color: props.store_color }}
                >
                  {props.order.order_status}
                </Text>
              </Column>
            </Row>

            <Row>
              <Column align="left" width="50%" className="">
                <Text className="inline-block h-full align-middle font-body text-black-muted text-1xs sm:text-sm !py-2.5 !my-0">
                  Order ID
                </Text>
              </Column>
              <Column align="right" width="50%" className="">
                <Text className="inline-block h-full align-middle font-body font-medium text-1xs sm:text-sm !py-2.5 !my-0">
                  {props.order.order_id}
                </Text>
              </Column>
            </Row>

            <Row>
              <Column align="left" width="50%" className="">
                <Text className="inline-block h-full align-middle font-body text-black-muted text-1xs sm:text-sm  !py-2.5 !my-0">
                  Delivery Method
                </Text>
              </Column>
              <Column align="right" width="50%" className="">
                <Text className="inline-block h-full align-middle font-body font-medium  text-black text-1xs sm:text-sm !py-2.5 !my-0">
                  {props.order.delivery_method}
                </Text>
              </Column>
            </Row>
            <Row>
              <Hr />
            </Row>
          </>
        )}

        {order.products.map((product, index) => (
          <Row key={index}>
            <Column align="left" width="50%" className="">
              <Img src={product.image} alt="" className="w-6.75 h-6.75 inline-block align-middle mr-2.5 rounded-5" />
              <Text className="inline-block h-full align-middle font-body text-black-muted text-1xs sm:text-sm  !py-2.5 !my-0">
                {product.name} x {product.quantity}
              </Text>
            </Column>
            <Column align="right" width="50%" className="">
              <Text className="inline-block h-full align-middle font-body font-medium  text-black text-1xs sm:text-sm !py-2.5 !my-0">
                {toCurrency(product.price, order.currency)}
              </Text>
            </Column>
          </Row>
        ))}

        {!props.is_affiliate && (
          <>
            <Row>
              <Hr />
            </Row>
            {(order?.fees ?? []).map((fee, index) => (
              <Row key={index}>
                <Column align="left" width="50%" className="">
                  <Text className="inline-block h-full align-middle font-body text-black-muted text-1xs sm:text-sm  !py-2.5 !my-0">
                    {fee.label}
                  </Text>
                </Column>
                <Column align="right" width="50%" className="">
                  <Text className="inline-block h-full align-middle font-body font-medium  text-black text-1xs sm:text-sm !py-2.5 !my-0">
                    {toCurrency(fee.amount, order.currency)}
                  </Text>
                </Column>
              </Row>
            ))}
          </>
        )}

        <Row>
          <Hr />
        </Row>

        <Row>
          <Column align="left" width="50%" className="">
            <Text className="inline-block h-full align-middle font-body font-medium text-black text-1xs sm:text-sm  !py-2.5 !my-0">
              Total
            </Text>
          </Column>
          <Column align="right" width="50%" className="">
            <Text
              className="inline-block h-full align-middle font-body font-semibold  text-black text-1xs sm:text-sm !py-2.5 !my-0"
              style={{ color: props.store_color }}
            >
              {toCurrency(
                props.is_affiliate
                  ? order.products.reduce(
                      (acc, product) => acc + (product.price as number) * (product.quantity as number),
                      0,
                    )
                  : order.total,
                order.currency,
              )}
            </Text>
          </Column>
        </Row>
      </Section>
    </React.Fragment>
  );
};

export const DeliveryDetails = (props: EmailOrderData) => {
  const order = props.order;

  return (
    <React.Fragment>
      <Text className="inline-block h-full align-middle font-display font-semibold  text-black text-base sm:text-lg">
        Delivery Information
      </Text>

      <Section className="px-5 py-3.5 bg bg-grey-fields-100 rounded-20">
        {order?.delivery_area && (
          <Row>
            <Column align="left" width="50%" className="">
              <Text className="inline-block h-full align-middle font-body text-black-muted text-1xs sm:text-sm  !py-2.5 !my-0">
                Delivery Area
              </Text>
            </Column>
            <Column align="right" width="50%" className="">
              <Text className="inline-block h-full align-middle font-body font-medium  text-black text-1xs sm:text-sm !py-2.5 !my-0">
                {order.delivery_area}
              </Text>
            </Column>
          </Row>
        )}

        <Row>
          <Column align="left" width="20%" className="">
            <Text className="inline-block h-full align-middle font-body text-black-muted text-1xs sm:text-sm  !py-2.5 !my-0">
              Address
            </Text>
          </Column>
          <Column align="right" width="80%" className="">
            <Text className="inline-block h-full align-middle font-body font-medium  text-black text-1xs sm:text-sm !py-2.5 !my-0">
              {order.delivery_address}
            </Text>
          </Column>
        </Row>
      </Section>
    </React.Fragment>
  );
};

export const CustomerDetails = (props: EmailOrderData) => {
  const order = props.order;

  return (
    <React.Fragment>
      <Text className="inline-block h-full align-middle font-display font-semibold  text-black text-base sm:text-lg">
        Customer Information
      </Text>

      <Section className="px-5 py-3.5 bg bg-grey-fields-100 rounded-20">
        <Row>
          <Column align="left" width="20%" className="">
            <Text className="inline-block h-full align-middle font-body text-black-muted text-1xs sm:text-sm  !py-2.5 !my-0">
              Name
            </Text>
          </Column>
          <Column align="right" width="80%" className="">
            <Text className="inline-block h-full align-middle font-body font-medium  text-black text-1xs sm:text-sm !py-2.5 !my-0">
              {order.customer_info?.name}
            </Text>
          </Column>
        </Row>
        <Row>
          <Column align="left" width="20%" className="">
            <Text className="inline-block h-full align-middle font-body text-black-muted text-1xs sm:text-sm  !py-2.5 !my-0">
              Phone
            </Text>
          </Column>
          <Column align="right" width="80%" className="">
            <Link
              href={`https://wa.me/${order.customer_info?.phone.replace('+', '').split('-').join('')}`}
              style={{ color: props.store_color }}
              className="font-medium font-body text-1xs sm:text-sm !py-2.5 !my-0"
            >
              {normalizePhone(order.customer_info?.phone)}
            </Link>
          </Column>
        </Row>
        {order?.customer_info.email && (
          <Row>
            <Column align="left" width="50%" className="">
              <Text className="inline-block h-full align-middle font-body text-black-muted text-1xs sm:text-sm  !py-2.5 !my-0">
                Email
              </Text>
            </Column>
            <Column align="right" width="50%" className="">
              <Text className="inline-block h-full align-middle font-body font-medium  text-black text-1xs sm:text-sm !py-2.5 !my-0">
                {order.customer_info?.email}
              </Text>
            </Column>
          </Row>
        )}
      </Section>
    </React.Fragment>
  );
};

export default OrderDetails;
