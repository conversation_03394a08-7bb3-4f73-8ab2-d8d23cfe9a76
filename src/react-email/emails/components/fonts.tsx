import { Body, Container, Font, Heading, Html, Preview, Section, Tailwind, Text } from '@react-email/components';
import * as React from 'react';

interface Props {}

const EmailFonts: React.FC<Props> = ({}) => {
  return (
    <>
      <style
        type="text/css"
        data-premailer="ignore"
        dangerouslySetInnerHTML={{
          __html: `
          @import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"); 
          body {
            margin: 0;
            padding: 0;
            min-width: 100% !important;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
          }

          .content {
            width: 94%;
            max-width: 680px;
          }

          h1,
          h2,
          h3 {
            font-family: "FH Oscar", "Inter", sans-serif !important;
            font-weight: 700;
          }
           `,
        }}
      ></style>
      <Font
        fontFamily="FH Oscar"
        fallbackFontFamily="sans-serif"
        webFont={{
          url: `https://res.cloudinary.com/catlog/raw/upload/v1674609065/fonts/FHOscar-Bold.otf`,
          format: 'opentype',
        }}
        fontWeight={700}
        fontStyle="normal"
      />
      <Font
        fontFamily="FH Oscar"
        fallbackFontFamily="sans-serif"
        webFont={{
          url: `https://res.cloudinary.com/catlog/raw/upload/v1674609065/fonts/FHOscar-Medium.otf`,
          format: 'opentype',
        }}
        fontWeight={500}
        fontStyle="normal"
      />
      <Font
        fontFamily="FH Oscar"
        fallbackFontFamily="sans-serif"
        webFont={{
          url: `https://res.cloudinary.com/catlog/raw/upload/v1674609065/fonts/FHOscar-Light.otf`,
          format: 'opentype',
        }}
        fontWeight={300}
        fontStyle="normal"
      />
    </>
  );
};
export default EmailFonts;
