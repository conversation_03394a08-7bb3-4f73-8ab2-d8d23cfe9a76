import {
  Body,
  Column,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Preview,
  Row,
  Section,
  Text,
  Tailwind,
  Hr,
  Link,
} from '@react-email/components';
import EmailFonts from './components/fonts';
import config from '../tailwind.config';
import EmailButton from './components/button';
import EmailHeader from './components/header';
import EmailFooter from './components/footer';
import * as React from 'react';
import { AffiliateInviteProps } from './utils/types';

const EmailTemplate = (props: AffiliateInviteProps) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="affiliate_invite" />
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    You've been invited to join{' '}
                    <span className="text-black-secondary font-body">{props.store_name}</span> as an affiliate.
                  </Text>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    As an affiliate, you'll get notifications when a customer orders from your link. Here's your
                    affiliate link:
                  </Text>
                  <Heading
                    as="h6"
                    className="text-center py-10 bg-grey-fields-100 rounded-20 tracking-wide font-semibold text-base sm:text-lg text-primary-500"
                  >
                    {props.invite_link}
                  </Heading>
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    If you have any issues with this email, please reach out to{' '}
                    <Link href={`tel:${props.store_phone}`} className="text-primary-500 font-medium font-body">
                      {props.store_name}
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-purple" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: 'John Doe',
  store_name: 'Catlog',
  preview_text: "You've been invited to be an affiliate!",
  invite_link: 'https://catlog.shop',
  store_phone: '+2348138888888',
} as AffiliateInviteProps;

export default EmailTemplate;
