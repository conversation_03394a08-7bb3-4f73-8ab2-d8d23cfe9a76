import {
  Body,
  Column,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Preview,
  Row,
  Section,
  Text,
  Tailwind,
  Hr,
  Link,
} from "@react-email/components";
import EmailFonts from "./components/fonts";
import config from "../tailwind.config";
import EmailButton from "./components/button";
import EmailHeader from "./components/header";
import EmailFooter from "./components/footer";
import * as React from "react";

interface Props {
  preview_text: string;
  name: string;
  source_amount: string;
  destination_currency: string;
  destination_balance: string;
  destination_amount: string;
  fee: string;
  wallet_link: string;
  date: string;
}

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="conversion_successful" />
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}!
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Your conversion of{" "}
                    <span className="text-black-secondary font-semibold font-body">{props.source_amount}</span> to
                    <br />
                    <span className="text-black-secondary font-semibold font-body">
                      {" "}
                      {props.destination_currency}
                    </span>{" "}
                    was successful.
                  </Text>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Your {props.destination_currency} wallet balance is now <br />
                    <span className="text-black-secondary font-semibold font-body">{props.destination_balance}</span>
                  </Text>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Here’s a summary of the conversion.
                  </Text>
                  <Section className="p-5 bg bg-grey-fields-100 rounded-20">
                    <Row>
                      <Column>
                        <Text className="m-0 font-body text-grey-subtext">Amount Credited</Text>
                      </Column>
                    </Row>
                    <Row>
                      <Column width="50%">
                        <Text className="m-0  font-body text-black text-base sm:text-lg font-semibold">
                          {props.destination_amount}
                        </Text>
                      </Column>
                    </Row>
                    <Row>
                      <Hr />
                    </Row>
                    <Row className="py-2.5">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Amount Converted</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium  text-black text-base">
                          {props.source_amount}
                        </Text>
                      </Column>
                    </Row>
                    <Row className="py-2.5">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Fee Charged</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium  text-black text-base">
                          {props.fee}
                        </Text>
                      </Column>
                    </Row>
                    <Row className="py-2.5">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Date</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium  text-black text-base">
                          {props.date}
                        </Text>
                      </Column>
                    </Row>
                  </Section>
                  <EmailButton href={props.wallet_link} className="my-6.25">
                    Manage Funds
                  </EmailButton>
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    If you have any questions or complaints regarding this conversion, please reach out to us on{" "}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-yellow" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: "John Doe",
  source_amount: "NGN 100",
  destination_currency: "Euros",
  destination_balance: "100",
  fee: "10",
  wallet_link: "https://catlog.shop",
  date: "12/12/2022",
  destination_amount: "3000",
  preview_text: "Conversion Successful",
} as Props;

export default EmailTemplate;
