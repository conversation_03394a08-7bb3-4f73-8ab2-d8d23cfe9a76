import {
  Body,
  Column,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Preview,
  Row,
  Section,
  Text,
  Tailwind,
  Hr,
  Link,
} from "@react-email/components";
import EmailFonts from "./components/fonts";
import config from "../tailwind.config";
import EmailButton from "./components/button";
import EmailHeader from "./components/header";
import EmailFooter from "./components/footer";
import * as React from "react";

interface Props {
  name: string;
  store_name: string;
  preview_text: string;
  plan_name: string;
  amount: string;
  card: string;
  payment_reference: string;
  next_payment_date: string;
  interval: string;
}

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="new_subscription" />
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}!
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    You have successfully subscribed to{" "}
                    <span className="text-black-secondary font-semibold font-body">{props.plan_name} plan!</span>{" "}
                  </Text>

                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Here's the details of your subscription below:
                  </Text>
                  <Section className="p-5 bg bg-grey-fields-100 rounded-20">
                    <Row className="py-1.25">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Plan Name</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium  text-black">
                          {props.plan_name}
                        </Text>
                      </Column>
                    </Row>

                    <Row className="py-1.25">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Interval</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium  text-black">
                          {props.interval}
                        </Text>
                      </Column>
                    </Row>

                    <Row className="py-1.25">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Amount</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium  text-black">
                          NGN {props.amount}
                        </Text>
                      </Column>
                    </Row>

                    <Row className="py-1.25">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Payment method</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium  text-black">
                          *** {props.card}
                        </Text>
                      </Column>
                    </Row>

                    <Row className="py-1.25">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Payment reference</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium  text-black">
                          {props.payment_reference}
                        </Text>
                      </Column>
                    </Row>

                    <Row className="py-1.25">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Next payment date</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium  text-black">
                          {props.next_payment_date}
                        </Text>
                      </Column>
                    </Row>
                  </Section>
                  <Hr />

                  <Text className="font-body text-grey-subtext">
                    If you have any issues with this subscription, please reach out to us on{" "}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-green" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: "John Doe",
  plan_name: "Basic",
  amount: "100",
  card: " 3456",
  payment_reference: "123456789",
  next_payment_date: "12/12/2022",
  interval: "month",
} as Props;

export default EmailTemplate;
