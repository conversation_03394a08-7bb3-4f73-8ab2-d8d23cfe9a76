import {
  Body,
  Column,
  Container,
  Head,
  <PERSON><PERSON>,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Row,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";
import config from "../tailwind.config";
import EmailFonts from "./components/fonts";
import EmailFooter from "./components/footer";
import * as React from "react";
import EmailHeader from "./components/header";

interface Props {
  preview_text: string;
  name: string;
  amount: string;
  store_name: string;
  order_or_invoice: string;
  payment_method_name: string;
  payment_method_icon: string;
  reference: string;
  date: string;
}

const EmailTemplate = (props: Props) => {
  const previewText = `Verify your email address`;
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="payment_success" />
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}!
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Your payment of{" "}
                    <span className="text-black-secondary font-body font-medium">{props.amount}</span> to{" "}
                    <span className="text-black-secondary font-body">{props.store_name}</span> for{" "}
                    <span className="text-black-secondary font-body font-medium">{props.order_or_invoice}</span> was
                    successful
                  </Text>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Here’s a summary of the payment.
                  </Text>
                  <Section className="p-5 bg bg-grey-fields-100 rounded-20 mb-5">
                    <Row>
                      <Column>
                        <Text className="m-0 font-body text-grey-subtext">Total amount</Text>
                      </Column>
                    </Row>
                    <Row>
                      <Column width="50%">
                        <Text className=" font-body text-black text-base sm:text-lg font-semibold">{props.amount}</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Img src={props.payment_method_icon} alt="" className="w-6.75 h-6.75 inline-block align-middle mr-2.5" />
                        <Text className="inline-block h-full align-middle font-body text-black-muted text-1sm">
                          {props.payment_method_name}
                        </Text>
                      </Column>
                    </Row>
                    <Row>
                      <Hr />
                    </Row>
                    <Row>
                      <Column width="50%">
                        <Text className=" font-body text-black-placeholder">Reference</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="inline-block h-full align-middle font-body font-medium  text-black text-1sm">
                          {props.reference}
                        </Text>
                      </Column>
                    </Row>

                    <Row>
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Date</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium  text-black text-1sm">
                          {props.date}
                        </Text>
                      </Column>
                    </Row>
                  </Section>
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    If you have any questions or complaints regarding this payment, please reach out to us on{" "}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-green" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  preview_text: "Verify your email address",
  name: "John Doe",
  amount: "100",
  store_name: "Catlog",
  order_or_invoice: "order",
  payment_method_name: "GTB",
  payment_method_icon: "https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/foot-banner-light-yellow.png",
  reference: "123456789",
  date: "12/12/2022",
} as Props;

export default EmailTemplate;
