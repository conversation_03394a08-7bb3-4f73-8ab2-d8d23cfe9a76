import {
  Body,
  Column,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Preview,
  Row,
  Section,
  Text,
  Tailwind,
  Hr,
  Link,
} from '@react-email/components';
import EmailFonts from './components/fonts';
import config from '../tailwind.config';
import EmailButton from './components/button';
import EmailHeader from './components/header';
import EmailFooter from './components/footer';
import * as React from 'react';

interface Props {
  preview_text: string;
  name: string;
  received: string;
  customer: string;
  order_or_invoice: string;
  current_balance: string;
  date: string;
  credited: string;
  fee: string;
  payment_method_name: string;
  payment_method_icon: string;
  funds_link: string;
}

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="payment_received" />
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    You just received{' '}
                    <span className="text-black-secondary font-semibold font-body">{props.received}</span>{' '}
                    {props.customer} {props.order_or_invoice}. Your wallet balance is now{' '}
                    <span className="text-black-secondary font-semibold font-body">{props.current_balance}</span>
                  </Text>

                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Here’s a summary of the payment.
                  </Text>
                  <Section className="p-5 bg bg-grey-fields-100 rounded-20">
                    <Row>
                      <Column>
                        <Text className="m-0 font-body text-grey-subtext">Total amount</Text>
                      </Column>
                    </Row>
                    <Row>
                      <Column width="50%">
                        <Text className=" font-body text-black text-base sm:text-lg font-semibold !my-1.5">
                          {props.received}
                        </Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Img
                          src={props.payment_method_icon}
                          alt=""
                          className="w-6.75 h-6.75 inline-block align-middle mr-2.5 rounded-full"
                        />
                        <Text className="inline-block h-full align-middle font-body text-black-muted text-sm sm:text-1sm !my-0">
                          {props.payment_method_name}
                        </Text>
                      </Column>
                    </Row>
                    <Row>
                      <Hr />
                    </Row>
                    <Row className="py-1.5">
                      <Column width="50%">
                        <Text className=" font-body text-black-placeholder m-0">Fee</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="inline-block h-full align-middle font-body font-medium  text-black text-sm sm:text-1sm !py-1 m-0">
                          {props.fee}
                        </Text>
                      </Column>
                    </Row>
                    <Row className="py-1.5">
                      <Column width="50%">
                        <Text className=" font-body text-black-placeholder m-0">Amount Credited</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="inline-block h-full align-middle font-body font-medium  text-black text-sm sm:text-1sm !py-1 m-0">
                          {props.credited}
                        </Text>
                      </Column>
                    </Row>
                    <Row className="py-1.5">
                      <Column width="50%">
                        <Text className=" font-body text-black-placeholder m-0">Date</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="inline-block h-full align-middle font-body font-medium  text-black text-sm sm:text-1sm !py-1 m-0">
                          {props.date}
                        </Text>
                      </Column>
                    </Row>
                  </Section>
                  <EmailButton href={props.funds_link} className="my-6.25">
                    Manage Funds
                  </EmailButton>
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    If you have any questions or complaints regarding this payment, please reach out to us on{' '}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-green" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: 'John Doe',
  received: '100',
  customer: 'customer',
  order_or_invoice: 'order',
  current_balance: '100',
  payment_method_name: 'GTB',
  payment_method_icon:
    'https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/foot-banner-light-yellow.png',
  fee: '100',
  credited: '100',
  date: '12/12/2022',
  funds_link: 'https://catlog.shop',
} as Props;

export default EmailTemplate;
