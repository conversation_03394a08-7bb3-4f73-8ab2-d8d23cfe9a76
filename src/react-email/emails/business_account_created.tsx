import {
  Body,
  Column,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Preview,
  Row,
  Section,
  Text,
  Tailwind,
  Hr,
  Link,
} from "@react-email/components";
import EmailFonts from "./components/fonts";
import config from "../tailwind.config";
import EmailButton from "./components/button";
import EmailHeader from "./components/header";
import EmailFooter from "./components/footer";
import * as React from "react";

interface Props {
  preview_text: string;
  name: string;
  store_name: string;
  bank_name: string;
  bank_logo: string;
  account_number: string;
  account_name: string;
  app_link: string;
}

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}

        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="tokens_threshold_notification">
                <>
                  Your business
                  <br />
                  account is here!
                </>
              </EmailHeader>
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}!
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    We're happy to let you know that a business bank account has now been created for{" "}
                    <span className="text-black-secondary font-semibold font-body">{props.store_name}</span>
                    <br/>
                    <br/>
                    You can use this account to collect payments from your customers anytime. This account will appear
                    more professional to your customers and also help you keep track of all your business finances in
                    one place.
                  </Text>
                  
                  <Text className="text-grey-subtext font-body text-base">Here’s your account details:</Text>
                  <Section className="p-5 bg bg-grey-fields-100 rounded-20">
                    <Row>
                      <Column>
                        <Text className="m-0 font-body text-grey-subtext">Account Details</Text>
                      </Column>
                    </Row>
                    <Row>
                      <Column width="50%">
                        <Text className=" font-body text-black text-base sm:text-lg font-semibold">
                          {props.account_name}
                        </Text>
                      </Column>
                    </Row>
                    <Row>
                      <Hr />
                    </Row>
                    <Row>
                      <Column align="left" width="50%" className="">
                        <Img src={props.bank_logo} alt="" className="w-6.75 h-6.75 inline-block align-middle mr-2.5" />
                        <Text className="inline-block h-full align-middle font-body text-black-muted text-base">
                          {props.bank_name}
                        </Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="inline-block h-full align-middle font-body font-medium  text-black text-base">
                          {props.account_number}
                        </Text>
                      </Column>
                    </Row>
                  </Section>
                 
                  <EmailButton href={props.app_link} className="my-6.25">
                    View On Dashboard
                  </EmailButton>
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    If you have any questions or complaints regarding this email, please reach out to us on{" "}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-yellow" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: "John Doe",
  store_name: "Store Name",
  bank_name: "GTB",
  bank_logo: "https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/foot-banner-light-yellow.png",
  account_number: "*********",
  account_name: "My Account",
  app_link: "https://catlog.shop",
} as Props;

export default EmailTemplate;
