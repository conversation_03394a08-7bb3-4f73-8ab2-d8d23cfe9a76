import {
  Body,
  Column,
  Container,
  Head,
  <PERSON>ing,
  Hr,
  Html,
  Link,
  Preview,
  Row,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";
import config from "../tailwind.config";
import EmailFonts from "./components/fonts";
import EmailFooter from "./components/footer";
import * as React from "react";
import EmailHeader from "./components/header";


interface Props {
  name: string;
  preview_text: string;
  code: string;
}

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="verify_bvn" />
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Someone requested to use your{" "}
                    <span className="text-black-secondary font-medium font-body">BVN</span> and we wanted to make sure
                    it’s really you
                  </Text>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Use this <span className="text-black-secondary font-medium font-body">6 digit code</span> to
                    complete the process:
                  </Text>
                  <Heading
                    as="h2"
                    className="text-center py-10 bg-grey-fields-100 rounded-20 tracking-[10px] text-2xl sm:text-[35px]"
                  >
                    {props.code}
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    This code expires in 10 minutes
                  </Text>
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    If you did not initiate this request, please reach out to us on{" "}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-yellow" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: "John Doe",
  preview_text: "Verify your email address",
  code: "123456",
} as Props;

export default EmailTemplate;
