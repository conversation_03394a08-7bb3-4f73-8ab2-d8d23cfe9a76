import {
  Body,
  Column,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Preview,
  Row,
  Section,
  Text,
  Tailwind,
  Hr,
  Link,
} from '@react-email/components';
import EmailFonts from './components/fonts';
import config from '../tailwind.config';
import EmailButton from './components/button';
import EmailHeader from './components/header';
import EmailFooter from './components/footer';
import * as React from 'react';

interface Props {
  name: string;
  store_name: string;
  received: string;
  source_name: string;
  current_balance: string;
  fee: string;
  credited: string;
  funds_link: string;
  date: string;
  preview_text: string;
}

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="payment_received" />
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}!
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    You just received{' '}
                    <span className="text-black-secondary font-semibold font-body">{props.received}</span> from{' '}
                    <span className="text-black-secondary font-semibold font-body"> {props.source_name}</span>. Your
                    wallet balance is now{' '}
                    <span className="text-black-secondary font-semibold font-body">{props.current_balance}</span>
                  </Text>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Here’s a summary of the payment.
                  </Text>
                  <Section className="p-5 bg bg-grey-fields-100 rounded-20">
                    <Row>
                      <Column>
                        <Text className="m-0 font-body text-grey-subtext">Total amount</Text>
                      </Column>
                    </Row>
                    <Row>
                      <Column width="50%">
                        <Text className="m-0  font-body text-black text-base sm:text-lg font-semibold">
                          {props.received}
                        </Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body text-black-muted text-sm sm:text-1sm !py-1">
                          {props.source_name}
                        </Text>
                      </Column>
                    </Row>
                    <Row>
                      <Hr />
                    </Row>
                    <Row className="py-1.5">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Fee</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium  text-black text-sm sm:text-1sm !py-1">
                          {props.fee}
                        </Text>
                      </Column>
                    </Row>
                    <Row className="py-1.5">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder text-sm sm:text-1sm !py-1">
                          Amount Credited
                        </Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium  text-black text-sm sm:text-1sm !py-1">
                          {props.credited}
                        </Text>
                      </Column>
                    </Row>
                    <Row className="py-1.5">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder text-sm sm:text-1sm !py-1">Date</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium  text-black text-sm sm:text-1sm !py-1">
                          {props.date}
                        </Text>
                      </Column>
                    </Row>
                  </Section>
                  <EmailButton href={props.funds_link} className="my-6.25">
                    Manage Funds
                  </EmailButton>
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    If you have any questions or complaints regarding this payment, please reach out to us on{' '}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-green" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: 'John Doe',
  received: '100',
  source_name: 'John Doe',
  current_balance: '100',
  fee: '10',
  credited: '100',
  funds_link: 'https://catlog.shop',
  date: '12/12/2022',
} as Props;

export default EmailTemplate;
