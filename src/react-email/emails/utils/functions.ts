export function chunkArray<T>(array: T[], chunkSize: number): T[][] {
  const result: T[][] = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    const chunk = array.slice(i, i + chunkSize);
    result.push(chunk);
  }
  return result;
}

export const amountFormat = (amount: number | string, toFixed: number = 2): string => {
  const numericAmount = parseFloat(amount.toString());

  if (isNaN(numericAmount)) {
    throw new Error('Invalid amount provided');
  }

  const fixedAmount = numericAmount.toFixed(toFixed);
  const [integerPart, decimalPart] = fixedAmount.split('.');
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  return decimalPart ? `${formattedInteger}.${decimalPart}` : formattedInteger;
};

export const toCurrency = (amount: string | number, currency: string = 'NGN', decimals?: number) => {
  return `${currency} ${amountFormat(amount, decimals)}`;
};

export function toTitleCase(snakeStr: string): string {
  return snakeStr
    .split('_') // Split by underscore
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize first letter of each word
    .join(' '); // Join words with space
}

export const millify = (num: number, currency?: string): string => {
  if (num < 10_000) return `${currency ?? ''} ${num.toLocaleString()}`; // Format numbers below 10K

  const units = ['', 'K', 'M', 'B', 'T'];
  let unitIndex = 0;

  while (num >= 1000 && unitIndex < units.length - 1) {
    num /= 1000;
    unitIndex++;
  }

  // Check if number has decimals, format accordingly
  const formattedNum = num % 1 === 0 ? Math.floor(num) : num.toFixed(2);

  return `${currency ?? ''} ${formattedNum}${units[unitIndex]}`;
};

export const normalizePhone = (phone: string) => {
  const phoneSplits = phone.split('-');
  const digits = phoneSplits.length > 0 ? phoneSplits[1] : phoneSplits[0];

  return `0${digits}`;
};
