import {
  Body,
  Column,
  Container,
  Head,
  <PERSON><PERSON>,
  Hr,
  Html,
  Link,
  Preview,
  Row,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";
import config from "../tailwind.config";
import EmailButton from "./components/button";
import EmailFonts from "./components/fonts";
import EmailFooter from "./components/footer";
import * as React from "react";
import EmailHeader from "./components/header";


interface Props {
  preview_text?: string;
  name: string;
  store_name: string;
  store_phone: string;
  amount: string;
  balance: string;
  reason: string;
}

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}

        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="credits_received" />
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}!
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Your Catlog Credit wallet has been credited with{" "}
                    <span className="text-black-secondary font-semibold font-body">{props.amount}</span>, your total
                    credit balance is now{" "}
                    <span className="text-black-secondary font-semibold font-body">{props.balance}</span>{" "}
                  </Text>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Reason: <span className="text-black-secondary font-medium font-body">{props.reason}</span>
                  </Text>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    You can use your Catlog Credits to pay for your subscription or book deliveries.
                  </Text>
                  <EmailButton href="https://app.catlog.shop/" className="my-6.25">
                    Go To Dashboard
                  </EmailButton>
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    If you have any questions or complaints regarding this payment, please reach out to us on{" "}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-yellow" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: "John Doe",
  amount: "100",
  balance: "200",
  reason: "Testing",
} as Props;

export default EmailTemplate;
