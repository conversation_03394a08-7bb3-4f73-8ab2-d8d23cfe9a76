import {
  Body,
  Column,
  Container,
  Head,
  <PERSON><PERSON>,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Row,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";
import config from "../tailwind.config";
import EmailFonts from "./components/fonts";
import EmailFooter from "./components/footer";
import * as React from "react";
import EmailHeader from "./components/header";
import EmailButton from "./components/button";

interface Props {
  name: string;
  store_name: string;
  preview_text: string;
  delivery_amount: string;
  drop_off_date: string;
  pick_up_date: string;
  courier_name: string;
  courier_image: string;
  drop_off_address: string;
  pick_up_address: string;
  item_count: string;
  tracking_link: string;
}

const EmailTemplate = (props: Props) => {
  const previewText = `Verify your email address`;
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="delivery_booked" />
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}!
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Your payment of{" "}
                    <span className="text-black-secondary font-semibold font-body">{props.delivery_amount}</span> was
                    successful and your delivery has been booked.
                  </Text>

                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Here's the summary of your delivery.
                  </Text>
                  <Section className="p-5 bg bg-grey-fields-100 rounded-20 mb-5">
                    <Row className="py-1.25">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Items</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium  text-black">
                          {props.item_count}
                        </Text>
                      </Column>
                    </Row>

                    <Row className="py-1.25">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Pick-up address</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium  text-black">
                          {props.pick_up_address}
                        </Text>
                      </Column>
                    </Row>

                    <Row className="py-1.25">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Drop-off address</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium  text-black">
                          {props.drop_off_address}
                        </Text>
                      </Column>
                    </Row>

                    <Row className="py-1.25">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Courier</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Img
                          src={props.courier_image}
                          alt=""
                          className="w-6.75 h-6.75 inline-block align-middle mr-2.5"
                        />
                        <Text className="inline-block h-full align-middle font-body text-black m-0 text-1sm">
                          {props.courier_name}
                        </Text>
                      </Column>
                    </Row>

                    <Row className="py-1.25">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Pick-up date</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium  text-black">
                          {props.pick_up_date}
                        </Text>
                      </Column>
                    </Row>

                    <Row className="py-1.25">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Drop-off date</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium  text-black">
                          {props.drop_off_date}
                        </Text>
                      </Column>
                    </Row>
                  </Section>
                  <EmailButton href={props.tracking_link} className="my-6.25">
                    Track Delivery
                  </EmailButton>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    You will receive emails whenever{" "}
                    <span className="text-black-secondary font-semibold font-body">{props.courier_name}</span> updates
                    the status of this delivery.
                  </Text>
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    If you have any issues with this delivery, please reach out to us on{" "}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-orange" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: "John Doe",
  delivery_amount: "100",
  item_count: "10",
  pick_up_address: "Lagos, Nigeria",
  drop_off_address: "Lagos, Nigeria",
  courier_name: "Jumia",
  pick_up_date: "01/01/2021",
  drop_off_date: "01/01/2021",
} as Props;

export default EmailTemplate;
