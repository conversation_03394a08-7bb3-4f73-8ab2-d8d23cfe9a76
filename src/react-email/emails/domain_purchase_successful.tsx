import {
  Body,
  Column,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Preview,
  Row,
  Section,
  Text,
  Tailwind,
  Hr,
  Link,
} from '@react-email/components';
import EmailFonts from './components/fonts';
import config from '../tailwind.config';
import EmailButton from './components/button';
import EmailHeader from './components/header';
import EmailFooter from './components/footer';
import * as React from 'react';

interface Props {
  name: string;
  store_name: string;
  domain: string;
  purchase_date: string;
  preview_text: string;
}

const DomainPurchaseSuccessful = (props: Props) => {
  const previewText = props.preview_text || `Your domain purchase was successful!`;
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {previewText && <Preview>{previewText}</Preview>}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              {/* TODO: Add a custom header type for domain purchase if needed */}
              <EmailHeader type="payment_success" />
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}!
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Your domain <span className="text-black-secondary font-semibold font-body">{props.domain}</span> has
                    been successfully purchased and linked to your store{' '}
                    <span className="text-black-secondary font-semibold font-body">{props.store_name}</span>.
                  </Text>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Here are the details of your domain purchase:
                  </Text>
                  <Section className="p-5 bg bg-grey-fields-100 rounded-20 mb-5">
                    <Row className="py-1.25">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Domain</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium  text-black">
                          {props.domain}
                        </Text>
                      </Column>
                    </Row>
                    <Row className="py-1.25">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Store</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium  text-black">
                          {props.store_name}
                        </Text>
                      </Column>
                    </Row>
                    <Row className="py-1.25">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Purchase Date</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium  text-black">
                          {props.purchase_date}
                        </Text>
                      </Column>
                    </Row>
                  </Section>
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    If you have any issues with your domain, please reach out to us on{' '}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-green" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

DomainPurchaseSuccessful.PreviewProps = {
  name: 'John Doe',
  store_name: 'Test Store',
  domain: 'example.com',
  purchase_date: '2024-06-12',
  preview_text: 'Your domain purchase was successful!',
} as Props;

export default DomainPurchaseSuccessful;
