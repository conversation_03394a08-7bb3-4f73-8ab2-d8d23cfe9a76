import {
  Body,
  Column,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Preview,
  Row,
  Section,
  Text,
  Tailwind,
  Hr,
  Link,
} from '@react-email/components';
import EmailFonts from './components/fonts';
import config from '../tailwind.config';
import EmailButton from './components/button';
import EmailHeader from './components/header';
import EmailFooter from './components/footer';
import * as React from 'react';

interface Props {
  preview_text?: string;
  name: string;
  body_text: string;
  tracking_code: string;
  courier_name: string;
  title_text: string;
  tracking_link: string;
}

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}

        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="seller_delivery_status">
                {props.title_text.split('<br/>').map((i) => (
                  <React.Fragment key={i}>
                    {i}
                    <br />
                  </React.Fragment>
                ))}
              </EmailHeader>
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}!
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Your delivery{' '}
                    <span className="text-black-secondary font-semibold font-body">{props.tracking_code}</span> with{' '}
                    <span className="text-black-secondary font-semibold font-body">{props.courier_name}</span> <br />
                    {props.body_text}
                  </Text>

                  <EmailButton href={props.tracking_link} className="my-6.25">
                    Track Delivery
                  </EmailButton>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    You will receive emails whenever{' '}
                    <span className="text-black-muted font-medium font-body">{props.courier_name}</span> updates the
                    status of this delivery.
                  </Text>
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    If you did not initiate this request, please reach out to us via{' '}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-orange" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: 'John Doe',
  title_text: 'Delivery is <br/> on the way',
  body_text: 'Your delivery is on the way',
  tracking_code: '123456',
  courier_name: 'Jumia',
  store_name: 'Catlog',
  tracking_link: 'https://catlog.shop',
} as Props;

export default EmailTemplate;
