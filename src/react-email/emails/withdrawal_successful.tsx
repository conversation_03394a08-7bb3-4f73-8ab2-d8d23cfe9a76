import {
  Body,
  Column,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Preview,
  Row,
  Section,
  Text,
  Tailwind,
  Hr,
  Link,
} from "@react-email/components";
import EmailFonts from "./components/fonts";
import config from "../tailwind.config";
import EmailButton from "./components/button";
import EmailHeader from "./components/header";
import EmailFooter from "./components/footer";
import * as React from "react";

interface Props {
  amount: string;
  name: string;
  remaining_balance: string;
  account_number: string;
  fee: string;
  date: string;
  wallet_link: string;
  preview_text?: string;
  image_link: string;
}

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="withdrawal_successful" />
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {}
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Your withdrawal of{" "}
                    <span className="text-black-secondary font-semibold font-body">{props.amount} </span> from your
                    Catlog wallet was successful.
                  </Text>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Your wallet balance is now{" "}
                    <span className="text-black-secondary font-semibold font-body">{props.remaining_balance}</span>
                  </Text>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Here’s a summary of the payment.
                  </Text>
                  <Section className="p-5 bg bg-grey-fields-100 rounded-20">
                    <Row>
                      <Column>
                        <Text className="m-0 font-body text-grey-subtext">Amount Withdrawn</Text>
                      </Column>
                    </Row>
                    <Row>
                      <Column width="50%">
                        <Text className=" font-body text-black text-base sm:text-lg font-semibold">{props.amount}</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Img src={props.image_link} alt="" className="w-6.75 h-6.75 inline-block align-middle mr-2.5" />
                        <Text className="inline-block h-full align-middle font-body text-black-muted text-base">
                          {props.account_number}
                        </Text>
                      </Column>
                    </Row>
                    <Row>
                      <Hr />
                    </Row>
                    <Row className="py-2.5">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Fee</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium  text-black text-base">
                          {props.fee}
                        </Text>
                      </Column>
                    </Row>
                    <Row className="py-2.5">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Date</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium  text-black text-base">
                          {props.date}
                        </Text>
                      </Column>
                    </Row>
                    <Row className="py-2.5">
                      <Column width="50%">
                        <Text className="m-0 font-body text-black-placeholder">Remaining Balance</Text>
                      </Column>
                      <Column align="right" width="50%" className="">
                        <Text className="m-0 inline-block h-full align-middle font-body font-medium  text-black text-base">
                          {props.remaining_balance}
                        </Text>
                      </Column>
                    </Row>
                  </Section>
                  <EmailButton href={props.wallet_link} className="my-6.25">
                    Manage Funds
                  </EmailButton>
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    If you have any questions or complaints regarding this payment, please reach out to us on{" "}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-yellow" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: "John Doe",
  amount: "100",
  fee: "10",
  date: "12/12/2022",
  remaining_balance: "100",
  image_link: "https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/foot-banner-light-yellow.png",
  account_number: "*********",
  wallet_link: "https://catlog.shop",
} as Props;

export default EmailTemplate;
