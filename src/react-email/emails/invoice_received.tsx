import {
  Body,
  Column,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Preview,
  Row,
  Section,
  Text,
  Tailwind,
  Hr,
  Link,
} from "@react-email/components";
import EmailFonts from "./components/fonts";
import config from "../tailwind.config";
import EmailButton from "./components/button";
import EmailHeader from "./components/header";
import EmailFooter from "./components/footer";
import * as React from "react";

interface Props {
  name: string;
  store_name: string;
  preview_text: string;
  invoice_amount: string;
  message: string;
  invoice_link: string;
}

const EmailTemplate = (props: Props) => {

  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="invoice_received" />
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    You have an invoice of{" "}
                    <span className="text-black-secondary font-body font-medium">{props.invoice_amount}</span> from{" "}
                    <span className="text-black-secondary font-body font-medium">{props.store_name}</span>
                  </Text>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">{props.message}</Text>
                  <EmailButton href={props.invoice_link} className="my-6.25">
                    View Invoice & Make Payment
                  </EmailButton>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    We’ve also attached a PDF copy of this invoice.
                  </Text>
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    If you have any issues with this subscription, please reach out to us on{" "}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-green" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: "John Doe",
  store_name: "Catlog",
  preview_text: "Verify your email address",
  invoice_amount: "$100",
  message: "This is a test message",
  invoice_link: "https://catlog.shop",
} as Props;

export default EmailTemplate;
