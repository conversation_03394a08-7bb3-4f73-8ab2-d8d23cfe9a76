import {
  Body,
  Column,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Preview,
  Row,
  Section,
  Text,
  Tailwind,
  Hr,
  Link,
} from "@react-email/components";
import EmailFonts from "./components/fonts";
import config from "../tailwind.config";
import EmailButton from "./components/button";
import EmailHeader from "./components/header";
import EmailFooter from "./components/footer";
import * as React from "react";

interface Props {
  name: string;
  preview_text: string;
  message: string;
}

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="kyc_rejected" />
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Your request to activate payments on your Catlog store failed.
                  </Text>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    <span className="text-black-secondary font-body font-medium">See the reason below:</span>
                  </Text>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">{props.message}</Text>
                  <Hr />
                  <Text className="font-body text-grey-subtext sm:text-base text-1sm">
                    Please take a moment to provide the correct information and we'll promptly approve you. You can also{" "}
                    <Link
                      href="https://api.whatsapp.com/send/?phone=2349042550548"
                      className="text-primary-500 font-medium font-body"
                    >
                      contact support here
                    </Link>
                  </Text>
                  <EmailButton href="https://app.catlog.shop/invoices/kyc" className="my-6.25">
                    Update Information
                  </EmailButton>
                 
                  <Hr />
                  <Text className="font-body text-grey-subtext text-1sm">
                    If you have any issues with this, please reach out to us on{" "}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-red" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: "John Doe",
  store_name: "Catlog",
  preview_text: "Verify your email address",
  invoice_amount: "$100",
  message: "This is a test message",
  invoice_link: "https://catlog.shop",
} as Props;

export default EmailTemplate;
