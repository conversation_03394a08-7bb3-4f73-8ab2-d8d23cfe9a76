import {
  Body,
  Column,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Link,
  Preview,
  Row,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
import config from '../tailwind.config';
import EmailButton from './components/button';
import EmailFonts from './components/fonts';
import EmailFooter from './components/footer';
import * as React from 'react';
import EmailHeader from './components/header';

interface Props {
  preview_text?: string;
  name: string;
  cta_link: string;
  products_count: string;
  failed_count: string;
  source: string;
  failed_items?: Array<{
    name: string;
    option_name?: string;
    error?: string;
  }>;
}

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}

        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="product_export_successful">
                Products Updated <br />
                Successfully
              </EmailHeader>
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}!
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    We've successfully updated{' '}
                    <span className="text-black-secondary font-semibold font-body">{props.products_count}</span>{' '}
                    products from <span className="text-black-secondary font-semibold font-body">{props.source}</span>.
                    {parseInt(props.failed_count) > 0 && (
                      <>
                        {' '}
                        Unfortunately,{' '}
                        <span className="text-red-500 font-semibold font-body">{props.failed_count}</span> items could
                        not be updated.
                      </>
                    )}
                  </Text>

                  {parseInt(props.failed_count) > 0 && props.failed_items && props.failed_items.length > 0 && (
                    <Section className="p-5 bg bg-grey-fields-100 rounded-20">
                      <Row>
                        <Column>
                          <Text className="m-0 font-body text-black-secondary text-base font-semibold">
                            Failed Items
                          </Text>
                        </Column>
                      </Row>
                      <Row>
                        <Hr />
                      </Row>
                      {props.failed_items.map((item, index) => (
                        <Row className="py-1.5" key={index}>
                          <Column width="50%">
                            <Text className=" font-body text-black-placeholder m-0">
                              {item.name}
                              {item.option_name && ` - ${item.option_name}`}
                            </Text>
                          </Column>
                          <Column align="right" width="50%" className="">
                            <Text className="inline-block h-full align-middle font-body text-dark text-sm !py-1 m-0">
                              Error: {item.error}
                            </Text>
                          </Column>
                        </Row>
                      ))}
                    </Section>
                  )}

                  <EmailButton href={props.cta_link} className="my-6.25">
                    Manage Products
                  </EmailButton>
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    If you have any questions or complaints regarding this email, please reach out to us on{' '}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-yellow" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: 'John Doe',
  products_count: '20',
  failed_count: '2',
  source: 'Excel/CSV Import',
  cta_link: 'https://catlog.shop',
  failed_items: [
    { name: 'Product 1', option_name: 'Option A', error: 'Invalid price format' },
    { name: 'Product 2', error: 'Item not found' },
  ],
} as Props;

export default EmailTemplate;
