import {
  Body,
  Column,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Preview,
  Row,
  Section,
  Text,
  Tailwind,
  Hr,
  Link,
} from "@react-email/components";
import EmailFonts from "./components/fonts";
import config from "../tailwind.config";
import EmailButton from "./components/button";
import EmailHeader from "./components/header";
import EmailFooter from "./components/footer";
import * as React from "react";

interface Props {
  preview_text: string;
  name: string;
  plan_name: string;
  days_left: string;
  main_feature: string;
  downgrade_message_1: string;
  downgrade_message_2: string;
  downgrade_message_3: string;
}

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}{" "}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="trial_ended" />
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}!
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Your free trial of{" "}
                    <span className="text-black-secondary font-semibold font-body">{props.plan_name} plan</span> has
                    ended.
                  </Text>
                  <Text className="text-grey-subtext font-body text-base">
                    We hope you've enjoyed using {props.main_feature} and encourage you to head over to your dashboard
                    to renew your subscription.
                  </Text>
                  <EmailButton href="https://app.catlog.shop/dashboard?renew_plan=true?" className="my-6.25">
                    Renew Subscription
                  </EmailButton>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Please note that if your subscription is not renewed in{" "}
                    <span className="text-black-secondary font-semibold font-body">{props.days_left} days</span> we
                    would have to take you back to the{" "}
                    <span className="text-black-secondary font-semibold font-body">Starter plan</span> and this means
                    that:
                  </Text>
                  <ul className="list-image-[url(https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/check.png)]">
                    <li className="font-body text-black-muted py-1.25">{props.downgrade_message_1}</li>
                    <li className="font-body text-black-muted py-1.25">{props.downgrade_message_2} </li>
                    <li className="font-body text-black-muted py-1.25">{props.downgrade_message_3} </li>
                  </ul>
                  <Hr />
                  <Text className="font-body text-grey-subtext sm:text-1sm">
                    If you have any questions or complaints regarding this payment, please reach out to us on{" "}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-yellow" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  preview_text: "Verify your email address",
  name: "John Doe",
  plan_name: "Basic",
  days_left: "10",
  downgrade_message_1: "This is a test message",
  downgrade_message_2: "This is a test message",
  downgrade_message_3: "This is a test message",
} as Props;

export default EmailTemplate;
