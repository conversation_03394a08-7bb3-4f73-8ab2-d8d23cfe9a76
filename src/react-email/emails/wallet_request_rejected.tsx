import {
  Body,
  Column,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Preview,
  Row,
  Section,
  Text,
  Tailwind,
  Hr,
  Link,
} from "@react-email/components";
import EmailFonts from "./components/fonts";
import config from "../tailwind.config";
import EmailButton from "./components/button";
import EmailHeader from "./components/header";
import EmailFooter from "./components/footer";
import * as React from "react";

interface Props {
  name: string;
  preview_text: string;
  message: string;
  currencies: string;
}

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="kyc_rejected">
                <>
                  Your Request
                  <br /> has Failed!
                </>
              </EmailHeader>
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Your request to enable {props.currencies} for your store has failed.
                  </Text>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    <span className="text-black-secondary font-body font-medium">See the reason below:</span>
                  </Text>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">{props.message}</Text>
                  <Hr />
                  <Text className="font-body text-grey-subtext sm:text-base text-1sm">
                    Please wait 30 days to make another request, if you think this decision was a mistake please{" "}
                    <Link
                      href="https://api.whatsapp.com/send/?phone=2349042550548"
                      className="text-primary-500 font-medium font-body"
                    >
                      contact support here
                    </Link>
                  </Text>
                  <Hr />
                  <Text className="font-body text-grey-subtext text-1sm">
                    If you have any issues with this, please reach out to us on{" "}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-red" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: "John Doe",
  store_name: "Catlog",
  preview_text: "Verify your email address",
  invoice_amount: "$100",
  message: "This is a test message",
  currencies: "Pounds, Euros, Dollars",
} as Props;

export default EmailTemplate;
