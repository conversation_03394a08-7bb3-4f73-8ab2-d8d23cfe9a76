import {
  Body,
  Column,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Row,
  Section,
  Text,
  Hr,
  Link,
  Tailwind,
} from "@react-email/components";
import EmailFonts from "./components/fonts";
import config from "../tailwind.config";
import EmailButton from "./components/button";
import EmailHeader from "./components/header";
import EmailFooter from "./components/footer";
import * as React from "react";

interface Props {
  preview_text: string;
  name: string;
  currencies: string;
  app_link: string;
}

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}{" "}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="request_approved" />
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}!
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                  Your wallet request has been approved for {" "}
                    <span className="text-black-secondary font-semibold font-body">{props.currencies}</span> you can now collect payments in these currencies.
                  </Text>
                  <Text className="font-body text-black-secondary text-sm sm:text-base">
                  This means that:
                  </Text>
                  <ul className="list-image-[url(https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/check.png)]">
                    <li className="font-body text-black-muted py-1.25">You can now receive payments in these currencies</li>
                    <li className="font-body text-black-muted py-1.25">You can now convert funds to & from these currencies</li>
                    <li className="font-body text-black-muted py-1.25">You can also make payouts.</li>
                  </ul>
                  <Text className="text-grey-subtext font-body text-base">
                  Time to start racking some coins.
                  </Text>
                  <EmailButton href={props.app_link} className="my-6.25">
                    Start Collecting Payments
                  </EmailButton>
                  
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    If you have any questions or complaints regarding this payment, please reach out to us on{" "}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-green" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  preview_text: "Verify your email address",
  name: "John Doe",
  currencies: "Pounds, Euros, Dollars",
  app_link: "https://catlog.shop",
} as Props;

export default EmailTemplate;
