import {
  Body,
  Column,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Row,
  Section,
  Text,
  Tailwind,
  Hr,
} from '@react-email/components';
import EmailFonts from './components/fonts';
import config from '../tailwind.config';
import EmailButton from './components/button';
import EmailHeader from './components/header';
import EmailFooter from './components/footer';
import { EmailOrderData } from './utils/types';
import OrderDetails from './components/orders/order-details';
import * as React from 'react';

interface EmailTemplateComponent extends React.FC<EmailOrderData> {
  PreviewProps: EmailOrderData;
}

const EmailTemplate = ((props: EmailOrderData) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="order_received" />
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    A new order has been placed through your affiliate link!
                  </Text>
                  <Hr />
                  <OrderDetails {...props} />
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    You can view more details about this order in your affiliate dashboard.
                  </Text>
                  <EmailButton href={props.order.link} className="my-6.25">
                    View Order Details
                  </EmailButton>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-purple" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
}) as EmailTemplateComponent;

const previewProps: EmailOrderData = {
  preview_text: 'New order received through your affiliate link!',
  store_name: 'Catlog',
  name: 'John Doe',
  store_logo: 'https://catlog.shop/logo.png',
  store_color: '#6366F1',
  store_phone: '+1234567890',
  order: {
    order_id: '123456',
    link: 'https://catlog.shop/affiliate/orders/123456',
    payment_link: 'https://catlog.shop/pay/123456?byCustomer=true',
    order_status: 'pending',
    products: [
      {
        name: 'Product 1',
        price: 1000,
        quantity: 1,
        image: 'https://catlog.shop/product1.jpg',
      },
    ],
    currency: 'USD',
    total: 1500,
    delivery_method: 'delivery',
    fees: [
      {
        label: 'Delivery Fee',
        amount: 500,
      },
    ],
    customer_info: {
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+1234567890',
    },
  },
};

EmailTemplate.PreviewProps = previewProps;

export default EmailTemplate;
