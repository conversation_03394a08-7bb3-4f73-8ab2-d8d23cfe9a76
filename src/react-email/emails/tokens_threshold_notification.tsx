import {
  Body,
  Column,
  Container,
  Head,
  <PERSON><PERSON>,
  Hr,
  Html,
  Link,
  Preview,
  Row,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";
import config from "../tailwind.config";
import EmailButton from "./components/button";
import EmailFonts from "./components/fonts";
import EmailFooter from "./components/footer";
import * as React from "react";
import EmailHeader from "./components/header";

interface Props {
  preview_text?: string;
  name: string;
  store_name: string;
  token_balance: string;
  percentage_used: string;
  cta_link: string;
}

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}

        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="tokens_threshold_notification" />
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}!
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Your Chowbot token balance is running low — you have only{" "}
                    <span className="text-black-secondary font-semibold font-body">{props.token_balance}</span> tokens left,
                    which means you've used{" "}
                    <span className="text-black-secondary font-semibold font-body">{props.percentage_used}</span> of
                    your tokens.
                  </Text>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Please head over to your dashboard to buy extra tokens or upgrade your subscription plan.
                  </Text>

                  <EmailButton href={props.cta_link} className="my-6.25">
                    Buy Extra Tokens
                  </EmailButton>
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    If you have any questions or complaints regarding this email, please reach out to us on{" "}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-yellow" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: "John Doe",
  store_name: "Catlog",
  token_balance: "100",
  percentage_used: "10",
  cta_link: "https://catlog.shop",
} as Props;

export default EmailTemplate;
