import { Types } from "mongoose"

export default {
  "plans": [
    {
      "_id": Types.ObjectId("61a922875918a2f3edd2e6a1"),
      "description": [
        "One store",
        "Store link",
        "Up to 10 products"
      ],
      "name": "Starter",
      "amount": 0,
      "interval": 365,
      "interval_text": "Forever",
      "type": "STARTER",
      "methods": [],
      "createdAt": new Date(1638474375267),
      "updatedAt": new Date(1653267800886),
      "__v": 0,
      "country": "NG"
    },
    {
      "_id": Types.ObjectId("61925d48d08972070dada5c2"),
      "description": [
        "Everyting in starter",
        "Custome store link",
        "Up to 100 products"
      ],
      "name": "Basic",
      "amount": 900,
      "interval": 30,
      "interval_text": "Monthly",
      "type": "BASIC",
      "methods": [
        {
          "_id": Types.ObjectId("61925d48d08972070dada5c3"),
          "code": "PLN_zkbiqi3oy5s4lg2",
          "type": "PAYSTACK",
          "total_amount": 915,
          "percent": 1.5
        }
      ],
      "createdAt": new Date(1636982088539),
      "updatedAt": new Date(1653267800886),
      "__v": 0,
      "country": "NG"
    }
  ],
  "countries": [
    {
      "_id": Types.ObjectId("628adcb188e9cea9e335d9e0"),
      "name": "Nigeria",
      "currency": "NGN",
      "dial_code": "+234",
      "code": "NG",
      "emoji": "🇳🇬",
      "__v": 0
    },
    {
      "_id": Types.ObjectId("628adcc888e9cea9e335d9e1"),
      "name": "Ghana",
      "currency": "GHC",
      "dial_code": "+233",
      "code": "GH",
      "emoji": "🇬🇭",
      "__v": 0
    }
  ],
  "paymentmethods": [
    {
      "_id": Types.ObjectId("619580c7400ff40f3e295b11"),
      "name": "Paystack",
      "icon": "nothing",
      "color": "#000",
      "type": "PAYSTACK",
      "created_at": new Date(1637187783388),
      "updated_at": new Date(1637187783388),
      "__v": 0
    }
  ]
}