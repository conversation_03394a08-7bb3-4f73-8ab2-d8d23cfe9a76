import { Types } from 'mongoose';
import faker from 'faker';
import bcrypt from 'bcrypt';
import { PLAN_TYPE } from '../enums/plan.enum';

export const TEST_USER_OBJECT_ID = '630501aa00710878fd214f08';
export const TEST_STORE_ID = '630501aa00710878fd214f08';
export const TEST_CATEGORY_ID = '613a3b2ee82e38329418ccba';
// export const TEST_USER_TOKEN =
//   'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IiJ9.xRLlg501bn8xAWAyMX8pVwF6_tDq9-eUr8KqzRSZaPU';
export const TEST_ITEM_ID = '6173effb5da62ef9ce7d47e2';
export const TEST_PLAN_ID = '61a922605918a2a645d2e6a0';
export const TEST_SUBSCRIPTION_ID = '63049415b4cc63538fde22a5';
export const TOKENS = {
  NO_STORE:
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjYzMDUwMWFhMDA3MTA4NzhmZDIxNGYwOCIsInR5cGUiOiJuZXciLCJzdG9yZSI6e30sImlhdCI6MTY2MTI3MjQ5MiwiZXhwIjoxNjY0ODcyNDkyfQ.TEWEnwKP5u_-7YXkpM6PeyQ1tKkoS-i1aTKDFxW5Rk0',
  WITH_STORE_NO_SUBSCRIPTION:
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjYzMDUwMWFhMDA3MTA4NzhmZDIxNGYwOCIsInR5cGUiOiJuZXciLCJzdG9yZSI6eyJpZCI6IjYzMDUwMWU0MDA3MTA4NzhmZDIxNGYwOSIsIm5hbWUiOiJLYl9zY2lzc29ycyJ9LCJpYXQiOjE2NjEyNzI1ODksImV4cCI6MTY2NDg3MjU4OX0.jLO2nJXwtFpssJk6Fa7Io6wnV5pji3qK2NAfEHctywA',
  WITH_STORE_AND_STARTER_SUBSCRIPTION:
    //'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjYzMDQ4ZGZmMTdiOTA1NTI0ODE1ZjdjYSIsInR5cGUiOiJuZXciLCJzdG9yZSI6eyJpZCI6IjYzMDQ4ZTUxMDNmODMwNTI4MTAzYzY1MCIsIm5hbWUiOiJLYl9zY2lzc29ycyIsInN1YnNjcmlwdGlvbiI6IjYzMDQ5NDE1YjRjYzYzNTM4ZmRlMjJhNSJ9LCJpYXQiOjE2NjEyNDQ1NTEsImV4cCI6MTY2NDg0NDU1MX0.8vOvFehcuDLuJNtywtqlNyY4pl7K87Zw__x9H1Vm2Dk',
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdG9yZXMiOlsiNjMwNDk0MTViNGNjNjM1MzhmZGUyMmE1Il0sImlkIjoiNjMwNTAxYWEwMDcxMDg3OGZkMjE0ZjA4IiwidHlwZSI6Im5ldyIsInN0b3JlIjp7ImlkIjoiNjMwNTAxYWEwMDcxMDg3OGZkMjE0ZjA4IiwibmFtZSI6IktiX3NjaXNzb3JzIiwic3Vic2NyaXB0aW9uIjoiNjMwNDk0MTViNGNjNjM1MzhmZGUyMmE1In0sImlhdCI6MTY2MTI0NDU1MSwiZXhwIjoxNjY0ODQ0NTUxfQ.HBs0E6oO3CxNqoqzZDOGz0ZeZJlTlE2QJBirG_G1sSg'
};

export const storeMock = {
  _id: Types.ObjectId(TEST_STORE_ID),
  name: 'kb_scissors',
  description: faker.commerce.productDescription(),
  owner: Types.ObjectId(TEST_USER_OBJECT_ID),
  categories: [
    { _id: Types.ObjectId(TEST_CATEGORY_ID), name: 'Test Category' },
  ],
  slug: 'TEST_SLUG_STORE',
  country: 'NG',
  phone: faker.phone.phoneNumber(),
  views: 0,
};

export const userMock = {
  _id: Types.ObjectId(TEST_USER_OBJECT_ID),
  name: 'Catlog',
  email: '<EMAIL>',
  password: bcrypt.hashSync('password', 10),
  phone: '+234-6023173892',
  stores: [TEST_STORE_ID]
};

export const itemMock = {
  _id: Types.ObjectId(TEST_ITEM_ID),
  name: faker.commerce.productName(),
  description: faker.commerce.productDescription(),
  slug: 'TEST_SLUG_ITEM',
  available: true,
  store: Types.ObjectId(TEST_STORE_ID),
  views: 0,
  category: Types.ObjectId(TEST_CATEGORY_ID),
};

export const subscriptionMock = {
  _id: Types.ObjectId(TEST_SUBSCRIPTION_ID),
  plan: Types.ObjectId(TEST_PLAN_ID),
  owner: Types.ObjectId(TEST_USER_OBJECT_ID),
};

export const planMock = {
  _id: Types.ObjectId(TEST_PLAN_ID),
  name: 'Starter Plan',
  amount: 0,
  description: 'This is the month package',
  type: PLAN_TYPE.STARTER,
  interval: 'MONTH',
  interval_text: 'string',
  methods: [
    {
      code: 'string',
      type: PLAN_TYPE.STARTER,
      total_amount: 0,
      percent: 0,
    },
  ],
};
