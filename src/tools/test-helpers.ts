import { CartService } from '../modules/cart/cart.service';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';
import { ExtractJwt, Strategy } from 'passport-jwt';
import {
  planMock,
  storeMock,
  subscriptionMock,
  TEST_STORE_ID,
  TEST_USER_OBJECT_ID,
} from './testdata';

@Injectable()
export class MockJwtStrategy extends PassportStrategy(Strategy) {
  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: true,
      secretOrKey: '12345678',
    });
  }
  validate(payload: any) {
    return {
      stores: [],
      role: 'OWNER',
      ...payload,
      store: { ...payload.store, plan: { type: 'STARTER' }, subscription: { ...subscriptionMock, plan: { ...planMock } } },
    };
    // return {
    //   id: TEST_USER_OBJECT_ID,
    //   role: 'OWNER',
    //   store: {
    //     id: TEST_STORE_ID,
    //     name: storeMock.name,
    //     subscription: { ...subscriptionMock, plan: { ...planMock } },
    //   },
  }
}
