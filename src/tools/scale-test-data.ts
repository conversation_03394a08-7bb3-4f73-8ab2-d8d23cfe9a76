import { Types } from "mongoose";

import dayjs from "dayjs";
import faker from "faker";

export default {
  analytics: [
    ...(() => {
      
      const analytics = [];
      for (let i = 0; i < 10000; i++) {
        const time = faker.datatype.datetime({
          min: dayjs(Date.now()).set('year', 2019).toDate().getTime(),
          max: Date.now(),
        });
        analytics.push({
          _id: new Types.ObjectId(),
          store: "61ba34bec3808b7b5b6b12eb",
          ip: '::ffff:127.0.0.1',
          time: time,
          client: 'A client',
          created_at: time,
          updated_at: time,
          __v: 0,
        });

      }
      return analytics;
    })(),
  ],
};



 