import { Types } from 'mongoose';
import faker from 'faker';
import bcrypt from 'bcrypt';
import { PLAN_TYPE } from '../enums/plan.enum';

export const TEST_USER_OBJECT_ID = '63bf363e6afb86d6656001d6';
export const TEST_STORE_ID = '63bf367f6afb865ab86001d7';
export const TEST_CATEGORY_ID = '63bf37f16afb86df756001da';
// export const TEST_USER_TOKEN =
//   'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IiJ9.xRLlg501bn8xAWAyMX8pVwF6_tDq9-eUr8KqzRSZaPU';
export const TEST_ITEM_ID = '63bf38286afb863f226001dc';
export const TEST_PLAN_ID = '61a922875918a2f3edd2e6a1';
export const TEST_SUBSCRIPTION_ID = '63bf39df6afb862cbf6001f3';
export const TOKENS = {
  NO_STORE:
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjYzYmYzNjNlNmFmYjg2ZDY2NTYwMDFkNiIsInR5cGUiOiJuZXciLCJzdG9yZSI6e30sImlhdCI6MTY3MzQ3NTY0NiwiZXhwIjoxNjc3MDc1NjQ2fQ.Hyf7FYmRDJKXEqeTJBc1jAifQ_AwDKi80d7mclPygyI',
  WITH_STORE_NO_SUBSCRIPTION:
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjYzYmYzNjNlNmFmYjg2ZDY2NTYwMDFkNiIsInR5cGUiOiJuZXciLCJzdG9yZSI6eyJpZCI6IjYzYmYzNjdmNmFmYjg2NWFiODYwMDFkNyIsIm5hbWUiOiJrYl9zY2lzc29ycyJ9LCJpYXQiOjE2NzM0NzU3MTEsImV4cCI6MTY3NzA3NTcxMX0.mwR3Yi9IeylgHdlmN3qRNJfqV4KGE7XJKe2GJISouEc',
  WITH_STORE_AND_STARTER_SUBSCRIPTION:
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjYzYmYzNjNlNmFmYjg2ZDY2NTYwMDFkNiIsInR5cGUiOiJuZXciLCJzdG9yZSI6eyJpZCI6IjYzYmYzNjdmNmFmYjg2NWFiODYwMDFkNyIsIm5hbWUiOiJrYl9zY2lzc29ycyIsInN1YnNjcmlwdGlvbiI6IjYzYmYzOWRmNmFmYjg2MmNiZjYwMDFmMyJ9LCJpYXQiOjE2NzM0NzY1NzYsImV4cCI6MTY3NzA3NjU3Nn0.eHuKhznYESpvS0rerfctLXyOwEX5SzOcJuWXZyFhKgM',
};

export const storeMock = {
  _id: Types.ObjectId(TEST_STORE_ID),
  name: 'kb_scissors',
  description: faker.commerce.productDescription(),
  owner: Types.ObjectId(TEST_USER_OBJECT_ID),
  categories: [
    { _id: Types.ObjectId(TEST_CATEGORY_ID), name: 'Test Category' },
  ],
  slug: 'TEST_SLUG_STORE',
  country: 'NG',
  phone: faker.phone.phoneNumber(),
  views: 0,
};

export const userMock = {
  _id: Types.ObjectId(TEST_USER_OBJECT_ID),
  name: 'Catlog',
  email: '<EMAIL>',
  password: bcrypt.hashSync('password', 10),
  phone: '+234-8071571571',
};

export const itemMock = {
  _id: Types.ObjectId(TEST_ITEM_ID),
  name: faker.commerce.productName(),
  description: faker.commerce.productDescription(),
  slug: 'TEST_SLUG_ITEM',
  available: true,
  store: Types.ObjectId(TEST_STORE_ID),
  views: 0,
  category: Types.ObjectId(TEST_CATEGORY_ID),
};

export const subscriptionMock = {
  _id: Types.ObjectId(TEST_SUBSCRIPTION_ID),
  plan: Types.ObjectId(TEST_PLAN_ID),
  owner: Types.ObjectId(TEST_USER_OBJECT_ID),
};

export const planMock = {
  _id: Types.ObjectId(TEST_PLAN_ID),
  name: 'Starter Plan',
  amount: 0,
  description: 'This is the month package',
  type: PLAN_TYPE.STARTER,
  interval: 'MONTH',
  interval_text: 'string',
  methods: [
    {
      code: 'string',
      type: PLAN_TYPE.STARTER,
      total_amount: 0,
      percent: 0,
    },
  ],
};
