import { bool } from 'aws-sdk/clients/signer';
import { INTENTS, INTERACTIVE_MESSAGE_TYPES, WHATSAPP_BOT_STEPS } from '../enums/whatsapp.enum';
import { CURRENCIES } from '../modules/country/country.schema';
import { Item } from '../modules/item/item.schema';
import { Branch, BranchType } from '../modules/store/branches/branches.schema';
import { MessageData } from '../modules/whatsapp-bot/utils/functions.utils';
import { MESSAGES } from '../modules/whatsapp-bot/utils/messages.utils';
import { MESSAGE_TYPES } from '../repositories/whatsapp.repository';

export interface WhatsappMessageDto {
  object: string;
  entry: Entry[];
}
export interface Entry {
  id: string;
  changes: Change[];
}

export interface Change {
  value: Value;
  field: string;
}

export interface Value {
  messaging_product: string;
  metadata: Metadata;
  contacts?: Contact[];
  messages?: Message[];
  statuses?: Status[];
}
export interface Status {
  id: string;
  status: 'delivered' | 'sent';
  timestamp: string;
  recipient_id: string;
  conversation: {
    id: string;
    origin: {
      type: 'utility';
    };
  };
}

export interface Location {
  address: string;
  latitude: number;
  longitude: number;
  name: string;
}

export interface Metadata {
  display_phone_number: string;
  phone_number_id: string;
}

export interface Contact {
  profile: Profile;
  wa_id: string;
}

export interface Profile {
  name: string;
}
export interface Message {
  type: MESSAGE_TYPES;
  from: string;
  timestamp: string;
  id: string;
  context?: {
    from: string;
    id: string;
  };
  text?: Text;
  location?: Location;
  interactive?: { type: INTERACTIVE_MESSAGE_TYPES } & {
    [key in INTERACTIVE_MESSAGE_TYPES]: {
      id: string;
      title: string;
      description?: string;
      response_json?: string;
    };
  };
  button?: {
    payload: string;
    text: string;
  };
}

export interface Text {
  body: string;
}

export interface BotContext {
  botPhoneId: string;
  destinationPhone?: string;
  currency?: CURRENCIES;
}
export enum SESSION_TYPES {
  GENERIC,
  ORDER_CONFIRMATION,
  CHECK_IN,
}

export interface GlobalTimeOut {
  value: number;
}

export interface paginateOption {
  page: number;
  limit: number;
  sort: any;
}

interface SessionStoreData {
  name: string;
  id: string;
  contact_phone: string;
  secondary_phone: string;
  storeCode: string;
  deliveries_enabled: boolean;
  pickup_enabled: boolean;
  hero_image: string;
  // address: string;
  pickup_address?: {
    longitude: number;
    latitude: number;
    name: string;
    address: string;
  };
  state: string;
  delivery_areas?: { id: string; title: string; fee: number }[];
  confirm_order_before_payment: boolean;
  currency: CURRENCIES;
  menu_images?: string[];
  store_menu?: string;
  use_menu_ordering?: boolean;
  average_delivery_timeline?: string;
  require_geolocation?: boolean;
  branches_id?: string;
  branches?: BranchType[];
  subscription_id?: string;
  custom_order_success_message?: string;
  auto_delivery?: boolean;
  auto_confirm_chowbot_orders?: boolean;
  collect_order_notes?: boolean;
  pass_chowbot_fee_to_deliveries?: boolean;
}

export interface BotSession {
  type: SESSION_TYPES;
  key: string;
  date_initiated: string;
}

export interface OrderConfirmationSession extends BotSession {
  bot_phone_id: string;
  seller_phone: string;
  secondary_phone: string;
  store_name: string;
  store_currency: string;
  date_initiated: string;
  timeout_job_id: string;
  generic_session_key: string;
  key: string;
  store_id: string;
  order_id: string;
  meta?: any;
  order_is_escalated?: boolean;
  active_confirmations_at_start: number;
}

export interface CheckInSession extends BotSession {
  from: string;
  to: string;
  phone_id: string;
  step: WHATSAPP_BOT_STEPS;
  order?: string;
  store?: {
    id: string;
    name: string;
    code: string;
  };
  customer?: string;
}

export interface GenericBotSession extends BotSession {
  timeout_job: string;
  last_incoming_message_time: Date;
  from: string;
  to: string;
  phone_id: string;
  conversation_id?: string;
  db_session?: string;
  customer?: {
    name: string;
    phone_number: string;
    id: string;
    name_verified: boolean;
  };
  store?: SessionStoreData;
  previous_store?: SessionStoreData;
  order?: {
    cart_id?: string;
    coupon_code?: string;
    order_id?: string;
    items?: any[];
    delivery_method?: 'DELIVERY' | 'PICKUP' | 'NONE';
    delivery_info?: {
      name: string;
      phone: string;
      delivery_address: string;
      user_provided_address?: string;
      delivery_area: string;
      delivery_notes?: string;
      longitude?: number;
      latitude?: number;
      delivery_id?: string;
      address_id?: string;
      auto_delivery_fee?: number;
    };
    invoice_id?: string;
    receipt_id?: string;
    receipt_link?: string;
    order_notes?: string;
    is_cancelled?: boolean;
    invalid_items?: {
      name: string;
      id: string;
      variant_id?: string;
      status: 'deleted' | 'unavailable' | 'depleted' | 'over_stock';
      quantity?: number;
      inventory?: number;
    }[];
    payment_fee?: number; //stores delivery fee + payment charge
  };
  payment?: {
    status: string;
    id: string;
    payment_method: 'TRANSFER | OTHERS';
  };
  history: {
    step: WHATSAPP_BOT_STEPS;
    secondary_steps?: WHATSAPP_BOT_STEPS[];
    timestamp: string;
    step_message?: {
      type: MESSAGES;
      data: any;
    };
  }[];
  step: WHATSAPP_BOT_STEPS;
  secondary_steps?: WHATSAPP_BOT_STEPS[];
  intent: INTENTS;
  search_data?: {
    skip?: number;
    page?: number;
    query?: string;
  };
  metadata?: any;
}
export type BotStateMachine = {
  [key in WHATSAPP_BOT_STEPS]?: (session: GenericBotSession, data?: MessageData) => BotProcessorResult;
};
export type BotProcessorResult = Promise<{
  clear?: boolean;
  restart?: boolean;
  startFromItems?: boolean;
  popHistory?: boolean;
  endSession?: boolean;
  repeat?: boolean;
  popHistoryAndResendPrevious?: boolean;
  currentStep?: WHATSAPP_BOT_STEPS;
  secondarySteps?: WHATSAPP_BOT_STEPS[];
  intent?: INTENTS;
  stepMessage?: {
    type: MESSAGES;
    data: any;
  };
  meta?: any;
}>;

export interface DeliveryFormResponse {
  delivery_area: string;
  delivery_address: string;
  delivery_notes: string;
  full_name: string;
  flow_token: string;
  phone_number: string;
}

export interface GptItemSearch {
  results: { item_id: string; selected_option_id: string; intent: string; quantity: number | string }[];
}
[];
export interface GptItemSearchOptimized {
  results: { item_name: string; selected_option_name: string; intent: string; quantity: number | string }[];
}
[];

export interface CartForm {
  [key: string]: {
    id: string;
    quantity: number;
    item_quantity?: number;
    hasVariants: boolean;
    selectedVariants: string[];
    options: Item['variants']['options'];
    intent?: 'ADD' | 'REMOVE';
    name: string;
  };
}
