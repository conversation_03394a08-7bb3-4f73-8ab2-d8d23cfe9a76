import * as e from 'express';
import { Subscription } from '../modules/subscription/subscription.schema';
import { STORE_ROLES } from '../utils/permissions.util';
import { User } from '../modules/user/user.schema';
import { Store } from '../modules/store/store.schema';
import { COUNTRY_CODE } from '../modules/country/country.schema';

export interface IRequest extends e.Request {
  user: User & {
    role?: STORE_ROLES;
    store: {
      id: string;
      name: string;
      subscription: Subscription;
      payments_enabled: boolean;
      flags: Store['flags'];
      configuration: Store['configuration'];
      country: COUNTRY_CODE;
    };
  };
}

export interface IResponse extends e.Response {}
