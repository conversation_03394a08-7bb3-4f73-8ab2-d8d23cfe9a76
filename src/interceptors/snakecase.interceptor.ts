import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import snakecaseKeys from 'snakecase-keys';
import { Observable } from 'rxjs';
import { CURRENCIES } from '../modules/country/country.schema';

/**
 * Converts all outgoing data to snakecase
 */
@Injectable()
export class SnakecaseInterceptor implements NestInterceptor {
  async intercept(context: ExecutionContext, next: CallHandler<any>): Promise<Observable<any>> {
    const data = await next.handle().toPromise();
    // const stripedData = stripUnderScoreId(data);
    return snakecaseKeys(JSON.parse(JSON.stringify(data)), {
      deep: true,
      exclude: [/^[A-Z]+$/], //dont convert fully capitalized keys to snakecase
    });
  }
}
