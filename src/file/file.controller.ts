import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  NotFoundException,
  Param,
  Post,
  Query,
  Res,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiConsumes, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { Base64FileUploadDto, FileUploadDto, UploadedFileResponse } from '../models/dtos/file.dto';
import { S3Repository } from '../repositories/s3.repositories';
import { FileTypeToPathMap, genChars } from '../utils';
import { Response } from 'express';
import { IResponse } from '../interfaces/request.interface';

@Controller('file')
@ApiTags('File')
export class FileController {
  constructor(private readonly s3: S3Repository) {}

  @Post('')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Upload a file',
    type: FileUploadDto,
  })
  @ApiOkResponse({ type: UploadedFileResponse })
  @UseInterceptors(FileInterceptor('file'))
  @HttpCode(HttpStatus.OK)
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() fileUploadDto: FileUploadDto,
  ): Promise<UploadedFileResponse> {
    const { type } = fileUploadDto;
    file.filename = `${genChars(10)}.jpeg`;

    let path = '';
    if (type) {
      path = FileTypeToPathMap[type];
    }

    const data = await this.s3.upload(file, undefined, path);

    return {
      message: '',
      data: {
        link: data.Location,
      },
    };
  }

  @Post('/video')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'upload a file',
    type: FileUploadDto,
  })
  @ApiOkResponse({ type: UploadedFileResponse })
  @UseInterceptors(FileInterceptor('file'))
  @HttpCode(HttpStatus.OK)
  async uploadVideoFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() fileUploadDto: FileUploadDto,
  ): Promise<UploadedFileResponse> {
    const { type } = fileUploadDto;

    let path = 'VIDEOS';
    if (type) {
      path = `VIDEOS/${FileTypeToPathMap[type]}`;
    }

    file.filename = `${genChars(10)}.mp4`;
    const data = await this.s3.upload(file, undefined, path);

    return {
      message: '',
      data: {
        link: data.Location,
      },
    };
  }

  @Post('/images/base64')
  @ApiConsumes('application/json')
  @ApiBody({
    description: 'Upload a base64 string as a file',
    type: Base64FileUploadDto,
  })
  @ApiOkResponse({ description: 'File uploaded successfully' })
  @HttpCode(HttpStatus.OK)
  async uploadBaseFile(@Body() fileUploadDto: Base64FileUploadDto): Promise<any> {
    const { base64, fileName, type } = fileUploadDto;

    let path = '';
    if (type) {
      path = FileTypeToPathMap[type];
    }

    // Extract mime type and base64 data
    let mimeType: string;
    let base64Data: string;
    if (base64.startsWith('data:')) {
      const matches = base64.match(/^data:(.+);base64,(.+)$/);
      if (matches && matches.length === 3) {
        mimeType = matches[1];
        base64Data = matches[2];
      } else {
        throw new Error('Invalid base64 string');
      }
    } else {
      // Default to 'image/jpeg' if mime type is not provided
      mimeType = 'image/jpeg';
      base64Data = base64;
    }

    // Generate a random filename with the correct extension
    const extension = mimeType.split('/')[1] || 'jpeg';
    const randomFileName = `${genChars(10)}.${extension}`;

    const data = await this.s3.uploadB64(base64Data, randomFileName, undefined, path, mimeType);

    return {
      message: 'File uploaded successfully',
      data: {
        link: data.Location,
      },
    };
  }

  @Get('/images')
  async getImage(
    @Query('path') path: string,
    @Query('width') width: number,
    @Query('height') height: number,
    @Res() res: IResponse,
  ) {
    try {
      const resizedImage = await this.s3.resizeImage(path, {
        width: isNaN(width) ? null : width,
        height: isNaN(height) ? null : height,
      });

      // Convert the resized image buffer to base64
      const base64Image = resizedImage.toString('base64');
      // Convert Base64 string to a buffer
      const buffer = Buffer.from(base64Image, 'base64');

      // Set headers for file download
      res.setHeader('Content-Type', 'image/jpeg');
      res.setHeader('Content-Disposition', `inline; filename="${path}"`);

      res.end(buffer);
      return {};
      // return imageDataUri;
    } catch (error) {
      throw new NotFoundException('Image not found');
    }
  }

  async uploadImage(file: string, path: string) {
    const key = genChars(24);
    if (path[-1] !== '/') path += '/';
    return await this.s3.withPath(path).uploadB64(file, key);
  }
}
