import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtConfig } from '../config/types/jwt.config';
import { BrokerTransportService } from '../broker/broker-transport.service';
import { User, UserDocument } from '../modules/user/user.schema';
import { BROKER_PATTERNS } from '../enums/broker.enum';
import { INTERNAL_ROLES, InternalDashboardPermissions, SCOPES, STORE_ROLES } from '../utils/permissions.util';
import { Subscription } from '../modules/subscription/subscription.schema';
import { createHmac } from 'crypto';
import { ApiGuardConfig } from '../config/types/api-guard.config';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private readonly brokerTransport: BrokerTransportService, private configService: ConfigService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<JwtConfig>('jwtConfiguration').secret,
    });
  }

  async validate(payload: any) {
    if (!payload.type) {
      throw new UnauthorizedException('Please re-login');
    }

    if (payload.type == 'internal') {
      // Check if this is the main admin from config
      const apiGuardCfg = this.configService.get<ApiGuardConfig>('apiGuardConfig');

      if (apiGuardCfg) {
        const key = apiGuardCfg.username + ':' + apiGuardCfg.password;
        const hash = createHmac('sha256', key).update(apiGuardCfg.rn).digest('hex');

        if (hash === payload.id) {
          // This is the main admin user from config - give them all permissions
          const allInternalPermissions = Object.values(SCOPES.INTERNAL_DASHBOARD);
          return {
            id: payload.id,
            type: payload.type,
            internalRole: INTERNAL_ROLES.SUPER_ADMIN,
            permissions: allInternalPermissions,
          };
        }
      }

      // For users with internal dashboard roles
      if (payload.userId) {
        const dbUser = await this.brokerTransport
          .send<UserDocument>(BROKER_PATTERNS.USER.GET_USER, { _id: payload.userId })
          .toPromise();

        if (dbUser && dbUser.internal_dashboard_role) {
          const permissions = InternalDashboardPermissions[dbUser.internal_dashboard_role] || [];
          return {
            id: dbUser._id || dbUser.id,
            userId: payload.userId,
            type: payload.type,
            internalRole: dbUser.internal_dashboard_role,
            permissions,
          };
        }
      }

      return { id: payload.id, type: payload.type };
    }

    const store = await this.brokerTransport
      .send(BROKER_PATTERNS.STORE.GET_STORE, { _id: payload.store?.id })
      .toPromise();

    const user = await this.brokerTransport
      .send<UserDocument>(BROKER_PATTERNS.USER.GET_USER, { _id: payload.id })
      .toPromise();

    // const subscription = await this.brokerTransport
    //   .send<Subscription>(BROKER_PATTERNS.PAYMENT.GET_SUBSCRIPTION, {
    //     _id: payload.store?.subscription,
    //   })
    //   .toPromise();

    //RESTORE TO OLD METHOD

    // if (user.stores.length == 0 && store) user.stores = [store];

    const role = this.getUserRole(user, payload.store.id);

    return {
      ...user,
      ...payload,
      store,
      role,
    };
  }

  getUserRole(user: User, store_id: string) {
    const store = user.stores.find((store) => store.id === store_id);
    if (store) {
      const isStoreOwner = (store.owner as any).toString() === user.id;
      if (isStoreOwner) return STORE_ROLES.OWNER;

      const role = store.owners.find((owner) => owner.user === user.id)?.role;
      if (role) return STORE_ROLES[role];
    }

    return undefined;
  }
}
