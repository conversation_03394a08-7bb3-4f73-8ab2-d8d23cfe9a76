import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsDateString,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateIf,
  ValidateNested,
  isString,
} from 'class-validator';
import {
  DELIVERY_TYPE,
  DeliveryCourier,
  DeliveryItem,
  DeliveryPackageDimensions,
} from '../../modules/deliveries/deliveries.schema';
import { Transform, Type } from 'class-transformer';
import { DELIVERY_PROVIDERS } from '../../enums/deliveries';

export class CreateAddressDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @IsOptional()
  @ValidateIf((o, value) => value !== undefined)
  @Transform((value) => value || undefined)
  @IsString()
  @IsEmail()
  email?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  phone: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  address: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  customer?: string;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  save_as_customer?: boolean;

  // @ApiProperty()
  // @IsNumber()
  // @IsOptional()
  // latitude?: string;

  // @ApiProperty()
  // @IsNumber()
  // @IsOptional()
  // longitude?: string;
}

export class CreateDeliveryDraftDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  sender_address?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  receiver_address?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  order?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  package_category?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  chowdeck_fee_id?: number;

  @ApiProperty()
  @IsOptional()
  package_dimensions?: DeliveryPackageDimensions;

  @ApiProperty()
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => DeliveryItem)
  items: DeliveryItem[];

  @ApiProperty({ required: true, enum: DELIVERY_PROVIDERS })
  @IsEnum(DELIVERY_PROVIDERS)
  @IsNotEmpty()
  provider: DELIVERY_PROVIDERS;

  @ApiProperty()
  @IsOptional()
  @IsString()
  delivery_notes?: string;

  @ApiProperty()
  @IsOptional()
  @IsDateString()
  pickup_date?: string;

  @ApiProperty()
  @IsOptional()
  @IsDateString()
  delivery_date?: string;

  @ApiProperty()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => DeliveryCourier)
  courier?: DeliveryCourier;

  @ApiProperty({ required: false, enum: DELIVERY_TYPE })
  @IsOptional()
  @IsEnum(DELIVERY_TYPE)
  type?: DELIVERY_TYPE;
}

export class UpdateDeliveryDraftDto extends CreateDeliveryDraftDto {}

export class InitiateDeliveryDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  service_code: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  courier_id: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  courier_name: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  courier_image: string;

  @ApiProperty()
  @IsOptional()
  @IsDateString()
  delivery_date: string;
}
