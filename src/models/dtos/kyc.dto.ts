import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { VERIFICATION_METHODS } from '../../enums/kycmethods.enum';

export class KycBasicInfoDto {
  first_name: string;
  last_name: string;
}

export class KycLookupDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  bvn: string;

  @ApiProperty({ example: '09-10-1996', description: 'Date of birth DD/MM/YYYY' })
  @IsString()
  @IsNotEmpty()
  dob: string;
}

export class KycPhoneLookupDto {
  phone: string;
  dob: string;
}

export class KycUpdateDobDto {
  dob: string;
}

export class KycVerifyDto {
  token: string;
  bvn: string;
  storeId?: string;
}

export class KycPhoneVerifyDto {
  token: string;
  phone: string;
  storeId?: string;
}

export class KycResendVerificationToken {
  bvn: string;
  storeId?: string;
}

export class KycVerifyIdDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  id_type: VERIFICATION_METHODS;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  id_number: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  selfie: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  photo_id: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  filename: string;

  @ApiProperty()
  @IsOptional()
  vnin?: string;

  @IsOptional()
  storeId?: string;
}

export class KycAddressDto {
  address_line1: string;
  lga: string;
  city: string;
  state: string;
}

export class KycSubmitDto {
  storeId: string;
}

export class CopyKYCDto {
  kyc_id: string;
}
