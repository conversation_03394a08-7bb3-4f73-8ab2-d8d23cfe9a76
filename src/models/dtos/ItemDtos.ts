import { ApiProperty, OmitType, PickType } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsArray,
  IsOptional,
  IsString,
  IsBoolean,
  IsObject,
  IsDateString,
  IsEnum,
  ArrayNotEmpty,
  ArrayUnique,
  ValidateNested,
} from 'class-validator';
import { Item, ThumbnailTypes, ProductVideo } from '../../modules/item/item.schema';
import { PaginatedQueryWithDataDto } from './PaginatedDto';
import { Store } from '../../modules/store/store.schema';
import { Coupon, Discount } from '../../modules/item/discount-coupons/discounts-coupons.schema';
import { CURRENCIES } from '../../modules/country/country.schema';
import { ITEM_SOURCE } from '../../enums/item.enum';
import { Transform } from 'class-transformer';

export class CreateItemDtoItem {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  price: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  quantity?: number;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  is_always_available?: boolean;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  available?: boolean;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  price_unit?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  category: string;

  @ApiProperty({ type: [String], required: false })
  @IsArray()
  @IsOptional()
  tags?: string[];

  @ApiProperty()
  @IsArray()
  @IsOptional()
  images: string[];

  @ApiProperty()
  @ValidateNested()
  @IsArray()
  @IsOptional()
  videos?: ProductVideo[];

  @ApiProperty()
  @IsObject()
  @IsOptional()
  meta?: any;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  thumbnail?: number;

  @ApiProperty()
  @IsOptional()
  @IsEnum(ThumbnailTypes)
  thumbnail_type?: ThumbnailTypes;

  @ApiProperty()
  @IsObject()
  @IsOptional()
  variants?: Item['variants'];

  @ApiProperty({ required: true, enum: ITEM_SOURCE })
  @IsEnum(ITEM_SOURCE)
  @IsNotEmpty()
  upload_source: ITEM_SOURCE;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @IsNotEmpty()
  minimum_order_quantity?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @IsNotEmpty()
  cost_price?: number;

  @ApiProperty()
  @IsDateString()
  @IsNotEmpty()
  @IsOptional()
  expiry_date?: Item['expiry_date'];

  @ApiProperty({ type: [String], required: false })
  @IsArray()
  @IsOptional()
  info_blocks?: string[];

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  tiered_pricing?: string;
}

export class CreateItemDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  store: string;

  @ApiProperty({
    type: [CreateItemDtoItem],
  })
  @IsNotEmpty()
  @IsArray()
  items: [CreateItemDtoItem];
}

export class BulkUpdateItemDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  price: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  quantity: number;

  @ApiProperty()
  @IsBoolean()
  @IsNotEmpty()
  @IsOptional()
  is_always_available: boolean;

  @ApiProperty({ type: [String], required: false })
  @IsArray()
  @IsOptional()
  tags?: string[];

  @ApiProperty()
  @IsObject()
  variants: Item['variants'];
}

export class BulkUpdateDto {
  @ApiProperty({
    type: [BulkUpdateItemDto],
  })
  @IsNotEmpty()
  @IsArray()
  items: [BulkUpdateItemDto];
}

export class GetItemsPaginatedDto extends PaginatedQueryWithDataDto<Item> {
  @ApiProperty({
    type: [Item],
  })
  data: [Item];
}

export class UpdateItemDto {
  @ApiProperty({ type: String })
  @IsString()
  @IsOptional()
  @IsNotEmpty()
  name?: string;

  @ApiProperty({ type: Number })
  @IsNotEmpty()
  @IsOptional()
  @IsNumber()
  price?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  thumbnail?: number;

  @ApiProperty()
  @IsOptional()
  @IsEnum(ThumbnailTypes)
  thumbnail_type?: ThumbnailTypes;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  description?: string;

  @ApiProperty()
  @IsBoolean()
  @IsNotEmpty()
  @IsOptional()
  available?: boolean;

  @ApiProperty()
  @IsBoolean()
  @IsNotEmpty()
  @IsOptional()
  is_always_available?: boolean;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  @IsOptional()
  quantity?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @IsNotEmpty()
  minimum_order_quantity?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @IsNotEmpty()
  cost_price?: number;

  @ApiProperty()
  @IsArray()
  @IsOptional()
  images?: string[];

  @ApiProperty()
  @IsArray()
  @IsOptional()
  videos?: ProductVideo[];

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  category?: string;

  @ApiProperty({ type: [String], required: false })
  @IsArray()
  @IsOptional()
  tags?: string[];

  @ApiProperty()
  @IsObject()
  @IsOptional()
  variants?: Item['variants'];

  @ApiProperty()
  @IsBoolean()
  @IsNotEmpty()
  @IsOptional()
  is_featured?: boolean;

  @ApiProperty()
  @IsObject()
  @IsNotEmpty()
  @IsOptional()
  meta?: Item['meta'];

  @ApiProperty()
  @IsDateString()
  @IsNotEmpty()
  @IsOptional()
  expiry_date?: Item['expiry_date'];

  @ApiProperty({ type: [String], required: false })
  @IsArray()
  @IsOptional()
  info_blocks?: string[];

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  tiered_pricing?: string;
}

export class UpdateSortIndexDto {
  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  id?: string;

  @ApiProperty({ type: Number })
  @IsNotEmpty()
  @IsNumber()
  sort_index: number;
}

export class UpdateItemQuantityDto extends PickType(UpdateItemDto, ['is_always_available', 'quantity']) {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty()
  @IsArray()
  @IsOptional()
  variants?: Item['variants'];
}
export class UpdateItemQuantitiesDto {
  @ApiProperty({
    type: [UpdateItemQuantityDto],
  })
  @IsNotEmpty()
  @IsArray()
  items: [UpdateItemQuantityDto];
}
export class CreateItemResponse {
  @ApiProperty()
  message: string;

  @ApiProperty({ type: Item })
  data: Item;
}

export class ItemsPaginatedResponse extends OmitType(PaginatedQueryWithDataDto, ['data']) {
  @ApiProperty({
    type: 'object',
  })
  data: { store: Store; items: Item[] | { featuredItems: Item[]; otherItems: Item[] } };
}

export class DiscountResponse {
  @ApiProperty({ type: Discount })
  data: Discount;
}

export class CreateDiscountDto {
  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  label: string;

  @ApiProperty({ type: Boolean })
  @IsBoolean()
  @IsNotEmpty()
  active: boolean = true;

  @ApiProperty({ type: Number })
  @IsOptional()
  @IsNumber()
  discount_cap?: number;

  @ApiProperty({ type: Number })
  @IsNotEmpty()
  @IsNumber()
  percentage: number;

  @ApiProperty()
  @IsDateString()
  @IsNotEmpty()
  start_date: Discount['start_date'];

  @ApiProperty()
  @IsDateString()
  @IsNotEmpty()
  @IsOptional()
  end_date?: Discount['end_date'];

  @ApiProperty()
  @IsArray()
  @IsNotEmpty()
  items: Discount['items'];
}

export class UpdateDiscountDto {
  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  label?: string;

  @ApiProperty({ type: Number })
  @IsOptional()
  @IsNumber()
  discount_cap?: number;

  @ApiProperty({ type: Number })
  @IsNotEmpty()
  @IsNumber()
  @IsOptional()
  percentage?: number;

  @ApiProperty()
  @IsDateString()
  @IsNotEmpty()
  @IsOptional()
  start_date?: Discount['start_date'];

  @ApiProperty()
  @IsDateString()
  @IsNotEmpty()
  @IsOptional()
  end_date?: Discount['end_date'];

  @ApiProperty()
  @IsArray()
  @IsNotEmpty()
  @IsOptional()
  items?: Discount['items'];
}

export class DeleteDiscountDto {
  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  id: string;
}

export class CreateDiscountResponse extends DiscountResponse {}
export class UpdateDiscountResponse extends DiscountResponse {}
export class DeleteDiscountResponse extends DiscountResponse {}

export class CreateCouponDto {
  @ApiProperty({ type: String, enum: ['percentage', 'fixed'] })
  @IsString()
  @IsNotEmpty()
  type: 'percentage' | 'fixed';

  @ApiProperty({ type: Boolean })
  @IsBoolean()
  @IsNotEmpty()
  active: boolean;

  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  coupon_code: string;

  @ApiProperty({ type: Number })
  @IsNotEmpty()
  @IsNumber()
  @IsOptional()
  discount_cap?: number;

  @ApiProperty({ type: Number })
  @IsNotEmpty()
  @IsNumber()
  @IsOptional()
  discount_amount?: number;

  @ApiProperty({ type: Number })
  @IsNotEmpty()
  @IsNumber()
  @IsOptional()
  percentage?: number;

  @ApiProperty({ type: Number })
  @IsNotEmpty()
  @IsNumber()
  quantity: number;

  @ApiProperty()
  @IsDateString()
  @IsNotEmpty()
  @IsOptional()
  end_date: Coupon['end_date'];

  @ApiProperty({ type: Number })
  @IsOptional()
  @IsNumber()
  minimum_order_amount?: number;

  // @ApiProperty({ type: Boolean })
  // @IsOptional()
  // @IsBoolean()
  // sponsored_by_catlog?: boolean;
}

export class CreateCatlogSponsoredCouponsDto extends CreateCouponDto {
  @IsArray()
  @ArrayNotEmpty()
  @ArrayUnique()
  @IsString({ each: true })
  @Transform((value) => value.map((id: string) => id.trim()))
  stores: string[];
}

export class UpdateCouponDto {
  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({ type: String, enum: ['percentage', 'fixed'] })
  @IsString()
  @IsNotEmpty()
  type: 'percentage' | 'fixed';

  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  coupon_code?: string;

  @ApiProperty({ type: Boolean })
  @IsBoolean()
  @IsNotEmpty()
  @IsOptional()
  active?: boolean;

  @ApiProperty({ type: Number })
  @IsNotEmpty()
  @IsNumber()
  @IsOptional()
  discount_cap?: number;

  @ApiProperty({ type: Number })
  @IsNotEmpty()
  @IsNumber()
  @IsOptional()
  discount_amount?: number;

  @ApiProperty({ type: Number })
  @IsNotEmpty()
  @IsNumber()
  @IsOptional()
  percentage?: number;

  @ApiProperty({ type: Number })
  @IsOptional()
  @IsNumber()
  minimum_order_amount?: number;

  @ApiProperty({ type: Number })
  @IsNotEmpty()
  @IsNumber()
  @IsOptional()
  quantity?: number;

  @ApiProperty()
  @IsDateString()
  @IsNotEmpty()
  @IsOptional()
  end_date?: Coupon['end_date'];
}

export class DeleteCouponDto {
  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  id: string;
}
export class VerifyCouponDto {
  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  coupon_code: string;

  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  store: string;

  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  currency: CURRENCIES;

  @ApiProperty({ type: Number })
  @IsNumber()
  @IsNotEmpty()
  itemsAmount: number;

  @ApiProperty({ type: String })
  @IsOptional()
  @IsString()
  customer?: string;
}
