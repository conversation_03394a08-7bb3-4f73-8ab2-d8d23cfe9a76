import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { CURRENCIES } from '../../modules/country/country.schema';

export class CreateWithdrawalAccountDto {
  @IsString()
  @IsNotEmpty()
  account_number: string;

  @IsString()
  @IsOptional()
  account_name: string;

  @IsString()
  @IsNotEmpty()
  bank_code: string;

  @IsString()
  @IsNotEmpty()
  currency: CURRENCIES;
}

export class WithdrawalRequestDto {
  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  amount: number;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  withdrawal_account: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  wallet: string;
}

export class CompleteWithdrawalRequestDto {
  @IsString()
  @IsNotEmpty()
  code: string;

  @IsString()
  @IsNotEmpty()
  request_id: string;

  @IsString()
  @IsNotEmpty()
  security_pin: string;
}

export class ResolveAccountDto {
  @IsString()
  @IsNotEmpty()
  account_number: string;

  @IsString()
  @IsNotEmpty()
  bank_code: string;

  @IsString()
  @IsNotEmpty()
  currency: CURRENCIES;
}

export class DeleteWithdrawalAccountDto {
  @IsString()
  @IsNotEmpty()
  id: string;
}

//payment statement
export class CreatePaymentStatementDto {
  @ApiProperty({ required: true })
  @IsDateString()
  @IsNotEmpty()
  start_date: Date;

  @ApiProperty({ required: true })
  @IsDateString()
  @IsNotEmpty()
  end_date: Date;

  @IsString()
  @IsNotEmpty()
  currency: CURRENCIES;
}
