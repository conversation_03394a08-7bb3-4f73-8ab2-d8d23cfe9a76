import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { FileTypeEnum } from '../../repositories/s3.repositories';

export class Base64FileUploadDto {
  @ApiProperty()
  @IsString()
  base64: string;

  @ApiProperty()
  @IsString()
  fileName: string;

  @ApiPropertyOptional({ enum: FileTypeEnum, enumName: 'FileTypeEnum' })
  @IsOptional()
  @IsEnum(FileTypeEnum)
  type?: FileTypeEnum;
}

export class FileUploadDto {
  @ApiProperty({ type: 'string', format: 'binary' })
  file: any;

  @ApiPropertyOptional({ enum: FileTypeEnum, enumName: 'FileTypeEnum' })
  @IsOptional()
  @IsEnum(FileTypeEnum)
  type?: FileTypeEnum;
}

export class MenuImageDto {
  @ApiProperty({ type: 'string', format: 'binary' })
  @IsNotEmpty()
  menu_image: any;
}

export class UploadedFileResponse {
  @ApiProperty()
  message: string;

  @ApiProperty({
    type: 'object',
    properties: {
      link: {
        type: 'string',
      },
    },
  })
  data = {
    link: '',
  };
}
