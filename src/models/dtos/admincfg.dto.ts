import { IsB<PERSON>ean, Is<PERSON>num, <PERSON>In, <PERSON><PERSON><PERSON>E<PERSON>y, IsString } from 'class-validator';
import { WITHDRAWAL_PROVIDERS } from '../../modules/wallets/wallet.withdrawal.schema';
import { ADMIN_CONFIG } from '../../modules/adminconfig/adminconfig.schema';

export class AddAdminConfigDto {
  _id: any;

  @IsEnum(ADMIN_CONFIG)
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  value: string;
}
