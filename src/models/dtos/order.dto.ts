import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsDateString, IsEnum, isEnum, IsOptional, IsString } from 'class-validator';
import { DELIVERY_METHODS, ORDER_CHANNELS, ORDER_STATUSES } from '../../modules/orders/order.schema';
import { CUSTOMER_ORDERS_TAG, ORDER_PAID_TAG } from '../../enums/order.enum';

export class OrdersFilterQueryDto {
  @IsOptional()
  @IsString()
  @ApiProperty({ required: false })
  search?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  store?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  from?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  to?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEnum(ORDER_STATUSES)
  status?: ORDER_STATUSES;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEnum(ORDER_PAID_TAG)
  payment_status?: ORDER_PAID_TAG;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  customer?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEnum(DELIVERY_METHODS)
  fulfillment_method?: DELIVERY_METHODS;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEnum(ORDER_CHANNELS)
  channel?: ORDER_CHANNELS;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  products?: string[];
}

export class CustomersFilterQueryDto {
  @IsOptional()
  @IsString()
  @ApiProperty({ required: false })
  search?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  store?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEnum(CUSTOMER_ORDERS_TAG)
  status?: CUSTOMER_ORDERS_TAG;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  from?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  to?: string;
}
