import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsEnum, IsMongoId, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { PLAN_TYPE, PLANS } from '../../enums/plan.enum';
import { PAYMENT_METHODS } from '../../enums/payment.enum';
import { autoImplement } from '../../utils';
import { COUNTRY_CODE, CURRENCIES } from '../../modules/country/country.schema';
import { PLAN_OPTION_VERSION, PlanOption } from '../../modules/plan/plan-options/plan-options.schema';

export class CreatePlanDto {
  @ApiProperty({ example: 'Basic' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ example: 500 })
  @IsNumber()
  @IsNotEmpty()
  amount: number;

  // @ApiProperty({ example: 'This is the month package' })
  // @IsArray()
  // @IsNotEmpty()
  // description: string[];

  @ApiProperty({ example: 'MONTH', enum: ['MONTH', 'YEAR'] })
  @IsNumber()
  interval: number;

  @ApiProperty({ type: 'string' })
  @IsString()
  interval_text: string;

  @ApiProperty({ type: 'string', required: false })
  @IsString()
  @IsOptional()
  hook?: string;

  @ApiProperty({ type: 'string' })
  @IsString()
  type: PLAN_TYPE;

  @ApiProperty({ type: String, enum: [COUNTRY_CODE.GH, COUNTRY_CODE.NG, COUNTRY_CODE.ZA, COUNTRY_CODE.KE] })
  @IsString()
  country: COUNTRY_CODE;

  @ApiProperty({ type: 'number' })
  @IsNumber()
  @IsOptional()
  discount: number;

  @ApiProperty({ type: [PlanOption] })
  @IsArray()
  options: PlanOption[];

  @ApiProperty({ type: Boolean })
  @IsBoolean()
  is_paid_plan: boolean;

  // @ApiProperty({
  //   type: 'array',
  //   items: {
  //     type: 'object',
  //     properties: {
  //       code: {
  //         type: 'string',
  //       },
  //       type: {
  //         type: 'string',
  //         enum: [
  //           PAYMENT_METHODS.PAYSTACK,
  //           PAYMENT_METHODS.MONNIFY_TRANSFER,
  //           PAYMENT_METHODS.TRF_MOMO,
  //           PAYMENT_METHODS.ZILLA,
  //         ],
  //       },
  //       total_amount: {
  //         type: 'number',
  //       },
  //       percent: {
  //         type: 'number',
  //       },
  //     },
  //   },
  // })
  // @IsArray()
  // methods: {
  //   code: string;
  //   type: PAYMENT_METHODS;
  //   total_amount: number;
  //   percent: number;
  // }[];
}

export class UpdatePlanDto {
  @ApiProperty({ example: 'Basic' })
  @IsString()
  @IsOptional()
  name: string;

  @ApiProperty({ example: 500 })
  @IsNumber()
  @IsOptional()
  amount: number;

  @ApiProperty({ example: 'This is the month package' })
  @IsArray()
  @IsOptional()
  description: string[];

  @ApiProperty({ example: 'MONTH', enum: ['MONTH', 'YEAR'] })
  @IsNumber()
  @IsOptional()
  interval: number;

  @ApiProperty({ type: 'string' })
  @IsString()
  @IsOptional()
  interval_text: string;

  @ApiProperty({ type: 'string' })
  @IsString()
  @IsOptional()
  type: PLAN_TYPE;

  @ApiProperty({ type: String, enum: [COUNTRY_CODE.GH, COUNTRY_CODE.NG, COUNTRY_CODE.KE, COUNTRY_CODE.ZA] })
  @IsString()
  @IsOptional()
  country: COUNTRY_CODE;

  @ApiProperty({ type: 'number' })
  @IsNumber()
  @IsOptional()
  discount: number;

  // @ApiProperty({
  //   type: 'array',
  //   items: {
  //     type: 'object',
  //     properties: {
  //       code: {
  //         type: 'string',
  //       },
  //       type: {
  //         type: 'string',
  //         enum: [
  //           PAYMENT_METHODS.PAYSTACK,
  //           PAYMENT_METHODS.TRANSFER,
  //           PAYMENT_METHODS.TRF_MOMO,
  //           PAYMENT_METHODS.ZILLA,
  //         ],
  //       },
  //       total_amount: {
  //         type: 'number',
  //       },
  //       percent: {
  //         type: 'number',
  //       },
  //     },
  //   },
  // })
  // @IsOptional()
  // @IsArray()
  // methods: {
  //   code: string;
  //   type: PAYMENT_METHODS;
  //   total_amount: number;
  //   percent: number;
  // }[];
}

export class PlanDataDto extends autoImplement<Partial<CreatePlanDto>>() {
  @IsString()
  @ApiProperty({ description: 'id of the plan' })
  id: string;
}

export class ChangePlanDto {
  @IsString()
  @ApiProperty()
  @IsMongoId()
  @IsNotEmpty()
  plan: string;
}

export class CancelSubscriptionToggleDto {
  @IsBoolean()
  @ApiProperty()
  @IsNotEmpty()
  cancel_at_period_end: boolean;
}

export class CreatePlanOptionDto {
  @ApiProperty()
  @IsNumber()
  interval: number;

  @ApiProperty()
  @IsString()
  interval_text: string;

  @ApiProperty({ type: String, enum: [COUNTRY_CODE.GH, COUNTRY_CODE.NG, COUNTRY_CODE.KE, COUNTRY_CODE.ZA] })
  @IsString()
  country: COUNTRY_CODE;

  @ApiProperty({ type: String, enum: [CURRENCIES.NGN, CURRENCIES.ZAR, CURRENCIES.KES, CURRENCIES.GHC] })
  @IsEnum(CURRENCIES)
  currency: CURRENCIES;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  amount: number;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  actual_amount: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  discount?: number;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  price_per_token: number;

  @ApiProperty({ default: 0 })
  @IsNumber()
  @IsOptional()
  chowbot_tokens: number;

  @ApiProperty({ type: 'string', enum: Object.values(PLANS.TYPES) })
  @IsString()
  @IsNotEmpty()
  plan_type: PLAN_TYPE;

  @ApiProperty({ type: 'string', enum: PLAN_OPTION_VERSION })
  @IsString()
  @IsOptional()
  version: PLAN_OPTION_VERSION;
}
