import { Prop } from '@nestjs/mongoose';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  isBoolean,
  IsDate,
  IsDateString,
  IsEnum,
  IsInstance,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  MinLength,
  ValidateNested,
} from 'class-validator';
import { FEE_TYPES } from '../../enums/order.enum';
import { CURRENCIES } from '../../modules/country/country.schema';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class InvoicePaymentDto {
  @IsString()
  @IsNotEmpty()
  reference: string;
}
export class InvoiceFee {
  @IsNumber()
  amount: number;

  @Prop({ enum: [FEE_TYPES.DELIVERY, FEE_TYPES.DISCOUNT, FEE_TYPES.VAT, FEE_TYPES.OTHERS, FEE_TYPES.PAYMENT] })
  type: FEE_TYPES;
}

export class InvoiceItem {
  @IsString()
  id: string;

  @IsString()
  name: string;

  @IsNumber()
  price: number;

  @IsNumber()
  quantity: number;
}

export class SendInvoiceDto {
  @IsString()
  @IsNotEmpty()
  meassage_subject: string;

  @IsString()
  @IsNotEmpty()
  meassage_body: string;

  @IsBoolean()
  attach_pdf: boolean;
}

export class InvoiceCreateDto {
  @IsString()
  title: string;

  @IsString()
  store: string;

  @IsString()
  customer: string;

  @IsString()
  @IsOptional()
  store_logo?: string;

  @IsString()
  @IsOptional()
  store_address?: string;

  @IsString()
  @IsNotEmpty()
  currency?: CURRENCIES;

  @IsDateString()
  date_created: Date;

  @IsDateString()
  date_due: Date;

  @IsArray()
  @Type(() => InvoiceItem)
  @ArrayMinSize(1, { message: 'Please add at least one item' })
  @ValidateNested({ each: true })
  items: {
    id: string;
    name: string;
    price: number;
    quantity: number;
  }[];

  @IsArray()
  @Type(() => InvoiceFee)
  @ValidateNested({ each: true, message: 'Please add a valid fee' })
  @IsOptional()
  fees: {
    amount: number;
    type: FEE_TYPES;
    label?: String;
  }[];
}

export class InvoiceDraftDto {
  @IsString()
  title: string;

  @IsString()
  store: string;

  @IsString()
  @IsOptional()
  customer?: string;

  @IsString()
  @IsOptional()
  store_logo?: string;

  @IsString()
  @IsOptional()
  store_address?: string;

  @IsString()
  @IsNotEmpty()
  currency?: CURRENCIES;

  @IsDateString()
  @IsOptional()
  date_created?: Date;

  @IsDateString()
  @IsOptional()
  date_due?: Date;

  @IsArray()
  @IsOptional()
  @Type(() => InvoiceItem)
  @ValidateNested({ each: true })
  items?: {
    id: string;
    name: string;
    price: number;
    quantity: number;
  }[];

  @IsArray()
  @IsOptional()
  @Type(() => InvoiceFee)
  @ValidateNested({ each: true })
  fees?: {
    amount: number;
    type: FEE_TYPES;
    label?: String;
  }[];
}

export class SimpleInvoiceCreateDto {
  @ApiProperty({ description: 'Customer ID', example: '60e6b3c7c60e9b002b7d82aa' })
  @IsString()
  @IsNotEmpty()
  customer: string;

  @ApiProperty({ description: 'Narration for the invoice', example: 'Payment for consultation services' })
  @IsString()
  @IsNotEmpty()
  narration: string;

  @ApiProperty({ description: 'Total amount for the invoice', example: 1500 })
  @IsNumber()
  @IsNotEmpty()
  amount: number;

  @ApiProperty({ description: 'Currency for the invoice', enum: CURRENCIES, example: CURRENCIES.NGN })
  @IsEnum(CURRENCIES)
  @IsNotEmpty()
  currency: CURRENCIES;

  @ApiProperty({ description: 'Store ID', example: '60e6b3c7c60e9b002b7d82aa' })
  @IsOptional()
  @IsString()
  store?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  store_logo?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  store_address?: string;
}
