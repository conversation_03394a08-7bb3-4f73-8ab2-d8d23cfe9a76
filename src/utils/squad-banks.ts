export const squadBanks = [
  {
    name: 'Sterling Bank',
    code: '000001',
    nipCode: '232',
  },
  {
    name: 'Keystone Bank',
    code: '000002',
    nipCode: '082',
  },
  {
    name: 'FCMB',
    code: '000003',
    nipCode: '214',
  },
  {
    name: 'United Bank for Africa',
    code: '000004',
    nipCode: '033',
  },
  {
    name: 'Diamond Bank',
    code: '000005',
    nipCode: '063',
  },
  {
    name: 'JAIZ Bank',
    code: '000006',
    nipCode: '301',
  },
  {
    name: 'Fidelity Bank',
    code: '000007',
    nipCode: '070',
  },
  {
    name: 'Polaris Bank',
    code: '000008',
    nipCode: '076',
  },
  {
    name: 'Citi Bank',
    code: '000009',
    nipCode: '023',
  },
  {
    name: 'Ecobank Bank',
    code: '000010',
    nipCode: '050',
  },
  {
    name: 'Unity Bank',
    code: '000011',
    nipCode: '215',
  },
  {
    name: 'StanbicIBTC Bank',
    code: '000012',
    nipCode: '221',
  },
  {
    name: 'GTBank Plc',
    code: '000013',
    nipCode: '058',
  },
  {
    name: 'Access Bank',
    code: '000014',
    nipCode: '044',
  },
  {
    name: 'Zenith Bank Plc',
    code: '000015',
    nipCode: '057',
  },
  {
    name: 'First Bank of Nigeria',
    code: '000016',
    nipCode: '011',
  },
  {
    name: 'Wema Bank',
    code: '000017',
    nipCode: '035',
  },
  {
    name: 'Union Bank',
    code: '000018',
    nipCode: '032',
  },
  {
    name: 'Enterprise Bank',
    code: '000019',
  },
  {
    name: 'Heritage',
    code: '000020',
  },
  {
    name: 'Standard Chartered',
    code: '000021',
    nipCode: '068',
  },
  {
    name: 'Suntrust Bank',
    code: '000022',
    nipCode: '100',
  },
  {
    name: 'Providus Bank',
    code: '000023',
    nipCode: '101',
  },
  {
    name: 'Rand Merchant Bank',
    code: '000024',
    nipCode: '502',
  },
  {
    name: 'Titan Trust Bank',
    code: '000025',
    nipCode: '102',
  },
  {
    name: 'Taj Bank',
    code: '000026',
    nipCode: '302',
  },
  {
    name: 'Globus Bank',
    code: '000027',
    nipCode: '00103',
  },
  {
    name: 'Central Bank of Nigeria',
    code: '000028',
  },
  {
    name: 'Lotus Bank',
    code: '000029',
    nipCode: '303',
  },
  {
    name: 'Premium Trust Bank',
    code: '000031',
    nipCode: '105',
  },
  {
    name: 'eNaira',
    code: '000033',
  },
  {
    name: 'Signature Bank',
    code: '000034',
    nipCode: '106',
  },
  {
    name: 'Optimus Bank',
    code: '000036',
    nipCode: '107',
  },
  {
    name: 'FEWCHORE FINANCE COMPANY LIMITED',
    code: '050002',
  },
  {
    name: 'SageGrey Finance Limited',
    code: '050003',
    nipCode: '40165',
  },
  {
    name: 'AAA Finance',
    code: '050005',
  },
  {
    name: 'Branch International Financial Services',
    code: '050006',
    nipCode: 'FC40163',
  },
  {
    name: 'Tekla Finance Limited',
    code: '050007',
  },
  {
    name: 'Fast Credit',
    code: '050009',
  },
  {
    name: 'Fundquest Financial Services Limited',
    code: '050010',
  },
  {
    name: 'Enco Finance',
    code: '050012',
  },
  {
    name: 'Dignity Finance',
    code: '050013',
  },
  {
    name: 'Trinity Financial Services Limited',
    code: '050013',
  },
  {
    name: 'FSDH Merchant Bank',
    code: '400001',
    nipCode: '501',
  },
  {
    name: 'Coronation Merchant Bank',
    code: '060001',
    nipCode: '559',
  },
  {
    name: 'FBNQUEST Merchant Bank',
    code: '060002',
  },
  {
    name: 'Nova Merchant Bank',
    code: '060003',
  },
  {
    name: 'Greenwich Merchant Bank',
    code: '060004',
    nipCode: '562',
  },
  {
    name: 'Omoluabi savings and loans',
    code: '070007',
  },
  {
    name: 'ASOSavings & Loans',
    code: '090001',
    nipCode: '401',
  },
  {
    name: 'Trustbond Mortgage Bank',
    code: '090005',
  },
  {
    name: 'SafeTrust',
    code: '090006',
  },
  {
    name: 'FBN Mortgages Limited',
    code: '090107',
  },
  {
    name: 'Imperial Homes Mortgage Bank',
    code: '100024',
    nipCode: '415',
  },
  {
    name: 'AG Mortgage Bank',
    code: '100028',
    nipCode: '90077',
  },
  {
    name: 'Gateway Mortgage Bank',
    code: '070009',
    nipCode: '812',
  },
  {
    name: 'Abbey Mortgage Bank',
    code: '070010',
    nipCode: '404',
  },
  {
    name: 'Refuge Mortgage Bank',
    code: '070011',
    nipCode: '90067',
  },
  {
    name: 'Lagos Building Investment Company',
    code: '070012',
    nipCode: '90052',
  },
  {
    name: 'Platinum Mortgage Bank',
    code: '070013',
    nipCode: '268',
  },
  {
    name: 'First Generation Mortgage Bank',
    code: '070014',
  },
  {
    name: 'Brent Mortgage Bank',
    code: '070015',
  },
  {
    name: 'Infinity Trust Mortgage Bank',
    code: '070016',
  },
  {
    name: 'MayFresh Mortgage Bank',
    code: '070019',
  },
  {
    name: 'Jubilee-Life Mortgage Bank',
    code: '090003',
  },
  {
    name: 'Haggai Mortgage Bank Limited',
    code: '070017',
  },
  {
    name: 'Coop Mortgage Bank',
    code: '070021',
  },
  {
    name: 'Delta Trust Microfinance Bank',
    code: '070023',
  },
  {
    name: 'Homebase Mortgage Bank',
    code: '070024',
  },
  {
    name: 'Akwa Savings & Loans Limited',
    code: '070025',
  },
  {
    name: 'FHA Mortgage Bank',
    code: '070026',
  },
  {
    name: 'New Prudential Bank',
    code: '090108',
  },
  {
    name: 'NPF MicroFinance Bank',
    code: '070001',
    nipCode: '50629',
  },
  {
    name: 'Fortis Microfinance Bank',
    code: '070002',
  },
  {
    name: 'Covenant MFB',
    code: '070006',
  },
  {
    name: 'Page Financials',
    code: '070008',
  },
  {
    name: 'Parralex Microfinance bank',
    code: '090004',
  },
  {
    name: 'Ekondo MFB',
    code: '090097',
    nipCode: '098',
  },
  {
    name: 'VFD MFB',
    code: '090110',
    nipCode: '566',
  },
  {
    name: 'FinaTrust Microfinance Bank',
    code: '090111',
  },
  {
    name: 'Seed Capital Microfinance Bank',
    code: '090112',
  },
  {
    name: 'Empire trust MFB',
    code: '090114',
  },
  {
    name: 'TCF MFB',
    code: '090115',
  },
  {
    name: 'AMML MFB',
    code: '090116',
  },
  {
    name: 'Boctrust Microfinance Bank',
    code: '090117',
  },
  {
    name: 'IBILE Microfinance Bank',
    code: '090118',
    nipCode: '51244',
  },
  {
    name: 'Ohafia Microfinance Bank',
    code: '090119',
  },
  {
    name: 'Wetland Microfinance Bank',
    code: '090120',
  },
  {
    name: 'Hasal Microfinance Bank',
    code: '090121',
    nipCode: '50383',
  },
  {
    name: 'Gowans Microfinance Bank',
    code: '090122',
  },
  {
    name: 'Verite Microfinance Bank',
    code: '090123',
  },
  {
    name: 'Xslnce Microfinance Bank',
    code: '090124',
  },
  {
    name: 'Regent Microfinance Bank',
    code: '090125',
  },
  {
    name: 'Fidfund Microfinance Bank',
    code: '090126',
  },
  {
    name: 'BC Kash Microfinance Bank',
    code: '090127',
  },
  {
    name: 'Ndiorah Microfinance Bank',
    code: '090128',
  },
  {
    name: 'Money Trust Microfinance Bank',
    code: '090129',
  },
  {
    name: 'Consumer Microfinance Bank',
    code: '090130',
    nipCode: '50910',
  },
  {
    name: 'Allworkers Microfinance Bank',
    code: '090131',
  },
  {
    name: 'Richway Microfinance Bank',
    code: '090132',
  },
  {
    name: 'AL-Barakah Microfinance Bank',
    code: '090133',
  },
  {
    name: 'Accion Microfinance Bank',
    code: '090134',
    nipCode: '602',
  },
  {
    name: 'Personal Trust Microfinance Bank',
    code: '090135',
    nipCode: '51146',
  },
  {
    name: 'Microcred Microfinance Bank',
    code: '090136',
  },
  {
    name: 'PecanTrust Microfinance Bank',
    code: '090137',
    nipCode: '51226',
  },
  {
    name: 'Royal Exchange Microfinance Bank',
    code: '090138',
  },
  {
    name: 'Visa Microfinance Bank',
    code: '090139',
  },
  {
    name: 'Sagamu Microfinance Bank',
    code: '090140',
  },
  {
    name: 'Chikum Microfinance Bank',
    code: '090141',
    nipCode: '312',
  },
  {
    name: 'Yes Microfinance Bank',
    code: '090142',
  },
  {
    name: 'Apeks Microfinance Bank',
    code: '090143',
  },
  {
    name: 'CIT Microfinance Bank',
    code: '090144',
  },
  {
    name: 'Fullrange Microfinance Bank',
    code: '090145',
  },
  {
    name: 'Trident Microfinance Bank',
    code: '090146',
  },
  {
    name: 'Hackman Microfinance Bank',
    code: '090147',
    nipCode: '51251',
  },
  {
    name: 'Bowen Microfinance Bank',
    code: '090148',
    nipCode: '50931',
  },
  {
    name: 'IRL Microfinance Bank',
    code: '090149',
  },
  {
    name: 'Virtue Microfinance Bank',
    code: '090150',
  },
  {
    name: 'Mutual Trust Microfinance Bank',
    code: '090151',
  },
  {
    name: 'Nagarta Microfinance Bank',
    code: '090152',
  },
  {
    name: 'FFS Microfinance Bank',
    code: '090153',
  },
  {
    name: 'CEMCS Microfinance Bank',
    code: '090154',
    nipCode: '50823',
  },
  {
    name: 'Advans La Fayette Microfinance Bank',
    code: '090155',
  },
  {
    name: 'e-Barcs Microfinance Bank',
    code: '090156',
  },
  {
    name: 'Infinity Microfinance Bank',
    code: '090157',
  },
  {
    name: 'Futo Microfinance Bank',
    code: '090158',
  },
  {
    name: 'Credit Afrique Microfinance Bank',
    code: '090159',
  },
  {
    name: 'Addosser Microfinance Bank',
    code: '090160',
  },
  {
    name: 'Okpoga Microfinance Bank',
    code: '090161',
  },
  {
    name: 'Stanford Microfinance Bank',
    code: '090162',
    nipCode: '090162',
  },
  {
    name: 'First Royal Microfinance Bank',
    code: '090164',
    nipCode: '090164',
  },
  {
    name: 'Petra Microfinance Bank',
    code: '090165',
    nipCode: '50746',
  },
  {
    name: 'Eso-E Microfinance Bank',
    code: '090166',
  },
  {
    name: 'Daylight Microfinance Bank',
    code: '090167',
  },
  {
    name: 'Gashua Microfinance Bank',
    code: '090168',
  },
  {
    name: 'Alpha Kapital Microfinance Bank',
    code: '090169',
  },
  {
    name: 'Mainstreet Microfinance Bank',
    code: '090171',
    nipCode: '090171',
  },
  {
    name: 'Astrapolaris Microfinance Bank',
    code: '090172',
    nipCode: 'MFB50094',
  },
  {
    name: 'Reliance Microfinance Bank',
    code: '090173',
  },
  {
    name: 'Malachy Microfinance Bank',
    code: '090174',
  },
  {
    name: 'HighStreet Microfinance Bank',
    code: '090175',
  },
  {
    name: 'Bosak Microfinance Bank',
    code: '090176',
  },
  {
    name: 'Lapo Microfinance Bank',
    code: '090177',
  },
  {
    name: 'GreenBank Microfinance Bank',
    code: '090178',
  },
  {
    name: 'FAST Microfinance Bank',
    code: '090179',
  },
  {
    name: 'Amju Unique Microfinance Bank',
    code: '090180',
    nipCode: '50926',
  },
  {
    name: 'Baines Credit Microfinance Bank',
    code: '090188',
    nipCode: '51229',
  },
  {
    name: 'Esan Microfinance Bank',
    code: '090189',
  },
  {
    name: 'Mutual Benefits Microfinance Bank',
    code: '090190',
    nipCode: '090190',
  },
  {
    name: 'KCMB Microfinance Bank',
    code: '090191',
  },
  {
    name: 'Midland Microfinance Bank',
    code: '090192',
  },
  {
    name: 'Unical Microfinance Bank',
    code: '090193',
    nipCode: '50871',
  },
  {
    name: 'NIRSAL Microfinance Bank',
    code: '090194',
  },
  {
    name: 'Grooming Microfinance Bank',
    code: '090195',
    nipCode: '51276',
  },
  {
    name: 'Pennywise Microfinance Bank',
    code: '090196',
  },
  {
    name: 'ABU Microfinance Bank',
    code: '090197',
  },
  {
    name: 'RenMoney Microfinance Bank',
    code: '090198',
  },
  {
    name: 'New Dawn Microfinance Bank',
    code: '090205',
  },
  {
    name: 'UNN MFB',
    code: '090251',
  },
  {
    name: 'Yobe Microfinance Bank',
    code: '090252',
  },
  {
    name: 'Coalcamp Microfinance Bank',
    code: '090254',
  },
  {
    name: 'Imo State Microfinance Bank',
    code: '090258',
  },
  {
    name: 'Alekun Microfinance Bank',
    code: '090259',
  },
  {
    name: 'Above Only Microfinance Bank',
    code: '090260',
    nipCode: '51204',
  },
  {
    name: 'Quickfund Microfinance Bank',
    code: '090261',
    nipCode: '51293',
  },
  {
    name: 'Stellas Microfinance Bank',
    code: '090262',
    nipCode: '51253',
  },
  {
    name: 'Navy Microfinance Bank',
    code: '090263',
  },
  {
    name: 'Auchi Microfinance Bank',
    code: '090264',
  },
  {
    name: 'Lovonus Microfinance Bank',
    code: '090265',
  },
  {
    name: 'Uniben Microfinance Bank',
    code: '090266',
  },
  {
    name: 'Kuda Microfinance Bank',
    code: '090267',
    nipCode: '50211',
  },
  {
    name: 'Adeyemi College Staff Microfinance Bank',
    code: '090268',
  },
  {
    name: 'Greenville Microfinance Bank',
    code: '090269',
  },
  {
    name: 'AB Microfinance Bank',
    code: '090270',
  },
  {
    name: 'Lavender Microfinance Bank',
    code: '090271',
  },
  {
    name: 'Olabisi Onabanjo University Microfinance Bank',
    code: '090272',
  },
  {
    name: 'Emeralds Microfinance Bank',
    code: '090273',
  },
  {
    name: 'Prestige Microfinance Bank',
    code: '090274',
  },
  {
    name: 'Trustfund Microfinance Bank',
    code: '090276',
  },
  {
    name: 'Al-Hayat Microfinance Bank',
    code: '090277',
  },
  {
    name: 'Glory Microfinance Bank',
    code: '090278',
  },
  {
    name: 'Ikire Microfinance Bank',
    code: '090279',
  },
  {
    name: 'Megapraise Microfinance Bank',
    code: '090280',
  },
  {
    name: 'MintFinex Microfinance Bank',
    code: '090281',
  },
  {
    name: 'Arise Microfinance Bank',
    code: '090282',
  },
  {
    name: 'Nnew Women Microfinance Bank',
    code: '090283',
  },
  {
    name: 'First Option Microfinance Bank',
    code: '090285',
  },
  {
    name: 'Safe Haven Microfinance Bank',
    code: '090286',
    nipCode: '51113',
  },
  {
    name: 'AssetMatrix Microfinance Bank',
    code: '090287',
  },
  {
    name: 'Pillar Microfinance Bank',
    code: '090289',
  },
  {
    name: 'FCT Microfinance Bank',
    code: '090290',
  },
  {
    name: 'Halal Credit Microfinance Bank',
    code: '090291',
  },
  {
    name: 'Afekhafe Microfinance Bank',
    code: '090292',
  },
  {
    name: 'Brethren Microfinance Bank',
    code: '090293',
  },
  {
    name: 'Eagle Flight Microfinance Bank',
    code: '090294',
  },
  {
    name: 'Omiye Microfinance Bank',
    code: '090295',
  },
  {
    name: 'Polyunwana Microfinance Bank',
    code: '090296',
    nipCode: '50864',
  },
  {
    name: 'Alert Microfinance Bank',
    code: '090297',
  },
  {
    name: 'FedPoly Nasarawa Microfinance Bank',
    code: '090298',
  },
  {
    name: 'Kontagora Microfinance Bank',
    code: '090299',
  },
  {
    name: 'Purplemoney Microfinance Bank',
    code: '090303',
  },
  {
    name: 'Evangel Microfinance Bank',
    code: '090304',
  },
  {
    name: 'Sulspap Microfinance Bank',
    code: '090305',
  },
  {
    name: 'Aramoko Microfinance Bank',
    code: '090307',
    nipCode: '50083',
  },
  {
    name: 'Brightway Microfinance Bank',
    code: '090308',
  },
  {
    name: 'EdFin Microfinance Bank',
    code: '090310',
  },
  {
    name: 'U & C Microfinance Bank',
    code: '090315',
    nipCode: '50840',
  },
  {
    name: 'PatrickGold Microfinance Bank',
    code: '090317',
  },
  {
    name: 'Federal University Dutse Microfinance Bank',
    code: '090318',
  },
  {
    name: 'KadPoly Microfinance Bank',
    code: '090320',
    nipCode: '50502',
  },
  {
    name: 'MayFair Microfinance Bank',
    code: '090321',
    nipCode: '50563',
  },
  {
    name: 'Rephidim Microfinance Bank',
    code: '090322',
    nipCode: '50994',
  },
  {
    name: 'Mainland Microfinance Bank',
    code: '090323',
  },
  {
    name: 'Ikenne Microfinance Bank',
    code: '090324',
  },
  {
    name: 'Sparkle',
    code: '090325',
    nipCode: '51310',
  },
  {
    name: 'Balogun Gambari Microfinance Bank',
    code: '090326',
  },
  {
    name: 'Trust Microfinance Bank',
    code: '090327',
  },
  {
    name: 'Eyowo',
    code: '090328',
    nipCode: '50126',
  },
  {
    name: 'Neptune Microfinance Bank',
    code: '090329',
  },
  {
    name: 'UNAAB Microfinance Bank',
    code: '090331',
    nipCode: '50870',
  },
  {
    name: 'Evergreen Microfinance Bank',
    code: '090332',
  },
  {
    name: 'Oche Microfinance Bank',
    code: '090333',
  },
  {
    name: 'Iyeru Okin Microfinance Bank',
    code: '090337',
  },
  {
    name: 'Jessefield Microfinance Bank',
    code: '090352',
  },
  {
    name: 'BIPC Microfinance Bank',
    code: '090336',
  },
  {
    name: 'OAU Microfinance Bank',
    code: '090345',
  },
  {
    name: 'Nassarawa Microfinance Bank',
    code: '090349',
  },
  {
    name: 'CashConnect Microfinance Bank',
    code: '090360',
  },
  {
    name: 'Molusi Microfinance Bank',
    code: '090362',
  },
  {
    name: 'Headway Microfinance Bank',
    code: '090363',
  },
  {
    name: 'Nuture Microfinance Bank',
    code: '090364',
  },
  {
    name: 'Corestep Microfinance Bank',
    code: '090365',
    nipCode: '50204',
  },
  {
    name: 'Firmus Microfinance Bank',
    code: '090366',
    nipCode: '51314',
  },
  {
    name: 'Seedvest Microfinance Bank',
    code: '090369',
  },
  {
    name: 'Ilisan Microfinance Bank',
    code: '090370',
  },
  {
    name: 'Legend Microfinance Bank',
    code: '090372',
  },
  {
    name: 'Think Finance Microfinance Bank',
    code: '090373',
  },
  {
    name: 'Coastline Microfinance Bank',
    code: '090374',
  },
  {
    name: 'Apple Microfinance Bank',
    code: '090376',
  },
  {
    name: 'Isaleoyo Microfinance Bank',
    code: '090377',
  },
  {
    name: 'New Golden Pastures Microfinance Bank',
    code: '090378',
  },
  {
    name: 'GTI Microfinance Bank',
    code: '090385',
  },
  {
    name: 'Interland Microfinance Bank',
    code: '090386',
  },
  {
    name: 'EK-Reliable Microfinance Bank',
    code: '090389',
  },
  {
    name: 'Davodani Microfinance Bank',
    code: '090391',
  },
  {
    name: 'Conpro Microfinance Bank',
    code: '090380',
  },
  {
    name: 'Bridgeway Microfinance Bank',
    code: '090393',
  },
  {
    name: 'Amac Microfinance Bank',
    code: '090394',
  },
  {
    name: 'Borgu Microfinance Bank',
    code: '090395',
  },
  {
    name: 'Oscotech Microfinance Bank',
    code: '090396',
  },
  {
    name: 'Nwannegadi Microfinance Bank',
    code: '090399',
  },
  {
    name: 'Federal Polytechnic Nekede Microfinance Bank',
    code: '090398',
  },
  {
    name: 'Shepherd Trust Microfinance Bank',
    code: '090401',
  },
  {
    name: 'UDA Microfinance Bank',
    code: '090403',
  },
  {
    name: 'Olowolagba Microfinance Bank',
    code: '090404',
  },
  {
    name: 'MONIE POINT (Rolez Microfinance Bank)',
    code: '090405',
    nipCode: '50515',
  },
  {
    name: 'Business Support Microfinance Bank',
    code: '090406',
  },
  {
    name: 'FCMB BETA',
    code: '090409',
  },
  {
    name: 'GMB Microfinance Bank',
    code: '090408',
  },
  {
    name: 'Maritime Microfinance Bank',
    code: '090410',
  },
  {
    name: 'Giginya Microfinance bank',
    code: '090411',
  },
  {
    name: 'Preeminent Microfinance Bank',
    code: '090412',
  },
  {
    name: 'BOI Microfinance Bank',
    code: '090444',
  },
  {
    name: 'Moyofade Microfinance Bank',
    code: '090448',
  },
  {
    name: 'Mkobo Microfinance Bank',
    code: '090455',
  },
  {
    name: 'Rehoboth Microfinance Bank',
    code: '090463',
    nipCode: '50761',
  },
  {
    name: 'Unimaid Microfinance Bank',
    code: '090464',
  },
  {
    name: 'OLOFIN OWENA Microfinance Bank',
    code: '090468',
  },
  {
    name: 'Assets Microfinance Bank',
    code: '090473',
  },
  {
    name: 'Oluchukwu Microfinance Bank',
    code: '090471',
  },
  {
    name: 'Maintrust Microfinance Bank',
    code: '090465',
  },
  {
    name: 'Aniocha Microfinance bank',
    code: '090469',
  },
  {
    name: 'Caretaker Microfinance Bank',
    code: '090472',
  },
  {
    name: 'Giant Stride Microfinance Bank',
    code: '090475',
  },
  {
    name: 'Balogun Fulani Microfinance Bank',
    code: '090181',
  },
  {
    name: 'Verdant Microfinance Bank',
    code: '090474',
  },
  {
    name: 'Changan RTS Microfinance Bank',
    code: '090470',
  },
  {
    name: 'Anchorage Microfinance Bank',
    code: '090476',
  },
  {
    name: 'Light MFB',
    code: '090477',
  },
  {
    name: 'Cintrust Microfinance Bank',
    code: '090480',
  },
  {
    name: 'Fedeth Microfinance Bank',
    code: '090482',
    nipCode: '50298',
  },
  {
    name: 'Ada Microfinance Bank',
    code: '090483',
  },
  {
    name: 'Ibu-Aje Microfinance Bank',
    code: '090488',
  },
  {
    name: 'Alvana Microfinance Bank',
    code: '090489',
  },
  {
    name: 'Chukwunenye MFB',
    code: '090490',
  },
  {
    name: 'Nsuk MFB',
    code: '090491',
  },
  {
    name: 'Oraukwu MFB',
    code: '090492',
  },
  {
    name: 'Boji MFB',
    code: '090494',
  },
  {
    name: 'Goodnews Microfinance Bank',
    code: '090495',
    nipCode: '50739',
  },
  {
    name: 'Randalpha Microfinance Bank',
    code: '090496',
    nipCode: '090496',
  },
  {
    name: 'Pristine Divitis Microfinance Bank',
    code: '090499',
  },
  {
    name: 'Shalom Microfinance Bank',
    code: '090502',
  },
  {
    name: 'Projects Microfinance Bank',
    code: '090503',
  },
  {
    name: 'Zikora Microfinance Bank',
    code: '090504',
  },
  {
    name: 'Nigerian Prisons Microfinance Bank',
    code: '090505',
  },
  {
    name: 'Solid Allianze MFB',
    code: '090506',
    nipCode: '51062',
  },
  {
    name: 'FIMS MFB',
    code: '090507',
  },
  {
    name: 'SEAP Microfinance Bank',
    code: '090513',
  },
  {
    name: 'RIMA Growth Pathway Microfinance Bank',
    code: '090515',
  },
  {
    name: 'Aniocha Microfinance bank',
    code: '090469',
  },
  {
    name: 'Numo Microfinance Bank',
    code: '090516',
  },
  {
    name: 'Uhuru Microfinance Bank',
    code: '090517',
    nipCode: '51322',
  },
  {
    name: 'Afemai Microfinance Bank',
    code: '090518',
  },
  {
    name: 'Iboma Fadama Microfinance Bank',
    code: '090519',
  },
  {
    name: 'Chase Microfinance Bank',
    code: '090523',
  },
  {
    name: 'Solidrock microfinance Bank',
    code: '090524',
  },
  {
    name: 'TripleA Microfinance Bank',
    code: '090525',
  },
  {
    name: 'Crescent Microfinance Bank',
    code: '090526',
    nipCode: '51297',
  },
  {
    name: 'Ojokoro Microfinance Bank',
    code: '090527',
  },
  {
    name: 'Mgbidi Microfinance Bank',
    code: '090528',
  },
  {
    name: 'Ampersand Microfinance Bank',
    code: '090529',
    nipCode: '51341',
  },
  {
    name: 'Confidence MFB',
    code: '090530',
  },
  {
    name: 'Aku Microfinance Bank',
    code: '090531',
    nipCode: '51336',
  },
  {
    name: 'Polybadan Microfinance Bank',
    code: '090534',
  },
  {
    name: 'Ikoyi-Osun Microfinance Bank',
    code: '090536',
    nipCode: '50439',
  },
  {
    name: 'Lobrem Microfinance Bank',
    code: '090537',
  },
  {
    name: 'BluePrint Investments Microfinance Bank',
    code: '090538',
  },
  {
    name: 'Enrich Microfinance Bank',
    code: '090539',
  },
  {
    name: 'Aztec Microfinance Bank',
    code: '090540',
  },
  {
    name: 'Excellent Microfinance Bank',
    code: '090541',
  },
  {
    name: 'Otuo Microfinance Bank',
    code: '090542',
  },
  {
    name: 'Iwoama Microfinance Bank',
    code: '090543',
  },
  {
    name: 'Aspire Microfinance Bank',
    code: '090544',
  },
  {
    name: 'Abulesoro Microfinance Bank',
    code: '090545',
    nipCode: '51312',
  },
  {
    name: 'Ijebu-Ife Microfinance Bank',
    code: '090546',
  },
  {
    name: 'Rockshield Microfinance Bank',
    code: '090547',
    nipCode: '50767',
  },
  {
    name: 'Ally Microfinance Bank',
    code: '090548',
  },
  {
    name: 'KC Microfinance Bank',
    code: '090549',
  },
  {
    name: 'Green Energy Microfinance Bank',
    code: '090550',
  },
  {
    name: 'FairMoney Microfinance Bank',
    code: '090551',
    nipCode: '51318',
  },
  {
    name: 'Consistent Trust Microfinance Bank',
    code: '090553',
  },
  {
    name: 'Kayvee Microfinance Bank',
    code: '090554',
  },
  {
    name: 'BishopGate Microfinance Bank',
    code: '090555',
  },
  {
    name: 'Egwafin Microfinance Bank',
    code: '090556',
  },
  {
    name: 'Lifegate Microfinance Bank',
    code: '090557',
  },
  {
    name: 'Shongom Microfinance Bank',
    code: '090558',
  },
  {
    name: 'Shield Microfinance Bank',
    code: '090559',
    nipCode: '50582',
  },
  {
    name: 'Tanadi Microfinance Bank',
    code: '090560',
    nipCode: '090560',
  },
  {
    name: 'Akuchuckwu Microfinance Bank',
    code: '090561',
    nipCode: '090561',
  },
  {
    name: 'Cedar Microfinance Bank',
    code: '090562',
  },
  {
    name: 'Balera Microfinance Bank',
    code: '090563',
  },
  {
    name: 'Supreme Microfinance Bank',
    code: '090564',
    nipCode: '50968',
  },
  {
    name: 'Oke-Aro Oredegbe Microfinance Bank',
    code: '090565',
  },
  {
    name: 'Okuku Microfinance Bank',
    code: '090566',
  },
  {
    name: 'Orokam Microfinance Bank',
    code: '090567',
  },
  {
    name: 'Broadview Microfinance Bank',
    code: '090568',
  },
  {
    name: 'Qube Microfinance Bank',
    code: '090569',
  },
  {
    name: 'Iyamoye Microfinance Bank',
    code: '090570',
  },
  {
    name: 'Ilaro Poly Microfinance Bank',
    code: '090571',
    nipCode: '50442',
  },
  {
    name: 'EWT Microfinance Bank',
    code: '090572',
  },
  {
    name: 'Snow MFB',
    code: '090573',
  },
  {
    name: 'First Midas Microfinance Bank',
    code: '090575',
  },
  {
    name: 'Octopus Microfinance Bank',
    code: '090576',
  },
  {
    name: 'Gbede Microfinance Bank',
    code: '090579',
  },
  {
    name: 'Otech Microfinance Bank',
    code: '090580',
  },
  {
    name: 'Stateside Microfinance Bank',
    code: '090583',
    nipCode: '50809',
  },
  {
    name: 'GOLDMAN MFB',
    code: '090574',
    nipCode: '090574',
  },
  {
    name: 'Nkpolu-Ust MFB',
    code: '090535',
  },
  {
    name: 'Iwade MFB Ltd',
    code: '090578',
  },
  {
    name: 'Microbiz MFB',
    code: '090587',
  },
  {
    name: 'Orisun MFB',
    code: '090588',
  },
  {
    name: 'Mercury MFB',
    code: '090589',
  },
  {
    name: 'Gabsyn Microfinance Bank Limited',
    code: '090591',
  },
  {
    name: 'Tasued Microfinance Bank',
    code: '090593',
  },
  {
    name: 'Kenechukwu Microfinance Bank',
    code: '090602',
  },
  {
    name: 'Waya Microfinance Bank',
    code: '090950',
    nipCode: '51355',
  },
  {
    name: 'IBA Microfinance Bank',
    code: '090598',
  },
  {
    name: 'Island Microfinance Bank',
    code: '090584',
  },
  {
    name: 'Ave Maria Microfinance Bank',
    code: '090600',
  },
  {
    name: 'Akpo Microfinance Bank',
    code: '090608',
  },
  {
    name: 'Ummah Microfinance Bank',
    code: '090609',
  },
  {
    name: 'Amoye Microfinance Bank',
    code: '090610',
  },
  {
    name: 'Medef Microfinance Bank',
    code: '090612',
  },
  {
    name: 'IBOLO Microfinance Bank',
    code: '090532',
  },
  {
    name: 'Banc Corp MFB',
    code: '090581',
    nipCode: '50117',
  },
  {
    name: 'Flourish MFB',
    code: '090614',
  },
  {
    name: 'Beststar MFB',
    code: '090615',
    nipCode: '50123',
  },
  {
    name: 'Rayyan MFB',
    code: '090616',
  },
  {
    name: 'Macrod MFB',
    code: '090603',
  },
  {
    name: 'Cashbridge Microfinance Bank',
    code: '090634',
    nipCode: '51353',
  },
  {
    name: 'Iyin Ekiti MFB',
    code: '090620',
  },
  {
    name: 'Creditville MFB',
    code: '090611',
  },
  {
    name: 'MAB Allianz MFB',
    code: '090623',
  },
  {
    name: 'FET',
    code: '100001',
  },
  {
    name: 'Paga',
    code: '100002',
    nipCode: '100002',
  },
  {
    name: 'Parkway-ReadyCash',
    code: '100003',
    nipCode: '311',
  },
  {
    name: 'Opay Digital Services LTD',
    code: '100004',
    nipCode: '999992',
  },
  {
    name: 'Cellulant',
    code: '100005',
  },
  {
    name: 'eTranzact',
    code: '100006',
  },
  {
    name: 'Stanbic IBTC @ease wallet',
    code: '100007',
  },
  {
    name: 'Ecobank Xpress Account',
    code: '100008',
  },
  {
    name: 'GTMobile',
    code: '100009',
  },
  {
    name: 'TeasyMobile',
    code: '100010',
  },
  {
    name: 'Mkudi',
    code: '100011',
  },
  {
    name: 'VTNetworks',
    code: '100012',
  },
  {
    name: 'AccessMobile',
    code: '100013',
  },
  {
    name: 'FBNMobile',
    code: '100014',
  },
  {
    name: 'Kegow (Chamsmobile)',
    code: '100036',
  },
  {
    name: 'FortisMobile',
    code: '100016',
  },
  {
    name: 'Hedonmark',
    code: '100017',
  },
  {
    name: 'ZenithMobile',
    code: '100018',
  },
  {
    name: 'Fidelity Mobile',
    code: '100019',
  },
  {
    name: 'MoneyBox',
    code: '100020',
  },
  {
    name: 'Eartholeum',
    code: '100021',
  },
  {
    name: 'GoMoney',
    code: '100022',
    nipCode: '100022',
  },
  {
    name: 'TagPay',
    code: '100023',
  },
  {
    name: 'Zinternet Nigera Limited',
    code: '100025',
  },
  {
    name: 'One Finance',
    code: '100026',
  },
  {
    name: 'Innovectives Kesh',
    code: '100029',
  },
  {
    name: 'EcoMobile',
    code: '100030',
  },
  {
    name: 'FCMB Easy Account',
    code: '100031',
  },
  {
    name: 'Contec Global Infotech Limited (NowNow)',
    code: '100032',
  },
  {
    name: 'PalmPay Limited',
    code: '100033',
    nipCode: '999991',
  },
  {
    name: 'Zenith Eazy Wallet',
    code: '100034',
  },
  {
    name: 'Access Yello',
    code: '100052',
  },
  {
    name: 'M36',
    code: '100035',
  },
  {
    name: 'TitanPaystack',
    code: '100039',
    nipCode: '100039',
  },
  {
    name: 'Taj_Pinspay',
    code: '080002',
  },
  {
    name: 'Intellifin',
    code: '100027',
  },
  {
    name: 'PayAttitude Online',
    code: '110001',
  },
  {
    name: 'Flutterwave Technology Solutions Limited',
    code: '110002',
  },
  {
    name: 'Interswitch Limited',
    code: '110003',
  },
  {
    name: 'First Apple Limited',
    code: '110004',
  },
  {
    name: '3line Card Management Limited',
    code: '110005',
  },
  {
    name: 'Paystack Payment Limited',
    code: '110006',
  },
  {
    name: 'Teamapt Limited',
    code: '110007',
  },
  {
    name: 'Cyberspace Limited',
    code: '110014',
  },
  {
    name: 'Vas2nets Limited',
    code: '110015',
  },
  {
    name: 'Crowdforce',
    code: '110017',
  },
  {
    name: 'Prophius',
    code: '110032',
  },
  {
    name: 'Accelerex Network Limited',
    code: '090202',
  },
  {
    name: 'NIP Virtual Bank',
    code: '999999',
  },
  {
    name: '9Payment Service Bank',
    code: '120001',
    nipCode: '120001',
  },
  {
    name: 'HopePSB',
    code: '120002',
    nipCode: '120002',
  },
  {
    name: 'MoMo PSB',
    code: '120003',
    nipCode: '120003',
  },
  {
    name: 'SmartCash PSB',
    code: '120004',
    nipCode: '120004',
  },
];
