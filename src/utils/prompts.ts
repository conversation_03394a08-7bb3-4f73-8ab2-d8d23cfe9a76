export const PROMPT_TEMPLATES = {
  GET_PRODUCT_DETAILS_FROM_CAPTION: `Given this Object structure 
  { 'name':'', 'price':'', 'description':''} & the following text that describes a product, CRITICALLY extract the product name, description, price
  and output as JSON. Do not respond with any other text but the JSON, remove the currency symbol from the price. 
  The description would be used on an ecommerce platform, so make it SEO friendly, 
  also REMOVE the following from the description: instructions of how to order, hashtags, handle tags, phone numbers, mentions of third party brands like whatsapp, facebook e.t.c.`,

  GET_MENU_ITEMS_FROM_IMAGE_TEXT: `Given the following text which was extracted from a restaurant menu image, return a json array 
  containing all the items in this format [{name, description, price}], the price should be returned as just numbers, no currencies, please ensure to return the exact description as provided but correct any spelling errors`,

  // EXPERIMENTAL
  GET_PRODUCT_FROM_MESSAGE_OPTIMIZED: `Given an optional (it could be empty) json for a cart of items with structure [{name, selected_option_name}] labelled 'CART=>' and a minified json of a restaurant's menu with 
  structure [{n: name, o: [options] labelled 'MENU=>'  and a customer's order request labelled 'ORDER_REQUEST=>'. The o (options) field in a menu item represents the different options of an item. 
  Given the enumeration of intents [ADD, REMOVE]. The query can either ADD, REMOVE items in a cart. If the query is to remove an item from the cart 
  search the cart only. If the query is to add or change an item, search the menu and the cart. Return a JSON array containing all menu or cart items that match the search term or 
  one of their corresponding options (if it has options) and return with only a json with the following structure {results:[{item_name, selected_option_name, intent, quantity}]}.
  If the query requests to change or replace an item, return an item to remove and an item to add with intent and quantity. Please note that sometimes an option can be the basis for a change.
  If no item is specified but a quantity is specified return the last item in the cart with intent and quantity.
  Note if you can't find an item return an empty array. The default order quantity is 1 if not specified in the order. If the request is to remove 'ALL of a particular item or 'THE item' set the quantity to 9999.
  If no option is selected set it to null. Do not add any extra item if no match is found. Do not respond with a list of all matching items for a each request just one match. If you can't find matching items return results as null.`,
};
