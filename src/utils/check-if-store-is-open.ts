import { Store } from '../modules/store/store.schema';
import dayjs from 'dayjs';
import weekday from 'dayjs/plugin/weekday';

dayjs.extend(weekday); // Allows getting specific weekdays

// const checkStoreIsOpen = (store?: Store) => {
//   if (store) {
//     if (store?.maintenance_mode) {
//       return {
//         open: false,
//         isOnMaintenance: true,
//         title: 'Business is Unavailable',
//         message: `${store.name} is currently not available to take orders. Please check back later, or click the button below to contact support.`,
//       };
//     }

//     const today = new Date();
//     const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
//     const dayOfTheWeek = days[today.getDay()];
//     const tomorrow = dayOfTheWeek === 'saturday' ? days[0] : days[today.getDay() + 1];
//     const tomorrowsStoreHoursExist = Object.keys(store?.configuration?.hours || {}).some((key) => key === tomorrow);
//     let storeTime: string[];

//     try {
//       storeTime = store.configuration.hours[dayOfTheWeek]?.split('-');

//       const dayHour = today.getUTCHours() + 1; /**/
//       const dayMinutes = today.getUTCMinutes();
//       const currentDayMinutes = (dayHour === 24 ? 0 : dayHour) * 60 + dayMinutes; /**/

//       //calculate minutes of for the respective selected times
//       let [opening, closing] = storeTime.map((h, i) => {
//         const isAM = h.includes('AM');
//         const hWithoutAMPM = h.slice(0, 5);

//         const [hours, minutes] = hWithoutAMPM.split(':').map((t, index) => {
//           const isHours = index === 0;
//           const time = Number(t);

//           if (!isAM && isHours && time < 12) {
//             return time + 12;
//           }

//           if (isAM && time === 12 && isHours) {
//             /**/
//             return time - 12;
//           }

//           return time;
//         });

//         return hours * 60 + minutes;
//       });

//       let isOpen: boolean;

//       // Making sure closing is always greater than opening e.g. for 9am - 4am
//       if (opening <= closing) {
//         isOpen = currentDayMinutes >= opening && currentDayMinutes <= closing;
//       } else {
//         isOpen = currentDayMinutes >= opening || currentDayMinutes <= closing;
//       }

//       const visitedBeforeOpening = currentDayMinutes < opening;
//       const visitedAfterClosing = currentDayMinutes > closing;
//       const openTomorrowTime: string =
//         tomorrowsStoreHoursExist && store.configuration?.hours[tomorrow] !== '12:00AM-12:00PM'
//           ? store.configuration.hours[tomorrow].split('-')[0]
//           : null;

//       return {
//         open: isOpen,
//         visitedBeforeOpening,
//         visitedAfterClosing,
//         openTime: storeTime[0],
//         closeTime: storeTime[1],
//         openTomorrowTime,
//         title: isOpen ? 'Open' : 'Store is Closed',
//         message: isOpen
//           ? `We are open today from ${storeTime[0]} to ${storeTime[1]}.`
//           : `${store.name} is currently closed. Please check back ${visitedBeforeOpening ? 'today' : 'tomorrow'} at ${
//               visitedBeforeOpening ? storeTime[0] : openTomorrowTime
//             }.`,
//       };
//     } catch (err) {
//       if (storeTime === undefined) return { open: true };
//     }

//     if (storeTime === undefined) return { open: true };
//   }

//   return { open: true };
// };

const checkStoreIsOpen = (store?: Store) => {
  if (store) {
    if (store?.maintenance_mode) {
      return {
        open: false,
        isOnMaintenance: true,
        title: 'Business is Unavailable',
        message: `${store.name} is currently not available to take orders. Please check back later, or click the button below to contact support.`,
      };
    }

    if (Object.keys(store.configuration.hours).length > 0) {
      const storeStatus = checkStoreStatus(store.configuration.hours, store?.country ?? 'NG');

      return {
        open: storeStatus.isOpen,
        visitedBeforeOpening: storeStatus.visitedBeforeOpening,
        visitedAfterClosing: storeStatus.visitedAfterClosing,
        openTime: storeStatus.todayOpenTime,
        closeTime: storeStatus.todayCloseTime,
        openTomorrowTime: storeStatus.nextOpenTime,
        title: storeStatus.isOpen ? 'Open' : 'Store is Closed',
        nextDayOpen: storeStatus.nextDayOpen,
        message: storeStatus.isOpen
          ? `We are open today from ${storeStatus.todayOpenTime} to ${storeStatus.todayCloseTime}.`
          : `${store.name} is currently closed. Please check back ${
              storeStatus.nextDayOpen === 'today' ? 'today' : `on ${storeStatus.nextOpenTime}`
            } at ${storeStatus.nextOpenTime}.`,
      };
    } else {
      return { open: true };
    }
  }

  return { open: true };
};

function checkStoreStatus(hours, country) {
  const daysOfWeek = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];

  // Map country codes to UTC offsets
  const timezoneOffsets = {
    NG: 1, // Nigeria (UTC+1)
    GH: 0, // Ghana (UTC+0)
    ZA: 2, // South Africa (UTC+2)
    KE: 3, // Kenya (UTC+3)
  };

  const now = new Date();

  // Adjust the current time based on the country's timezone
  const utcMinutes = now.getUTCMinutes();
  const utcHours = now.getUTCHours();
  const timezoneOffset = timezoneOffsets[country] || 0; // Default to UTC if country is not provided
  const localHours = (utcHours + timezoneOffset + 24) % 24; // Handle potential overflow
  const currentTime = localHours * 60 + utcMinutes; // Convert current time to minutes

  const currentDay = daysOfWeek[now.getUTCDay()];

  function parseTime(timeString) {
    const [time, modifier] = timeString.split(/(AM|PM)/);
    let [hours, minutes] = time.split(':').map(Number);
    if (modifier === 'PM' && hours < 12) hours += 12;
    if (modifier === 'AM' && hours === 12) hours = 0;
    return hours * 60 + minutes; // Return time in minutes
  }

  function getNextOpenDay(dayIndex) {
    for (let i = 0; i <= 7; i++) {
      // Start from 0 to check if today can be the next open day
      const nextDayIndex = (dayIndex + i) % 7;
      const nextDay = daysOfWeek[nextDayIndex];
      if (hours[nextDay] && hours[nextDay] !== '12:00AM-12:00AM') {
        const [openTime] = hours[nextDay].split('-');
        return {
          nextDay: nextDayIndex === now.getDay() ? 'today' : nextDay,
          nextOpenTime: openTime,
        };
      }
    }
    return { nextDay: null, nextOpenTime: null }; // If no next open day is found
  }

  if (!hours[currentDay] || hours[currentDay] === '12:00AM-12:00AM') {
    // Store is closed today, find the next opening
    const { nextDay, nextOpenTime } = getNextOpenDay(now.getUTCDay());
    return {
      isOpen: false,
      visitedBeforeOpening: false,
      visitedAfterClosing: true,
      todayOpenTime: null,
      todayCloseTime: null,
      nextDayOpen: nextDay,
      nextOpenTime: nextOpenTime,
    };
  }

  const [openTime, closeTime] = hours[currentDay].split('-');
  const openMinutes = parseTime(openTime);
  const closeMinutes = parseTime(closeTime);

  const isOpen = currentTime >= openMinutes && currentTime < closeMinutes;
  const visitedBeforeOpening = currentTime < openMinutes;
  const visitedAfterClosing = currentTime >= closeMinutes;

  if (isOpen) {
    return {
      isOpen: true,
      visitedBeforeOpening: false,
      visitedAfterClosing: false,
      todayOpenTime: openTime,
      todayCloseTime: closeTime,
      nextDayOpen: 'today',
      nextOpenTime: openTime,
    };
  } else {
    const { nextDay, nextOpenTime } = getNextOpenDay(now.getUTCDay());
    return {
      isOpen: false,
      visitedBeforeOpening: visitedBeforeOpening,
      visitedAfterClosing: visitedAfterClosing,
      todayOpenTime: openTime,
      todayCloseTime: closeTime,
      nextDayOpen: nextDay,
      nextOpenTime: nextOpenTime,
    };
  }
}

// function checkStoreStatus(hours) {
//   const daysOfWeek = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
//   const now = new Date();
//   const currentDay = daysOfWeek[now.getDay()];
//   const currentTime = now.getHours() * 60 + now.getMinutes(); // Convert current time to minutes

//   function parseTime(timeString) {
//     const [time, modifier] = timeString.split(/(AM|PM)/);
//     let [hours, minutes] = time.split(':').map(Number);
//     if (modifier === 'PM' && hours < 12) hours += 12;
//     if (modifier === 'AM' && hours === 12) hours = 0;
//     return hours * 60 + minutes; // Return time in minutes
//   }

//   function getNextOpenDay(dayIndex) {
//     for (let i = 0; i <= 7; i++) {
//       // Start from 0 to check if today can be the next open day
//       const nextDayIndex = (dayIndex + i) % 7;
//       const nextDay = daysOfWeek[nextDayIndex];
//       if (hours[nextDay] && hours[nextDay] !== '12:00AM-12:00AM') {
//         const [openTime] = hours[nextDay].split('-');
//         return {
//           nextDay: nextDayIndex === now.getDay() ? 'today' : nextDay,
//           nextOpenTime: openTime,
//         };
//       }
//     }
//     return { nextDay: null, nextOpenTime: null }; // If no next open day is found
//   }

//   if (!hours[currentDay] || hours[currentDay] === '12:00AM-12:00AM') {
//     // Store is closed today, find the next opening
//     const { nextDay, nextOpenTime } = getNextOpenDay(now.getDay());
//     return {
//       isOpen: false,
//       visitedBeforeOpening: false,
//       visitedAfterClosing: true,
//       todayOpenTime: null,
//       todayCloseTime: null,
//       nextDayOpen: nextDay,
//       nextOpenTime: nextOpenTime,
//     };
//   }

//   const [openTime, closeTime] = hours[currentDay].split('-');
//   const openMinutes = parseTime(openTime);
//   const closeMinutes = parseTime(closeTime);

//   const isOpen = currentTime >= openMinutes && currentTime < closeMinutes;
//   const visitedBeforeOpening = currentTime < openMinutes;
//   const visitedAfterClosing = currentTime >= closeMinutes;

//   if (isOpen) {
//     return {
//       isOpen: true,
//       visitedBeforeOpening: false,
//       visitedAfterClosing: false,
//       todayOpenTime: openTime,
//       todayCloseTime: closeTime,
//       nextDayOpen: 'today',
//       nextOpenTime: openTime,
//     };
//   } else {
//     const { nextDay, nextOpenTime } = getNextOpenDay(now.getDay());
//     return {
//       isOpen: false,
//       visitedBeforeOpening: visitedBeforeOpening,
//       visitedAfterClosing: visitedAfterClosing,
//       todayOpenTime: openTime,
//       todayCloseTime: closeTime,
//       nextDayOpen: nextDay,
//       nextOpenTime: nextOpenTime,
//     };
//   }
// }

export default checkStoreIsOpen;

export function convertStringToTime(timeString) {
  // Check if the input is valid
  if (!/^\d{2}:\d{2}(AM|PM)$/.test(timeString)) {
    throw new Error('Invalid time format. Please use HH:MMAM/PM format.');
  }

  // Split the time string into components
  const [time, period] = timeString.split(/(?=[AP]M)/);
  const [hours, minutes] = time.split(':');

  // Create a new Date object for today
  const date = new Date();

  // Set the hours and minutes
  let hourValue = parseInt(hours, 10);
  if (period === 'PM' && hourValue !== 12) {
    hourValue += 12;
  } else if (period === 'AM' && hourValue === 12) {
    hourValue = 0;
  }

  date.setHours(hourValue, parseInt(minutes, 10), 0, 0);

  return date;
}

export function getNextOpenDate(day, time) {
  const now = dayjs();

  function parseTime(timeString) {
    const [time, modifier] = timeString.split(/(AM|PM)/);
    let [hours, minutes] = time.split(':').map(Number);
    if (modifier === 'PM' && hours < 12) hours += 12;
    if (modifier === 'AM' && hours === 12) hours = 0;
    return { hours, minutes };
  }

  const { hours, minutes } = parseTime(time);

  if (day === 'today') {
    return now.hour(hours).minute(minutes).second(0);
  } else {
    // Get the day index (0 - Sunday, 6 - Saturday)
    const targetDayIndex = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'].indexOf(
      day.toLowerCase(),
    );
    const currentDayIndex = now.day();

    let daysToAdd = (targetDayIndex - currentDayIndex + 7) % 7; // Calculate days to add to reach the target day
    if (daysToAdd === 0) {
      daysToAdd = 7; // If it's today but not the same day, set it to next week
    }

    return now.add(daysToAdd, 'day').hour(hours).minute(minutes).second(0);
  }
}
