import { formatPhoneNumber, generateNumber } from './index';

describe('index.spec.ts', () => {
  it('should format number to Nigeria country code', () => {
    const number = '+234-8123432933';
    const formattedNumber = formatPhoneNumber(number);
    expect(formattedNumber.match(/\+23481/)).toBeTruthy();

    // const number1 = '234-8123223023';
    // const formattedNumber1 = formatPhoneNumber(number1);
    // expect(formattedNumber1).toStrictEqual('+' + number1);
  });

  it('should generate number with the supplied length', () => {
    for (let i = 0; i < 10; i++) {
      const genNum = generateNumber(6);
      expect(genNum.match(/\d+/)).toBeTruthy();
      expect(genNum).toHaveLength(6);
    }
  });
});
