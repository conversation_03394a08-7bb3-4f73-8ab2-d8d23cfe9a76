import moment, { DurationInputArg2 } from 'moment';

const GLOBAL_DATE_FORMAT = 'YYYY-MM-DD';
// const GLOBAL_TIME_FORMAT = 'YYYY-MM-DD HH-MM-SS';

export const generateDate = (date?: string | Date): Date => {
  return moment(date).toDate();
};

export const formatDate = (date?: string | Date): string => {
  return moment(date).format(GLOBAL_DATE_FORMAT);
};

export const dateFromString = (dateString: string) => {
  return new Date(dateString);
};

export const getDateDiffInDays = (a: string | Date, b: string | Date = moment().toDate()): number => {
  return moment(a).diff(moment(b), 'days');
};

export const getDateDiffInHours = (a: Date, b: Date = moment().toDate()): number => {
  return Math.abs(a.getTime() - b.getTime()) / 36e5;
};

export const getTimeAt = (time: string | Date, diff: number, unit: DurationInputArg2): Date => {
  return generateDate(moment(time).add(diff, unit).toISOString());
};

export const getDateAt = (date: string | Date, diff: number): Date => {
  return getTimeAt(date, diff, 'days');
};

export const startOfDay = (date: string | Date): Date => {
  return moment(date).startOf('day').toDate();
};

export const endOfDay = (date: string | Date): Date => {
  return moment(date).endOf('day').toDate();
};
