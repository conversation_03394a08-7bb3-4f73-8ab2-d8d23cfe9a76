import { PDFMargin } from 'puppeteer';
import { getAppEnv } from '.';
import { WebServicesRepository } from '../repositories/webservices.repository';

async function generatePDFFromWebpage(
  pageUrl: string,
  pageWidth: number,
  pageHeight?: number,
  pagePadding?: PDFMargin,
  delay?: number,
  scale?: number,
  webservices?: WebServicesRepository,
) {
  try {
    if (getAppEnv() !== 'local' && webservices) {
      try {
        const { data } = await webservices.generatePdf({
          page_url: pageUrl,
          page_width: pageWidth,
          page_height: pageHeight,
          scale: scale,
          page_margin: pagePadding as any,
        });

        return { pdf: data };
      } catch (e) {
        console.log(e);
        return { error: e };
      }
    }

    // const url = process.env.CATLOG_WWW + '/invoices/pdf/' + invoiceId;
    const puppeteer = await import('puppeteer');
    console.log('PUPPETEER PRELAUNCH');
    const browser = await puppeteer.launch({
      headless: true,
      executablePath: process.env.CHROME_BIN || null,
      args: ['--no-sandbox', '--media-cache-size=0', '--disk-cache-size=0'],
    });
    console.log('PUPPETEER PRE PAGE');
    const page = await browser.newPage();

    console.log('New Page');

    await page.goto(pageUrl, { timeout: 0, waitUntil: 'networkidle0' });
    await page.emulateMediaType('screen');

    const bodyHandle = await page.$('body');
    const boundingBox = await bodyHandle.boundingBox();
    const height = boundingBox.height;
    await bodyHandle.dispose();

    // Add delay here
    if (delay) {
      await new Promise((resolve) => setTimeout(resolve, delay));
    }

    const pdf = await page.pdf({
      printBackground: true,
      width: pageWidth,
      height: pageHeight ?? height,
      margin: pagePadding,
      scale: scale ?? 1,
    });

    await browser.close();

    return { pdf };
  } catch (e) {
    console.log(e);
    return { error: e };
  }
}

export { generatePDFFromWebpage };
