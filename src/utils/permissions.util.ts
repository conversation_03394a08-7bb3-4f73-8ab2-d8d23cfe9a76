import { COUNTRY_CODE } from '../modules/country/country.schema';

const GENERAL_ACCESS = 'GENERAL_ACCESS';

enum DASHBOARD {
  VIEW_ANALYTICS = 'VIEW_DASHBOARD_ANALYTICS',
  VIEW_TASKS = 'VIEW_DASHBOARD_TASKS',
  VIEW_MOST_VIEWED = 'VIEW_MOST_VIEWED',
}

enum PRODUCTS {
  VIEW_PRODUCTS = 'VIEW_PRODUCTS',
  CREATE_PRODUCTS = 'CREATE_PRODUCTS',
  EDIT_PRODUCTS = 'EDIT_PRODUCTS',
  FEATURE_PRODUCTS = 'FEATURE_PRODUCTS',
  EXPORT_PRODUCTS = 'EXPORT_PRODUCTS',
  IMPORT_PRODUCTS = 'IMPORT_PRODUCTS',
}

enum ORDERS {
  VIEW_ANALYTICS = 'VIEW_ORDER_ANALYTICS',
  VIEW_ORDERS = 'VIEW_ORDERS',
  UPDATE_ORDERS = 'UPDATE_ORDERS',
  RECORD_ORDERS = 'RECORD_ORDERS',
  EXPORT_ORDERS = 'EXPORT_ORDERS',
}

enum INVOICES {
  VIEW_ANALYTICS = 'VIEW_INVOICE_ANALYTICS',
  MANAGE = 'MANAGE_INVOICES',
}

enum CUSTOMERS {
  VIEW_CUSTOMERS = 'VIEW_CUSTOMERS',
  UPDATE_CUSTOMERS = 'UPDATE_CUSTOMERS',
}

enum SUBSCRIPTIONS {
  VIEW_SUBSCRIPTIONS = 'VIEW_SUBSCRIPTIONS',
  MANAGE_SUBSCRIPTIONS = 'MANAGE_SUBSCRIPTIONS',
  VIEW_PAYMENT_HISTORY = 'VIEW_PAYMENT_HISTORY',
}

enum SETTINGS {
  ENABLE_DIRECT_CHECKOUT = 'ENABLE_DIRECT_CHECKOUT',
  ENABLE_PAYMENTS = 'ENABLE_PAYMENTS',
  UPDATE_STORE_DETAILS = 'UPDATE_STORE_DETAILS',
  UPDATE_STORE_CATEGORIES = 'UPDATE_STORE_CATEGORIES',
  UPDATE_PAYMENT_METHODS = 'UPDATE_PAYMENT_METHODS',
  VIEW_TEAM_MEMBERS = 'VIEW_TEAM_MEMBERS',
  UPDATE_TEAM_MEMBERS = 'UPDATE_TEAM_MEMBERS',
  UPDATE_MAINTENANCE_MODE = 'UPDATE_MAINTENANCE_MODE',
  UPDATE_SECURITY_PIN = 'UPDATE_SECURITY_PIN',
  UPDATE_STORE_BRANCHES = 'UPDATE_STORE_BRANCHES',
}

enum WALLETS {
  MANAGE_WITHDRAWAL_ACCOUNTS = 'MANAGE_WITHDRAWAL_ACCOUNTS',
  CAN_MAKE_WITHDRAWAL = 'CAN_MAKE_WITHDRAWAL',
  VIEW_WALLET = 'CAN_VIEW_WALLET',
  CAN_START_KYC = 'CAN_START_KYC',
  VIEW_KYC_INFO = 'VIEW_KYC_INFO',
  VIEW_TRANSACTIONS = 'VIEW_TRANSACTIONS',
  VIEW_BALANCE = 'VIEW_BALANCE',
  CAN_MAKE_CONVERSION = 'CAN_MAKE_CONVERSION',
  CAN_REQUEST_WALLET = 'CAN_REQUEST_WALLET',
}

enum CHOWBOT {
  CAN_VIEW_TOKEN_BALANCE = 'CAN_VIEW_TOKEN_BALANCE',
}

enum PLAN_PERMISSIONS {
  CAN_OWN_MULTIPLE_STORES = 'CAN_OWN_MULTIPLE_STORES',
  CAN_MANAGE_DISCOUNTS = 'CAN_MANAGE_DISCOUNTS',
  CAN_CUSTOMIZE_LINK = 'CAN_CUSTOMIZE_LINK',
  CAN_MANAGE_PRODUCT_OPTIONS = 'CAN_MANAGE_PRODUCT_OPTIONS',
  CAN_MANAGE_COUPONS = 'CAN_MANAGE_COUPONS',
  CAN_MANAGE_CURRENCIES = 'CAN_MANAGE_CURRENCIES',
  CAN_MANAGE_TEAMS = 'CAN_MANAGE_TEAMS',
  CAN_CUSTOMIZE_COLOR = 'CAN_CUSTOMIZE_COLOR',
  CAN_MANAGE_STORE_BRANCHES = 'CAN_MANAGE_STORE_BRANCHES',
  CAN_EXTRACT_ITEMS_FROM_MENU_IMAGE = 'CAN_EXTRACT_ITEMS_FROM_MENU_IMAGE',
  CAN_ACCESS_TOKENS = 'CAN_ACCESS_TOKENS',
  CAN_USE_FACEBOOK_PIXEL = 'CAN_USE_FACEBOOK_PIXEL',
  CAN_USE_CHOWDECK = 'CAN_USE_CHOWDECK',
  CAN_USE_CHOWBOT = 'CAN_USE_CHOWBOT',
  CAN_MANAGE_AFFILIATES = 'CAN_MANAGE_AFFILIATES',
  CAN_MANAGE_STORE_CONTENT = 'CAN_MANAGE_STORE_CONTENT',
  CAN_MANAGE_STORE_HIGHLIGHTS = 'CAN_MANAGE_STORE_HIGHLIGHTS',
  CAN_MANAGE_PRODUCT_INFO_BLOCKS = 'CAN_MANAGE_PRODUCT_INFO_BLOCKS',
  CAN_MANAGE_TIERED_PRICING = 'CAN_MANAGE_TIERED_PRICING',
}

enum RECEIPTS_PERMISSIONS {
  CAN_GENERATE_RECEIPTS = 'CAN_GENERATE_RECEIPTS',
}

enum COUNTRY_PERMISSIONS {
  CAN_COLLECT_PAYMENTS = 'CAN_COLLECT_PAYMENTS',
  CAN_BOOK_DELIVERIES = 'CAN_BOOK_DELIVERIES',
}

// New internal dashboard permissions
enum INTERNAL_DASHBOARD {
  VIEW_ALL_USERS = 'VIEW_ALL_USERS',
  EDIT_USERS = 'EDIT_USERS',
  VIEW_ALL_STORES = 'VIEW_ALL_STORES',
  EDIT_STORES = 'EDIT_STORES',
  VIEW_ALL_SUBSCRIPTIONS = 'VIEW_ALL_SUBSCRIPTIONS',
  EDIT_SUBSCRIPTIONS = 'EDIT_SUBSCRIPTIONS',
  VIEW_ALL_PAYMENTS = 'VIEW_ALL_PAYMENTS',
  VIEW_SYSTEM_LOGS = 'VIEW_SYSTEM_LOGS',
  MANAGE_PLANS = 'MANAGE_PLANS',
  MANAGE_COUNTRIES = 'MANAGE_COUNTRIES',
  IMPERSONATE_USERS = 'IMPERSONATE_USERS',
  MANAGE_DASHBOARD_USERS = 'MANAGE_DASHBOARD_USERS',
  MANAGE_KYC = 'MANAGE_KYC',
}

// Internal dashboard roles
enum INTERNAL_ROLES {
  SUPER_ADMIN = 'SUPER_ADMIN',
  ADMIN = 'ADMIN',
  SUPPORT = 'SUPPORT',
  ANALYST = 'ANALYST',
}

/* ROLES AND PERMISSIONS */

const STAFF = [
  DASHBOARD.VIEW_ANALYTICS,
  DASHBOARD.VIEW_MOST_VIEWED,
  DASHBOARD.VIEW_TASKS,
  PRODUCTS.VIEW_PRODUCTS,
  PRODUCTS.CREATE_PRODUCTS,
  ORDERS.VIEW_ORDERS,
  CUSTOMERS.VIEW_CUSTOMERS,
  SUBSCRIPTIONS.VIEW_SUBSCRIPTIONS,
  SETTINGS.UPDATE_STORE_CATEGORIES,
  SETTINGS.VIEW_TEAM_MEMBERS,
  CUSTOMERS.UPDATE_CUSTOMERS,
  WALLETS.VIEW_KYC_INFO,
  WALLETS.VIEW_TRANSACTIONS,
  SETTINGS.UPDATE_PAYMENT_METHODS,
  INVOICES.MANAGE,
  GENERAL_ACCESS,
  WALLETS.VIEW_WALLET,
  SETTINGS.UPDATE_MAINTENANCE_MODE,
];

const OPERATOR = [
  DASHBOARD.VIEW_ANALYTICS,
  DASHBOARD.VIEW_MOST_VIEWED,
  DASHBOARD.VIEW_TASKS,
  PRODUCTS.VIEW_PRODUCTS,
  PRODUCTS.CREATE_PRODUCTS,
  PRODUCTS.EDIT_PRODUCTS,
  ORDERS.UPDATE_ORDERS,
  ORDERS.VIEW_ORDERS,
  CUSTOMERS.VIEW_CUSTOMERS,
  SUBSCRIPTIONS.VIEW_SUBSCRIPTIONS,
  SETTINGS.UPDATE_STORE_CATEGORIES,
  SETTINGS.VIEW_TEAM_MEMBERS,
  CUSTOMERS.UPDATE_CUSTOMERS,
  WALLETS.VIEW_KYC_INFO,
  WALLETS.VIEW_TRANSACTIONS,
  SETTINGS.UPDATE_PAYMENT_METHODS,
  INVOICES.MANAGE,
  SETTINGS.ENABLE_DIRECT_CHECKOUT,
  GENERAL_ACCESS,
  WALLETS.VIEW_WALLET,
  SETTINGS.UPDATE_MAINTENANCE_MODE,
];

const ADMIN = [
  ...OPERATOR,
  ORDERS.VIEW_ANALYTICS,
  SETTINGS.UPDATE_TEAM_MEMBERS,
  SETTINGS.UPDATE_STORE_DETAILS,
  WALLETS.MANAGE_WITHDRAWAL_ACCOUNTS,
  WALLETS.CAN_MAKE_WITHDRAWAL,
  WALLETS.CAN_MAKE_CONVERSION,
  WALLETS.CAN_REQUEST_WALLET,
  WALLETS.CAN_START_KYC,
  WALLETS.VIEW_BALANCE,
  SETTINGS.ENABLE_PAYMENTS,
  INVOICES.VIEW_ANALYTICS,
  SETTINGS.UPDATE_STORE_BRANCHES,
  CHOWBOT.CAN_VIEW_TOKEN_BALANCE,
];

const OWNER = [...ADMIN, SUBSCRIPTIONS.MANAGE_SUBSCRIPTIONS, SETTINGS.UPDATE_SECURITY_PIN];

/* PLANS AND PERMISSIONS */

const STARTER = [PLAN_PERMISSIONS.CAN_CUSTOMIZE_LINK, PLAN_PERMISSIONS.CAN_MANAGE_DISCOUNTS, GENERAL_ACCESS];

const BASIC = [
  ...STARTER,
  PLAN_PERMISSIONS.CAN_MANAGE_PRODUCT_OPTIONS,
  PLAN_PERMISSIONS.CAN_MANAGE_COUPONS,
  RECEIPTS_PERMISSIONS.CAN_GENERATE_RECEIPTS,
  COUNTRY_PERMISSIONS.CAN_BOOK_DELIVERIES,
  PLAN_PERMISSIONS.CAN_CUSTOMIZE_COLOR,
  ORDERS.RECORD_ORDERS,
  PRODUCTS.FEATURE_PRODUCTS,
];

const BUSINESS_PLUS = [
  ...BASIC,
  PLAN_PERMISSIONS.CAN_OWN_MULTIPLE_STORES,
  PLAN_PERMISSIONS.CAN_MANAGE_TEAMS,
  PLAN_PERMISSIONS.CAN_MANAGE_CURRENCIES,
  PLAN_PERMISSIONS.CAN_MANAGE_STORE_BRANCHES,
  PLAN_PERMISSIONS.CAN_USE_FACEBOOK_PIXEL,
  ORDERS.EXPORT_ORDERS,
  PLAN_PERMISSIONS.CAN_EXTRACT_ITEMS_FROM_MENU_IMAGE,
  PLAN_PERMISSIONS.CAN_MANAGE_AFFILIATES,
  PRODUCTS.IMPORT_PRODUCTS,
  PRODUCTS.EXPORT_PRODUCTS,
  PLAN_PERMISSIONS.CAN_MANAGE_STORE_CONTENT,
  PLAN_PERMISSIONS.CAN_MANAGE_STORE_HIGHLIGHTS,
  PLAN_PERMISSIONS.CAN_MANAGE_PRODUCT_INFO_BLOCKS,
  PLAN_PERMISSIONS.CAN_MANAGE_TIERED_PRICING
];

const KITCHEN = [
  ...BUSINESS_PLUS,
  PLAN_PERMISSIONS.CAN_ACCESS_TOKENS,
  PLAN_PERMISSIONS.CAN_USE_CHOWDECK,
  PLAN_PERMISSIONS.CAN_USE_CHOWBOT,
  PLAN_PERMISSIONS.CAN_USE_FACEBOOK_PIXEL,

  // PLAN_PERMISSIONS.CAN_OWN_MULTIPLE_STORES,
  // PLAN_PERMISSIONS.CAN_MANAGE_TEAMS,
  // PLAN_PERMISSIONS.CAN_MANAGE_CURRENCIES,
  // PLAN_PERMISSIONS.CAN_MANAGE_STORE_BRANCHES,
  // ORDERS.EXPORT_ORDERS,
];

/* INTERNAL DASHBOARD ROLES AND PERMISSIONS */

// Support staff - can view data but has limited edit capabilities
const ID_SUPPORT = [
  INTERNAL_DASHBOARD.VIEW_ALL_USERS,
  INTERNAL_DASHBOARD.VIEW_ALL_STORES,
  INTERNAL_DASHBOARD.VIEW_ALL_SUBSCRIPTIONS,
  INTERNAL_DASHBOARD.VIEW_ALL_PAYMENTS,
  INTERNAL_DASHBOARD.VIEW_SYSTEM_LOGS,
];

// Analyst - can view all data for reporting
const ID_ANALYST = [...ID_SUPPORT];

// Admin - can manage most resources but not system users
const ID_ADMIN = [
  ...ID_SUPPORT,
  INTERNAL_DASHBOARD.EDIT_USERS,
  INTERNAL_DASHBOARD.EDIT_STORES,
  INTERNAL_DASHBOARD.EDIT_SUBSCRIPTIONS,
  INTERNAL_DASHBOARD.MANAGE_KYC,
  INTERNAL_DASHBOARD.IMPERSONATE_USERS,
];

// Super Admin - Full access
const ID_SUPER_ADMIN = [
  ...ID_ADMIN,
  INTERNAL_DASHBOARD.MANAGE_PLANS,
  INTERNAL_DASHBOARD.MANAGE_COUNTRIES,
  INTERNAL_DASHBOARD.MANAGE_DASHBOARD_USERS,
];

const NG_PERMISSIONS = [COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS, COUNTRY_PERMISSIONS.CAN_BOOK_DELIVERIES];
const GH_PERMISSIONS = [COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS, COUNTRY_PERMISSIONS.CAN_BOOK_DELIVERIES];
const KE_PERMISSIONS = [COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS];
const SA_PERMISSIONS = [COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS];

/* EXPORTS */

const SCOPES = {
  DASHBOARD,
  ORDERS,
  PRODUCTS,
  CUSTOMERS,
  SETTINGS,
  SUBSCRIPTIONS,
  PLAN_PERMISSIONS,
  COUNTRY_PERMISSIONS,
  GENERAL_ACCESS,
  INVOICES,
  WALLETS,
  RECEIPTS_PERMISSIONS,
  CHOWBOT,
  INTERNAL_DASHBOARD,
};

enum STORE_ROLES {
  OWNER = 'OWNER',
  OPERATOR = 'OPERATOR',
  ADMIN = 'ADMIN',
  STAFF = 'STAFF',
}

const RolePermissions = {
  OWNER,
  ADMIN,
  OPERATOR,
  STAFF,
};

const PlanPermissions = {
  STARTER,
  BASIC,
  BUSINESS_PLUS,
  KITCHEN,
};

const CountryPermissons = { NG: NG_PERMISSIONS, GH: GH_PERMISSIONS, KE: KE_PERMISSIONS, ZA: SA_PERMISSIONS };

const InternalDashboardPermissions = {
  [INTERNAL_ROLES.SUPER_ADMIN]: ID_SUPER_ADMIN,
  [INTERNAL_ROLES.ADMIN]: ID_ADMIN,
  [INTERNAL_ROLES.SUPPORT]: ID_SUPPORT,
  [INTERNAL_ROLES.ANALYST]: ID_ANALYST,
};

export type Permission =
  | DASHBOARD
  | ORDERS
  | WALLETS
  | PRODUCTS
  | INVOICES
  | CUSTOMERS
  | SETTINGS
  | SUBSCRIPTIONS
  | PLAN_PERMISSIONS
  | COUNTRY_PERMISSIONS
  | RECEIPTS_PERMISSIONS
  | CHOWBOT;

const COUNTRIES_WITH_PAYMENTS = [COUNTRY_CODE.GH, COUNTRY_CODE.NG, COUNTRY_CODE.KE, COUNTRY_CODE.ZA];

export {
  SCOPES,
  STORE_ROLES,
  INTERNAL_ROLES,
  RolePermissions,
  PlanPermissions,
  CountryPermissons,
  InternalDashboardPermissions,
  COUNTRIES_WITH_PAYMENTS,
};
