import { uploadImage } from '../modules/store/kyc/utils/uploadImage';
import { S3Repository } from '../repositories/s3.repositories';

const path = require('path');

export const takeScreenshotsFromWebpage = async (urls: { path: string; filename: string }[], s3: S3Repository) => {
  const puppeteer = await import('puppeteer');
  const browser = await puppeteer.launch({
    headless: true,
    executablePath: process.env.CHROME_BIN || null,
    args: ['--no-sandbox', '--media-cache-size=0', '--disk-cache-size=0'],
  });
  const page = await browser.newPage();
  await page.setViewport({ width: 1080, height: 1920 });

  const screenshotPaths = [];

  for (const url of urls) {
    try {
      await page.goto(url.path, { waitUntil: 'networkidle0', timeout: 600000 });

      // Capture screenshot as buffer
      const screenshotBuffer = await page.screenshot({ type: 'jpeg', quality: 80 });
      const base64Image = screenshotBuffer.toString('base64');

      const uploadedUrl = await uploadImage(base64Image, s3, 'SCREENSHOTS');
      screenshotPaths.push(uploadedUrl.Location);
    } catch (error) {
      console.error(`Error loading ${url}:`, error);
    }
  }

  await browser.close();
  return screenshotPaths;
};
