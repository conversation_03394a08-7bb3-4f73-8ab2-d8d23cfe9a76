import axios, { AxiosError } from 'axios';

// Utility function to extract error details
export function extractErrorDetails(error: unknown): { message: string; details?: any } {
  // Default error message if the error is not an Axios error
  let errorDetails = {
    message: 'An unexpected error occurred',
    details: null,
  };

  if (axios.isAxiosError(error)) {
    const axiosError = error as AxiosError;

    if (axiosError.response) {
      // The server responded with a status code out of the range of 2xx
      errorDetails = {
        message: `Request failed with status code ${axiosError.response.status}`,
        details: {
          data: axiosError.response.data, // Response provided by the server
          status: axiosError.response.status,
          headers: axiosError.response.headers,
          message: axiosError.response?.data?.message ?? null,
        },
      };
    } else if (axiosError.request) {
      // The request was made but no response was received
      errorDetails.message = 'No response was received';
    } else {
      // Something happened in setting up the request
      errorDetails.message = axiosError.message;
    }
  } else if (error instanceof Error) {
    // Non-Axios errors such as programming errors or other exceptions
    errorDetails.message = error.message;
  }

  return errorDetails;
}

export function extractErrorMessage(error: any) {
  if (typeof error === 'string') {
    return error;
  }

  return error?.details?.message ?? error?.message ?? 'Something went wrong!';
}
