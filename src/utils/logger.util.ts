import { LoggerOptions } from 'winston';
import * as Sentry from '@sentry/node';
import { Severity } from '@sentry/node';
import { WinstonModule } from 'nest-winston';
import { Injectable, LoggerService } from '@nestjs/common';

export interface ICatlogLoggerOptions {
  winstonOptions: LoggerOptions;
}

@Injectable()
export class CatlogLogger {
  private readonly logger: LoggerService;
  private context: string;
  private tags: { [p: string]: any };
  public registerErrors = Sentry.captureException;

  constructor(options: ICatlogLoggerOptions) {
    this.logger = WinstonModule.createLogger(options.winstonOptions);
  }

  setContextAndTags(context: string, tags: { [p: string]: any }) {
    this.context = context;
    this.tags = tags;
  }

  setContext(context: string) {
    this.context = context;
    return this;
  }

  error(err: any, message?: any, trace?: string, context?: string): any {
    // Sentry.captureException(err, { level: Severity.Error, tags: this.tags });
    console.log(err);
    this.logger.error(err || message, trace, context || this.context);
  }

  log(message: any, context?: string) {
    this.logger.log(message, context || this.context);
  }

  warn(message: any, context?: string): any {
    this.logger.warn(message, context || this.context);
    Sentry.captureMessage(message, {
      level: Severity.Warning,
      tags: this.tags,
    });
  }
}
