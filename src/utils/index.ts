import { Types } from 'mongoose';
import { isArray, isObject } from 'class-validator';
import { PaginateResult } from 'mongoose';
import { phone } from 'phone';
import { PaginatedQueryDto } from '../models/dtos/PaginatedDto';
import moment from 'moment';
import { ItemDocument } from '../modules/item/item.schema';
import { Store } from '../modules/store/store.schema';
import { DiscountDocument } from '../modules/item/discount-coupons/discounts-coupons.schema';
import { IRequest } from '../interfaces/request.interface';
import { Permission, RolePermissions, PlanPermissions } from './permissions.util';
import { BadRequestException, ForbiddenException } from '@nestjs/common';
import { Plan } from '../modules/plan/plan.schema';
import { PAYMENT_METHODS } from '../enums/payment.enum';
import { FileTypeEnum } from '../repositories/s3.repositories';

export const amountFormat = (amount: number | string, toFixed: number = 2): string => {
  const numericAmount = parseFloat(amount.toString());

  if (isNaN(numericAmount)) {
    throw new Error('Invalid amount provided');
  }

  const fixedAmount = numericAmount.toFixed(toFixed);
  const [integerPart, decimalPart] = fixedAmount.split('.');
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  return decimalPart ? `${formattedInteger}.${decimalPart}` : formattedInteger;
};

export const toCurrency = (amount: string | number, currency: string = 'NGN', decimals?: number) => {
  return `${currency} ${amountFormat(amount, decimals)}`;
};

export function isValidObjectId(id: string) {
  if (Types.ObjectId.isValid(id)) {
    if (String(new Types.ObjectId(id)) === id) return true;
    return false;
  }
  return false;
}

export function genChars(length = 10, allCaps = false, digitsOnly = false) {
  let str = '';
  const char = digitsOnly
    ? '1234567890'
    : allCaps
    ? 'ABCDEFGHIJKLMNPQRSTUVWXYZ123456789'
    : 'abdefghijklmpqrstuvwxyz123456789';
  for (let i = 0; i < length; i++) {
    str += char.charAt(Math.floor(Math.random() * char.length));
  }
  return str;
}

export function sluggify(str: string, camelStyle = false) {
  if (camelStyle === true)
    return str
      .replace(/[^\w\s]/gi, '')
      .trim()
      .toLocaleUpperCase()
      .split(' ')
      .join('_');

  return str
    .replace(/_/g, ' ') // Replace underscores with spaces
    .replace(/[^\w\s]/gi, '') // Remove non-word characters
    .trim()
    .toLocaleLowerCase()
    .split(' ')
    .join('-');
}

export function removeSpecialCharacters(input: string): string {
  // Replace all non-alphanumeric characters except spaces with an empty string
  return input.replace(/[^\w\s]/g, '');
}

/**
 * Takes an array of docs object with properties of `_id` and returns an array of the object with `_id` converted to `_id`.
 * @param docs T[] | T
 * @returns T[]
 */
export function stripUnderScoreId<T>(docs: T) {
  if (!isArray(docs)) {
    for (const prop in docs) {
      if (!docs.hasOwnProperty(prop) || docs[prop] == undefined) {
        continue;
      }

      if (prop === '_id') {
        (docs as any).id = String((docs as any)._id);
        delete (docs as any)._id;
        continue;
      }

      if (isObjectId(docs[prop])) {
        continue;
      }

      if (typeof docs === 'string' || docs instanceof String) {
        continue;
      }

      // Collapse potentially cyclic properties into a flat object
      docs[prop] = JSON.parse(JSON.stringify(docs[prop]));

      if (isObject(docs[prop]) || isArray(docs[prop])) {
        docs[prop] = stripUnderScoreId(docs[prop]);
      }
    }
    return docs;
  }

  for (let i = 0; i < ((docs as unknown) as T[]).length; i++) {
    docs[i] = stripUnderScoreId(docs[i]);
  }

  return docs;
}

// Creates a script used by the message-debtors endpoint
export const getScript = (data: any, discounts: any) => `Hi ${
  data.name
}, your subscription to Catlog's basic plan expired ${data.months_owed} month${
  data.months_owed == 1 ? '' : 's'
} ago, we hope Catlog has been beneficial to your business and we would love to keep you around.

If you'll like to keep using Catlog, we encourage you to pay for the month${
  data.months_owed == 1 ? '' : 's'
} owed, we'll be giving you a ${discounts[data.months_owed].discount} discount if pay within the next 48hours.

You can make payments here: <${discounts[data.months_owed].payment_link}>

Please note that if we don't receive your payment within the next 3 days, we'll have to roll your store back to the starter plan.

This means that:
- Customers will only see 10 products on your store
- Your custom link will be revoked
- Customers will not be able to find your products on google anymore

Feel free to reply to this message if you have any questions.

Cheers!

_This is an automated message_`;

export const isObjectId = (obj) => obj?._bsontype === 'ObjectID' && isValidObjectId(obj);

export function serializeAllObjectIdsToString<T extends Record<string, any | any[]>>(doc: T) {
  if (!isArray(doc)) {
    for (const prop in doc) {
      if (!doc[prop]) {
        continue;
      }
      if (doc[prop]?._bsontype === 'ObjectID' && isValidObjectId(doc[prop])) {
        doc[prop] = doc[prop].toString();
      }
      if (
        (isObject(doc[prop]) || isArray(doc[prop])) &&
        !Buffer.isBuffer(doc[prop]) &&
        typeof doc[prop] !== 'function'
      ) {
        doc[prop] = serializeAllObjectIdsToString(doc[prop]);
      }
    }

    return doc;
  }

  for (let i = 0; i < doc.length; i++) {
    (doc as any)[i] = serializeAllObjectIdsToString(doc[i]);
  }

  return doc;
}

export const formatString = (str: string): string => {
  if (!str) return '';
  const totalSplit = Math.floor(str.length / 4);
  let start = 0;
  let end = 4;
  let output = '';
  for (let i = 0; i < totalSplit; i++) {
    output += str.slice(start, end);
    output += i === totalSplit - 1 ? '' : '-';
    start += 4;
    end += 4;
  }
  return output.trim();
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const generateNumber = (length = 10) => {
  return Math.ceil(Math.floor(100000 + Math.random() * 900000)).toString();
};

// export const formatPhoneNumber = (phoneNumber: string, country?: string) => {
//   return phone(phoneNumber.replace('-', ''), { country }).phoneNumber;
// };

export const formatPhoneNumber = (phoneNumber: string) => {
  return phoneNumber.replace('-', '').replace('+', '');
};

export const regularizePhoneNumber = (phoneNumber: string) => {
  return phoneNumber.replace(/^[^0-9]*[0-9]+-/, '0');
  // return phoneNumber.replace('-', '').replace('+', '');
};

export function isProduction(): boolean {
  return process.env.NODE_ENV === 'production';
}

export function getAppEnv() {
  return (process.env.CATLOG_ENV || 'local') as 'staging' | 'production' | 'local' | 'test' | 'experiments';
}

/**
 * Checks if the app is running in local environment and the MongoDB URI doesn't include localhost
 * @returns boolean
 */
export function isUsingProdDBLocally() {
  return getAppEnv() === 'local' && !process.env.MONGODB_URI?.includes('localhost');
}

export function capitalizeFirstLetter(string: string) {
  return string.charAt(0).toUpperCase() + string.slice(1);
}

export function formatText(text: string, actions: ('capitalize' | 'uppercase' | 'bold' | 'italic')[] = []) {
  let formatted = text;

  if (actions.length === 0) return text;
  if (actions.includes('capitalize')) formatted = capitalizeFirstLetter(text.trim());
  if (actions.includes('uppercase')) formatted = formatted.toUpperCase();
  if (actions.includes('bold')) formatted = `*${formatted.trim()}*`;
  if (actions.includes('italic')) formatted = `_${formatted.trim()}_`;

  return formatted;
}

export function mapPaginatedResponse(result: PaginateResult<any>) {
  return {
    page: result.page,
    next_page: result.nextPage,
    prev_page: result.prevPage,
    total: result.totalDocs,
    total_pages: result.totalPages,
    sort: result.sort || '',
    per_page: result.limit,
  };
}
export function convertToObjectIds(ids: string[]) {
  return ids.map((itemId) => new Types.ObjectId(itemId as string));
}

export function mapPaginateQuery(query: PaginatedQueryDto) {
  return {
    limit: query.per_page || 10,
    page: query.page || 1,
    sort: query.sort,
  };
}

// Converts mongoose date query to paginated response
export function mapDateQuery(filter: any) {
  const createdAt = filter.created_at || filter.createdAt;
  if (createdAt) {
    return {
      from: moment(createdAt.$gte).format(),
      to: moment(createdAt.$lte).format(),
    };
  }
  return {};
}

export function resolveCategories(items: ItemDocument[], store: Store) {
  return items.map((item) => {
    item = item.toJSON ? item.toJSON() : item;
    item.category = store.categories?.find((category) => {
      const categoryId = String(category.id) || String(category._id);
      return item.category && categoryId === item.category.toString();
    });

    [item.category] = stripUnderScoreId([item.category]);
    return item;
  });
}

export function getDiscountPrice(price: number, discount: DiscountDocument) {
  let discount_price;
  const discountAmount = (price * discount.percentage) / 100;
  const discountExceeded = discount.discount_cap ? discountAmount > discount.discount_cap : false;

  const discountExpired = discount.end_date ? new Date(discount.end_date) < new Date() : false;
  const discountStarted = discount.start_date ? new Date(discount.start_date) < new Date() : false;

  discountExceeded ? (discount_price = price - discount.discount_cap) : (discount_price = price - discountAmount);

  discountExpired || !discount.active || !discountStarted ? (discount_price = undefined) : undefined;

  return discount_price;
}

export function actionIsAllowed(data: {
  userRole?: string;
  permission?: Permission;
  planPermission?: Permission;
  plan?: string;
}) {
  const { userRole, permission, planPermission, plan } = data;

  const roleAllows = permission ? RolePermissions[userRole]?.includes(permission) : true;
  const planAllows = planPermission ? PlanPermissions[plan]?.includes(planPermission) : true;

  return roleAllows && planAllows;
}

export function protectRoute(
  condition: boolean,
  permission: Permission,
  user: IRequest['user'],
  message: string,
  callback?: VoidFunction,
) {
  if (
    condition &&
    !actionIsAllowed({
      plan: (user?.store?.subscription?.plan as Plan).type,
      planPermission: permission,
    })
  ) {
    if (callback) {
      callback();
      return;
    }

    throw new ForbiddenException(message);
  }
}

export function paymentsEnabledGuard(store: Store) {
  if (!store.payments_enabled) {
    throw new ForbiddenException('Please enable payments to perform this activity');
  }
}

export function checkIfUserOwnsStore(store: Store, storeId: string) {
  console.log('CHK:', store.id, storeId);
  if (store.id.toString() !== storeId.toString()) {
    throw new ForbiddenException("You don't have access to this store, you might need to switch to the correct store");
  }
}

export function autoImplement<T>(): new () => T {
  return class {} as any;
}

export function getUserFriendlyPaymentMethod(method: PAYMENT_METHODS) {
  const methodsMap = {
    [PAYMENT_METHODS.PAYSTACK]: 'Paystack',
    [PAYMENT_METHODS.ZILLA]: 'Zilla',
    [PAYMENT_METHODS.TRANSFER]: 'Bank Transfer',
    [PAYMENT_METHODS.MONO_DIRECT_PAY]: 'Mono DirectPay',
    [PAYMENT_METHODS.THEPEER]: 'Thepeer',
    [PAYMENT_METHODS.STARTBUTTON]: 'Startbutton',
    [PAYMENT_METHODS.UNKNOWN]: '',
  };

  return methodsMap[method];
}

export function isValidInstagramUsername(username) {
  const regex = /^(?!.*@)(?!.*\.\.)(?!.*\.$)(?!.*\.[_.-])[A-Za-z0-9_.]+$/;
  return regex.test(username);
}

export function secondsSince(date: Date): number {
  const now = new Date(); // Get the current date and time
  const diff = now.getTime() - date.getTime(); // Calculate difference in milliseconds
  return Math.floor(diff / 1000); // Convert milliseconds to seconds
}

export const FileTypeToPathMap: { [key in FileTypeEnum]: string } = {
  [FileTypeEnum.ITEMS]: 'ITEMS/',
  [FileTypeEnum.KYC]: 'KYC/',
  [FileTypeEnum.STORES]: 'STORES/',
  [FileTypeEnum.HIGHLIGHTS]: 'HIGHLIGHTS/'
};
