import { getAppEnv } from '.';
import { ONE_HOUR, ONE_MINUTE } from './constants';

export const GENERIC_CACHE_PREFIX = 'SESSIONS:WHATSAPP:GENERIC';
export const CHECK_IN_CACHE_PREFIX = 'SESSIONS:WHATSAPP:CHECK_IN';
export const UTILITY_CACHE_PREFIX = 'SESSIONS:WHATSAPP:UTILITY';

export const CACHE_TTL = (ONE_HOUR * 2 + ONE_MINUTE * 5) / 1000; // IN SECONDS
export const GLOBAL_TO_TTL = (ONE_MINUTE * 10) / 1000; // IN SECONDS

export const GET_CONFIRM_ORDER_FLOW_ID = (isPickup: boolean) => {
  const envs = {
    staging: {
      pickup: '776956831202488',
      delivery: '292457880577251',
    },
    production: {
      pickup: '438032228874029',
      delivery: '720424883638167',
    },
    local: {
      pickup: '7599908413408051',
      delivery: '1096660311584179',
    },
    experiments: {
      pickup: '7599908413408051',
      delivery: '1096660311584179',
    },
  };

  return envs[getAppEnv()][isPickup ? 'pickup' : 'delivery'];
};

export const GET_APP_TIMEOUT = (type: 'generic' | 'seller' | 'payment_reminder' | 'generic_extension') => {
  // IN MILLISECONDS
  const timeouts = {
    generic: {
      staging: ONE_MINUTE * 5,
      production: ONE_HOUR * 1.5,
      experiments: ONE_HOUR * 1.5,
      local: ONE_MINUTE * 3,
    },
    seller: {
      staging: ONE_MINUTE * 10,
      production: ONE_MINUTE * 10,
      experiments: ONE_MINUTE * 10,
      local: ONE_MINUTE * 4,
    },
    payment_reminder: {
      staging: ONE_MINUTE * 5,
      production: ONE_MINUTE * 5,
      experiments: ONE_MINUTE * 5,
      local: ONE_MINUTE,
    },
    generic_extension: {
      staging: ONE_MINUTE * 5,
      production: ONE_MINUTE * 5,
      experiments: ONE_MINUTE * 5,
      local: ONE_MINUTE * 1,
    },
  };

  return timeouts[type][getAppEnv()];
};

export const GET_DELIVERY_INFO_FLOW_ID = () => {
  const envs = {
    staging: '991064729304533',
    production: '637294329050628',
    experiments: '650038164149101',
    local: '650038164149101',
  };

  return envs[getAppEnv()];
};

export const GET_FEEDBACK_FLOW_ID = () => {
  const envs = {
    staging: '',
    production: '812157630865432',
    experiments: '',
    local: '492855733512823',
  };

  return envs[getAppEnv()];
};

export const GET_ALT_DELIVERY_INFO_FLOW_ID = () => {
  const envs = {
    staging: '798792561883028',
    production: '1629416254472728',
    experiments: '1498073444420708',
    local: '1498073444420708',
  };

  return envs[getAppEnv()];
};

export const WA_BOT_PHONE_NOS = {
  production: ['2347073047124'],
  staging: ['2348164262569'],
  experiments: ['15550569453'],
  local: ['15550569453'],
};

export const WA_BOT_PHONE_IDS = {
  production: ['281885188347779'],
  staging: ['110190245216781'], //Todo: Change this later
  experiments: ['110190245216781'],
  local: ['110190245216781'],
};

export const ALLOWED_PHONE_NOS = {
  production: [],
  staging: [
    '2348052291107', //silas
    '2348071040063', //kayode
    '2347081372872', //Kayode O
    '2348148023216', //Dan
    '2348165014207', //Kome
    '233550880119', //Victor
    '2348133885411', //Ayo
    '2349023885971', //Ibukun
    '2347081606751', //Catlog WA
    '2347030233033', //Christian
    '2348060492837', //Joey
  ],
  experiments: [],
  local: [],
};

export const GLOBAL_TIMEOUT_CACHE_KEY = 'GLOBAL_TIMEOUTS';
export const ACTIVE_CONFIRMATION_SESSIONS = 'ACTIVE_CONFIRMATION_SESSIONS';
export const DELIVERY_INFO_FLOW_SECRET = '!!!CATLOG_DELIVERY_FLOW!!!';
export const FLOW_MESSAGE_VERSION = '3';
export const DEFAULT_RESTAURANT_HERO_IMAGE = 'https://catlog-1.s3.eu-west-2.amazonaws.com//i5ph9xe7jk.jpeg';
export const CATLOG_SUPPORT_PHONE = '2347081606751';
export const NONE_ID = 'NONE';
