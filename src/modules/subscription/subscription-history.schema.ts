import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../user/user.schema';
import { Subscription } from '../subscription/subscription.schema';
import { PLAN_TYPE } from '../../enums/plan.enum';
import { SUBSCRIPTION_CHANGE_TYPE, SUBSCRIPTION_STATUS } from '../../enums/payment.enum';

export type SubscriptionHistoryDocument = SubscriptionHistory & Document;

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class SubscriptionHistory {
  _id: string;

  @ApiProperty({ description: 'User who owns the subscription' })
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true })
  user: User | string;

  @ApiProperty({ description: 'The subscription referenced' })
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Subscription', required: true })
  subscription: Subscription | string;

  @ApiProperty({ description: 'Type of change (UPGRADE, DOWNGRADE, RENEWAL etc.)' })
  @Prop({ type: String, default: SUBSCRIPTION_CHANGE_TYPE.RENEWAL, enum: Object.values(SUBSCRIPTION_CHANGE_TYPE) })
  change_type?: SUBSCRIPTION_CHANGE_TYPE;

  @ApiProperty({ description: 'Snapshot of values before the change' })
  @Prop({ type: Object, default: {} })
  old_values?: Partial<Subscription>;

  @ApiProperty({ description: 'Snapshot of values after the change' })
  @Prop({ type: Object, default: {} })
  new_values?: Partial<Subscription>;
}

export const SubscriptionHistorySchema = SchemaFactory.createForClass<SubscriptionHistory>(SubscriptionHistory);
