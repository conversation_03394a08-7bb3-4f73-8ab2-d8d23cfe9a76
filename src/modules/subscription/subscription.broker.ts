import { MessagePattern } from '@nestjs/microservices';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import mongoose, { FilterQuery } from 'mongoose';
import { Subscription } from './subscription.schema';
import { SubscriptionService } from './subscription.service';
import { Controller } from '@nestjs/common';
import { CreateTokenPurchaseHistoryDto } from './dtos/token-history.dto';
import { TokenPurchaseHistoryService } from './tokens-purchase-history/token-purchase-history.service';
import { SkipThrottle } from '@nestjs/throttler';
import { CreateFreeSubscriptionDto } from './dtos/create';
import { User } from '../user/user.schema';

@SkipThrottle()
@Controller()
export class SubscriptionBroker {
  constructor(
    private readonly subscriptionService: SubscriptionService,
    private readonly tokenPurchaseHistoryService: TokenPurchaseHistoryService,
  ) {}

  @MessagePattern(BROKER_PATTERNS.PAYMENT.CREATE_NEW_SUBSCRIPTION)
  async createNewSubscription(payload: { data: CreateFreeSubscriptionDto; user: User; upfrontPaymentAmount: number }) {
    return this.subscriptionService.createNewSubscription(payload.data, payload.user, payload.upfrontPaymentAmount);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.GET_SUBSCRIPTION)
  async getSubscription(data: mongoose.FilterQuery<Subscription>) {
    return this.subscriptionService.getSubscription(data);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.GET_SUBSCRIPTION_IDS)
  async getSubscriptionIds(data: mongoose.FilterQuery<Subscription>) {
    return this.subscriptionService.getSubscriptionIds(data);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.GET_SUBSCRIPTIONS_LEAN)
  async getSubscriptions(data: { filter: mongoose.FilterQuery<Subscription>; select: any }) {
    return this.subscriptionService.getSubscriptionsLean(data.filter, data.select);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.GET_MULTIPLE_SUBSCRIPTIONS)
  async getMultipleSubscriptions({ filter, withUser }) {
    return this.subscriptionService.getMultipleSubscriptions(filter, withUser);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.GET_SUBSCRIPTIONS_WITH_OWNER)
  async getMultipleSubscriptionsWithOwner(data: mongoose.FilterQuery<Subscription>) {
    return this.subscriptionService.getMultipleSubscriptions(data, true);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.UPDATE_SUBSCRIPTION)
  async updateSubscription({ filter, update, payment_id, store }) {
    return this.subscriptionService.updateSubscription(filter, update, payment_id, store);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.BASIC_SUBSCRIPTION_UPDATE)
  async basicSubscriptionUpdate({ filter, update }) {
    return this.subscriptionService.basicSubscriptionUpdate(filter, update);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.EXTRA_TOKEN_PURCHASE)
  async addExtraTokenToSubscription({ subscriptionId, tokenAmount, paymentId, storeId }) {
    return this.subscriptionService.addExtraTokenToSubscription(subscriptionId, tokenAmount, paymentId, storeId);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.RECORD_TOKEN_PURCHASE)
  async recordTokenPurchase(data: CreateTokenPurchaseHistoryDto) {
    return this.tokenPurchaseHistoryService.create(data);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.AGGREGATE_SUBSCRIPTION)
  async aggregateSubscription(data: { aggregations: any[]; paginationQuery: { per_page: number; page: number } }) {
    return this.subscriptionService.aggregateSubscriptions(data.aggregations, data.paginationQuery);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.AGGREGATE_SUBSCRIPTION_REMINDERS)
  async aggregateSubscriptionReminders(data: {
    aggregations: any[];
    paginationQuery: { per_page: number; page: number };
    reminderDateRange: { start: Date; end: Date };
  }) {
    return this.subscriptionService.aggregateSubscriptionReminders(
      data.aggregations,
      data.paginationQuery,
      data.reminderDateRange,
    );
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.COUNT_SUBSCRIPTION_DOCUMENTS)
  async countDocs(filter: FilterQuery<Subscription>) {
    return this.subscriptionService.countSubscriptions(filter);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.TOKENS.DEBIT_TOKEN)
  async debitTokens(subscriptionId: string) {
    return this.subscriptionService.debitSubscriptionTokens(subscriptionId);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.TOKENS.GET_TOKEN_BALANCE)
  async getTokenBanlance({ filter, addPlan }) {
    return this.subscriptionService.getSubscriptionTokensInfo(filter, addPlan);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.UPDATE_BIGIN_RECORD_ID)
  async updateBiginRecordId({ userId, recordId }) {
    return this.subscriptionService.updateBiginRecordId(userId, recordId);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.UPDATE_BIGIN_USER)
  async updateBiginUser(data: { subscriptionId: string }) {
    return this.subscriptionService.updateBiginUser(data.subscriptionId);
  }
}
