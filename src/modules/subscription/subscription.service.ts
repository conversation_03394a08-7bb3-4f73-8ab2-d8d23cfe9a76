import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectConnection, InjectModel } from '@nestjs/mongoose';
import mongoose, { FilterQuery, Model } from 'mongoose';
import { BrokerTransportService } from '../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import {
  PAYMENT_TYPES,
  TOKEN_PURCHASE_METHOD,
  SUBSCRIPTION_STATUS,
  SUBSCRIPTION_CHANGE_TYPE,
} from '../../enums/payment.enum';
import { PLAN_RANKING, PLAN_TYPE } from '../../enums/plan.enum';
import { PaginatedQueryDto } from '../../models/dtos/PaginatedDto';
import { CancelSubscriptionToggleDto, ChangePlanDto } from '../../models/dtos/PlanDtos';
import { AMOUNT_PER_TOKEN, COUNTRY_CODE_NAME_MAP } from '../../utils/constants';
import { getDateAt } from '../../utils/time';
import { Payment } from '../payment/payment.schema';
import { PlanOption } from '../plan/plan-options/plan-options.schema';
import { Plan } from '../plan/plan.schema';
import { Store } from '../store/store.schema';
import { User } from '../user/user.schema';
import { CreateFreeSubscriptionDto } from './dtos/create';
import { CreateTokenPurchaseHistoryDto } from './dtos/token-history.dto';
import { Subscription, SubscriptionDocument } from './subscription.schema';
import { TokenPurchaseHistoryService } from './tokens-purchase-history/token-purchase-history.service';
import { SubscriptionTokens, checkTokenBalances } from './utils/balance-check-and-debit';
import dayjs from 'dayjs';
import { UpdateSubscriptionDto } from './dtos/update-subscription.dto';
import { createSubdomainURL, dateToYYYYMMDD, delay, getDocId } from '../../utils/functions';
import { CustomerIoRepository } from '../../repositories/customer-io.repository';
import { IRequest } from '../../interfaces/request.interface';
import { otherPlansMap, planProductMap, productCodeToIdMap, ZohoRepository } from '../../repositories/zoho.repository';
import { COUNTRY_CODE, COUNTRY_CURRENCY_MAP, CURRENCY_COUNTRY_MAP } from '../country/country.schema';
import { get } from 'http';
import { ResendRepository } from '../../repositories/resend.repository';
import { SubscriptionHistory, SubscriptionHistoryDocument } from './subscription-history.schema';
import { UserService } from '../user/user.service';
import { PaymentGettersService } from '../payment/services/payment.getters.service';
import { NOTIFICATION_TYPE } from '../user/notifications/notification.schema';

interface PaymentWithCreatedAt extends Payment {
  created_at: Date;
}

interface ChowbotTokens {
  tokens_bought: number;
  tokens_balance: number;
  extra_tokens: number;
  credit_limit: number;
}

@Injectable()
export class SubscriptionService {
  constructor(
    private readonly brokerTransport: BrokerTransportService,
    @InjectModel(Subscription.name)
    private readonly subscriptionModel: Model<SubscriptionDocument>,
    @InjectModel(SubscriptionHistory.name)
    private readonly subscriptionHistoryModel: Model<SubscriptionHistoryDocument>,
    @InjectConnection() protected readonly connection: mongoose.Connection,
    private readonly logger: Logger,
    private readonly tokenPurchasHistoryService: TokenPurchaseHistoryService,
    private readonly customerIo: CustomerIoRepository,
    private readonly bigin: ZohoRepository,
    private readonly resend: ResendRepository,
    private readonly userService: UserService,
    private readonly paymentGetterService: PaymentGettersService,
  ) {}

  async countSubscriptions(filter: FilterQuery<Subscription>) {
    return this.subscriptionModel.countDocuments(filter);
  }

  async switchToFreePlan(storeId: string, planReq: ChangePlanDto) {
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
      .toPromise();

    const plan = await this.brokerTransport
      .send<Plan>(BROKER_PATTERNS.PLAN.GET_PLAN, { _id: planReq.plan })
      .toPromise();

    const subscription = await this.subscriptionModel
      .findById((store?.subscription as Subscription)?.id)
      .populate('plan_option');

    if (!plan || !subscription) {
      this.logger.error("User doesn't have an active subscription");
      throw new BadRequestException('Invalid subscription or plan');
    }

    if (plan.type !== PLAN_TYPE.STARTER) {
      throw new BadRequestException('You can only switch to the free plan');
    }

    const planOption = plan.options.find(
      (option) => option.country === (subscription?.plan_option as PlanOption).country,
    );

    const session = await this.connection.startSession();
    session.startTransaction();

    await this.brokerTransport
      .send(BROKER_PATTERNS.STORE.DISABLE_STORE_FEATURES, {
        filter: { owner: store?.owner },
        isPaidSubscription: false,
        toPlanKey: plan.type,
        planOption: planOption,
      })
      .toPromise();

    await subscription.update({ plan: planReq.plan, plan_option: planOption, status: SUBSCRIPTION_STATUS.ACTIVE });

    await session.commitTransaction();

    return {};
  }

  async cancelSubscription(storeId: string, cancelReq: CancelSubscriptionToggleDto) {
    // Retrieve the store
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
      .toPromise();

    // Retrieve the subscription
    const subscription = await this.subscriptionModel.findById((store?.subscription as Subscription)?.id);

    if (!subscription) {
      this.logger.error("User doesn't have an active subscription");
      throw new BadRequestException('Invalid subscription');
    }

    subscription.cancel_at_period_end = cancelReq.cancel_at_period_end;
    await subscription.save();

    return {};
  }

  async createNewSubscription(payload: CreateFreeSubscriptionDto, user: User, upfrontPaymentAmount: number = 0) {
    let subscription = await this.subscriptionModel
      .findOne({
        owner: payload.owner,
      })
      .populate('plan plan_option');

    let store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: payload.store })
      .toPromise();

    const plan = await this.brokerTransport
      .send<Plan>(BROKER_PATTERNS.PLAN.GET_PLAN, { _id: payload.plan })
      .toPromise();

    if (!plan) {
      throw new BadRequestException('Plan with id does not exist');
    }

    const planOption = plan.options.find((option) => getDocId(option) === payload.plan_option);

    if (store && subscription && String(subscription.owner) === String(store.owner)) {
      await this.updateStoreSubscription(
        payload.store,
        subscription.id,
        subscription.plan_option as PlanOption,
        (subscription?.plan as Plan)?.amount > 0,
        plan.type !== PLAN_TYPE.STARTER && !subscription.paid_upfront,
        subscription.paid_upfront,
      );

      await this.customerIo.createOrUpdateUser({
        id: getDocId(user),
        email: user.email,
        plan: plan?.name,
        plan_interval: planOption?.interval_text,
      });

      return subscription;
    }

    if (subscription) {
      throw new BadRequestException('User already has a subscription');
    }

    if (plan.type === PLAN_TYPE.KITCHEN) {
      payload['meta'] = {
        chowbot_tokens: {
          tokens_bought: 0,
          tokens_balance: 50,
          extra_tokens: 0,
          credit_limit: 0,
        },
      };
    }

    subscription = await this.subscriptionModel.create({
      ...payload,
      last_payment_date: new Date(),
      isFree: true,
      next_payment_date:
        plan.type === PLAN_TYPE.KITCHEN || upfrontPaymentAmount > 0
          ? getDateAt(new Date(), 30)
          : getDateAt(new Date(), 7),
      status: SUBSCRIPTION_STATUS.ACTIVE,
      initial_plan: plan?.type,
      paid_upfront: upfrontPaymentAmount > 0,
      plan_option_version: planOption.version,
    });

    await this.updateStoreSubscription(
      payload.store,
      subscription.id,
      planOption,
      plan.is_paid_plan,
      false,
      upfrontPaymentAmount > 0,
    );
    await this.customerIo.createOrUpdateUser({
      id: getDocId(user),
      email: user.email,
      plan: plan.name,
      plan_interval: planOption.interval_text,
    });

    await this.saveUserToBigin(
      user,
      store,
      plan,
      planOption,
      subscription,
      { count: upfrontPaymentAmount > 0 ? 1 : 0, total_amount: upfrontPaymentAmount },
      upfrontPaymentAmount > 0,
    );

    await new this.subscriptionHistoryModel({
      user: subscription.owner,
      subscription: subscription._id,
      change_type: SUBSCRIPTION_CHANGE_TYPE.SIGNUP,
      old_values: {},
      new_values: {
        plan: plan,
        plan_option: planOption,
        status: SUBSCRIPTION_STATUS.ACTIVE,
        isFree: true,
      },
    }).save();

    return { ...subscription, plan, plan_option: planOption };
  }

  async updateStoreSubscription(
    store_id: string,
    subscription_id: string,
    planOption: PlanOption,
    isPaidSubscription: boolean = false,
    isFreeTrial: boolean = false,
    paidUpfront: boolean = false,
  ) {
    await this.brokerTransport
      .send(BROKER_PATTERNS.STORE.ADD_STORE_SUBSCRIPTION, {
        store_id,
        subscription_id,
        planOption,
        isPaidSubscription,
        isFreeTrial,
        paidUpfront,
      })
      .toPromise();
  }

  /**
   * This function is used to update a subscription without triggering any side effects
   * Should only be used to update metadata like payment_reference etc
   * @param filter - The filter to find the subscription
   * @param update - The update to apply to the subscription
   * @returns The updated subscription
   */
  async basicSubscriptionUpdate(filter, update) {
    return await this.subscriptionModel.findOneAndUpdate(filter, update, { new: true });
  }

  async updateSubscription(filter, update, paymentId?, storeId?) {
    let plan: Plan;
    let planOption: PlanOption;
    let user: User;
    let existingSubscription: Subscription;

    if (update?.plan && update?.plan_option) {
      plan = await this.brokerTransport
        .send<Plan>(BROKER_PATTERNS.PLAN.GET_PLAN, { _id: update.plan })
        .toPromise();

      planOption = plan.options.find(
        (option: PlanOption & { _id?: string }) => getDocId(option) === update.plan_option,
      );

      existingSubscription = await this.subscriptionModel
        .findOne(filter)
        .populate('plan')
        .populate('plan_option')
        .exec();

      user = await this.brokerTransport
        .send<User>(BROKER_PATTERNS.USER.GET_USER, { _id: existingSubscription?.owner })
        .toPromise();
    }

    // For activating existing subscriptions
    if (update.plan && update.plan_option && update.status === SUBSCRIPTION_STATUS.ACTIVE && paymentId && storeId) {
      if (plan.type === PLAN_TYPE.KITCHEN) {
        // Retain existing extra_tokens from old subscription
        const existingTokens = existingSubscription?.meta?.chowbot_tokens || { extra_tokens: 0 };

        update['meta'] = {
          ...existingSubscription.meta,
          chowbot_tokens: {
            tokens_bought: planOption.chowbot_tokens,
            tokens_balance: planOption.chowbot_tokens,
            extra_tokens: existingTokens.extra_tokens ?? 0,
            credit_limit: -(0.1 * planOption.chowbot_tokens),
            threshold_notification_sent: false,
          },
        };

        const owner = existingSubscription.owner as string;

        const payload: CreateTokenPurchaseHistoryDto = {
          payment_id: paymentId,
          tokens_bought: planOption.chowbot_tokens,
          method: TOKEN_PURCHASE_METHOD.SUBSCRIPTION,
          balance_at_purchase: update.meta.chowbot_tokens,
          price_per_token: planOption.price_per_token,
          owner,
          store: storeId,
          subscription: existingSubscription._id.toString(),
        };

        this.tokenPurchasHistoryService.create(payload);
      }
    }

    // Perform the update
    const newSubscription = await this.subscriptionModel
      .findOneAndUpdate(filter, update, {
        new: true,
      })
      .lean();

    if (plan && planOption && user) {
      const paymentCount = await this.brokerTransport
        .send<{ count: number; total_amount: number }>(
          BROKER_PATTERNS.PAYMENT.COUNT_SUBSCRIPTION_PAYMENTS,
          existingSubscription.owner.toString(),
        )
        .toPromise();

      await this.customerIo.createOrUpdateUser({
        id: getDocId(user),
        email: user.email,
        plan: plan?.name,
        plan_interval: planOption?.interval_text,
        no_of_subscription_payments_made: paymentCount?.count,
      });

      await this.saveUserToBigin(
        user,
        user.stores[0],
        plan,
        planOption,
        newSubscription as Subscription,
        paymentCount,
        !!paymentId,
      );

      // Create the SubscriptionHistory entry
      newSubscription.plan = plan ?? existingSubscription.plan;
      newSubscription.plan_option = planOption ?? existingSubscription.plan_option;

      // Prepare old_values and new_values for comparison
      const old_values = {
        plan: existingSubscription.plan as Plan,
        plan_option: existingSubscription.plan_option as PlanOption,
        last_payment_date: existingSubscription.last_payment_date,
        next_payment_date: existingSubscription.next_payment_date,
      };

      const new_values = {
        plan: newSubscription.plan as Plan,
        plan_option: newSubscription.plan_option as PlanOption,
        last_payment_date: newSubscription.last_payment_date,
        next_payment_date: newSubscription.next_payment_date,
      };
      // Determine the change_type
      const change_type = await this.determineSubscriptionChangeType(existingSubscription, newSubscription);

      // Create the SubscriptionHistory entry
      await new this.subscriptionHistoryModel({
        user: newSubscription.owner,
        subscription: newSubscription._id,
        change_type,
        old_values,
        new_values,
      }).save();
    }

    return newSubscription;
  }

  async determineSubscriptionChangeType(oldSub, newSub): Promise<SUBSCRIPTION_CHANGE_TYPE> {
    const oldPlanRank = PLAN_RANKING[oldSub.plan.type];
    const newPlanRank = PLAN_RANKING[newSub.plan.type];

    // Check if plan changed
    if (oldSub.plan !== newSub.plan) {
      if (newPlanRank > oldPlanRank) {
        return SUBSCRIPTION_CHANGE_TYPE.UPGRADE;
      } else if (newPlanRank < oldPlanRank) {
        return SUBSCRIPTION_CHANGE_TYPE.DOWNGRADE;
      }
    }

    // Plan is the same, check if interval changed
    const oldInterval = oldSub.plan_option?.interval;
    const newInterval = newSub.plan_option?.interval;

    if (oldInterval !== undefined && newInterval !== undefined && oldInterval !== newInterval) {
      return SUBSCRIPTION_CHANGE_TYPE.INTERVAL_CHANGE;
    }

    // If none of the above conditions match, it's a renewal
    return SUBSCRIPTION_CHANGE_TYPE.RENEWAL;
  }

  async addExtraTokenToSubscription(subscriptionId: string, tokenAmount: number, paymentId: string, storeId: string) {
    const subscription = await this.subscriptionModel.findOneAndUpdate(
      { _id: subscriptionId },
      { $inc: { 'meta.chowbot_tokens.extra_tokens': tokenAmount } },
      { new: true },
    );

    const owner = subscription.owner as string;

    const payload: CreateTokenPurchaseHistoryDto = {
      payment_id: paymentId,
      tokens_bought: tokenAmount,
      method: TOKEN_PURCHASE_METHOD.TOKEN_PURCHASE,
      balance_at_purchase: subscription.meta.chowbot_tokens,
      price_per_token: AMOUNT_PER_TOKEN,
      owner,
      store: storeId,
      subscription: subscription.id,
    };

    this.tokenPurchasHistoryService.create(payload);

    return subscription;
  }

  // async countSubscriptions() {
  //   return this.subscriptionModel.aggregate<SubscriptionDocument>([
  //     {
  //       $match: {
  //         $or: aggregations.map((a) => ({ plan: mongoose.Types.ObjectId(a) })),
  //       },
  //     },
  //     {
  //       $lookup: {
  //         from: 'users',
  //         localField: 'owner',
  //         foreignField: '_id',
  //         as: 'owner',
  //       },
  //     },
  //     {
  //       $lookup: {
  //         from: 'plans',
  //         localField: 'plan',
  //         foreignField: '_id',
  //         as: 'plan',
  //       },
  //     },
  //     { $unwind: '$owner' },
  //     { $unwind: '$plan' },
  //   ]);
  // }

  async aggregateSubscriptions(aggregations: string[], paginationQuery: { per_page: number; page: number }) {
    return this.subscriptionModel.aggregate<SubscriptionDocument>([
      {
        $match: {
          next_payment_date: { $lte: new Date() },
          data: { $ne: true },
          plan: { $in: aggregations.map((a) => mongoose.Types.ObjectId(a)) },
        },
      },
      {
        $sort: { _id: 1 },
      },
      {
        $skip: (paginationQuery.page - 1 || 0) * (paginationQuery.per_page || 25),
      },
      { $limit: paginationQuery.per_page || 25 },
      {
        $lookup: {
          from: 'users',
          localField: 'owner',
          foreignField: '_id',
          as: 'owner',
        },
      },
      {
        $lookup: {
          from: 'plans',
          localField: 'plan',
          foreignField: '_id',
          as: 'plan',
        },
      },
      {
        $lookup: {
          from: 'planoptions',
          localField: 'plan_option',
          foreignField: '_id',
          as: 'plan_option',
        },
      },
      // { $unwind: '$owner' },
      {
        $unwind: {
          path: '$owner',
          preserveNullAndEmptyArrays: true, // Preserve unmatched documents as well
        },
      },
      // {
      //   $replaceRoot: {
      //     newRoot: '$owner',
      //   },
      // },
      { $unwind: '$plan' },
      { $unwind: '$plan_option' },
    ]);
  }

  async aggregateSubscriptionReminders(
    aggregations: string[],
    paginationQuery: { per_page: number; page: number },
    reminderDateRange: { start: Date | string; end: Date | string },
  ) {
    // Ensure proper conversion of dates if they're strings
    const startDate =
      reminderDateRange.start instanceof Date ? reminderDateRange.start : new Date(reminderDateRange.start);

    const endDate = reminderDateRange.end instanceof Date ? reminderDateRange.end : new Date(reminderDateRange.end);

    return this.subscriptionModel.aggregate<SubscriptionDocument>([
      {
        $match: {
          next_payment_date: {
            $gte: startDate,
            $lte: endDate,
          },
          plan: { $in: aggregations.map((a) => mongoose.Types.ObjectId(a)) },
        },
      },
      {
        $sort: { _id: 1 },
      },
      {
        $skip: (paginationQuery.page - 1 || 0) * (paginationQuery.per_page || 25),
      },
      { $limit: paginationQuery.per_page || 25 },
      {
        $lookup: {
          from: 'users',
          localField: 'owner',
          foreignField: '_id',
          as: 'owner',
        },
      },
      {
        $lookup: {
          from: 'plans',
          localField: 'plan',
          foreignField: '_id',
          as: 'plan',
        },
      },
      {
        $lookup: {
          from: 'planoptions',
          localField: 'plan_option',
          foreignField: '_id',
          as: 'plan_option',
        },
      },
      {
        $unwind: {
          path: '$owner',
          preserveNullAndEmptyArrays: true,
        },
      },
      { $unwind: '$plan' },
      {
        $unwind: {
          path: '$plan_option',
          preserveNullAndEmptyArrays: true,
        },
      },
    ]);
  }

  async paginateDebtors(paginationQuery: PaginatedQueryDto) {
    const debtorsCount = await this.subscriptionModel.countDocuments({ status: SUBSCRIPTION_STATUS.IN_ACTIVE });
    const aggregations = await this.subscriptionModel.aggregate<SubscriptionDocument>([
      {
        $match: {
          status: SUBSCRIPTION_STATUS.IN_ACTIVE,
          data: { $ne: true },
        },
      },
      {
        $skip: (paginationQuery.page - 1 || 0) * (paginationQuery.per_page || 25),
      },
      { $limit: paginationQuery.per_page || 25 },
      {
        $lookup: {
          from: 'users',
          localField: 'owner',
          foreignField: '_id',
          as: 'owner',
          pipeline: [
            {
              $project: { name: 1, phone: 1, stores: 1 },
            },
          ],
        },
      },
      { $unwind: '$owner' },
      {
        $lookup: {
          from: 'stores',
          localField: 'owner.stores.0',
          foreignField: '_id',
          as: 'owner.store',
          pipeline: [
            {
              $project: { name: 1, slug: 1, phone: 1 },
            },
          ],
        },
      },
      {
        $lookup: {
          from: 'plans',
          localField: 'plan',
          foreignField: '_id',
          as: 'plan',
          pipeline: [
            {
              $project: { name: 1, type: 1 },
            },
          ],
        },
      },
      { $unwind: '$plan' },
      {
        $facet: {
          metadata: [
            { $count: 'subscriptions' },
            {
              $addFields: {
                page: paginationQuery.page || 1,
                total_debtors: debtorsCount,
              },
            },
          ],
          data: [
            {
              $project: {
                last_payment_reference: 1,
                last_payment_date: 1,
                next_payment_date: 1,
                plan: 1,
                owner: 1,
                status: 1,
              },
            },
          ], // add projection here wish you re-shape the docs
        },
      },
    ]);

    const d: any = aggregations[0];

    return {
      data: d.data,
      metadata: d.metadata[0],
    };
  }

  async getSubscription(filter: FilterQuery<Subscription>) {
    const subscription = await this.subscriptionModel.findOne(filter).populate('plan plan_option last_payment').lean();

    return {
      ...subscription,
      plan: {
        plan_option_id: (subscription?.plan_option as any)?._id ?? null,
        ...((subscription?.plan_option as PlanOption) ?? {}),
        ...((subscription?.plan as Plan) ?? {}),
      },
    };
  }

  async getSubscriptionsLean(filter: FilterQuery<Subscription>, select: any = {}) {
    return this.subscriptionModel.find(filter, select).lean();
  }

  async getMultipleSubscriptions(filter: FilterQuery<Subscription>, withUser: boolean = false) {
    if (withUser) return this.subscriptionModel.find(filter);

    const subscriptions = await this.subscriptionModel.find(filter).populate('plan plan_option last_payment').lean();
    return subscriptions.map((sub) => ({
      ...sub,
      plan: {
        plan_option_id: (sub.plan_option as any)._id,
        ...(sub.plan_option as PlanOption),
        ...(sub.plan as Plan),
      },
    }));
  }

  async updateExpiredSubscriptions() {
    const starterPlan = await this.brokerTransport
      .send<Plan>(BROKER_PATTERNS.PLAN.GET_PLAN, {
        type: PLAN_TYPE.STARTER,
      })
      .toPromise();

    const subscriptions = await this.subscriptionModel.find({
      next_payment_date: {
        $gte: new Date('2022-08-25'),
        $lt: new Date('2022-09-02'),
      },
      status: SUBSCRIPTION_STATUS.ACTIVE,
      plan: { $ne: starterPlan.id },
    });

    const updatedSubscriptions = await Promise.all(
      subscriptions.map(async (sub) => {
        return await this.subscriptionModel
          .findOneAndUpdate(
            {
              _id: sub.id,
            },
            {
              next_payment_date: new Date('2022-09-02'),
            },
            { new: true },
          )
          .exec();
      }),
    );

    return updatedSubscriptions;
  }

  async getSubscriptionIds(filter: FilterQuery<Subscription>) {
    return this.subscriptionModel.find(filter, { _id: 1 }).lean();
  }

  async getSubscriptionTokensInfo(filter, addPlan = false): Promise<SubscriptionTokens> {
    const subscription = await this.subscriptionModel.findOne(filter).populate('plan');
    if (!subscription || (subscription.plan as Plan).type !== PLAN_TYPE.KITCHEN) return { error: true, data: null };

    const res = checkTokenBalances(subscription.meta?.chowbot_tokens);
    if (addPlan) res.plan = (subscription.plan as Plan).type;
    return { data: res, error: false };
  }

  async debitSubscriptionTokens(
    subscriptionId: string,
  ): Promise<{ message: string; result?: SubscriptionDocument; error?: boolean }> {
    const subscription = await this.subscriptionModel.findById(subscriptionId).populate('plan');
    if (!subscription || (subscription.plan as Plan).type !== PLAN_TYPE.KITCHEN)
      return { error: true, message: 'Token debit failed', result: null };

    const session = await this.subscriptionModel.db.startSession();
    session.startTransaction();

    try {
      const update = {
        $set: {
          'meta.chowbot_tokens.tokens_balance': {
            $cond: {
              if: { $gt: ['$meta.chowbot_tokens.tokens_balance', 0] },
              then: { $subtract: ['$meta.chowbot_tokens.tokens_balance', 1] },
              else: {
                $cond: {
                  if: {
                    $and: [
                      { $lte: ['$meta.chowbot_tokens.tokens_balance', 0] },
                      { $eq: ['$meta.chowbot_tokens.extra_tokens', 0] },
                      { $gt: ['$meta.chowbot_tokens.tokens_balance', '$meta.chowbot_tokens.credit_limit'] },
                    ],
                  },
                  then: { $subtract: ['$meta.chowbot_tokens.tokens_balance', 1] },
                  else: '$meta.chowbot_tokens.tokens_balance',
                },
              },
            },
          },
          'meta.chowbot_tokens.extra_tokens': {
            $cond: {
              if: {
                $and: [
                  { $eq: ['$meta.chowbot_tokens.tokens_balance', 0] },
                  { $gt: ['$meta.chowbot_tokens.extra_tokens', 0] },
                ],
              },
              then: { $subtract: ['$meta.chowbot_tokens.extra_tokens', 1] },
              else: '$meta.chowbot_tokens.extra_tokens',
            },
          },
        },
      };

      const filter = {
        _id: subscriptionId,
        $expr: {
          $gt: [
            { $add: ['$meta.chowbot_tokens.tokens_balance', '$meta.chowbot_tokens.extra_tokens'] },
            '$meta.chowbot_tokens.credit_limit',
          ],
        },
      };

      const result = await this.subscriptionModel
        .findOneAndUpdate(filter, [update], {
          new: true,
          session: session,
        })
        .populate('owner');

      if (!result) {
        throw new BadRequestException('Conditions not met for token debit');
      }

      await session.commitTransaction();
      this.usageThresholdChecks(result);
      return { message: 'Token debit successful', result };
    } catch (error) {
      await session.abortTransaction();
      this.logger.error('DEBITTING TOKENS ERROR:', error);
      return { message: 'Token debit failed', error: !!error };
    } finally {
      session.endSession();
    }
  }

  async usageThresholdChecks(subscription: Subscription) {
    const originalTokensBalance = subscription.meta?.chowbot_tokens?.tokens_bought || 0;
    const currentTokenBalance = subscription.meta?.chowbot_tokens?.tokens_balance || 0;

    // Calculate the percentage of tokens used
    const percentageUsed = (1 - currentTokenBalance / originalTokensBalance) * 100;

    const user = subscription.owner as User;
    const lastThresholdNotification = subscription.meta?.chowbot_tokens?.last_threshold_notification;
    const lastNotificationIsMoreThan24hrs = lastThresholdNotification
      ? dayjs().diff(dayjs(lastThresholdNotification), 'hour') >= 24
      : true;

    if (
      percentageUsed >= 80 &&
      lastNotificationIsMoreThan24hrs &&
      subscription.meta?.chowbot_tokens.extra_tokens === 0 //might reconsider this
    ) {
      await this.brokerTransport
        .send(BROKER_PATTERNS.USER.SEND_PUSH_NOTIFICATION, {
          store: user.stores[0],
          owner_only: false,
          message: {
            title: 'You are running out of tokens',
            message: `Your have used up ${percentageUsed}% of your token, current token balance: ${currentTokenBalance}`,
            path: '/chowbot',
          },
          notification_type: NOTIFICATION_TYPE.GENERIC,
        })
        .toPromise();

      const emailData = {
        to: user.email,
        subject: 'You are running out of tokens',
        data: {
          name: user.name.split(' ')[0],
          token_balance: currentTokenBalance.toString(),
          percentage_used: percentageUsed.toString(),
          cta_link: `${process.env.CATLOG_DASHBOARD}/chowbot`,
        },
      };

      await this.resend.sendEmail(BROKER_PATTERNS.MAIL.TOKENS_THRESHOLD_NOTIFICATION, emailData);

      // Update the subscription to mark the notification as sent
      await this.subscriptionModel.findByIdAndUpdate(
        subscription._id,
        { 'meta.chowbot_tokens.last_threshold_notification': new Date() },
        { new: true },
      );
    }
  }

  async updateBiginUser(subscriptionId: string) {
    const subscription = await this.subscriptionModel.findById(subscriptionId).populate('plan plan_option');
    const user = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, { _id: subscription.owner })
      .toPromise();

    const paymentCount = await this.brokerTransport
      .send<{ count: number; total_amount: number }>(
        BROKER_PATTERNS.PAYMENT.COUNT_SUBSCRIPTION_PAYMENTS,
        getDocId(user),
      )
      .toPromise();

    if (!user) return { done: false };

    await this.saveUserToBigin(
      user,
      user.stores[0],
      subscription.plan as Plan,
      subscription.plan_option as PlanOption,
      subscription,
      paymentCount,
      false,
    );

    return { done: true };
  }

  async updateUserSubscription(data: UpdateSubscriptionDto): Promise<Subscription> {
    const user = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, { email: data.email })
      .toPromise();

    if (!user) throw new BadRequestException('User not found');

    const subscription = await this.subscriptionModel.findOne({ owner: getDocId(user) });
    const plan = await this.brokerTransport
      .send<Plan>(BROKER_PATTERNS.PLAN.GET_PLAN, { _id: data.plan })
      .toPromise();

    const planOption = plan.options.find(
      (option: PlanOption & { _id?: string }) => getDocId(option) === data.plan_option,
    );

    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }

    if (data.plan) {
      subscription.plan = data.plan;
    }
    if (data.plan_option) {
      subscription.plan_option = data.plan_option;
    }
    if (data.last_payment_date) {
      subscription.last_payment_date = data.last_payment_date;
    }
    if (data.next_payment_date) {
      subscription.next_payment_date = data.next_payment_date;
    }
    if (data.status) {
      subscription.status = data.status;
    }
    if (data.last_payment_reference) {
      subscription.last_payment_reference = data.last_payment_reference;
    }
    if (data.chowbot_tokens) {
      subscription.meta = {
        ...subscription.meta,
        chowbot_tokens: {
          ...subscription.meta.chowbot_tokens,
          ...data.chowbot_tokens,
        },
      };
    }

    await this.customerIo.createOrUpdateUser({
      id: getDocId(user),
      email: user.email,
      plan: plan?.name,
      plan_interval: planOption?.interval_text,
    });

    const paymentCount = await this.brokerTransport
      .send<{ count: number; total_amount: number }>(
        BROKER_PATTERNS.PAYMENT.COUNT_SUBSCRIPTION_PAYMENTS,
        getDocId(user),
      )
      .toPromise();

    await subscription.save();
    await this.saveUserToBigin(user, user.stores[0], plan, planOption, subscription, paymentCount, data.new_payment);

    return subscription;
  }

  async migrateFirstPaymentDate() {
    const subscriptionsToUpdate = await this.subscriptionModel
      .find({ last_payment_reference: { $exists: true, $ne: null } })
      .lean();

    const updates = subscriptionsToUpdate.map(async (subscription) => {
      let update;

      const earliestPayment = await this.brokerTransport
        .send<Payment>(BROKER_PATTERNS.PAYMENT.GET_FIRST_PAYMENT, {
          'meta.subscription': subscription._id,
          type: PAYMENT_TYPES.SUBSCRIPTION,
        })
        .toPromise();

      if (earliestPayment) {
        update = this.subscriptionModel.updateOne(
          { _id: subscription._id },
          {
            first_payment_date: earliestPayment.created_at,
          },
        );
      }

      return update;
    });

    const updatedSubscriptions = await Promise.all(updates);

    return {
      message: `Updated ${updatedSubscriptions.length} subscriptions.`,
    };
  }

  async migrateSubscriptions(): Promise<{ message: string; data: SubscriptionDocument[] }> {
    // Fetch all plans
    const plans = await this.brokerTransport.send<Plan[]>(BROKER_PATTERNS.PLAN.GET_PLANS, {}).toPromise();

    // Fetch subscriptions without plan_option
    const subscriptionsWithoutOption = await this.subscriptionModel
      .find({ plan_option: { $exists: false } })
      .limit(2000)
      .lean();

    if (subscriptionsWithoutOption.length === 0) {
      return {
        message: 'No more subscription to update',
        data: [],
      };
    }

    const updates = await Promise.all(
      subscriptionsWithoutOption.map(async (subscription) => {
        const subPlan = plans.find((plan) => String(plan.id) === String(subscription.plan));

        if (!subPlan || typeof subscription.plan !== 'object') {
          // Skip if the subscription doesn't have a valid plan
          return null;
        }

        // Find the new plan where the current plan's id is an option
        const newPlan = plans.find((plan) => plan.type === subPlan.type && plan.options && plan.options.length > 0);

        if (!newPlan) {
          // Skip if no matching plan found
          return null;
        }

        // Find the plan option with matching country & interval
        const planOption = newPlan.options.find(
          (option) => option.country === subPlan.country && option.interval === subPlan.interval,
        );

        if (!planOption) {
          // Skip if no matching plan option found
          return null;
        }

        // Update the subscription with the new plan's id and plan option's id
        return this.subscriptionModel.findByIdAndUpdate(subscription._id, {
          plan: newPlan.id,
          plan_option: planOption.id,
        });
      }),
    );

    return {
      message: `Updated ${updates.length} subscriptions. Trigger the process again to update more subscription`,
      data: updates,
    };
  }

  async updateBiginRecordId(userId: string, recordId: string) {
    const subscription = await this.subscriptionModel.findOne({ owner: userId });
    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }

    subscription.meta = {
      ...subscription.meta,
      bigin_record_id: recordId,
      bigin_record_updated_at: new Date(),
    };
    await subscription.save();

    return subscription;
  }

  async migrateToBigin() {
    const freePlan = await this.brokerTransport
      .send<Plan>(BROKER_PATTERNS.PLAN.GET_PLAN, { type: PLAN_TYPE.STARTER })
      .toPromise();
    const subscriptions = await this.subscriptionModel
      .find({
        plan: { $ne: getDocId(freePlan) },
        data: { $ne: true },
        $or: [{ 'meta.bigin_record_id': { $exists: false } }, { 'meta.bigin_record_id': { $eq: null } }],
      })
      .limit(100)
      .populate({
        path: 'owner', // Populate the 'owner' field (User model)
        populate: {
          path: 'stores', // Populate 'stores' within the User model
          model: 'Store', // Specify the model for 'stores'
          select: 'name slug location', // Only fetch specific fields
        },
      })
      .populate('plan') // Populate 'plan'
      .populate('plan_option') // Populate 'plan_option'
      .lean();

    for (let index = 0; index < subscriptions.length; index++) {
      const subscription = subscriptions[index];

      const user = subscription.owner as User;

      if (!user) continue;
      const plan = subscription.plan as Plan;
      const planOption = subscription.plan_option as PlanOption;
      const userId = getDocId(user);
      const paymentCount = await this.brokerTransport
        .send<{ count: number; total_amount: number }>(BROKER_PATTERNS.PAYMENT.COUNT_SUBSCRIPTION_PAYMENTS, userId)
        .toPromise();

      await this.bigin.createOrUpdateContact(
        {
          First_Name: user.name.split(' ')[0],
          Last_Name: user.name.split(' ')[1] ?? '(No Last Name)',
          Email: user.email,
          Phone: user.phone.split('-').join(''),
          Store_Name: user.stores[0]?.name,
          Store_Link: createSubdomainURL('https://catlog.shop', user.stores[0]?.slug),
          Selected_Plan: {
            id:
              productCodeToIdMap[
                [PLAN_TYPE.STARTER, PLAN_TYPE.KITCHEN].includes(plan.type)
                  ? otherPlansMap[plan.type]
                  : planProductMap[plan.type][planOption.interval_text][planOption.country]
              ],
          },
          Payment_Due_Date: dateToYYYYMMDD(subscription.next_payment_date),
          Signup_Date: dateToYYYYMMDD(new Date((user as any)?.createdAt)),
          Country: planOption.country as COUNTRY_CODE,
          Business_Type: user.stores[0]?.business_category?.type,
          Business_Category: user.stores[0]?.business_category?.name,
          Monthly_Orders: user.stores[0]?.business_category?.monthly_orders,
          No_of_Payments: paymentCount.count,
          Total_Payments: paymentCount.total_amount,
          On_Free_Trial: 'true',
          Subscription_Status: 'Active',
          User_Currency: COUNTRY_CURRENCY_MAP[planOption.country as COUNTRY_CODE],
        },
        {
          plan: plan.type,
          status: SUBSCRIPTION_STATUS.ACTIVE,
          planPrice: planOption.amount,
          userId: userId,
          userJustPaid: false,
          paidUpfront: subscription.paid_upfront && paymentCount.count === 1 && getDocId(plan) !== getDocId(freePlan),
          biginContactId: null,
        },
      );

      await delay(500);
    }

    return {
      message: `Updated ${subscriptions.length} subscriptions`,
    };
  }

  async updateExistingBiginUsers(page: number = 1, per_page: number = 100) {
    const perPage = per_page || 100;

    const todayStart = dayjs().startOf('day').toDate();
    const freePlan = await this.brokerTransport
      .send<Plan>(BROKER_PATTERNS.PLAN.GET_PLAN, { type: PLAN_TYPE.STARTER })
      .toPromise();
    const subscriptions = (await this.subscriptionModel
      .find({
        data: { $ne: true },
        $and: [
          { 'meta.bigin_record_id': { $exists: true, $ne: null } },
          { 'meta.bigin_record_updated_at': { $lt: todayStart } },
        ],
      })
      .skip((page - 1) * perPage)
      .limit(perPage)
      .populate({
        path: 'owner', // Populate the 'owner' field (User model)
        populate: {
          path: 'stores', // Populate 'stores' within the User model
          model: 'Store', // Specify the model for 'stores'
          select: 'name slug location onboarding_steps business_category', // Only fetch specific fields
        },
      })
      .populate('plan') // Populate 'plan'
      .populate('plan_option') // Populate 'plan_option'
      .lean()) as Subscription[];

    for (let index = 0; index < subscriptions.length; index++) {
      const subscription = subscriptions[index];

      const user = subscription.owner as User;

      if (!user) continue;
      const plan = subscription.plan as Plan;
      const planOption = subscription.plan_option as PlanOption;
      const paymentCount = await this.brokerTransport
        .send<{ count: number; total_amount: number }>(
          BROKER_PATTERNS.PAYMENT.COUNT_SUBSCRIPTION_PAYMENTS,
          getDocId(user),
        )
        .toPromise();

      await this.saveUserToBigin(user, user.stores[0], plan, planOption, subscription, paymentCount, false);

      await delay(200);
    }

    return {
      message: `Updated ${subscriptions.length} subscriptions`,
    };
  }

  async saveUserToBigin(
    user: User,
    store: Store,
    plan: Plan,
    planOption: PlanOption,
    subscription: Subscription,
    paymentCount: { count: number; total_amount: number },
    userJustPaid?: boolean,
  ) {
    const payload = {
      First_Name: user.name.split(' ')[0],
      Last_Name: user.name.split(' ')[1] ?? '(No Last Name)',
      Email: user.email,
      Phone: user.phone.split('-').join(''),
      Store_Name: store.name,
      Store_Link: createSubdomainURL('https://catlog.shop', store.slug),
      Selected_Plan: {
        id:
          productCodeToIdMap[
            [PLAN_TYPE.STARTER, PLAN_TYPE.KITCHEN].includes(plan.type)
              ? otherPlansMap[plan.type]
              : planProductMap[plan.type][planOption.interval_text][planOption.country]
          ],
      },
      Payment_Due_Date: dateToYYYYMMDD(subscription?.next_payment_date),
      Signup_Date: dateToYYYYMMDD(new Date((user as any)?.createdAt)),
      Country: planOption.country as COUNTRY_CODE,
      Business_Type: store.business_category?.type,
      Business_Category: store.business_category?.name,
      Monthly_Orders: store.business_category?.monthly_orders,
      No_of_Payments: paymentCount.count,
      Total_Payments: paymentCount.total_amount,
      On_Free_Trial: plan.type !== PLAN_TYPE.STARTER && !subscription.last_payment_reference ? 'true' : 'false',
      Subscription_Status:
        subscription.status === SUBSCRIPTION_STATUS.IN_ACTIVE || plan.type === PLAN_TYPE.STARTER
          ? 'In-Active'
          : 'Active',
      User_Currency: COUNTRY_CURRENCY_MAP[planOption.country as COUNTRY_CODE],
      Has_Taken_Order_With_Payment: store.onboarding_steps?.has_taken_first_order_with_payment ? 'Yes' : 'No',
    };

    await this.bigin.createOrUpdateContact(payload, {
      plan: plan.type,
      status: subscription.status as SUBSCRIPTION_STATUS,
      planPrice: planOption.amount,
      userId: getDocId(user),
      userJustPaid: userJustPaid ?? false,
      paidUpfront: subscription?.paid_upfront && paymentCount.count === 1 && plan.type !== PLAN_TYPE.STARTER,
      biginContactId: subscription.meta?.bigin_record_id,
    });
  }

  async migrateExpiredSubsUsersOnBigin(page: number = 1, per_page: number = 100) {
    const perPage = per_page || 100;

    const freePlan = await this.brokerTransport
      .send<Plan>(BROKER_PATTERNS.PLAN.GET_PLAN, { type: PLAN_TYPE.STARTER })
      .toPromise();

    const subscriptions = await this.subscriptionModel
      .find({
        data: { $ne: true },
        plan: getDocId(freePlan),
        $and: [{ 'meta.bigin_record_id': { $exists: true } }, { 'meta.bigin_record_id': { $ne: null } }],
      })
      .skip((page - 1) * perPage)
      .limit(perPage)
      .populate({
        path: 'owner', // Populate the 'owner' field (User model)
        populate: {
          path: 'stores', // Populate 'stores' within the User model
          model: 'Store', // Specify the model for 'stores'
          select: 'name slug location', // Only fetch specific fields
        },
      })
      .populate('plan') // Populate 'plan'
      .populate('plan_option') // Populate 'plan_option'
      .lean();

    for (let index = 0; index < subscriptions.length; index++) {
      const subscription = subscriptions[index];

      const user = subscription.owner as User;

      if (!user) continue;
      const plan = subscription.plan as Plan;
      const planOption = subscription.plan_option as PlanOption;
      const userId = getDocId(user);
      const paymentCount = await this.brokerTransport
        .send<{ count: number; total_amount: number }>(BROKER_PATTERNS.PAYMENT.COUNT_SUBSCRIPTION_PAYMENTS, userId)
        .toPromise();

      await this.bigin.createOrUpdateContact(
        {
          First_Name: user.name.split(' ')[0],
          Last_Name: user.name.split(' ')[1] ?? '(No Last Name)',
          Email: user.email,
          Phone: user.phone.split('-').join(''),
          Store_Name: user.stores[0]?.name,
          Store_Link: createSubdomainURL('https://catlog.shop', user.stores[0]?.slug),
          Selected_Plan: {
            id:
              productCodeToIdMap[
                [PLAN_TYPE.STARTER, PLAN_TYPE.KITCHEN].includes(plan.type)
                  ? otherPlansMap[plan.type]
                  : planProductMap[plan.type][planOption.interval_text][planOption.country]
              ],
          },
          Payment_Due_Date: dateToYYYYMMDD(subscription.next_payment_date),
          Signup_Date: dateToYYYYMMDD(new Date((user as any)?.createdAt)),
          Country: planOption.country as COUNTRY_CODE,
          Business_Type: user.stores[0]?.business_category?.type,
          Business_Category: user.stores[0]?.business_category?.name,
          Monthly_Orders: user.stores[0]?.business_category?.monthly_orders,
          No_of_Payments: paymentCount.count,
          Total_Payments: paymentCount.total_amount,
          On_Free_Trial: 'true',
          Subscription_Status: 'Active',
          User_Currency: COUNTRY_CURRENCY_MAP[planOption.country as COUNTRY_CODE],
        },
        {
          plan: plan.type,
          status: SUBSCRIPTION_STATUS.ACTIVE,
          planPrice: planOption.amount,
          userId: userId,
          userJustPaid: false,
          paidUpfront: false,
          biginContactId: subscription.meta?.bigin_record_id,
        },
      );

      await delay(200);
    }

    return {
      message: `Updated ${subscriptions.length} subscriptions`,
    };
  }

  async migrateSubscriptionHistory(batchSize: number = 1000) {
    let skip = 0;
    let hasMoreUsers = true;

    while (hasMoreUsers) {
      // Fetch users in batches
      const users = await this.userService.getUsersBatch(skip, batchSize);

      if (users.length === 0) {
        hasMoreUsers = false;
        break;
      }

      // Process each user
      for (const user of users) {
        await this.processUserSubscriptionHistory(user);
      }

      skip += batchSize;
    }

    return {
      message: `Updated subscription history for ${skip} users`,
    };
  }

  private async processUserSubscriptionHistory(user: any) {
    // Find the subscription for the user
    const subscription = await this.subscriptionModel.findOne({ owner: getDocId(user) }).exec();

    if (!subscription) {
      return;
    }

    // Fetch all subscription payments for the user, sorted by date
    const payments = await this.paymentGetterService.getSubscriptionPayments(getDocId(user));

    let previousPlan: Plan | null = null;
    let previousPlanOption: PlanOption | null = null;

    // Process each payment to create subscription history
    for (const payment of payments) {
      const { plan: planId, plan_option: planOptionId } = payment.meta;

      if (!planId || !planOptionId) {
        continue;
      }

      // Fetch full plan details using broker
      const currentPlan = await this.brokerTransport
        .send<Plan>(BROKER_PATTERNS.PLAN.GET_PLAN, { _id: planId })
        .toPromise();

      // Find the matching plan option
      const currentPlanOption = currentPlan.options.find(
        (option: PlanOption & { _id?: string }) => getDocId(option) === planOptionId,
      );

      if (!currentPlan || !currentPlanOption) {
        continue;
      }

      // Skip if this is the first payment (no previous plan to compare with)
      if (!previousPlan || !previousPlanOption) {
        previousPlan = currentPlan;
        previousPlanOption = currentPlanOption;
        continue;
      }

      // Prepare old and new values for history
      const old_values = {
        plan: previousPlan,
        plan_option: previousPlanOption,
      };

      const new_values = {
        plan: currentPlan,
        plan_option: currentPlanOption,
      };

      // Create a temporary old/new subscription objects for determining change type
      const oldSub = {
        plan: previousPlan,
        plan_option: previousPlanOption,
      };

      const newSub = {
        plan: currentPlan,
        plan_option: currentPlanOption,
      };

      // Determine the change type
      const change_type = await this.determineSubscriptionChangeType(oldSub, newSub);

      // Create the subscription history entry
      await new this.subscriptionHistoryModel({
        user: getDocId(user),
        subscription: subscription._id,
        change_type,
        old_values,
        new_values,
        created_at: payment.created_at, // Preserve the original payment timestamp
      }).save();

      // Update previous values for next iteration
      previousPlan = currentPlan;
      previousPlanOption = currentPlanOption;
    }
  }
}
