import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import mongoose, { Document } from 'mongoose';
import { User } from '../user/user.schema';
import { Plan } from '../plan/plan.schema';
import { SUBSCRIPTION_STATUS } from '../../enums/payment.enum';
import { PLAN_OPTION_VERSION, PlanOption } from '../plan/plan-options/plan-options.schema';
import { IsBoolean, IsDateString, IsNumber, IsOptional } from 'class-validator';
import { PLAN_TYPE } from '../../enums/plan.enum';

export type SubscriptionDocument = Subscription & Document;

export class ChowbotTokens {
  @ApiProperty()
  @IsNumber()
  tokens_bought: number;

  @ApiProperty()
  @IsNumber()
  tokens_balance: number;

  @ApiProperty()
  @IsNumber()
  extra_tokens: number;

  @ApiProperty()
  @IsNumber()
  credit_limit: number;

  @ApiProperty({ type: Date })
  @IsDateString()
  last_threshold_notification: string;
}

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class Subscription {
  _id: string;

  id: string;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  owner: User | string;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Plan' })
  plan: string | Plan;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'PlanOption' })
  plan_option: string | PlanOption;

  @ApiProperty()
  @Prop({ type: Date, default: null })
  last_payment_date?: Date;

  isFree?: boolean;

  @ApiProperty()
  @Prop({ type: String })
  last_payment_reference?: string;

  @ApiProperty()
  @Prop({ type: String })
  initial_plan?: PLAN_TYPE;

  @ApiProperty({
    type: 'string',
    enum: [SUBSCRIPTION_STATUS.ACTIVE, SUBSCRIPTION_STATUS.IN_ACTIVE],
  })
  @Prop({
    type: String,
    enum: [SUBSCRIPTION_STATUS.ACTIVE, SUBSCRIPTION_STATUS.IN_ACTIVE],
    default: SUBSCRIPTION_STATUS.IN_ACTIVE,
  })
  status?: string;

  @Prop({ type: Date })
  first_fail_charge?: Date;

  @ApiProperty()
  @Prop({ type: Date, default: null })
  next_payment_date?: Date;

  @ApiProperty()
  @Prop({ type: Date, default: null })
  first_payment_date?: Date;

  @ApiProperty()
  @Prop({ type: Boolean })
  data?: Boolean;

  @ApiProperty()
  @Prop({ type: Object, default: {} })
  meta?: {
    chowbot_tokens?: ChowbotTokens;
    chowbot_fee?: number;
    bigin_record_id?: string;
    bigin_record_updated_at?: Date;
  };

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  cancel_at_period_end?: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  paid_upfront?: boolean;

  @ApiProperty()
  @Prop({ type: String, default: PLAN_OPTION_VERSION.OLD })
  plan_option_version?: PLAN_OPTION_VERSION;
}

export const SubscriptionSchema = SchemaFactory.createForClass<Subscription>(Subscription);

SubscriptionSchema.virtual('last_payment', {
  ref: 'Payment',
  localField: 'last_payment_reference',
  foreignField: 'reference',
  justOne: true,
});
