import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { jsonHookMongoosePlugin, setIdMongoosePlugin } from '../../mongoose-plugins';
import { SharedModule } from '../../shared.module';
import { SubscriptionBroker } from './subscription.broker';
import { SubscriptionController } from './subscription.controller';
import { Subscription, SubscriptionSchema } from './subscription.schema';
import { SubscriptionService } from './subscription.service';
import mongoosePaginate from 'mongoose-paginate-v2';
import { TokenPurchaseHistoryService } from './tokens-purchase-history/token-purchase-history.service';
import {
  TokenPurchaseHistory,
  TokenPurchaseHistorySchema,
} from './tokens-purchase-history/tokens-purchase-history.schema';
import { SubscriptionHistory, SubscriptionHistorySchema } from './subscription-history.schema';
import { UserModule } from '../user/user.module';
import { PaymentModule } from '../payment/payment.module';

@Module({
  imports: [
    SharedModule,
    MongooseModule.forFeatureAsync([
      {
        name: Subscription.name,
        useFactory: () => {
          SubscriptionSchema.plugin(setIdMongoosePlugin());
          SubscriptionSchema.plugin(jsonHookMongoosePlugin(['__v', '_id']));
          return SubscriptionSchema;
        },
      },
      {
        name: SubscriptionHistory.name,
        useFactory: () => {
          SubscriptionHistorySchema.plugin(setIdMongoosePlugin());
          SubscriptionHistorySchema.plugin(jsonHookMongoosePlugin(['__v', '_id']));
          return SubscriptionHistorySchema;
        },
      },
      {
        name: TokenPurchaseHistory.name,
        useFactory: () => {
          TokenPurchaseHistorySchema.plugin(setIdMongoosePlugin());
          TokenPurchaseHistorySchema.plugin(jsonHookMongoosePlugin(['__v', '_id']));
          TokenPurchaseHistorySchema.plugin(mongoosePaginate);
          return TokenPurchaseHistorySchema;
        },
      },
    ]),
    forwardRef(() => UserModule),
    forwardRef(() => PaymentModule),
  ],
  providers: [SubscriptionService, TokenPurchaseHistoryService],
  controllers: [SubscriptionController, SubscriptionBroker],
})
export class SubscriptionModule {}
