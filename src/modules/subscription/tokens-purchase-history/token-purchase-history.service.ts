import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, PaginateModel, Types } from 'mongoose';
import { TokenPurchaseHistory, TokenPurchaseHistoryDocument } from './tokens-purchase-history.schema';
import { CreateTokenPurchaseHistoryDto, TokenHistoryQueryDTO } from '../dtos/token-history.dto';
import { PaginatedQueryDto } from '../../wallets/dtos/search.dto';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { mapPaginateQuery, mapPaginatedResponse } from '../../../utils';
import { Store } from '../../store/store.schema';
import { Subscription, SubscriptionDocument } from '../subscription.schema';
import { PlanOption } from '../../plan/plan-options/plan-options.schema';

@Injectable()
export class TokenPurchaseHistoryService {
  constructor(
    @InjectModel(TokenPurchaseHistory.name)
    private tokenPurchaseHistoryModel: PaginateModel<TokenPurchaseHistoryDocument>,
    @InjectModel(Subscription.name) private subscriptionModel: Model<SubscriptionDocument>,
    private readonly brokerTransport: BrokerTransportService,
  ) {}

  async getTokenUsageByDate(queryDto: TokenHistoryQueryDTO & PaginatedQueryDto, subscriptionId: string): Promise<any> {
    const { type, storeId, branchId } = queryDto;
    const { page, limit, sort } = mapPaginateQuery(queryDto);

    const options = {
      page: page,
      limit: limit,
      sort: { created_at: sort === 'ASC' ? 1 : -1 }, // Adjust sorting if necessary.
    };

    if (type === 'debit') {
      const store = await this.brokerTransport
        .send<any>(BROKER_PATTERNS.STORE.GET_STORE_LEAN, { filter: { _id: storeId }, select: { flags: 1 } })
        .toPromise();

      if (!store?.flags?.uses_chowbot) {
        throw new BadRequestException('This store does not use chowbot');
      }

      const filterCriteria = { is_billable: true };

      if (storeId) {
        filterCriteria['store'] = Types.ObjectId(storeId);
      }
      if (branchId) {
        filterCriteria['branch'] = Types.ObjectId(branchId);
      }
      // Aggregate to count sessions per day
      return await this.brokerTransport
        .send<any>(BROKER_PATTERNS.WHATSAPP_BOT.GET_TOKEN_USAGE, { filterCriteria, options })
        .toPromise();
    } else if (type === 'credit') {
      // Use Mongoose paginate for credit type
      const result = await this.tokenPurchaseHistoryModel.paginate({ subscription: subscriptionId }, options);

      result.docs = (await Promise.all(
        result.docs.map(async (doc) => {
          return doc.toJSON();
        }),
      )) as any;

      return {
        data: result.docs,
        ...mapPaginatedResponse(result),
      };
    }
  }

  async create(createDto: CreateTokenPurchaseHistoryDto): Promise<TokenPurchaseHistory> {
    const newTokenPurchaseHistory = new this.tokenPurchaseHistoryModel(createDto);
    return await newTokenPurchaseHistory.save();
  }

  async getTokenBalance(storeId: string) {
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
      .toPromise();

    if (!store) {
      throw new BadRequestException('Store does not exist');
    }

    // Assuming store.subscription is the ID of the subscription
    if (!store.subscription) {
      throw new BadRequestException('No subscription linked to this store');
    }

    if (!store.flags?.uses_chowbot) {
      throw new BadRequestException('This store does not use chowbot');
    }

    // Retrieve the subscription from the database
    const subscription = await this.subscriptionModel.findById(store.subscription).exec();

    if (!subscription) {
      throw new BadRequestException('Subscription not found');
    }

    const averageDailyUsage = await this.brokerTransport
      .send<number>(BROKER_PATTERNS.WHATSAPP_BOT.GET_TOKEN_AVERAGE_DAILY_USAGE, {
        storeId: storeId,
        days: 30,
      })
      .toPromise();

    // Check if meta and chowbot_tokens exist and extract the tokens_balance
    if (subscription.meta && subscription.meta.chowbot_tokens) {
      return { ...subscription.meta.chowbot_tokens, averageDailyUsage };
    } else {
      return null;
    }
  }
}
