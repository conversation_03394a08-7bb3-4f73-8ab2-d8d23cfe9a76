import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import mongoose, { Document } from 'mongoose';
import { User } from '../../user/user.schema';
import { Subscription } from '../subscription.schema';
import { TOKEN_PURCHASE_METHOD } from '../../../enums/payment.enum';
import { Store } from '../../store/store.schema';
import { Branch } from '../../store/branches/branches.schema';

export type TokenPurchaseHistoryDocument = TokenPurchaseHistory & Document;

export class SubscriptionTokenBalance {
  @ApiProperty()
  tokens_bought: number;

  @ApiProperty()
  tokens_balance: number;

  @ApiProperty()
  extra_tokens: number;

  @ApiProperty()
  credit_limit: number;
}

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class TokenPurchaseHistory {
  _id: string;

  id: string;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Payment', required: true })
  payment_id: string;

  @ApiProperty()
  @Prop({ required: true })
  tokens_bought: number;

  @ApiProperty({ enum: [TOKEN_PURCHASE_METHOD.SUBSCRIPTION, TOKEN_PURCHASE_METHOD.TOKEN_PURCHASE] })
  @Prop({ required: true, enum: [TOKEN_PURCHASE_METHOD.SUBSCRIPTION, TOKEN_PURCHASE_METHOD.TOKEN_PURCHASE] })
  method: string;

  @ApiProperty()
  @Prop({ type: SubscriptionTokenBalance, required: true })
  balance_at_purchase: SubscriptionTokenBalance;

  @ApiProperty()
  @Prop({ required: true })
  price_per_token: number;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true })
  owner: User | string;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Branch' })
  branch?: Branch | string;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Store' })
  store?: Store | string;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Subscription' })
  subscription?: Subscription | string;
}

export const TokenPurchaseHistorySchema = SchemaFactory.createForClass(TokenPurchaseHistory);
