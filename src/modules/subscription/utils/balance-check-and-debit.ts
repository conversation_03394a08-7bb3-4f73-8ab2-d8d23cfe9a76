import { PLAN_TYPE } from '../../../enums/plan.enum';

interface ChowbotTokens {
  tokens_bought: number;
  tokens_balance: number;
  extra_tokens: number;
  credit_limit: number;
}

export interface SubscriptionTokens {
  data: {
    canDebit: boolean;
    tokens_balance: number;
    extra_tokens: number;
    credit_limit: number;
    plan?: PLAN_TYPE;
  };
  error?: boolean;
}

export interface TokenDebitResponse {
  success: boolean;
  tokens_balance: number;
  extra_tokens: number;
  credit_limit: number;
}

export function checkTokenBalances(tokens: ChowbotTokens): SubscriptionTokens['data'] {
  const totalAvailable = tokens.tokens_balance + tokens.extra_tokens;
  if (totalAvailable <= tokens.credit_limit) {
    return {
      canDebit: false,
      tokens_balance: tokens.tokens_balance,
      extra_tokens: tokens.extra_tokens,
      credit_limit: tokens.credit_limit,
    }; // Not enough balance within the credit limit
  }
  return {
    canDebit: true,
    tokens_balance: tokens.tokens_balance,
    extra_tokens: tokens.extra_tokens,
    credit_limit: tokens.credit_limit,
  };
}

export function debitTokens(tokens: ChowbotTokens, amount: number): TokenDebitResponse {
  let { tokens_balance, extra_tokens, credit_limit } = tokens;
  let remainingAmount = amount;

  // Check if operation is possible
  if (!checkTokenBalances(tokens)) {
    return {
      success: false,
      tokens_balance: tokens.tokens_balance,
      extra_tokens: tokens.extra_tokens,
      credit_limit: tokens.credit_limit,
    };
  }
  // Debit from tokens_balance first
  if (tokens_balance >= remainingAmount) {
    tokens_balance -= remainingAmount;
    remainingAmount = 0;
  } else {
    remainingAmount -= tokens_balance;
    tokens_balance = 0;
  }

  // Debit from extra_tokens if needed
  if (remainingAmount > 0 && extra_tokens > 0) {
    if (extra_tokens >= remainingAmount) {
      extra_tokens -= remainingAmount;
      remainingAmount = 0;
    } else {
      remainingAmount -= extra_tokens;
      extra_tokens = 0;
    }
  }

  // Allow tokens_balance to go negative within the credit limit boundary
  if (remainingAmount > 0) {
    const potentialBalance = tokens_balance - remainingAmount;
    if (potentialBalance >= credit_limit) {
      tokens_balance = potentialBalance;
      remainingAmount = 0;
    } else {
      // Calculate the maximum amount that can be debited within the credit limit
      let allowableDebit = tokens_balance - credit_limit;
      tokens_balance -= allowableDebit;
      remainingAmount -= allowableDebit;
    }
  }

  return {
    success: remainingAmount === 0,
    tokens_balance,
    extra_tokens,
    credit_limit,
  };
}
