import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateFreeSubscriptionDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  plan: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  plan_option: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  owner: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  store: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  last_payment_reference?: string;
}

export class CreatePaidSubscriptionDto extends CreateFreeSubscriptionDto {}
