import { ApiProperty, PartialType } from '@nestjs/swagger';
import {
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
  IsEmail,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';
import { SUBSCRIPTION_STATUS } from '../../../enums/payment.enum';

class UpdateChowbotTokensDto {
  @ApiProperty()
  @IsNumber()
  @IsOptional()
  tokens_bought?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  tokens_balance?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  extra_tokens?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  credit_limit?: number;

  @ApiProperty({ type: Date })
  @IsDateString()
  @IsOptional()
  last_threshold_notification?: string;
}

export class UpdateSubscriptionDto {
  @ApiProperty()
  @IsString()
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  plan?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  plan_option?: string;

  @ApiProperty({ type: Date })
  @IsDateString()
  @IsOptional()
  last_payment_date?: Date;

  @ApiProperty({ type: Date })
  @IsDateString()
  @IsOptional()
  next_payment_date?: Date;

  @ApiProperty({
    type: 'string',
    enum: [SUBSCRIPTION_STATUS.ACTIVE, SUBSCRIPTION_STATUS.IN_ACTIVE],
  })
  @IsEnum(SUBSCRIPTION_STATUS)
  @IsOptional()
  status?: string;

  @ApiProperty({ type: UpdateChowbotTokensDto })
  @ValidateNested()
  @Type(() => UpdateChowbotTokensDto)
  @IsOptional()
  chowbot_tokens?: UpdateChowbotTokensDto;

  @ApiProperty()
  @IsString()
  @IsOptional()
  last_payment_reference?: string;

  @ApiProperty()
  @IsBoolean()
  @IsNotEmpty()
  new_payment: boolean;
}
