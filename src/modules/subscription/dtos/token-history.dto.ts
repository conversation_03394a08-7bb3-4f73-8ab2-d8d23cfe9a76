import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, ValidateIf, ValidateNested } from 'class-validator';
import { TOKEN_PURCHASE_METHOD } from '../../../enums/payment.enum';
import { Type } from 'class-transformer';
import { Store } from '../../store/store.schema';
import { Subscription } from '../subscription.schema';
import { Branch } from '../../store/branches/branches.schema';

export class TokenHistoryQueryDTO {
  @ApiProperty({
    description: 'Type of history to retrieve (credit or debit)',
    required: true,
    enum: ['credit', 'debit'],
  })
  @IsEnum(['credit', 'debit'])
  type: 'credit' | 'debit';

  @ApiProperty({ description: 'ID of the store', required: false })
  @IsOptional()
  @ValidateIf((o) => !o.branchId)
  @IsNotEmpty({ message: 'Either storeId or branchId must be provided' })
  storeId?: string;

  @ApiProperty({ description: 'ID of the branch', required: false })
  @IsOptional()
  @ValidateIf((o) => !o.storeId)
  @IsNotEmpty({ message: 'Either storeId or branchId must be provided' })
  branchId?: string;
}

class SubscriptionTokenBalanceDto {
  @ApiProperty({ description: 'Number of tokens purchased' })
  @IsNumber()
  tokens_bought: number;

  @ApiProperty({ description: 'Remaining token balance after purchase' })
  @IsNumber()
  tokens_balance: number;

  @ApiProperty({ description: 'Number of extra tokens available' })
  @IsNumber()
  extra_tokens: number;

  @ApiProperty({ description: 'Credit limit for tokens' })
  @IsNumber()
  credit_limit: number;
}

export class CreateTokenPurchaseHistoryDto {
  @ApiProperty({ description: 'ID of the associated payment' })
  @IsString()
  @IsNotEmpty()
  payment_id: string;

  @ApiProperty({ description: 'Number of tokens bought with this transaction' })
  @IsNumber()
  @IsNotEmpty()
  tokens_bought: number;

  @ApiProperty({ description: 'Method of subscription', enum: TOKEN_PURCHASE_METHOD })
  @IsEnum(TOKEN_PURCHASE_METHOD)
  method: string;

  @ApiProperty({ description: 'Token balance at the time of purchase', type: SubscriptionTokenBalanceDto })
  @ValidateNested()
  @Type(() => SubscriptionTokenBalanceDto)
  balance_at_purchase: SubscriptionTokenBalanceDto;

  @ApiProperty({ description: 'Price per token' })
  @IsNumber()
  @IsNotEmpty()
  price_per_token: number;

  @ApiProperty({ description: 'ID of the owner user' })
  @IsString()
  @IsNotEmpty()
  owner: string;

  @ApiProperty({ description: 'ID of the branch', required: false })
  @IsString()
  branch?: Branch | string;

  @ApiProperty({ description: 'ID of the store', required: false })
  @IsString()
  store?: Store | string;

  @ApiProperty({ description: 'ID of the associated subscription', required: false })
  @IsString()
  subscription?: Subscription | string;
}
