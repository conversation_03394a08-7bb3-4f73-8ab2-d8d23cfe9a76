import { Injectable, Inject, CACHE_MANAGER } from '@nestjs/common';
import { Cache } from 'cache-manager';
import { Mutex } from 'async-mutex';

@Injectable()
export class CacheService {
  private mutexes: Map<string, Mutex>;

  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {
    this.mutexes = new Map();
  }

  private getMutex(key: string): Mutex {
    if (!this.mutexes.has(key)) {
      this.mutexes.set(key, new Mutex());
    }
    return this.mutexes.get(key);
  }

  async setWithLock(key: string, value: any, ttl?: number) {
    const mutex = this.getMutex(key);
    const release = await mutex.acquire();
    try {
      await this.cacheManager.set(key, value, { ttl });
    } finally {
      release();
    }
  }

  async incrementWithLock(key: string, value: number, ttl?: number) {
    const mutex = this.getMutex(key);
    const release = await mutex.acquire();
    try {
      const currentValue = (await this.cacheManager.get<number>(key)) ?? 0;
      const newValue = currentValue + value;
      await this.cacheManager.set(key, newValue < 0 ? 0 : newValue, { ttl });
    } finally {
      release();
    }
  }

  async get<T>(key: string): Promise<T> {
    return await this.cacheManager.get<T>(key);
  }
}
