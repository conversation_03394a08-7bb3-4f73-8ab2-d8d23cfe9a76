import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { CURRENCIES } from '../../country/country.schema';
import { Document } from 'mongoose';

export type CurrencyRatesDocument = CurrencyRates & Document;

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class CurrencyRates {
  @ApiProperty({
    description: 'All currency rates mapped by base currency',
    example: {
      NGN: {
        USD: 0.0026,
        EUR: 0.0022,
        GBP: 0.0019,
      },
      KES: {
        NGN: 3.57,
        USD: 0.0093,
        EUR: 0.0078,
        GBP: 0.0068,
      },
      // ... other base currencies
    },
  })
  @Prop({
    type: Map,
    of: {
      type: Map,
      of: Number,
    },
    required: true,
    default: {},
  })
  rates: {
    [baseCurrency in CURRENCIES]?: {
      [targetCurrency in CURRENCIES]?: number;
    };
  };
}

export const CurrencyRatesSchema = SchemaFactory.createForClass(CurrencyRates);
