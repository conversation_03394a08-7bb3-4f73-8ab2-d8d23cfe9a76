import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import mongoose, { Document } from 'mongoose';
import { Wallet } from '../wallet.schema';
import { CURRENCIES } from '../../country/country.schema';

export enum ConversionStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
}
export type ConversionDocument = Conversion & Document;

export class ConversionMeta {
  @ApiProperty()
  @Prop({ type: String })
  fincra_quote_id?: string;

  @ApiProperty()
  @Prop({ type: String })
  fincra_conversion_id?: string;
}

@Schema({ timestamps: true })
export class Conversion {
  public id: string;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Wallet', required: true })
  from_wallet: string | Wallet;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Wallet', required: true })
  to_wallet: string | Wallet;

  @ApiProperty()
  @Prop({
    type: String,
    enum: Object.values(CURRENCIES),
    required: true,
  })
  from_currency: CURRENCIES;

  @ApiProperty()
  @Prop({
    type: String,
    enum: Object.values(CURRENCIES),
    required: true,
  })
  to_currency: CURRENCIES;

  @ApiProperty()
  @Prop({ type: Number, required: true })
  amount: number; // Amount in from_currency

  @ApiProperty()
  @Prop({ type: Number, required: true })
  amount_charged: number; // Amount in from_currency

  @ApiProperty()
  @Prop({ type: Number, required: true })
  destination_amount: number; // Amount in to_currency

  @ApiProperty()
  @Prop({ type: Number, required: true })
  rate: number; // Exchange rate used

  @ApiProperty()
  @Prop({ type: Number, required: true })
  provider_rate: number; // Provider rate

  @ApiProperty()
  @Prop({ type: Number, required: true })
  markup_from_rate: number; // Provider rate

  @ApiProperty()
  @Prop({ type: Number, required: true })
  fee: number;

  @ApiProperty()
  @Prop({ type: String, required: true, unique: true })
  reference: string; // Unique reference ID

  @ApiProperty()
  @Prop({
    type: String,
    enum: Object.values(ConversionStatus),
    default: ConversionStatus.PENDING,
  })
  status: ConversionStatus;

  @ApiProperty()
  @Prop({ type: String })
  failure_reason?: string;

  @ApiProperty()
  @Prop({ type: ConversionMeta })
  meta: ConversionMeta;
}

export const ConversionSchema = SchemaFactory.createForClass(Conversion);
