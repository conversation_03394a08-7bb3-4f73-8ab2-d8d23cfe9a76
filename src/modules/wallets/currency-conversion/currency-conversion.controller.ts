import {
  Controller,
  Post,
  Body,
  Logger,
  BadRequestException,
  UseGuards,
  Req,
  Get,
  Param,
  ParseArrayPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiSecurity, ApiParam } from '@nestjs/swagger';
import { CurrencyConversionService } from './currency-conversion.service';
import { GenerateQuoteDto } from '../dtos/generate-quote.dto';
import { InitiateConversionDto } from '../dtos/initiate-conversion.dto';
import { PlanPermissions, SCOPES } from '../../../utils/permissions.util';
import { JwtAuthGuard } from '../../../jwt/jwt-auth.guard';
import { RoleGuard } from '../../../guards/role.guard';
import { RolePermissions } from '../../../decorators/permission.decorator';
import { IRequest } from '../../../interfaces/request.interface';
import { IGenerateQuotePayload } from '../../../repositories/fincra.repository';
import { CURRENCIES } from '../../country/country.schema';
import { CurrencyRates } from './currency-rate.schema';
import { InternalApiJWTGuard } from '../../../guards/api.guard';

@ApiTags('Currency Conversion')
@ApiSecurity('bearer')
@Controller('currency-conversion')
export class CurrencyConversionController {
  private readonly logger = new Logger(CurrencyConversionController.name);

  constructor(private readonly currencyConversionService: CurrencyConversionService) {}

  @Get(':base_currency')
  @ApiOperation({ summary: 'Get conversion rates for a base currency' })
  @ApiParam({
    name: 'base_currency',
    enum: CURRENCIES,
    description: 'The base currency code',
    example: 'USD',
  })
  @ApiResponse({
    status: 200,
    description: 'Currency rates retrieved successfully',
    type: CurrencyRates,
  })
  @ApiResponse({ status: 404, description: 'Currency rates not found' })
  async getRatesForCurrency(@Param('base_currency') base_currency: CURRENCIES) {
    const data = await this.currencyConversionService.getRatesForCurrency(base_currency);
    return {
      message: 'Currency rates retrieved successfully',
      data,
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all conversion rates' })
  @ApiResponse({
    status: 200,
    description: 'Currency rates retrieved successfully',
    type: CurrencyRates,
  })
  @ApiResponse({ status: 404, description: 'Currency rates not found' })
  async getAllRates() {
    const data = await this.currencyConversionService.getAllRates();
    return {
      message: 'Currency rates retrieved successfully',
      data,
    };
  }

  @Post('generate-quote')
  @ApiOperation({ summary: 'Generate a quote for currency conversion' })
  @ApiResponse({ status: 200, description: 'Quote generated successfully.' })
  @ApiResponse({ status: 400, description: 'Invalid request payload or parameters.' })
  @RolePermissions(SCOPES.WALLETS.CAN_MAKE_CONVERSION)
  @UseGuards(JwtAuthGuard, RoleGuard)
  async generateQuote(@Req() req: IRequest, @Body() generateQuoteDto: GenerateQuoteDto) {
    const storeId = req.user.store.id;
    const data = await this.currencyConversionService.generateQuote(generateQuoteDto, storeId);
    return {
      message: 'Quote generated successfully',
      data,
    };
  }

  @Post('initiate-conversion')
  @ApiOperation({ summary: 'Initiate a currency conversion' })
  @ApiResponse({ status: 200, description: 'Conversion initiated successfully.' })
  @ApiResponse({ status: 400, description: 'Invalid request payload or parameters.' })
  @RolePermissions(SCOPES.WALLETS.CAN_MAKE_CONVERSION)
  @UseGuards(JwtAuthGuard, RoleGuard)
  async initiateConversion(@Req() req: IRequest, @Body() initiateConversionDto: InitiateConversionDto) {
    const storeId = req.user.store.id;
    const data = await this.currencyConversionService.initiateConversion({
      storeId,
      amount: initiateConversionDto.amount,
      quoteReference: initiateConversionDto.quote_reference,
      securityPin: initiateConversionDto.security_pin,
      sourceWallet: initiateConversionDto.from_wallet,
      destinationWallet: initiateConversionDto.to_wallet,
    });

    return {
      message: 'Conversion initiated successfully',
      data,
    };
  }

  @Post('/failed-conversion/:conversion_id')
  @ApiOperation({ summary: 'Handle a failed currency conversion' })
  @ApiResponse({ status: 200, description: 'Failed conversion handled successfully.' })
  @ApiResponse({ status: 400, description: 'Invalid request payload or parameters.' })
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async handleFailedConversion(@Param('conversion_id') conversion_id: string) {
    const data = await this.currencyConversionService.handleConversionFailure(conversion_id, 'Something went wrong');

    return {
      message: 'Failed conversion handled successfully',
      data,
    };
  }
}
