import {
  Injectable,
  Inject,
  CACHE_MANAGER,
  <PERSON><PERSON>,
  BadRequestException,
  NotFoundException,
  HttpException,
} from '@nestjs/common';
import { Cache } from 'cache-manager';
import { Conversion, ConversionDocument, ConversionStatus } from './conversion.schema';
import {
  FincraRepository,
  IGenerateQuotePayload,
  IGenerateQuoteResponse,
  IInitiateConversionPayload,
} from '../../../repositories/fincra.repository';
import { WalletService } from '../wallet.service';
import { InjectModel } from '@nestjs/mongoose';
import { TRANSACTION_CHANNELS, Wallet, WalletDocument } from '../wallet.schema';
import { Model, PaginateModel } from 'mongoose';
import { CURRENCIES } from '../../country/country.schema';
import { delay, getDocId, isPairSupported, toKobo, toNaira } from '../../../utils/functions';
import { GenerateQuoteDto } from '../dtos/generate-quote.dto';
import { conversionFeeCalculator, rateMarkdownCalculator } from '../../../utils/fees';
import { InjectQueue } from '@nestjs/bull';
import { JOBS, QUEUES } from '../../../enums/queues.enum';
import { Queue } from 'bull';
import { WalletJob } from '../wallet.queue';
import { Store } from '../../store/store.schema';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { User } from '../../user/user.schema';
import { CurrencyRates, CurrencyRatesDocument } from './currency-rate.schema';
import { Cron, CronExpression } from '@nestjs/schedule';
import { toCurrency, isUsingProdDBLocally } from '../../../utils';
import { Mutex } from 'async-mutex';
import { ONE_MINUTE_IN_SECONDS } from '../../../utils/constants';
import { NOTIFICATION_TYPE } from '../../user/notifications/notification.schema';

@Injectable()
export class CurrencyConversionService {
  private mutex: Mutex;
  constructor(
    private readonly fincraRepository: FincraRepository,
    private readonly walletService: WalletService,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
    @InjectModel(Wallet.name)
    public readonly walletModel: PaginateModel<WalletDocument>,
    @InjectModel(Conversion.name)
    public readonly conversionModel: PaginateModel<ConversionDocument>,
    @InjectQueue(QUEUES.WALLET)
    public readonly walletQueue: Queue<WalletJob>,
    @InjectModel(CurrencyRates.name)
    private readonly currencyRatesModel: Model<CurrencyRatesDocument>,
    private readonly logger: Logger,
  ) {
    this.mutex = new Mutex();
  }

  async generateQuote(data: GenerateQuoteDto, storeId: string) {
    const payload: IGenerateQuotePayload = {
      sourceCurrency: data.source_currency,
      destinationCurrency: data.destination_currency,
      amount: data.amount.toString(),
    };
    const response = await this.fincraRepository.generateQuote(payload);

    if (response.error) {
      this.logger.error(response.error, 'Error generating quote');
      throw new BadRequestException(`Error generating quote: ${response.error}`);
    }

    const quoteData = response.data.data;

    // Apply fee and rate markdown
    const fee = conversionFeeCalculator(quoteData.sourceAmount);
    const markedDownRate = quoteData.rate - rateMarkdownCalculator(quoteData.rate);

    const amountToCharge = quoteData.sourceAmount + fee;
    const amountToReceive = quoteData.sourceAmount * markedDownRate;

    // Even though `quoteData.expireAt` might be shorter,
    // we decide to keep our cache for 5 minutes:
    const ttl = ONE_MINUTE_IN_SECONDS * 5; // 5 minutes in seconds

    // Calculate TTL based on quote expiration
    // const expireAt = new Date(quoteData.expireAt);
    // const ttl = Math.floor((expireAt.getTime() - Date.now()) / 1000);

    // Cache the quote using the quote reference as the key
    const cacheKey = `conversion_quote:${quoteData.reference}`;
    const cachedQuoteData = {
      sourceCurrency: quoteData.sourceCurrency,
      destinationCurrency: quoteData.destinationCurrency,
      sourceAmount: quoteData.sourceAmount,
      fee: fee,
      rate: markedDownRate,
      amountToCharge: amountToCharge,
      amountToReceive: amountToReceive,
      reference: quoteData.reference,
      expireAt: quoteData.expireAt,
      originalRate: quoteData.rate,
    };
    await this.cacheManager.set(cacheKey, cachedQuoteData, { ttl });

    this.logger.log(`Quote cached with key ${cacheKey} for ${ttl} seconds`);

    return { ...cachedQuoteData, originalRate: undefined };
  }

  async initiateConversion(data: {
    storeId: string;
    amount: number;
    quoteReference: string;
    securityPin: string;
    sourceWallet: string;
    destinationWallet: string;
  }) {
    const { storeId, amount, quoteReference, securityPin, sourceWallet, destinationWallet } = data;

    // 1. Validate security pin
    let securityPinMatch: boolean;
    try {
      securityPinMatch = await this.walletService.brokerTransport
        .send<boolean>(BROKER_PATTERNS.STORE.VERIFY_SECURITY_PIN, {
          store_id: storeId,
          security_pin: securityPin,
        })
        .toPromise();
    } catch (e) {
      throw new BadRequestException(e);
    }
    if (!securityPinMatch) {
      throw new BadRequestException('Invalid security pin');
    }

    // 2. Retrieve the quote from cache
    const cacheKey = `conversion_quote:${quoteReference}`;
    const cachedQuote = await this.cacheManager.get<IGenerateQuoteResponse['data']>(cacheKey);

    // If quote is not in cache, it means it's truly "expired/not found" from our perspective.
    // We won't attempt a new quote because we have no old data to compare to.
    if (!cachedQuote) {
      // Return whichever HttpException or status code you prefer
      throw new HttpException('Quote not found or has expired.', 499);
    }

    // 3. Retrieve and validate wallets
    const fromWallet = await this.walletModel.findOne({ _id: sourceWallet }).lean();
    const toWallet = await this.walletModel.findOne({ _id: destinationWallet }).lean();
    if (!fromWallet || !toWallet) {
      throw new NotFoundException('Invalid wallets provided.');
    }

    if (fromWallet.currency !== cachedQuote.sourceCurrency || toWallet.currency !== cachedQuote.destinationCurrency) {
      throw new BadRequestException('Currency mismatch with the quote.');
    }

    // 4. Check balance
    const amountToCharge = Number(cachedQuote.amountToCharge);
    if (fromWallet.balance < toKobo(amountToCharge)) {
      if (fromWallet.balance > toKobo(cachedQuote.sourceAmount)) {
        throw new BadRequestException('Insufficient balance for the conversion fee.');
      }

      throw new BadRequestException('Insufficient balance in the source wallet.');
    }

    // 5. Validate the amount
    if (Number(cachedQuote.sourceAmount) !== amount) {
      throw new BadRequestException('Amount does not match the quote.');
    }

    // 6. Create a Conversion record with pending status (before calling Fincra)
    const conversionReference = this.generateUniqueReference();
    const conversion = new this.conversionModel({
      from_wallet: getDocId(fromWallet),
      to_wallet: getDocId(toWallet),
      from_currency: fromWallet.currency,
      to_currency: toWallet.currency,
      amount: toKobo(amount),
      fee: toKobo(cachedQuote.fee),
      amount_charged: toKobo(amountToCharge),
      destination_amount: toKobo(cachedQuote.amountToReceive),
      rate: cachedQuote.rate,
      reference: conversionReference,
      status: ConversionStatus.PENDING,
      provider_rate: cachedQuote.originalRate,
      markup_from_rate: toKobo(cachedQuote.originalRate * amount - cachedQuote.rate * amount),
      meta: { fincra_quote_id: quoteReference },
    });
    await conversion.save();

    // 7. Debit the user's wallet
    await this.walletService.debitHandler(
      getDocId(fromWallet),
      conversion.amount,
      TRANSACTION_CHANNELS.CONVERSION,
      `Converting ${toCurrency(toNaira(conversion.amount), fromWallet.currency)} to ${toWallet.currency}`,
      { conversion_reference: conversionReference },
      { name: 'Currency conversion', account_number: '', bank: '', purpose: 'Conversion', method: '' },
      conversion.fee,
    );

    // 8. Attempt to initiate the conversion with Fincra
    let fincraResponse: any;

    const fincraPayload: IInitiateConversionPayload = {
      quoteReference, // old quote reference
      customerReference: conversionReference,
    };

    try {
      fincraResponse = await this.fincraRepository.initiateCurrencyConversion(fincraPayload);

      if (fincraResponse.error) {
        const error = fincraResponse.error;
        // If the error is a 422 from Fincra saying "Quote does not exist or has expired"
        // then we attempt to re-quote
        const status = error?.status;
        const errorMsg = error?.data?.error;

        if (status === 422 && errorMsg.toLowerCase().includes('expired')) {
          this.logger.warn(`Fincra quote expired. Attempting re-quote...`);

          // 9a. Use cached data to re-generate a new quote:
          const reQuotePayload: GenerateQuoteDto = {
            source_currency: cachedQuote.sourceCurrency,
            destination_currency: cachedQuote.destinationCurrency,
            amount: Number(cachedQuote.sourceAmount),
          };

          const newQuote = await this.generateQuote(reQuotePayload, storeId);

          // 9b. Compare newQuote's rate to cachedQuote's rate
          if (Math.abs(newQuote.rate - cachedQuote.rate) < 0.000001) {
            // If rates are effectively the same, adopt the new quote
            this.logger.log(`Rate unchanged. Proceeding with new quote reference: ${newQuote.reference}.`);

            // Optionally update the conversion doc to hold new meta or rate:
            conversion.rate = newQuote.rate;
            conversion.meta.fincra_quote_id = newQuote.reference; // new quote
            await conversion.save();

            // Then re-initiate the conversion with the new quote reference
            const newFincraPayload: IInitiateConversionPayload = {
              quoteReference: newQuote.reference, // use new quote
              customerReference: conversionReference, // same conversion reference
            };

            fincraResponse = await this.fincraRepository.initiateCurrencyConversion(newFincraPayload);

            if (fincraResponse.error) {
              throw 'Conversion initiation failed, please try again!';
            }
          } else {
            throw 'Rate has changed. Please request a new conversion.';
          }
        } else {
          // Handle other types of errors from Fincra
          this.logger.error(`Fincra error: ${JSON.stringify(error)}`);
          throw `Conversion failed: ${errorMsg || 'Unknown error'}`;
        }
      }
    } catch (error) {
      // If the rate changed, throw an appropriate error
      this.logger.warn(`Rate changed from ${cachedQuote.rate}. Conversion aborted.`);
      // Optionally reverse the debit if needed
      await this.handleConversionFailure(conversion.id, error);
      throw new BadRequestException(error);
    }

    // 10. Save final fincra conversion details
    conversion.meta = {
      ...(conversion.meta ?? {}),
      fincra_conversion_id: fincraResponse?.data?.data?.id?.toString(),
    };

    await conversion.save();

    return conversion;
  }

  private generateUniqueReference(): string {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `CLGC-${timestamp}-${randomString}`;
  }

  public async handleConversionFailure(conversionId: string, failureReason: string) {
    const conversion = await this.conversionModel.findById(conversionId);

    if (conversion.status !== ConversionStatus.PENDING) {
      throw new BadRequestException('Conversion is not pending');
    }

    // Update conversion status to FAILED
    await this.conversionModel.updateOne(
      { _id: conversionId },
      { $set: { status: ConversionStatus.FAILED, failure_reason: failureReason } },
    );

    //@dan can we add a retry mechanism to this function? if the conversion fails can we check if it's because of an expired quote - fetch a new one, compare the rates and retry the conversion
    await this.walletService.creditHandler(
      conversion.from_wallet as string,
      conversion.amount_charged,
      0,
      TRANSACTION_CHANNELS.REVERSAL,
      `Reversal for conversion of ${toCurrency(toNaira(conversion.amount), conversion.from_currency)} to ${
        conversion.to_currency
      }`,
      { conversion_reference: conversion.reference },
      { name: '', account_number: '', bank: '', purpose: 'Conversion', method: '' },
    );

    await this.sendConversionFailedNotification(conversion);
  }

  async handleFincraWebhookEvent(payload: any) {
    const { event, data } = payload;

    if (!event.startsWith('conversion')) {
      throw new BadRequestException('Invalid event type.');
    }

    const conversion = await this.conversionModel.findOne({ 'meta.fincra_conversion_id': data.id.toString() });
    if (!conversion) {
      throw new NotFoundException('Conversion record not found.');
    }

    if (event === 'conversion.successful' && data.status === 'successful') {
      await this.walletService.creditHandler(
        conversion.to_wallet as string,
        conversion.destination_amount,
        0,
        TRANSACTION_CHANNELS.CONVERSION,
        `Conversion from ${toCurrency(toNaira(conversion.amount), conversion.from_currency)}`,
        { conversion_reference: data.reference },
        { name: '', account_number: '', bank: '', purpose: 'Conversion', method: '' },
      );

      await this.conversionModel.updateOne(
        { _id: getDocId(conversion) },
        { $set: { status: ConversionStatus.COMPLETED, settled_at: data.settledAt } },
      );

      await this.sendConversionSuccessNotification(conversion, conversion.destination_amount);

      this.logger.log(`Successfully credited wallet for conversion ${data.reference}`);
    } else {
      // Handle failure
      await this.handleConversionFailure(conversion._id, 'Webhook event status not successful');
    }
  }

  private async sendConversionSuccessNotification(conversion: ConversionDocument, amountReceived: number) {
    // Fetch the destination wallet
    const toWallet = await this.walletModel.findById(conversion.to_wallet);

    if (!toWallet) {
      this.logger.warn('Destination wallet not found for sending notification');
      return;
    }

    // Get the store and owner information
    const store = await this.walletService.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { wallet: getDocId(toWallet) })
      .toPromise();

    if (!store) {
      this.logger.warn('Store not found for sending notification');
      return;
    }

    const owner = await this.walletService.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, { _id: getDocId(store.owner) })
      .toPromise();

    if (!owner) {
      this.logger.warn('Owner not found for sending notification');
      return;
    }

    // Send email
    await this.walletService.resend.sendEmail(BROKER_PATTERNS.MAIL.CONVERSION_SUCCESSFUL, {
      to: owner.email,
      subject: `Your conversion is complete 🎉`,
      data: {
        name: owner.name.split(' ')[0],
        source_amount: toCurrency(toNaira(conversion.amount), conversion.from_currency),
        source_currency: conversion.from_currency,
        destination_amount: toCurrency(toNaira(amountReceived), conversion.to_currency),
        destination_currency: conversion.to_currency,
        fee: toCurrency(toNaira(conversion.fee), conversion.from_currency),
        date: new Date().toDateString(),
        destination_balance: toCurrency(toNaira(toWallet.balance), conversion.to_currency),
      },
    });

    // Send push notification
    await this.walletService.brokerTransport
      .emit(BROKER_PATTERNS.USER.SEND_PUSH_NOTIFICATION, {
        message: {
          title: `Conversion of ${toCurrency(toNaira(conversion.amount), conversion.from_currency)} to ${
            conversion.to_currency
          } complete 🎉`,
          message: `${toCurrency(toNaira(amountReceived), conversion.to_currency)} has been credited to your ${
            conversion.to_currency
          } wallet`,
          path: `/dashboard`,
        },
        owner_only: true,
        store: store.id,
        notification_type: NOTIFICATION_TYPE.CONVERSION,
        data: {
          id: conversion._id,
          store_id: store.id,
          source_amount: toCurrency(toNaira(conversion.amount), conversion.from_currency),
          destination_currency: conversion.to_currency,
        },
      })
      .toPromise();
  }

  async sendConversionFailedNotification(conversion: ConversionDocument) {
    // Fetch the destination wallet
    const sourceWallet = await this.walletModel.findById(conversion.from_wallet);

    if (!sourceWallet) {
      this.logger.warn('Destination wallet not found for sending notification');
      return;
    }

    // Get the store and owner information
    const store = await this.walletService.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: getDocId(sourceWallet.store) })
      .toPromise();

    if (!store) {
      this.logger.warn('Store not found for sending notification');
      return;
    }

    const owner = await this.walletService.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, { _id: getDocId(store.owner) })
      .toPromise();

    if (!owner) {
      this.logger.warn('Owner not found for sending notification');
      return;
    }

    // Send email
    await this.walletService.resend.sendEmail(BROKER_PATTERNS.MAIL.CONVERSION_FAILED, {
      to: owner.email,
      subject: 'Your conversion has failed 😢',
      data: {
        name: owner.name.split(' ')[0],
        source_amount: toCurrency(toNaira(conversion.amount), conversion.from_currency),
        destination_currency: conversion.to_currency,
        remaining_balance: toCurrency(toNaira(sourceWallet.balance), conversion.from_currency),
      },
    });

    // Send push notification
    await this.walletService.brokerTransport
      .emit(BROKER_PATTERNS.USER.SEND_PUSH_NOTIFICATION, {
        message: {
          title: `Conversion Failed ❌`,
          message: `Your conversion of ${toCurrency(toNaira(conversion.amount), conversion.from_currency)} to ${
            conversion.to_currency
          } failed. Please try again.`,
          path: `/dashboard`,
        },
        owner_only: true,
        store: store.id,
        notification_type: NOTIFICATION_TYPE.CONVERSION,
        data: {
          id: conversion._id,
          store_id: store.id,
          source_amount: toCurrency(toNaira(conversion.amount), conversion.from_currency),
          destination_currency: conversion.to_currency,
        },
      })
      .toPromise();
  }

  async getRatesForCurrency(baseCurrency: CURRENCIES): Promise<Partial<Record<CURRENCIES, number>>> {
    // Fetch the latest CurrencyRates document as a plain object
    const latestCurrencyRates = await this.currencyRatesModel.findOne().sort({ created_at: -1 }).lean().exec();

    if (!latestCurrencyRates) {
      this.logger.warn('No currency rates found in the database');
      throw new NotFoundException('No currency rates available');
    }

    // Extract the rates for the specified base currency
    const ratesForBase = latestCurrencyRates.rates[baseCurrency];

    if (!ratesForBase || Object.keys(ratesForBase).length === 0) {
      this.logger.warn(`Currency rates not found for base currency: ${baseCurrency}`);
      throw new NotFoundException(`Currency rates not found for base currency: ${baseCurrency}`);
    }

    return ratesForBase;
  }

  async getAllRates(): Promise<CurrencyRates> {
    return await this.currencyRatesModel.findOne().sort({ created_at: -1 }).lean().exec();
  }

  async getRatesById(id: string): Promise<CurrencyRates> {
    return await this.currencyRatesModel.findById(id).lean().exec();
  }

  @Cron(CronExpression.EVERY_30_MINUTES)
  async updateCurrencyRates() {
    if (this.mutex.isLocked() || isUsingProdDBLocally()) return;

    await this.mutex.acquire();

    try {
      this.logger.log('Starting updateCurrencyRates job');

      const currencies = Object.values(CURRENCIES) as CURRENCIES[];

      // Use Partial to allow some currencies to be missing initially
      const aggregatedRates: Partial<Record<CURRENCIES, Partial<Record<CURRENCIES, number>>>> = {};

      for (const baseCurrency of currencies) {
        const rates: Partial<Record<CURRENCIES, number>> = {};

        for (const quoteCurrency of currencies) {
          if (!isPairSupported(baseCurrency, quoteCurrency)) {
            continue;
          }

          const payload: IGenerateQuotePayload = {
            sourceCurrency: baseCurrency,
            destinationCurrency: quoteCurrency,
            amount: '1',
          };

          try {
            const response = await this.fincraRepository.generateQuote(payload);

            if (response.error) {
              this.logger.error(`Error generating quote for ${baseCurrency} to ${quoteCurrency}: ${response.error}`);
              continue;
            }

            const quoteData = response.data.data;
            const rate = quoteData.rate;

            rates[quoteCurrency] = rate;
          } catch (error) {
            this.logger.error(`Exception generating quote for ${baseCurrency} to ${quoteCurrency}: ${error.message}`);
          }
        }

        if (Object.keys(rates).length > 0) {
          aggregatedRates[baseCurrency] = rates;
          this.logger.log(`Collected rates for base currency ${baseCurrency}`);
        } else {
          this.logger.warn(`No rates found for base currency ${baseCurrency}`);
        }
      }

      if (Object.keys(aggregatedRates).length > 0) {
        try {
          const newCurrencyRates = new this.currencyRatesModel({
            rates: aggregatedRates,
          });

          await newCurrencyRates.save();
          this.logger.log('Saved new CurrencyRates document');
        } catch (error) {
          this.logger.error(`Failed to save CurrencyRates document: ${error.message}`);
        }
      } else {
        this.logger.warn('No rates to save');
      }

      this.logger.log('Completed updateCurrencyRates job');
    } catch (error) {
      this.logger.error(`Error updating currency rates: ${error.message}`);
    } finally {
      this.mutex.release();
    }
  }
}
