import { BadRequestException } from '@nestjs/common';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { blocWithdrawalFeeCalculator, paystackTranferFeeCalculator } from '../../utils/fees';
import { Store } from '../store/store.schema';
import { User } from '../user/user.schema';
import { PaginatedQueryDto, ValidateWithdrawalsDto } from './dtos/search.dto';
import { TRANSACTION_CHANNELS, TRANSACTION_TYPE } from './wallet.schema';
import { WalletService } from './wallet.service';
import { WITHDRAWAL_PROVIDERS, WITHDRAWAL_STATUSES, WithdrawalAccount } from './wallet.withdrawal.schema';
import { SquadTxWebhookData, TransactionData } from '../../repositories/squad.repository';
import { toCurrency } from '../../utils';

export class WalletResolutionsService extends WalletService {
  async triggerFailedSquadWebhooks() {
    const requestData = await this.squad.getFailedWebhooks();

    if (requestData.error) {
      throw new BadRequestException("Couldn't fetch transactions");
    }

    const transactionsWithinRange = requestData.data;
    const transactionReferences = transactionsWithinRange.map((t) => t.payload.transaction_reference);
    let missingTransactions: SquadTxWebhookData[] = [];

    //look through db for references that don't exist
    const foundTransactionReferences = (
      await this.transactionModel.find(
        { 'meta.squad_transaction_id': { $in: transactionReferences } },
        'meta.squad_transaction_id',
      )
    ).map((t) => t.meta.squad_transaction_id);

    if (foundTransactionReferences.length !== transactionReferences.length) {
      missingTransactions = transactionsWithinRange.filter(
        ({ payload }) => !foundTransactionReferences.includes(payload.transaction_reference),
      );
      // missingTxReferences = transactionReferences.filter();

      console.log('Missing transactions', missingTransactions);

      try {
        const webhookRequests = await Promise.all(
          missingTransactions.map((t) => {
            const payload = t.payload;
            const { hash, ...dataToSend } = payload;
            this.squad.triggerSquadWebhook(dataToSend);
          }),
        );

        const deleteRequests = await Promise.all(
          missingTransactions.map((t) => this.squad.deleteTxFromFailedWebhooks(t.payload.transaction_reference)),
        );
      } catch (e) {
        throw new BadRequestException("Couldn't trigger webhooks");
      }
    }

    return { missingTransactions };
  }

  async triggerMissingSquadWebhooks(filter: { references: string[] }) {
    let requestData: TransactionData[] = [];

    try {
      const responses = await Promise.all(filter.references.map((r) => this.squad.getTransactionsByReference(r)));
      requestData = responses.map((r) => r?.data).flat();
    } catch (e) {
      throw new BadRequestException("Couldn't fetch one or more transactions");
    }

    // if (requestData.error) {
    //   throw new BadRequestException("Couldn't fetch transactions");
    // }

    const transactionsWithinRange = requestData;
    const transactionReferences = transactionsWithinRange.map((t) => t.transaction_reference);
    let missingTransactions: TransactionData[] = [];

    //look through db for references that don't exist
    const foundTransactionReferences = (
      await this.transactionModel.find(
        { 'meta.squad_transaction_id': { $in: transactionReferences } },
        'meta.squad_transaction_id',
      )
    ).map((t) => t.meta.squad_transaction_id);

    if (foundTransactionReferences.length !== transactionReferences.length) {
      missingTransactions = transactionsWithinRange.filter(
        ({ transaction_reference }) => !foundTransactionReferences.includes(transaction_reference),
      );
      // missingTxReferences = transactionReferences.filter();

      console.log('Missing transactions', missingTransactions);

      try {
        const webhookRequests = await Promise.all(
          missingTransactions.map((t) =>
            this.squad.triggerSquadWebhook({ ...t, customer_identifier: t.customer.customer_identifier }),
          ),
        );
      } catch (e) {
        throw new BadRequestException("Couldn't trigger webhooks");
      }
    }

    return { missingTransactions };
  }

  async reprocessFailedWithdrawal(requestId: string, reference: string) {
    const request = await this.withdrawalRequestModel.findById(requestId).populate('withdrawal_account');

    if (!request) {
      throw new BadRequestException('Request not found');
    }

    const wallet = await this.walletModel.findOneAndUpdate(
      {
        _id: request.wallet,
        balance: {
          $gte: request.amount + request.fee,
        },
      },
      {
        $inc: {
          balance: -(request.amount + request.fee),
        },
      },
      { new: true },
    );

    if (!request || !wallet) throw new Error('Welp');

    request.fee = request.fee;
    request.status = WITHDRAWAL_STATUSES.SUCCESSFUL;
    request.vendorReference = reference;
    request.gateway_fee =
      request.provider === WITHDRAWAL_PROVIDERS.PAYSTACK
        ? paystackTranferFeeCalculator(request.amount, request.currency)
        : blocWithdrawalFeeCalculator(request.amount);
    await request.save();

    new this.transactionModel({
      amount: request.amount,
      type: TRANSACTION_TYPE.DEBIT,
      channel: TRANSACTION_CHANNELS.WITHDRAWAL,
      narration: `Withdrawal Refund`,
      balance_before: wallet.balance + request.amount,
      reference: reference,
      wallet: wallet._id,
      meta: {
        withdrawal_request: request._id,
      },
      source: {
        name: (request?.withdrawal_account as WithdrawalAccount).account_name,
        account_number: (request?.withdrawal_account as WithdrawalAccount).account_number,
        method: 'Paystack',
        purpose: 'Withdrawal',
      },
      fee: request.fee,
      currency: wallet.currency,
    }).save();
  }

  async partiallyDebitFailedWithdrawal(requestId: string, reference: string, amount: number) {
    const request = await this.withdrawalRequestModel.findById(requestId).populate('withdrawal_account');

    if (!request) {
      throw new BadRequestException('Request not found');
    }

    const owedAmount = request.amount + request.fee;
    const amountToPay = Math.min(amount * 100, owedAmount - (request?.partial_debit_amount ?? 0));

    if (amountToPay <= 0) {
      throw new BadRequestException('Payment previously fully made');
    }

    const wallet = await this.walletModel.findOneAndUpdate(
      {
        _id: request.wallet,
        balance: {
          $gte: amountToPay,
        },
      },
      {
        $inc: {
          balance: -amountToPay,
        },
      },
      { new: true },
    );

    if (!wallet) throw new Error('Not enough wallet balance');

    request.partial_debit_amount = (request.partial_debit_amount ?? 0) + amountToPay;

    let hasFullyRepaid = request.partial_debit_amount === owedAmount;

    request.status = hasFullyRepaid ? WITHDRAWAL_STATUSES.SUCCESSFUL : WITHDRAWAL_STATUSES.PENDING;
    request.vendorReference = reference;
    request.gateway_fee =
      request.provider === WITHDRAWAL_PROVIDERS.PAYSTACK
        ? paystackTranferFeeCalculator(request.amount, request.currency)
        : blocWithdrawalFeeCalculator(request.amount);
    await request.save();

    new this.transactionModel({
      amount: amountToPay,
      type: TRANSACTION_TYPE.DEBIT,
      channel: TRANSACTION_CHANNELS.WITHDRAWAL,
      narration: `Partial Refund for withdrawal of ${toCurrency(request.amount / 100, wallet.currency)}`,
      balance_before: wallet.balance + amountToPay,
      reference: reference,
      wallet: wallet._id,
      meta: {
        withdrawal_request: request._id,
      },
      source: {
        name: (request?.withdrawal_account as WithdrawalAccount).account_name,
        account_number: (request?.withdrawal_account as WithdrawalAccount).account_number,
        method: 'Paystack',
        purpose: 'Withdrawal',
      },
      fee: hasFullyRepaid ? request.fee : 0,
      currency: wallet.currency,
    }).save();
  }

  async validateWithdrawals(data: ValidateWithdrawalsDto) {
    const validWithdrawals = await this.withdrawalRequestModel
      .find({
        vendorReference: { $in: data.references },
        status: WITHDRAWAL_STATUSES.SUCCESSFUL,
        currency: data.currency,
      })
      .select('vendorReference')
      .lean();

    const validReferences = validWithdrawals.map((w) => w.vendorReference);

    const invalidWithdrawals = data.references.filter((r) => !validReferences.includes(r));

    return invalidWithdrawals;
  }

  async checkWalletTransactions(paginationQuery: PaginatedQueryDto) {
    const w = await this.walletModel.paginate(
      {},
      {
        select: 'balance store currency',
        page: paginationQuery?.page,
        limit: paginationQuery?.per_page ?? 50,
        lean: true,
        populate: [
          {
            path: 'store',
            select: 'name',
            model: 'Store',
          },
        ],
      },
    );
    const wallets = w?.docs;
    const walletIds = wallets.map((w) => w._id);

    const transactions = await this.transactionModel.find({ wallet: { $in: walletIds } }).lean();

    const sortedByWallet: {
      [key: string]: {
        credits: number;
        debits: number;
        balance: number;
        actualBalance: number;
        currency: string;
        store: string;
      };
    } = {};

    for (let index = 0; index < wallets.length; index++) {
      const wallet = wallets[index];

      sortedByWallet[wallet._id] = {
        actualBalance: wallet?.balance,
        credits: 0,
        debits: 0,
        balance: 0,
        currency: wallet?.currency,
        store: (wallet?.store as any)?.name,
      };
    }

    for (let index = 0; index < transactions.length; index++) {
      const tx = transactions[index];

      let { credits, debits, balance, actualBalance, currency, store } = sortedByWallet[tx.wallet] ?? {
        credits: 0,
        debits: 0,
        balance: 0,
      };

      if (tx.type === 'credit') {
        credits += tx.amount;
        debits += tx.fee;
      } else {
        debits += tx.amount + tx.fee;
      }

      sortedByWallet[tx.wallet] = {
        credits,
        debits,
        balance: credits - debits,
        actualBalance,
        currency,
        store,
      };
    }

    return {
      data: sortedByWallet,
      page: w.page,
      next_page: w.nextPage,
      prev_page: w.prevPage,
      total: w.totalDocs,
      total_pages: w.totalPages,
      per_page: w.limit,
    };
  }
}
