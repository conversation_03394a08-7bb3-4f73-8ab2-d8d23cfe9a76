import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Document } from 'mongoose';

export type MonoAccountIdsDocument = MonoAccountIds & Document;
export type MonoTransactionDocument = MonoTransaction & Document;

@Schema({ timestamps: true })
export class MonoAccountIds {
  public id: string;

  @ApiProperty()
  @Prop({ type: String })
  account_holder_id: string;

  @ApiProperty()
  @Prop({ type: String })
  sub_account_holder_id: string;

  @ApiProperty()
  @Prop({ type: String })
  account_id: string;

  @ApiProperty()
  @Prop({ type: String })
  alternate_account_id: string;

  @ApiProperty()
  @Prop({ type: String })
  wallet: string;
}

@Schema({ timestamps: true })
export class MonoTransaction {
  public id: string;

  @ApiProperty()
  @Prop({ type: String })
  type: string;

  @ApiProperty()
  @Prop({ type: String })
  narration: string;
  
  @ApiProperty()
  @Prop({ type: Number })
  amount: number;

  @ApiProperty()
  @Prop({ type: String })
  wallet: string;

  @ApiProperty()
  @Prop({ type: Date })
  date: Date;

  @ApiProperty()
  @Prop({ type: String })
  currency: string;

  @ApiProperty()
  @Prop({ type: String })
  category: string;

  @ApiProperty()
  @Prop({ type: String })
  mono_id: string;
}

export const MonoAccountIdsSchema = SchemaFactory.createForClass(
  MonoAccountIds,
);

export const MonoTransactionSchema = SchemaFactory.createForClass(
  MonoTransaction,
);