import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import mongoose, { Document } from 'mongoose';
import { Store } from '../store/store.schema';
import { Account, ACCOUNT_PROVIDERS } from './wallet.account.schema';
import { CURRENCIES } from '../country/country.schema';
import { WithdrawalAccount } from './wallet.withdrawal.schema';

export type WalletDocument = Wallet & Document;
export type TransactionDocument = Transaction & Document;
export type PaymentStatementDocument = PaymentStatement & Document;
export type WalletRequestDocument = WalletRequest & Document;

export enum TRANSACTION_CHANNELS {
  DIRECT_TRANSFER = 'DIRECT_TRANSFER',
  INVOICE_PAYMENT = 'INVOICE_PAYMENT',
  TEST_PAYMENT = 'TEST_PAYMENT',
  WITHDRAWAL = 'WITHDRAWAL',
  REVERSAL = 'REVERSAL',
  SUBSCRIPTION_PAYMENT = 'SUBSCRIPTION_PAYMENT',
  DELIVERY_PAYMENT = 'DELIVERY_PAYMENT',
  TOKEN_PAYMENT = 'TOKEN_PAYMENT',
  COUPON_REFUND = 'COUPON_REFUND',
  CREDIT_CASHOUT = 'CREDIT_CASHOUT',
  CONVERSION = 'CONVERSION',
  DOMAIN_PURCHASE = 'DOMAIN_PURCHASE',
}

export enum TRANSACTION_TYPE {
  CREDIT = 'credit',
  DEBIT = 'debit',
}

export enum WALLET_REQUEST_STATUS {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}

export class WalletAccountData {
  @ApiProperty()
  @Prop({ type: String })
  id?: string;

  @ApiProperty()
  @Prop({ enum: Object.keys(ACCOUNT_PROVIDERS) })
  provider: ACCOUNT_PROVIDERS;
}

export class TransactionMeta {
  @ApiProperty()
  @Prop({ type: String })
  payment_id?: string;

  @ApiProperty()
  @Prop({ type: String })
  original_payment_id?: string;

  @ApiProperty()
  @Prop({ type: String })
  original_transaction_id?: string;

  @ApiProperty()
  @Prop({ type: String })
  squad_transaction_id?: string;

  @ApiProperty()
  @Prop({ type: String })
  payaza_transaction_id?: string;

  @ApiProperty()
  @Prop({ type: String })
  withdrawal_request?: string;

  @ApiProperty()
  @Prop({ type: String })
  transfer_reference?: string;

  @ApiProperty()
  @Prop({ type: String })
  manual_credit_reference?: string;

  @ApiProperty()
  @Prop({ type: String })
  conversion_reference?: string;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  reversed?: boolean;
}

export class TransactionSource {
  @ApiProperty()
  @Prop({ type: String })
  name: string;

  @ApiProperty()
  @Prop({ type: String })
  account_number: string;

  @ApiProperty()
  @Prop({ type: String })
  bank?: string;

  @ApiProperty()
  @Prop({ type: String })
  purpose: string;

  @ApiProperty()
  @Prop({ type: String })
  method: string;
}

export class Limits {
  @ApiProperty()
  @Prop({ type: Number })
  collection_limit?: number;

  @ApiProperty()
  @Prop({ type: Number })
  daily_withdrawal_limit?: number;
}

export class AccountInformation {
  @ApiProperty()
  @Prop({ type: String })
  bank_code: string;

  @ApiProperty()
  @Prop({ type: String })
  bank_name: string;

  @ApiProperty()
  @Prop({ type: String })
  name: string;

  @ApiProperty()
  @Prop({ type: String })
  number: string;
}

export class WalletMeta {
  @ApiProperty()
  @Prop({ type: Boolean, required: false, default: false })
  has_crossed_free_threshold?: boolean;
}

@Schema({ timestamps: true })
export class Wallet {
  public id: string;

  @ApiProperty()
  @Prop({ type: String })
  account: string | Account;

  @ApiProperty()
  @Prop({
    type: [
      {
        id: String,
        provider: String,
        _id: false,
      },
    ],
    default: [],
  })
  accounts: {
    id: string;
    provider: string;
  }[];

  @ApiProperty()
  @Prop({ type: Number, default: 0 })
  balance: number;

  @ApiProperty()
  @Prop({ type: [String] })
  withdrawal_accounts: string[];

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Store', unique: true })
  store: string | Store;

  @ApiProperty()
  @Prop({
    type: String,
    enum: Object.values(CURRENCIES),
    default: CURRENCIES.NGN,
  })
  currency: CURRENCIES;

  @ApiProperty()
  @Prop({ type: Number, required: false })
  daily_withdrawal_limit?: number;

  @ApiProperty()
  @Prop({ type: Object, required: false })
  limits?: Limits;

  @ApiProperty()
  @Prop({ type: Boolean, required: false, default: false })
  has_completed_kyc?: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, required: false, default: true })
  auto_create_account?: boolean;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'WithdrawalAccount', required: false })
  auto_send_funds_to?: string | WithdrawalAccount;

  @ApiProperty()
  @Prop({ type: WalletMeta, required: false })
  meta?: WalletMeta;
}

@Schema({ timestamps: true })
export class Transaction {
  public id: string;

  @ApiProperty()
  @Prop({ type: Number })
  amount: number;

  @ApiProperty()
  @Prop({ type: Number })
  fee: number; // 1% fee with cap of 500

  @ApiProperty()
  @Prop({ type: TRANSACTION_TYPE, enum: [TRANSACTION_TYPE.CREDIT, TRANSACTION_TYPE.DEBIT] })
  type: string;

  @ApiProperty()
  @Prop({
    enum: Object.values(TRANSACTION_CHANNELS),
  })
  channel: TRANSACTION_CHANNELS;

  @ApiProperty()
  @Prop({ type: String })
  narration: string;

  @ApiProperty()
  @Prop({ type: TransactionSource })
  source: TransactionSource;

  @ApiProperty()
  @Prop({ type: Number })
  balance_before: number;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Wallet' })
  wallet: string;

  @ApiProperty()
  @Prop({ type: TransactionMeta })
  meta: TransactionMeta;

  @ApiProperty()
  @Prop({
    type: String,
    enum: [
      CURRENCIES.NGN,
      CURRENCIES.GHC,
      CURRENCIES.USD,
      CURRENCIES.KES,
      CURRENCIES.ZAR,
      CURRENCIES.CAD,
      CURRENCIES.GBP,
      CURRENCIES.EUR,
    ],
    default: CURRENCIES.NGN,
  })
  currency: CURRENCIES;
}

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class PaymentStatement {
  @ApiProperty()
  id: string;

  @ApiProperty()
  @Prop({ type: String })
  pin: string;

  @ApiProperty()
  @Prop({
    type: {
      start_date: Date,
      end_date: Date,
    },
  })
  period: {
    start_date: Date;
    end_date: Date;
  };

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Wallet' })
  wallet: string | Wallet;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Store' })
  store: string | Store;
}

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class WalletRequest {
  public id: string;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Store', required: true })
  store: string | Store;

  @ApiProperty()
  @Prop({
    type: [String],
    enum: Object.values(CURRENCIES),
    required: true,
  })
  requested_currencies: CURRENCIES[];

  @ApiProperty()
  @Prop({ type: String, required: true })
  reason: string;

  @ApiProperty()
  @Prop({ type: Boolean, required: true })
  collect_payments_from_abroad: boolean;

  @ApiProperty()
  @Prop({ type: String })
  current_payment_method?: string;

  @ApiProperty()
  @Prop({ type: String })
  plans_to_get_customers_abroad?: string;

  @ApiProperty()
  @Prop({ type: String, enum: Object.values(WALLET_REQUEST_STATUS), default: 'PENDING' })
  status: WALLET_REQUEST_STATUS;

  @ApiProperty()
  @Prop({ type: String })
  admin_notes?: string;
}

export const PaymentStatementSchema = SchemaFactory.createForClass(PaymentStatement);
export const WalletSchema = SchemaFactory.createForClass(Wallet);
export const TransactionSchema = SchemaFactory.createForClass(Transaction);
export const WalletRequestSchema = SchemaFactory.createForClass(WalletRequest);
