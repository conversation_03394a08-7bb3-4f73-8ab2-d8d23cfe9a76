// create-wallet-request.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { CURRENCIES } from '../../country/country.schema';

export class CreateInternationalWalletRequestDto {
  @ApiProperty({ description: 'Requested currencies', enum: CURRENCIES, isArray: true })
  @IsArray()
  @IsEnum(CURRENCIES, { each: true })
  requested_currencies: CURRENCIES[];

  @ApiProperty({ description: 'Reason for requesting the wallet' })
  @IsString()
  @IsNotEmpty()
  reason: string;

  @ApiProperty({ description: 'Collect payments from abroad' })
  @IsBoolean()
  collect_payments_from_abroad: boolean;

  @ApiProperty({ description: 'Current payment method', required: false })
  @IsOptional()
  @IsString()
  current_payment_method?: string;

  @ApiProperty({ description: 'Plan to get customers abroad', required: false })
  @IsOptional()
  @IsString()
  plans_to_get_customers_abroad?: string;
}
