import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsNumber, IsString } from 'class-validator';
import { CURRENCIES } from '../../country/country.schema';

export class GenerateQuoteDto {
  @ApiProperty({ description: 'Source currency code, e.g., USD', enum: CURRENCIES, example: CURRENCIES.USD })
  @IsEnum(CURRENCIES)
  @IsNotEmpty()
  source_currency: CURRENCIES;

  @ApiProperty({ description: 'Destination currency code, e.g., EUR', enum: CURRENCIES, example: CURRENCIES.EUR })
  @IsEnum(CURRENCIES)
  @IsNotEmpty()
  destination_currency: CURRENCIES;

  @ApiProperty({ description: 'Amount to be converted', example: 100 })
  @IsNumber()
  @IsNotEmpty()
  amount: number;
}
