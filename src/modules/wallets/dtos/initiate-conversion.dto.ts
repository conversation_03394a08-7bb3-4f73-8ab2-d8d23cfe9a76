import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsNumber } from 'class-validator';

export class InitiateConversionDto {
  @ApiProperty({ description: 'Amount to convert', example: 100 })
  @IsNumber()
  amount: number;

  @ApiProperty({ description: 'Quote reference returned by generateQuote' })
  @IsString()
  @IsNotEmpty()
  quote_reference: string;

  @ApiProperty({ description: 'Security pin' })
  @IsString()
  @IsNotEmpty()
  security_pin: string;

  @ApiProperty({ description: 'From wallet id' })
  @IsString()
  @IsNotEmpty()
  from_wallet: string;

  @ApiProperty({ description: 'To wallet id' })
  @IsString()
  @IsNotEmpty()
  to_wallet: string;
  //@dan I'm collecting from & to wallets because there's a small chance that multiple wallets exist in the same currency for the same user - and we don't want to randomly select the wrong one
}
