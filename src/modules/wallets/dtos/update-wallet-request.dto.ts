// update-wallet-request.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { WALLET_REQUEST_STATUS } from '../wallet.schema';

export class UpdateWalletRequestDto {
  @ApiProperty({ description: 'Wallet request id' })
  @IsString()
  @IsNotEmpty()
  wallet_request_id: string;

  @ApiProperty({ description: 'Status of the wallet request', enum: WALLET_REQUEST_STATUS })
  @IsEnum(WALLET_REQUEST_STATUS)
  status: WALLET_REQUEST_STATUS;

  @ApiProperty({ description: 'Admin notes', required: false })
  @IsOptional()
  @IsString()
  admin_notes?: string;
}
