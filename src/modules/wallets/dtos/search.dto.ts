import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayNotEmpty,
  IsArray,
  IsDate,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  isNotEmpty,
} from 'class-validator';
import { CURRENCIES } from '../../country/country.schema';

export enum SEARCH_TRANSACTION_TYPES {
  ALL = 'all',
  DEBIT = 'debit',
  CREDIT = 'credit',
}

export class SearchTransactionsDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  search: string;

  @ApiProperty()
  @IsEnum(SEARCH_TRANSACTION_TYPES)
  type: SEARCH_TRANSACTION_TYPES;

  @ApiProperty({ required: false })
  @IsDateString()
  from?: Date;

  @ApiProperty({ required: false })
  @IsDateString()
  to?: Date;
}

export class PaginatedQueryDto {
  @ApiProperty({ required: false })
  page: number;

  @ApiProperty({ required: false })
  per_page: number;

  @ApiProperty({ required: false, type: 'string', enum: ['ASC', 'DESC'] })
  sort?: any;
}

export class ValidateWithdrawalsDto {
  @IsArray()
  @IsNotEmpty()
  references: string[];

  @IsDateString()
  @IsNotEmpty()
  start_date: string;

  @IsString()
  @IsNotEmpty()
  currency: CURRENCIES;
}

export class ProcessRefundDto {
  @IsString()
  @IsNotEmpty()
  reference: string;

  @IsString()
  @IsNotEmpty()
  requestId: string;
}

export class ManuallyCreditWalletDto {
  @IsString()
  @IsNotEmpty()
  walletId: string;

  @IsNumber()
  @IsNotEmpty()
  amount: number;

  @IsString()
  @IsNotEmpty()
  otp: string;

  @IsString()
  @IsNotEmpty()
  narration: string;
}

export class ProcessPartialRefundDto {
  @IsString()
  @IsNotEmpty()
  reference: string;

  @IsString()
  @IsNotEmpty()
  requestId: string;

  @IsNumber()
  @IsNotEmpty()
  amount: number;
}

export class TriggerMissingSquadWebhooksDto {
  // @IsString()
  // @IsNotEmpty()
  // startDate: string;

  // @IsString()
  // @IsNotEmpty()
  // endDate: string;

  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  references: string[];
}
