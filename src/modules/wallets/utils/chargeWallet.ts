import mongoose, { Connection, Model } from 'mongoose';
import { transactionFeeCalculator } from '../../../utils/fees';
import {
  TransactionDocument,
  TransactionMeta,
  TransactionSource,
  TRANSACTION_TYPE,
  WalletDocument,
} from '../wallet.schema';
import { Mutex } from 'async-mutex';

export async function chargeWallet(data: {
  walletId: string;
  walletModel: Model<WalletDocument>;
  Transaction: Model<TransactionDocument>;
  dbConnection: Connection;
  amount: number;
  fee: number;
  channel: string;
  narration: string;
  type: TRANSACTION_TYPE;
  meta: TransactionMeta;
  source: TransactionSource;
  throwErrors: boolean;
  mutex: Mutex;
  isReversal?: boolean;
}) {
  const {
    walletId,
    walletModel,
    Transaction,
    dbConnection,
    amount,
    fee,
    channel,
    narration,
    type,
    throwErrors = true,
    source,
    mutex,
    meta,
    isReversal = false,
  } = data;
  const release = await mutex.acquire();

  try {
    // Check balance inside the transaction to prevent race conditions
    const wallet = await walletModel.findOne({ _id: walletId });

    if (!wallet) {
      release();
      if (throwErrors) {
        throw new Error('Wallet does not exist');
      } else {
        return {
          success: false,
          message: 'Wallet does not exist',
          wallet: null,
        };
      }
    }

    const existingTransaction = await Transaction.findOne({ meta: meta, type });

    if (existingTransaction) {
      release();
      return {
        success: false,
        wallet,
        message: 'Transaction already processed',
      }; // Transaction already processed
    }

    const transaction = new Transaction({
      amount: amount,
      type: type,
      channel: channel,
      narration: narration,
      balance_before: wallet.balance,
      wallet: wallet._id,
      meta: meta,
      fee: fee,
      source: source,
      currency: wallet.currency,
    });

    if (type === TRANSACTION_TYPE.DEBIT && amount + fee > wallet.balance && !isReversal) {
      release();
      if (throwErrors) {
        throw new Error("Can't withdraw more than balance");
      }
      return {
        success: false,
        wallet,
        message: 'Insufficient funds',
      };
    }

    if (type === TRANSACTION_TYPE.DEBIT) wallet.balance -= amount + fee;
    else if (type === TRANSACTION_TYPE.CREDIT) wallet.balance += amount - fee;
    else {
      release();
      throw new Error('Invalid transaction type');
    }

    await wallet.save();
    await transaction.save();

    release();

    return { success: true, wallet: wallet, transaction, message: 'Wallet Charged' };
  } catch (error) {
    release();
    if (throwErrors) throw error;
    return { success: false, message: error.message };
  }
}
