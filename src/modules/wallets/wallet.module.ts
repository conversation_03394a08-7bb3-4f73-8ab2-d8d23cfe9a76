import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import {
  Wallet,
  WalletSchema,
  Transaction,
  TransactionSchema,
  PaymentStatement,
  PaymentStatementSchema,
  WalletRequest,
  WalletRequestSchema,
} from './wallet.schema';
import { MonoAccountIds, MonoAccountIdsSchema, MonoTransaction, MonoTransactionSchema } from './wallet.mono.schema';
import {
  WithdrawalAccount,
  WithdrawalAccountSchema,
  WithdrawalRequest,
  WithdrawalRequestSchema,
} from './wallet.withdrawal.schema';

import setIdPlugin from '../../mongoose-plugins/set-id.mongoose-plugin';
import jsonHookPlugin from '../../mongoose-plugins/json-hook.mongoose-plugin';
import mongoosePaginate from 'mongoose-paginate-v2';

import { SharedModule } from '../../shared.module';
import { WalletService } from './wallet.service';
import { WalletBroker } from './wallet.broker';
import { WalletController } from './wallet.controller';
import { Account, AccountSchema } from './wallet.account.schema';
import { WalletQueue } from './wallet.queue';
import { BullModule } from '@nestjs/bull';
import { QUEUES } from '../../enums/queues.enum';
import setIdMongoosePlugin from '../../mongoose-plugins/set-id.mongoose-plugin';
import jsonHookMongoosePlugin from '../../mongoose-plugins/json-hook.mongoose-plugin';
import { WalletResolutionsService } from './resolutions.service';
import { CurrencyConversionService } from './currency-conversion/currency-conversion.service';
import { CurrencyConversionController } from './currency-conversion/currency-conversion.controller';
import { Conversion, ConversionSchema } from './currency-conversion/conversion.schema';
import { CurrencyRates, CurrencyRatesSchema } from './currency-conversion/currency-rate.schema';

@Module({
  imports: [
    BullModule.registerQueueAsync({
      name: QUEUES.WALLET,
    }),
    MongooseModule.forFeatureAsync([
      {
        name: Wallet.name,
        useFactory: () => {
          WalletSchema.plugin(setIdPlugin());
          WalletSchema.plugin(jsonHookPlugin(['_id', '__v']));
          WalletSchema.plugin(mongoosePaginate);
          return WalletSchema;
        },
      },
      {
        name: MonoAccountIds.name,
        useFactory: () => {
          MonoAccountIdsSchema.plugin(setIdPlugin());
          MonoAccountIdsSchema.plugin(jsonHookPlugin(['_id', '__v']));
          return MonoAccountIdsSchema;
        },
      },
      {
        name: MonoTransaction.name,
        useFactory: () => {
          MonoTransactionSchema.plugin(setIdPlugin());
          MonoTransactionSchema.plugin(jsonHookPlugin(['_id', '__v']));
          return MonoTransactionSchema;
        },
      },
      {
        name: Transaction.name,
        useFactory: () => {
          TransactionSchema.plugin(setIdPlugin());
          TransactionSchema.plugin(jsonHookPlugin(['_id', '__v']));
          TransactionSchema.plugin(mongoosePaginate);
          return TransactionSchema;
        },
      },
      {
        name: WithdrawalAccount.name,
        useFactory: () => {
          WithdrawalAccountSchema.plugin(setIdPlugin());
          WithdrawalAccountSchema.plugin(jsonHookPlugin(['_id', '__v']));
          return WithdrawalAccountSchema;
        },
      },
      {
        name: WithdrawalRequest.name,
        useFactory: () => {
          WithdrawalRequestSchema.plugin(setIdPlugin());
          WithdrawalRequestSchema.plugin(jsonHookPlugin(['_id', '__v']));
          return WithdrawalRequestSchema;
        },
      },
      {
        name: Account.name,
        useFactory: () => {
          AccountSchema.plugin(setIdPlugin());
          AccountSchema.plugin(jsonHookPlugin(['_id', '__v']));
          return AccountSchema;
        },
      },
      {
        name: PaymentStatement.name,
        useFactory: () => {
          PaymentStatementSchema.plugin(setIdMongoosePlugin());
          PaymentStatementSchema.plugin(jsonHookMongoosePlugin(['_id', '__v']));
          PaymentStatementSchema.plugin(mongoosePaginate);
          return PaymentStatementSchema;
        },
      },
      {
        name: WalletRequest.name,
        useFactory: () => {
          WalletRequestSchema.plugin(setIdMongoosePlugin());
          WalletRequestSchema.plugin(jsonHookMongoosePlugin(['_id', '__v']));
          WalletRequestSchema.plugin(mongoosePaginate);
          return WalletRequestSchema;
        },
      },
      {
        name: Conversion.name,
        useFactory: () => {
          ConversionSchema.plugin(setIdMongoosePlugin());
          ConversionSchema.plugin(jsonHookMongoosePlugin(['_id', '__v']));
          ConversionSchema.plugin(mongoosePaginate);
          return ConversionSchema;
        },
      },
      {
        name: CurrencyRates.name,
        useFactory: () => {
          CurrencyRatesSchema.plugin(setIdMongoosePlugin());
          CurrencyRatesSchema.plugin(jsonHookMongoosePlugin(['_id', '__v']));
          return CurrencyRatesSchema;
        },
      },
    ]),
    SharedModule,
  ],
  providers: [WalletQueue, WalletService, WalletResolutionsService, CurrencyConversionService],
  controllers: [WalletBroker, WalletController, CurrencyConversionController],
  exports: [WalletService, CurrencyConversionService],
})
export class WalletModule {}
