import { ApiProperty, PartialType } from '@nestjs/swagger';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsDefined,
  IsEmail,
  IsMongoId,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Item } from '../../item/item.schema';
import { FilterDto } from '../../../models/dtos/generic.dto';
import { Type, Transform } from 'class-transformer';
import { DELIVERY_METHODS, ORDER_CHANNELS, ORDER_STATUSES, OrderItem } from '../order.schema';
import { CURRENCIES } from '../../country/country.schema';
import { InvoiceFee } from '../../invoice/invoice.schema';
import { FEE_TYPES } from '../../../enums/order.enum';
import { CustomerOrigin } from '../customers/customer.schema';

export class CreateCustomerDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsDefined()
  name: string;

  @ApiProperty({ required: false })
  @Transform((email: string) => (email === '' ? undefined : email))
  @IsString()
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  phone: string;

  store?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  affiliate?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  origin?: CustomerOrigin;
}

export class CreateCustomerDtoWithOptionalName {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsDefined()
  @IsOptional()
  name: string;

  @ApiProperty({ required: false })
  @Transform((email: string) => (email === '' ? undefined : email))
  @IsString()
  @IsEmail()
  @IsOptional()
  email: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  phone: string;
}

export class DeliveryInfoDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  delivery_address: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  user_provided_address?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  name?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  phone?: string;

  @ApiProperty()
  @Transform((area: string) => (area === '' ? undefined : area))
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  area: string;
}

export class MetaDataDto {
  @ApiProperty()
  @IsObject()
  @IsOptional()
  chowbot: { session_id?: string; db_session_id?: string };
}

export class CreateOrderDto {
  @ApiProperty({
    type: 'object',
    properties: {
      name: {
        type: 'string',
      },
      email: {
        type: 'string',
      },
      phone: {
        type: 'string',
      },
    },
  })
  @ValidateNested()
  @Type(() => CreateCustomerDto)
  customer?: CreateCustomerDto;

  @ApiProperty({
    type: 'array',
    items: {
      type: 'object',
      properties: {
        item_id: {
          type: 'string',
        },
        quantity: {
          type: 'number',
        },
        item: {
          type: 'object',
        },
        variant_id: {
          type: 'string',
        },
      },
    },
  })
  @ArrayMinSize(1, { message: 'Please add at least one item' })
  @ValidateNested()
  // @Type(() => OrderItem)
  // items: {
  //   item_id: string;
  //   quantity: number;
  //   variant_id: string;
  //   snapshot: any;
  //   item: Item;
  //   variant?: {
  //     image: string;
  //     price: number;
  //     discount_price?: number;
  //     is_available: boolean;
  //     id: string;
  //   };
  // }[];
  items: OrderItem[];

  @ApiProperty()
  @IsMongoId()
  @IsNotEmpty()
  store: string;

  @IsOptional()
  @ApiProperty({ type: 'object' })
  @ValidateNested()
  @Type(() => DeliveryInfoDto)
  delivery_info?: DeliveryInfoDto;

  @ApiProperty()
  @IsString()
  @IsOptional()
  coupon?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  delivery?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  validated_delivery_address?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  order_notes?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  cart?: string;

  total_amount?: number;

  @ApiProperty()
  @IsString()
  delivery_method: DELIVERY_METHODS;

  @IsOptional()
  @ApiProperty({ type: 'object' })
  @ValidateNested()
  @Type(() => MetaDataDto)
  meta?: MetaDataDto;

  @ApiProperty({ default: ORDER_CHANNELS.STORE_FRONT })
  @IsString()
  @IsOptional()
  channel?: ORDER_CHANNELS;

  @IsString()
  @IsNotEmpty()
  currency?: CURRENCIES;

  @IsString()
  @IsOptional()
  rates?: string;

  @IsObject()
  @IsOptional()
  extra_details?: Record<string, string>;

  @IsArray()
  @Type(() => InvoiceFee)
  @ValidateNested({ each: true, message: 'Please add a valid fee' })
  @IsOptional()
  fees?: {
    amount: number;
    type: FEE_TYPES;
    label?: String;
  }[];

  @IsString()
  @IsOptional()
  affiliate?: string;
}

export class SellerCreateOrderDto extends CreateOrderDto {
  @IsBoolean()
  @IsNotEmpty()
  is_paid: boolean;

  @IsString()
  @IsNotEmpty()
  status: ORDER_STATUSES;

  @ApiProperty({
    type: 'object',
    properties: {
      name: {
        type: 'string',
      },
      email: {
        type: 'string',
      },
      phone: {
        type: 'string',
      },
    },
  })
  @ValidateNested()
  @Type(() => CreateCustomerDtoWithOptionalName)
  customer?: CreateCustomerDtoWithOptionalName;
}

export class ConfirmOrderDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  order: string;
}

export class CustomerFilterDto extends FilterDto {
  @ApiProperty()
  store: string;
}

export class UpdateCustomerDto extends PartialType<CreateCustomerDto>(CreateCustomerDto) {}

export class PublicCustomerUpdateDto extends UpdateCustomerDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsDefined()
  id: string;
}

export class FeedbackDto {
  @ApiProperty()
  @IsOptional()
  @IsNumber()
  product_rating?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  process_rating?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  delivery_rating?: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  general_feedback?: string;
}

export class CreateOrderFeedbackDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  store: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  order: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  customer: string;

  @ApiProperty({
    type: 'object',
    properties: {
      product_rating: {
        type: 'number',
      },
      process_rating: {
        type: 'number',
      },
      delivery_rating: {
        type: 'number',
      },
      general_feedback: {
        type: 'string',
      },
    },
  })
  @ValidateNested()
  @Type(() => FeedbackDto)
  feedback: FeedbackDto;
}
