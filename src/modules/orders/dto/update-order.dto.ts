import { ApiProperty } from '@nestjs/swagger';
import { ArrayMinSize, IsArray, IsBoolean, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { DELIVERY_METHODS, OrderFee, OrderItem, ORDER_STATUSES } from '../order.schema';
import { CreateCustomerDto, DeliveryInfoDto } from './create-order.dto';

export class UpdateOrderDto {
  @ApiProperty({
    type: 'string',
    enum: [ORDER_STATUSES.PROCESSING, ORDER_STATUSES.FULFILLED, ORDER_STATUSES.CANCELLED, ORDER_STATUSES.ABANDONED],
  })
  @IsString()
  @IsNotEmpty()
  @IsEnum(ORDER_STATUSES)
  status: ORDER_STATUSES;

  @IsString()
  @IsOptional()
  reason?: string;
}

export class UpdateOrderFromSellerDto {
  @ApiProperty()
  @IsArray()
  @IsOptional()
  @ArrayMinSize(1, { message: 'Please add at least one item' })
  items: {
    item_id: string;
    quantity: number;
    variant_id: string;
    // snapshot: any;
  }[];

  @ApiProperty()
  @IsString()
  @IsOptional()
  coupon_code?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  delivery_area?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  discount?: number;

  @ApiProperty({
    type: 'boolean',
  })
  @IsOptional()
  @IsBoolean()
  isPaid?: boolean;

  @ApiProperty()
  @IsString()
  @IsOptional()
  rates?: string;
}

export class UpdateOrderFromCustomerDto {
  @ApiProperty()
  @IsArray()
  @IsOptional()
  @ArrayMinSize(1, { message: 'Please add at least one item' })
  items?: {
    item_id: string;
    quantity: number;
    variant_id: string;
  }[];

  @ApiProperty()
  @IsOptional()
  customer_info?: CreateCustomerDto;

  @ApiProperty()
  @IsOptional()
  delivery_info?: DeliveryInfoDto;

  @ApiProperty()
  @IsString()
  delivery_method?: DELIVERY_METHODS;

  @ApiProperty()
  @IsString()
  @IsOptional()
  phone?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  rates?: string;
}
