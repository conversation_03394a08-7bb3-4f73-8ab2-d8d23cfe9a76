import { forwardRef, Module } from '@nestjs/common';
import { OrdersService } from './orders.service';
import { OrdersController } from './orders.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Order, OrderSchema } from './order.schema';
import { jsonHookMongoosePlugin, setIdMongoosePlugin } from '../../mongoose-plugins';
import { SharedModule } from '../../shared.module';
import mongoosePaginate from 'mongoose-paginate-v2';
import { Customer, CustomerSchema } from './customers/customer.schema';
import { CustomerController } from './customers/customer.controller';
import { OrderBroker } from './order.broker';
import { CustomerService } from './customers/customer.service';
import { BullModule } from '@nestjs/bull';
import { QUEUES } from '../../enums/queues.enum';
import { OrderQueue } from './order.queue';
import { OrderFeedbackController } from './order-feedback/order-feedback.controller';
import { OrderFeedbackService } from './order-feedback/order-feedback.service';
import { OrderFeedback, OrderFeedbackSchema } from './order-feedback/order-feedback.schema';
import { PaymentModule } from '../payment/payment.module';

@Module({
  imports: [
    BullModule.registerQueue({
      name: QUEUES.ORDER,
    }),
    MongooseModule.forFeatureAsync([
      {
        name: Order.name,
        useFactory: () => {
          OrderSchema.plugin(setIdMongoosePlugin());
          OrderSchema.plugin(jsonHookMongoosePlugin(['_id', '__v']));
          OrderSchema.plugin(mongoosePaginate);
          return OrderSchema;
        },
      },
      {
        name: Customer.name,
        useFactory: () => {
          CustomerSchema.plugin(setIdMongoosePlugin());
          CustomerSchema.plugin(jsonHookMongoosePlugin(['_id', '__v']));
          CustomerSchema.plugin(mongoosePaginate);
          return CustomerSchema;
        },
      },

      {
        name: OrderFeedback.name,
        useFactory: () => {
          OrderFeedbackSchema.plugin(setIdMongoosePlugin());
          OrderFeedbackSchema.plugin(jsonHookMongoosePlugin(['_id', '__v']));
          return OrderFeedbackSchema;
        },
      },
    ]),
    SharedModule,
    forwardRef(() => PaymentModule),
  ],
  controllers: [OrdersController, CustomerController, OrderBroker, OrderFeedbackController],
  providers: [OrdersService, CustomerService, OrderQueue, OrderFeedbackService],
  exports: [OrdersService, CustomerService, OrderFeedbackService],
})
export class OrdersModule {}
