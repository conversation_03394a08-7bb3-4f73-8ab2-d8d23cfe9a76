import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import mongoose, { Document } from 'mongoose';
import { Store } from '../store/store.schema';
import { Cart } from '../cart/cart.schema';
import { Item } from '../item/item.schema';
import { Customer } from './customers/customer.schema';
import { FEE_TYPES } from '../../enums/order.enum';
import { DeliveryArea } from '../store/delivery-areas/delivery-areas.schema';
import { CURRENCIES } from '../country/country.schema';
import { Address, Delivery } from '../deliveries/deliveries.schema';
import { Affiliate } from '../affiliates/affiliates.schema';

export type OrderDocument = Order & Document;

export type OrderFee = { label: string; amount: number; type: FEE_TYPES };

export type OrderItem = {
  item_id: string;
  quantity: number;
  variant_id: string;
  variant?: any;
  snapshot?: Item; //this is actually compulsory for item snapshot, made it optional cos of CreateOrderDto
  item?: Item;
};

export enum ORDER_STATUSES {
  PENDING = 'PENDING',
  PAYMENT_RECEIVED = 'PAYMENT_RECEIVED',
  PROCESSING = 'PROCESSING',
  FULFILLED = 'FULFILLED',
  CANCELLED = 'CANCELLED',
  ABANDONED = 'ABANDONED',
}

export enum DELIVERY_METHODS {
  PICKUP = 'PICKUP',
  DELIVERY = 'DELIVERY',
  NONE = 'NONE',
}

export enum ORDER_CHANNELS {
  PHYSICAL_STORE = 'PHYSICAL_STORE',
  STORE_FRONT = 'STORE_FRONT',
  INSTAGRAM = 'INSTAGRAM',
  WHATSAPP = 'WHATSAPP',
  TWITTER = 'TWITTER',
  SNAPCHAT = 'SNAPCHAT',
  FACEBOOK = 'FACEBOOK',
  OTHERS = 'OTHERS',
  CHATBOT = 'CHATBOT',
}

export const ORDER_STATUSES_CONFIG = {
  PENDING: {
    status: 'received and pending confirmation',
    status_text: 'has been',
  },
  CONFIRMED: {
    status: 'confirmed',
    status_text: 'has been',
  },
  PROCESSING: {
    status: 'processing',
    status_text: 'is now',
  },
  CANCELLED: {
    status: 'cancelled',
    status_text: 'has been',
  },
  FULFILLED: {
    status: 'fulfilled',
    status_text: 'has been',
  },
  PAYMENT_RECEIVED: {
    status: '',
    status_text: '',
  },
};

export class DeliveryInfo {
  name?: string;
  phone?: string;

  @Prop({ type: String, required: true })
  delivery_address: string;

  @Prop({ type: String, required: false })
  user_provided_address?: string;

  @Prop({ type: DeliveryArea, required: false })
  delivery_area?: DeliveryArea;

  // @Prop({ type: DeliveryArea, required: false })
  // delivery_area?: DeliveryArea;
}

export class Fee {
  label: string;
  amount: number;
  type: FEE_TYPES;
}

export class OrderMeta {
  chowbot: {
    session_id?: string;
    db_session_id?: string;
  };
}
// export class Fee {label: String; amount: Number; type: { type: String, enum: [FEE_TYPES.DELIVERY, FEE_TYPES.DISCOUNT, FEE_TYPES.OTHERS]}}

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class Order {
  @ApiProperty()
  id: string;

  @Prop({ type: String, unique: 1, index: true })
  _id: string;

  @ApiProperty()
  @Prop({ type: Number, required: true })
  total_amount: number;

  @ApiProperty()
  @Prop({ type: Number, required: false })
  discount_amount: number;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Customer' })
  customer: Customer;

  @Prop({ type: String })
  delivery_address?: string;

  @ApiProperty()
  @Prop({
    type: [
      {
        item_id: mongoose.Schema.Types.ObjectId,
        quantity: Number,
        snapshot: Object,
        variant_id: mongoose.Schema.Types.ObjectId,
        variant: Object,
      },
    ],
  })
  items: OrderItem[];

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Store' })
  store: string | Store;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Cart' })
  cart: string | Cart;

  @ApiProperty()
  @Prop({
    type: String,
    enum: [
      ORDER_STATUSES.PROCESSING,
      ORDER_STATUSES.FULFILLED,
      ORDER_STATUSES.CANCELLED,
      ORDER_STATUSES.ABANDONED,
      ORDER_STATUSES.PENDING,
      ORDER_STATUSES.PAYMENT_RECEIVED,
    ],
    default: ORDER_STATUSES.PENDING,
  })
  status: ORDER_STATUSES;

  @ApiProperty()
  @Prop({
    type: String,
    enum: [DELIVERY_METHODS.DELIVERY, DELIVERY_METHODS.PICKUP, DELIVERY_METHODS.NONE],
    default: DELIVERY_METHODS.DELIVERY,
  })
  delivery_method: DELIVERY_METHODS;

  @ApiProperty()
  @Prop({
    type: [{ status: String, time: Date }],
  })
  timeline: { status: ORDER_STATUSES; time: Date }[];

  @ApiProperty()
  @Prop({
    type: [
      {
        label: String,
        amount: Number,
        type: {
          type: String,
          enum: [
            FEE_TYPES.DELIVERY,
            FEE_TYPES.DISCOUNT,
            FEE_TYPES.COUPON,
            FEE_TYPES.OTHERS,
            FEE_TYPES.VAT,
            FEE_TYPES.PAYMENT,
          ],
        },
      },
    ],
  })
  fees: OrderFee[];

  @ApiProperty()
  @Prop({
    type: Date,
    index: true,
  })
  created_at: Date;

  @ApiProperty()
  @Prop({ type: String })
  coupon_code?: string;

  @ApiProperty()
  @Prop({ type: String })
  invoice?: string;

  @ApiProperty()
  @Prop({ type: String })
  receipt?: string;

  @ApiProperty()
  @Prop({ type: String })
  order_notes?: string;

  @ApiProperty()
  @Prop({ type: String })
  cancellation_reason?: string;

  @ApiProperty()
  @Prop({
    type: String,
    enum: [
      CURRENCIES.NGN,
      CURRENCIES.USD,
      CURRENCIES.EUR,
      CURRENCIES.GBP,
      CURRENCIES.GHC,
      CURRENCIES.ZAR,
      CURRENCIES.KES,
    ],
    default: CURRENCIES.NGN,
  })
  currency?: CURRENCIES;

  @ApiProperty()
  @Prop({ type: DeliveryInfo, required: false })
  delivery_info: DeliveryInfo;

  @ApiProperty()
  @Prop({ type: OrderMeta, required: false })
  meta: OrderMeta;

  @ApiProperty()
  @Prop({
    type: String,
    enum: Object.keys(ORDER_CHANNELS),
    default: ORDER_CHANNELS.STORE_FRONT,
  })
  channel?: ORDER_CHANNELS;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Address' })
  validated_delivery_address?: string | Address;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Delivery' })
  delivery?: string | Delivery;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  from_seller?: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  is_paid?: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  is_deleted?: boolean;

  @ApiProperty()
  @Prop({ type: Object, default: {} })
  extra_details?: Record<string, string>;

  @ApiProperty()
  @Prop({ type: String, required: false })
  payment_id?: string;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Affiliate' })
  affiliate?: string | Affiliate;
}

export const OrderSchema = SchemaFactory.createForClass(Order);
