import { Controller } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { FilterQuery, Model } from 'mongoose';
import { Order, OrderDocument, ORDER_STATUSES } from './order.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Customer, CustomerDocument } from './customers/customer.schema';
import { OrdersService } from './orders.service';
import { CreateCustomerDto, CreateOrderDto, CreateOrderFeedbackDto } from './dto/create-order.dto';
import { CustomerService } from './customers/customer.service';
import { OrderFeedbackService } from './order-feedback/order-feedback.service';
import { SkipThrottle } from '@nestjs/throttler';

@SkipThrottle()
@Controller()
export class OrderBroker {
  constructor(
    private readonly orderService: OrdersService,
    private readonly customerService: CustomerService,
    private readonly orderFeedbackService: OrderFeedbackService,
    @InjectModel(Customer.name)
    private readonly customerModel: Model<CustomerDocument>,
    @InjectModel(Order.name)
    private readonly orderModel: Model<OrderDocument>,
  ) {}

  @MessagePattern(BROKER_PATTERNS.ORDER.GET_ORDER)
  async getOrder(filter: FilterQuery<Order>) {
    return this.orderModel.findOne(filter).populate('customer').populate('store').sort({ created_at: -1 });
  }

  @MessagePattern(BROKER_PATTERNS.ORDER.GET_ORDER_PROPERTIES)
  async getOrderProperties(data: { filter: FilterQuery<Order>; select }) {
    return this.orderModel.findOne(data.filter, data.select).lean();
  }

  @MessagePattern(BROKER_PATTERNS.ORDER.COUNT_ORDERS)
  async countOrders(filter: FilterQuery<Order>) {
    return this.orderService.countOrders(filter);
  }

  @MessagePattern(BROKER_PATTERNS.ORDER.CREATE_ORDER)
  async createOrder(data) {
    return this.orderService.create(data, process.env.CATLOG_WWW ?? '', false, true);
  }

  @MessagePattern(BROKER_PATTERNS.ORDER.GET_ORDER_LEAN)
  async getOrderLean(filter: FilterQuery<Order>) {
    return this.orderModel.findOne(filter).populate('store').populate('customer').lean();
  }

  @MessagePattern(BROKER_PATTERNS.ORDER.GET_ALL)
  async getAll(filter: FilterQuery<Order>) {
    return this.orderModel.find(filter);
  }

  @MessagePattern(BROKER_PATTERNS.ORDER.GET_ALL_LEAN)
  async getAllLean(data: { filter: FilterQuery<Order>; sort?: any; limit?: number }) {
    return this.orderModel
      .find(data.filter)
      .sort(data.sort ?? {})
      .limit(data.limit ?? 100)
      .lean();
  }

  @MessagePattern(BROKER_PATTERNS.ORDER.ADD_STATUS_TO_TIMELINE)
  async addStatusToTimeline({ filter, payload }: { filter: FilterQuery<Order>; payload: ORDER_STATUSES }) {
    const order = await this.orderService.addStatusToTimeLine(filter, payload);

    return order;
  }

  @MessagePattern(BROKER_PATTERNS.ORDER.ADD_ORDER_RECEIPT)
  async addReceiptToOrder({ filter, payload }: { filter: FilterQuery<Order>; payload: string }) {
    const order = await this.orderService.addReceiptToOrder(filter, payload);

    return order;
  }

  @MessagePattern(BROKER_PATTERNS.ORDER.UPDATE_ORDER)
  async updateOrder({ filter, payload }: { filter: FilterQuery<Order>; payload: any }) {
    const order = await this.orderService.updateOrder(filter, payload);
    return order;
  }

  @MessagePattern(BROKER_PATTERNS.ORDER.UPDATE_ORDER_STATUS)
  async updateOrderStatus({
    store_id,
    id,
    status,
    reason,
    url,
    notifyCustomerViaBot = false,
    notifySellerViaBot = true,
  }) {
    const order = await this.orderService.updateStatus(
      store_id,
      id,
      { status, reason },
      url,
      notifyCustomerViaBot,
      notifySellerViaBot,
    );
    return order;
  }

  @MessagePattern(BROKER_PATTERNS.ORDER.UPDATE_CUSTOMER)
  async upateCustomer({ filter, payload }: { filter: FilterQuery<Customer>; payload: any }) {
    return this.customerService.updateCustomer(filter.store, filter._id, payload);
  }

  @MessagePattern(BROKER_PATTERNS.ORDER.MARK_ORDER_AS_PAID)
  async markOrderAsPaid({
    filter,
    receipt,
    paymentId = null,
  }: {
    filter: FilterQuery<Order>;
    receipt: string;
    paymentId?: string;
  }) {
    const order = await this.orderService.markOrderAsPaid(filter, false, receipt, paymentId);

    return order;
  }

  @MessagePattern(BROKER_PATTERNS.ORDER.GET_CUSTOMER)
  async getCustomer(filter: FilterQuery<Customer>) {
    return this.customerModel.findOne(filter);
  }
  @MessagePattern(BROKER_PATTERNS.ORDER.GET_CUSTOMERS)
  async getCustomers({ filter, select }: { filter: FilterQuery<Customer>; select: any }) {
    return this.customerModel.find(filter, select);
  }
  @MessagePattern(BROKER_PATTERNS.ORDER.CREATE_CUSTOMER)
  async createCustomer(data: CreateCustomerDto) {
    return this.customerService.createCustomer(data);
  }

  @MessagePattern(BROKER_PATTERNS.ORDER.GET_TOTAL_ORDER_AMOUNT)
  async getTotalOrderAmount(filter: FilterQuery<Order>) {
    return this.orderService.getTotalStoreOrderAmount(filter);
  }

  @MessagePattern(BROKER_PATTERNS.ORDER.RECORD_FEEDBACK)
  async recordOrderFeedback(data: any) {
    return this.orderFeedbackService.createFeedback(data);
  }

  @MessagePattern(BROKER_PATTERNS.ORDER.GET_WRAP_DATA)
  async getWrapData(data: any) {
    return this.orderService.ordersWrapData(data.store, data.startDate, data.endDate);
  }

  @MessagePattern(BROKER_PATTERNS.ORDER.GET_MILESTONES)
  async getMilestones({ store, currency }) {
    const order = await this.orderService.getOrderMilestones(store, currency);
    return order;
  }
}
