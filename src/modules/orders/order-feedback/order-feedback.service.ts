import { Injectable } from '@nestjs/common';
import { Model } from 'mongoose';
import { OrderFeedback, OrderFeedbackDocument } from './order-feedback.schema';
import { InjectModel } from '@nestjs/mongoose';
import { CreateOrderFeedbackDto } from '../dto/create-order.dto';

@Injectable()
export class OrderFeedbackService {
  constructor(
    @InjectModel(OrderFeedback.name)
    private readonly orderFeedbackModel: Model<OrderFeedbackDocument>,
  ) {}

  async createFeedback(data: CreateOrderFeedbackDto) {
    const feedback = await this.orderFeedbackModel.create(data);
    return feedback;
  }
}
