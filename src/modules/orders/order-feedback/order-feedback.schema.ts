import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import mongoose, { Document } from 'mongoose';
import { PAYMENT_TYPES } from '../../../enums/payment.enum';
import { IsEnum } from 'class-validator';
import { CURRENCIES } from '../../country/country.schema';
import { Store } from '../../store/store.schema';
import { Customer } from '../customers/customer.schema';

export type OrderFeedbackDocument = OrderFeedback & Document;

export class Feedback {
  product_rating?: number;
  process_rating?: number;
  delivery_rating?: number;
  general_feedback?: string;
}

@Schema({ timestamps: true })
export class OrderFeedback {
  _id: string;

  @ApiProperty()
  @Prop({ type: String, required: false })
  order?: string;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Store' })
  store: string | Store;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Customer' })
  customer: string | Customer;

  @ApiProperty()
  @Prop({ type: Feedback, required: true })
  feedback: Feedback;
}

export const OrderFeedbackSchema = SchemaFactory.createForClass(OrderFeedback);
