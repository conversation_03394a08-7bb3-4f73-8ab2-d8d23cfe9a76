import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Store } from '../../store/store.schema';
import { Affiliate } from '../../affiliates/affiliates.schema';

type Fee = {
  label: string;
  fee: number;
};

export enum CustomerOrigin {
  STOREFRONT = 'STOREFRONT',
  INSTAGRAM = 'INSTAGRAM',
  CHOWBOT = 'CHOWBOT',
  MANUAL = 'MANUAL',
  AFFILIATE = 'AFFILIATE',
}

export class CustomerMeta {
  @ApiProperty()
  @Prop({ type: String, required: false })
  affiliate?: string | Affiliate;
}

export type CustomerDocument = Customer & Document;

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class Customer {
  _id: any;
  id: string;

  @ApiProperty()
  @Prop({ type: String, required: false })
  name: string;

  @ApiProperty()
  @Prop({ type: String, required: false })
  email: string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  phone: string;

  @ApiProperty()
  @Prop({ type: String, required: false })
  formatted_phone?: string;

  @ApiProperty()
  @Prop({ type: Boolean, required: false, default: false })
  name_verified?: boolean;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Store' })
  store: Store;

  @ApiProperty()
  @Prop({ type: String, required: false, default: CustomerOrigin.STOREFRONT })
  origin?: CustomerOrigin;

  @ApiProperty()
  @Prop({ type: CustomerMeta, required: false })
  meta?: CustomerMeta;
}

export const CustomerSchema = SchemaFactory.createForClass(Customer);

CustomerSchema.virtual('order', {
  ref: 'Order',
  localField: '_id',
  foreignField: 'customer',
});
