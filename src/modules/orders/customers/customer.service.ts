import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  NotFoundException,
  PreconditionFailedException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectConnection, InjectModel } from '@nestjs/mongoose';
import { Connection, FilterQuery, PaginateModel, Types } from 'mongoose';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { PaginatedQueryDto } from '../../../models/dtos/PaginatedDto';
import { checkIfUserOwnsStore, mapPaginatedResponse, mapPaginateQuery } from '../../../utils';
import { Store } from '../../store/store.schema';
import { CreateCustomerDto } from '../dto/create-order.dto';
import { Order, OrderDocument, ORDER_STATUSES } from '../order.schema';
import { Customer, CustomerDocument, CustomerOrigin } from './customer.schema';
import { cleanString, getDocId } from '../../../utils/functions';
import { CustomersFilterQueryDto } from '../../../models/dtos/order.dto';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';

@Injectable()
export class CustomerService {
  constructor(
    @InjectModel(Order.name)
    private readonly orderModel: PaginateModel<OrderDocument>,
    @InjectModel(Customer.name)
    private readonly customerModel: PaginateModel<CustomerDocument>,
    @InjectConnection() protected readonly connection: Connection,
    protected readonly brokerTransport: BrokerTransportService,
  ) {}

  async createCustomer(body: CreateCustomerDto, isPublic: boolean = false) {
    const { affiliate, ...rest } = body;
    let parsedAffiliate;

    if (affiliate) {
      parsedAffiliate = await this.brokerTransport
        .send(BROKER_PATTERNS.AFFILIATE.GET_AFFILIATE, {
          slug: affiliate,
        })
        .toPromise();

      if (!parsedAffiliate && !isPublic) {
        throw new NotFoundException('Affiliate not found');
      }
    }

    const existingCustomer = await this.customerModel.findOne({
      phone: body.phone,
      store: body.store as any,
    });

    if (existingCustomer && isPublic) {
      return existingCustomer;
    }

    if (existingCustomer) {
      throw new PreconditionFailedException('Customer with phone number already exists');
    }

    const customer = await this.customerModel.create({
      ...body,
      name: cleanString(body.name),
      ...(affiliate && parsedAffiliate && { 'meta.affiliate': getDocId(parsedAffiliate) }),
      origin: body.origin ?? CustomerOrigin.STOREFRONT,
    } as any);

    if (parsedAffiliate) {
      await this.brokerTransport
        .send(BROKER_PATTERNS.AFFILIATE.UPDATE_CUSTOMERS, {
          affiliate_id: getDocId(parsedAffiliate),
          count: 1,
        })
        .toPromise();
    }

    return customer;
  }

  async getPaginatedCustomers(filterQuery: CustomersFilterQueryDto, paginationQuery: PaginatedQueryDto) {
    const filters = this.constructFilterQuery(filterQuery);
    const result = await this.customerModel.paginate(filters, {
      ...mapPaginateQuery(paginationQuery),
    });

    const parsedDocs = [];

    for (let customer of result.docs) {
      const totalOrders = await this.orderModel.countDocuments({
        customer: customer._id,
        status: { $nin: [ORDER_STATUSES.CANCELLED, ORDER_STATUSES.ABANDONED] },
        data: { $ne: true },
      });
      const [order] = await this.orderModel.aggregate([
        {
          $match: {
            customer: Types.ObjectId(customer._id),
            status: { $nin: [ORDER_STATUSES.CANCELLED, ORDER_STATUSES.ABANDONED] },
          },
        },
        { $group: { _id: null, sum: { $sum: '$total_amount' } } },
      ]);

      parsedDocs.push({ ...customer.toJSON(), totalOrders, totalOrderAmount: order?.sum });
    }

    return {
      data: parsedDocs,
      ...mapPaginatedResponse(result),
    };
  }

  async getPaginatedCustomersBasic(filterQuery: CustomersFilterQueryDto, paginationQuery: PaginatedQueryDto) {
    const filters = this.constructFilterQuery(filterQuery);
    const result = await this.customerModel.paginate(filters, {
      ...mapPaginateQuery(paginationQuery),
    });

    return {
      data: result.docs,
      ...mapPaginatedResponse(result),
    };
  }

  constructFilterQuery(filterQueryDto: CustomersFilterQueryDto) {
    const { store, search, status, from, to, ...genericFilters } = filterQueryDto;

    const $or: FilterQuery<Customer>['$or'] = [];

    const filters: FilterQuery<Customer> = {
      ...genericFilters,
      is_deleted: { $ne: true },
      store: store as any,
    };

    if (search) {
      $or.push(
        { name: new RegExp(search, 'ig') },
        { phone: new RegExp(search, 'ig') },
        { email: new RegExp(search, 'ig') },
      );
    }

    if (from || to) {
      filters.created_at = {
        $gte: new Date(from ?? new Date()),
        $lte: new Date(to ?? new Date()),
      };
    }

    if (status) {
    }

    if ($or?.length > 0) filters.$or = $or;
    return filters;
  }

  async getCustomer(storeId: string, customerId) {
    const customer = await this.customerModel.findById(customerId);
    if (!customer) {
      throw new NotFoundException('Customer with id does not exist');
    }

    if (String(customer.store) !== storeId) {
      throw new ForbiddenException("This customer doesn't belong to this store");
    }

    const totalOrders = await this.orderModel.countDocuments({
      customer: customer.id,
      status: { $nin: [ORDER_STATUSES.CANCELLED, ORDER_STATUSES.ABANDONED] },
      data: { $ne: true },
    });

    const [order] = await this.orderModel.aggregate([
      {
        $match: {
          customer: Types.ObjectId(customer.id),
          status: { $nin: [ORDER_STATUSES.CANCELLED, ORDER_STATUSES.ABANDONED] },
          data: { $ne: true },
        },
      },
      { $group: { _id: null, sum: { $sum: '$total_amount' } } },
    ]);

    const orders = await this.orderModel
      .find({ customer: customer._id, data: { $ne: true } }, { _id: 1, items: 1, total_amount: 1, currency: 1 })
      .sort({ created_at: -1 })
      .lean();
    // const orders = await this.orderModel.find({ customer: customer._id }, {}).lean();

    return { ...customer.toJSON(), totalOrders, totalOrderAmount: order?.sum, orders };
  }

  async getCustomerCount(storeId: string, filter: any) {
    const result = await this.customerModel.countDocuments({ store: storeId, ...filter });
    return result;
  }

  async updateCustomer(storeId, customerId, update) {
    const customer = await this.customerModel.findById(customerId);
    if (!customer) {
      throw new NotFoundException('Customer with id does not exist');
    }

    if (String(customer.store) !== storeId) {
      throw new ForbiddenException("This customer doesn't belong to this store");
    }

    // await this.brokerTransport
    //   .send(BROKER_PATTERNS.DELVERIES.UPDATE_ADDRESS, { filter: { customer: customerId }, update })
    //   .toPromise();

    return this.customerModel.findByIdAndUpdate(customerId, update, {
      new: true,
    });
  }

  async publicCustomerUpdate(customerId, update) {
    const customer = await this.customerModel.findById(customerId);
    if (!customer) {
      throw new NotFoundException('Customer with id does not exist');
    }

    if (customer.phone !== update.phone) {
      throw new UnauthorizedException("You're not authorized to update this customer");
    }

    // await this.brokerTransport
    //   .send(BROKER_PATTERNS.DELVERIES.UPDATE_ADDRESS, { filter: { customer: customerId }, update })
    //   .toPromise();

    const { store, ...rest } = update;
    return this.customerModel.findByIdAndUpdate(customerId, rest, {
      new: true,
    });
  }

  async deleteCustomer(filter) {
    const customer = await this.customerModel.findOneAndDelete(filter);
    if (!customer) {
      throw new BadRequestException("This customer doesn't belong to this store");
    }

    checkIfUserOwnsStore({ id: filter.store } as Store, (customer.store as unknown) as string);

    const orderCount = await this.orderModel.countDocuments({
      customer: filter._id,
    });

    if (orderCount > 0) {
      throw new BadRequestException('Cannot delete customer with order');
    }

    return;
  }

  async getCustomerStatistics(storeId: string, filter?: any) {
    const from = new Date(filter?.from || new Date());
    const to = new Date(filter?.to || new Date());

    // Calculate previous period for trend comparison
    const currentPeriodDays = Math.ceil((to.getTime() - from.getTime()) / (1000 * 60 * 60 * 24));
    const prevPeriodFrom = new Date(from);
    prevPeriodFrom.setDate(prevPeriodFrom.getDate() - currentPeriodDays);
    const prevPeriodTo = new Date(from);
    prevPeriodTo.setDate(prevPeriodTo.getDate() - 1);

    // Current period filter
    const currentPeriodFilter = {
      created_at: {
        $gte: from,
        $lte: to,
      },
    };

    // Previous period filter
    const prevPeriodFilter = {
      created_at: {
        $gte: prevPeriodFrom,
        $lte: prevPeriodTo,
      },
    };

    // 1. Number of new customers in current period
    const newCustomersCount = await this.customerModel.countDocuments({
      store: storeId as any,
      ...currentPeriodFilter,
    });

    // Number of new customers in previous period (for trend)
    const prevPeriodNewCustomersCount = await this.customerModel.countDocuments({
      store: storeId as any,
      ...prevPeriodFilter,
    });

    // 2. Get returning customers (customers with more than 1 order)
    const matchFilter: any = {
      store: new Types.ObjectId(storeId),
      status: { $nin: [ORDER_STATUSES.CANCELLED, ORDER_STATUSES.ABANDONED] },
      data: { $ne: true },
    };

    // If a date filter is provided, add it to match orders within that period
    if (filter?.from || filter?.to) {
      matchFilter.created_at = {};
      if (filter?.from) matchFilter.created_at.$gte = new Date(filter.from);
      if (filter?.to) matchFilter.created_at.$lte = new Date(filter.to);
    }

    const customerOrdersAggregation = await this.orderModel.aggregate([
      {
        $match: matchFilter,
      },
      {
        $group: {
          _id: '$customer',
          orderCount: { $sum: 1 },
        },
      },
      {
        $match: {
          orderCount: { $gt: 1 },
        },
      },
      {
        $count: 'returningCustomers',
      },
    ]);

    const returningCustomersCount = customerOrdersAggregation[0]?.returningCustomers || 0;

    // 3. Calculate average orders per customer
    const totalCustomersCount = await this.customerModel.countDocuments({
      store: storeId as any,
    });

    const totalOrdersCount = await this.orderModel.countDocuments({
      store: storeId as any,
      status: { $nin: [ORDER_STATUSES.CANCELLED, ORDER_STATUSES.ABANDONED] },
      data: { $ne: true },
    });

    const avgOrdersPerCustomer =
      totalCustomersCount > 0 ? parseFloat((totalOrdersCount / totalCustomersCount).toFixed(2)) : 0;

    // Calculate trends
    const newCustomersTrend = this.calcPercentageDifference(prevPeriodNewCustomersCount, newCustomersCount);

    return {
      new_customers: {
        count: newCustomersCount,
        trend: newCustomersTrend,
      },
      returning_customers: {
        count: returningCustomersCount,
      },
      avg_orders_per_customer: avgOrdersPerCustomer,
      total_customers: totalCustomersCount,
    };
  }

  private calcPercentageDifference(start: number, end: number): number {
    if (start === 0 || end === 0) {
      return start > end ? -100 : start === end ? 0 : 100;
    }
    return Math.floor((100.0 / start) * (end - start));
  }
}
