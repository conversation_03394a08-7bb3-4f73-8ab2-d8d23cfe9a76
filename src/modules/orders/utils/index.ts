import { Coupon } from '../../item/discount-coupons/discounts-coupons.schema';
import { CustomCheckoutFormItem } from '../../store/store.schema';

export function computeCouponDiscount(
  totalAmount: number,
  coupon: Coupon,
  convertCurrency: (amount: number) => number,
) {
  let discountAmount;
  const couponCap = convertCurrency(coupon.discount_cap);

  if (coupon.type == 'percentage') {
    discountAmount = (totalAmount * coupon.percentage) / 100;
    const discountExceeded = couponCap ? discountAmount > couponCap : false;
    discountAmount = discountExceeded ? couponCap : discountAmount;
  } else if (coupon.type == 'fixed') {
    discountAmount = convertCurrency(coupon.discount_amount);
  }

  return discountAmount > totalAmount ? -totalAmount : -discountAmount;
}

export function couponIsValid(coupon: Coupon) {
  return (
    coupon !== undefined &&
    coupon !== null &&
    coupon?.quantity !== 0 &&
    new Date(coupon?.end_date) >= new Date() &&
    coupon?.active !== false
  );
}

// export function filterItemsWithPricedOptions(formItems) {
//   if (!formItems || !Array.isArray(formItems)) {
//     return [];
//   }

//   return formItems.filter((item) => {
//     // Check if the item has options
//     if (!item.options || !Array.isArray(item.options) || item.options.length === 0) {
//       return false;
//     }

//     // Check if at least one option has a price > 0
//     return item.options.some(
//       (option) => option.price !== undefined && option.price !== null && Number(option.price) > 0,
//     );
//   });
// }

export function filterItemsWithPricedOptions(formItems) {
  if (!formItems || !Array.isArray(formItems)) {
    return [];
  }

  return formItems.filter((item) => {
    // Check if the item has options
    if (!item.options || !Array.isArray(item.options) || item.options.length === 0) {
      return false;
    }

    // Check if at least one option has a price > 0
    return item.options.some((option) => {
      const priceValue = Number(option.price);
      return option.price !== undefined && option.price !== null && !isNaN(priceValue) && priceValue > 0;
    });
  });
}

// function findMatchingOptionsWithPrice(
//   filteredItems: CustomCheckoutFormItem[],
//   keyValuePairs: { [key: string]: string },
// ) {
//   if (!filteredItems || !Array.isArray(filteredItems) || !keyValuePairs) {
//     return [];
//   }

//   const result: { label: string; amount: number }[] = [];

//   // Iterate through each key-value pair
//   Object.entries(keyValuePairs).forEach(([key, value]) => {
//     // Find the form item that matches the key (name)
//     const matchingItem = filteredItems.find((item) => item.name.toLowerCase() === key.toLowerCase());

//     // If a matching item is found
//     if (matchingItem && matchingItem.options && Array.isArray(matchingItem.options)) {
//       // Find the option that matches the value
//       const matchingOption = matchingItem.options.find((option) => option.value.toLowerCase() === value.toLowerCase());

//       // If a matching option is found with a price > 0
//       if (matchingOption && matchingOption.price !== undefined && Number(matchingOption.price) > 0) {
//         result.push({
//           label: value,
//           amount: Number(matchingOption.price),
//         });
//       }
//     }
//   });

//   return result;
// }

export function findMatchingOptionsWithPrice(
  filteredItems: CustomCheckoutFormItem[],
  keyValuePairs: { [key: string]: string },
) {
  if (!filteredItems || !Array.isArray(filteredItems) || !keyValuePairs) {
    return [];
  }

  const result: { label: string; amount: number }[] = [];

  // Iterate through each key-value pair
  Object.entries(keyValuePairs).forEach(([key, value]) => {
    // Find the form item that matches the key (name)
    const matchingItem = filteredItems.find((item) => item.name.toLowerCase() === key.toLowerCase());

    // If a matching item is found
    if (matchingItem && matchingItem.options && Array.isArray(matchingItem.options)) {
      // Find the option that matches the value
      const matchingOption = matchingItem.options.find(
        (option) => option.value && value && option.value.toLowerCase() === value.toLowerCase(),
      );

      // If a matching option is found with a price > 0
      if (matchingOption && matchingOption.price !== undefined) {
        const priceValue = Number(matchingOption.price);
        if (!isNaN(priceValue) && priceValue > 0) {
          result.push({
            label: value,
            amount: priceValue,
          });
        }
      }
    }
  });

  return result;
}
