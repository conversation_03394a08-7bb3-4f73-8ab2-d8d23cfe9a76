import { Module } from '@nestjs/common';
import { SharedModule } from '../../shared.module';
import { MongooseModule } from '@nestjs/mongoose';
import setIdPlugin from '../../mongoose-plugins/set-id.mongoose-plugin';
import jsonHookPlugin from '../../mongoose-plugins/json-hook.mongoose-plugin';
import mongoosePaginate from 'mongoose-paginate-v2';

import { Invoice, InvoiceSchema } from './invoice.schema';
import { InvoicesController } from './invoice.controller';
import { InvoiceService } from './invoice.service';
import { InvoiceBroker } from './invoice.broker';

@Module({
  imports: [
    MongooseModule.forFeatureAsync([
      {
        name: Invoice.name,
        useFactory: () => {
          InvoiceSchema.plugin(setIdPlugin());
          InvoiceSchema.plugin(jsonHookPlugin(['_id', '__v']));
          InvoiceSchema.plugin(mongoosePaginate);
          return InvoiceSchema;
        },
      },
    ]),
    SharedModule,
  ],
  controllers: [InvoicesController, InvoiceBroker],
  providers: [InvoiceService],
})
export class InvoiceModule {}
