import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  InternalServerErrorException,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { Readable } from 'stream';
import { CountryPermissons, RolePermissions } from '../../decorators/permission.decorator';
import { CountryGuard } from '../../guards/country.guard';
import { RoleGuard } from '../../guards/role.guard';
import { IRequest, IResponse } from '../../interfaces/request.interface';
import { JwtAuthGuard } from '../../jwt/jwt-auth.guard';
import {
  InvoiceCreateDto,
  InvoiceDraftDto,
  InvoicePaymentDto,
  SendInvoiceDto,
  SimpleInvoiceCreateDto,
} from '../../models/dtos/invoice.dto';
import { checkIfUserOwnsStore, paymentsEnabledGuard } from '../../utils';
import { SCOPES } from '../../utils/permissions.util';
import { Store } from '../store/store.schema';
import { InvoiceEmailDto, PaginatedQueryDto } from './dtos/get-invoices';
import { InvoiceService } from './invoice.service';
import { ApiExcludeEndpoint, ApiSecurity } from '@nestjs/swagger';
import { InternalApiJWTGuard } from '../../guards/api.guard';

@ApiSecurity('bearer')
@Controller('invoices')
export class InvoicesController {
  constructor(private readonly invoiceService: InvoiceService, private readonly logger: Logger) {}

  @Post('/')
  @RolePermissions(SCOPES.INVOICES.MANAGE)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  async create(@Body() invoiceData: InvoiceCreateDto, @Req() req: IRequest) {
    checkIfUserOwnsStore(req.user.store as Store, invoiceData.store);
    paymentsEnabledGuard(req.user.store as Store);
    const invoice = await this.invoiceService.newInvoice(invoiceData);

    return {
      message: 'Invoice created successfully',
      data: invoice,
    };
  }

  @Post('/payment-link')
  @RolePermissions(SCOPES.INVOICES.MANAGE)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  async createPaymentLink(@Body() invoiceData: SimpleInvoiceCreateDto, @Req() req: IRequest) {
    paymentsEnabledGuard(req.user.store as Store);
    const invoice = await this.invoiceService.createPaymentLink({ ...invoiceData, store: req.user.store.id });

    return {
      message: 'Payment link created successfully',
      data: invoice,
    };
  }

  @Put('/payment-link/:id')
  @RolePermissions(SCOPES.INVOICES.MANAGE)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  async updatePaymentLink(@Body() invoiceData: SimpleInvoiceCreateDto, @Param('id') id: string, @Req() req: IRequest) {
    paymentsEnabledGuard(req.user.store as Store);
    //need to implement update
    const invoice = await this.invoiceService.updatePaymentLink(req, id, invoiceData);

    return {
      message: 'Payment link updated successfully',
      data: invoice,
    };
  }

  @Post('/draft')
  @RolePermissions(SCOPES.INVOICES.MANAGE)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  async createDraft(@Body() invoiceData: InvoiceDraftDto, @Req() req: IRequest) {
    checkIfUserOwnsStore(req.user.store as Store, invoiceData.store);
    paymentsEnabledGuard(req.user.store as Store);
    const invoice = await this.invoiceService.draft(invoiceData);

    return {
      message: 'Invoice drafted succesafully',
      data: invoice,
    };
  }

  @Put('/:id')
  @RolePermissions(SCOPES.INVOICES.MANAGE)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  async updateInvoice(@Req() req: IRequest, @Param('id') id: string, @Body() invoiceData: InvoiceCreateDto) {
    paymentsEnabledGuard(req.user.store as Store);
    const data = await this.invoiceService.update(req, id, invoiceData);

    return {
      data,
      message: 'Invoice Updated',
    };
  }

  @Put('/draft/:id')
  @RolePermissions(SCOPES.INVOICES.MANAGE)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  async updateDraft(@Body() data: any, @Param('id') id: string, @Req() req: IRequest) {
    delete data.store;
    paymentsEnabledGuard(req.user.store as Store);
    const invoice = await this.invoiceService.updateDraft(id, data);

    return {
      message: 'Invoice drafted succesafully',
      data: invoice,
    };
  }

  @Get('/public/:id')
  async getInvoicePublic(@Param('id') id: string) {
    const data = await this.invoiceService.getInvoicePublic(id);
    return {
      data,
      message: 'successfully fetched invoice',
    };
  }

  @Get('')
  @RolePermissions(SCOPES.INVOICES.MANAGE)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  async getItems(@Query('filter') filter: any, @Query() query: PaginatedQueryDto, @Req() req: IRequest) {
    paymentsEnabledGuard(req.user.store as Store);
    try {
      if (!filter) {
        throw new BadRequestException('filter object is missing in url query');
      }

      const items = await this.invoiceService.getInvoices(req.user.store.id, filter, query);

      return {
        message: 'Items fetched successfully',
        ...items,
      };
    } catch (err) {
      this.logger.error(`error occurred fetching list of items`, err);
      if (!(err instanceof BadRequestException)) {
        throw new InternalServerErrorException();
      }
      throw err;
    }
  }

  // @Post('/paid/:id')
  // @RolePermissions(SCOPES.INVOICES.MANAGE)
  // @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  // @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  // async paidInvoice(@Param('id') id: string, @Body() payment: InvoicePaymentDto) {
  //   const invoice = await this.invoiceService.invoicePaid(id, payment.reference);

  //   return {
  //     message: 'Invoice updated',
  //     data: invoice,
  //   };
  // }

  // @Post('/paid/:id')
  // @RolePermissions(SCOPES.INVOICES.MANAGE)
  // @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  // @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  // async paidInvoice(@Param('id') id: string, @Body() payment: InvoicePaymentDto) {
  //   const invoice = await this.invoiceService.invoicePaid(id, payment.reference);

  //   return {
  //     message: 'Invoice updated',
  //     data: invoice,
  //   };
  // }

  @Get('/statistics')
  @RolePermissions(SCOPES.INVOICES.VIEW_ANALYTICS)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  async getInvoiceStatistics(@Req() req: IRequest) {
    paymentsEnabledGuard(req.user.store as Store);
    const stats = await this.invoiceService.getStatistics(req.user.store.id);

    return {
      message: 'Invoice statistics gotten',
      data: stats,
    };
  }

  @Put('/:id/paid')
  @RolePermissions(SCOPES.INVOICES.MANAGE)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  async updateInvoiceToPaid(@Req() req: IRequest, @Param('id') id: string) {
    paymentsEnabledGuard(req.user.store as Store);
    const data = await this.invoiceService.upadateInvoiceToPaid(req.user, id);
    return {
      data,
      message: 'Successfully updated invoice to paid',
    };
  }

  @Post('/:id/mail')
  @RolePermissions(SCOPES.INVOICES.MANAGE)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  async sendInvoiceEmail(@Param('id') id: string, @Req() req: IRequest, @Body() body: InvoiceEmailDto) {
    paymentsEnabledGuard(req.user.store as Store);
    await this.invoiceService.mailInvoice(req.user, body.message, body.email, id);

    return {
      message: 'Succesfully mailed invoice',
    };
  }

  @Get('/pdf/:id')
  async getPdf(@Param('id') id: string, @Res() res: IResponse) {
    const buffer = await this.invoiceService.getPdfFile(id);
    const readable = new Readable();
    readable._read = () => {};
    readable.push(buffer);
    readable.push(null);
    readable.pipe(res);
    return {};
  }

  @Delete(':id')
  @RolePermissions(SCOPES.INVOICES.MANAGE)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  async deleteInvoice(@Req() req: IRequest, @Param('id') id: string) {
    paymentsEnabledGuard(req.user.store as Store);
    const data = await this.invoiceService.deleteInvoice(req.user, id);
    return {
      data,
      message: 'Successfully deleted invoice',
    };
  }

  @Post('/send/')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, CountryGuard)
  async sendInvoice(@Body() dto: SendInvoiceDto) {
    return {
      message: 'Successfully sent invoice to customer',
    };
  }

  @Get('/setup-progress')
  @RolePermissions(SCOPES.INVOICES.MANAGE)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  async getInvoicesSetupProgress(@Req() req: IRequest) {
    paymentsEnabledGuard(req.user.store as Store);
    const data = await this.invoiceService.getSetupProgress(req?.user?.store?.id);

    return {
      data,
      message: 'Payments setup progress fetched',
    };
  }

  @Post('/migrate-payments-enabled')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async migratePaymentsEnabled() {
    const data = await this.invoiceService.migratePaymentsEnabled();

    return {
      message: 'migrated invoices',
      data,
    };
  }

  @Post('/migrate-order-payments')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async migrateOrderPayments() {
    const data = await this.invoiceService.migrateOrderPayments();

    return {
      message: 'migrated order payments',
      data,
    };
  }
}
