import { Pro<PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Document } from 'mongoose';
import { FEE_TYPES } from '../../enums/order.enum';
import { COUNTRY_CODE, CURRENCIES } from '../country/country.schema';

export type InvoiceDocument = Invoice & Document;

export enum INVOICE_STATUSES {
  PENDING = 'PENDING',
  PAID = 'PAID',
  EXPIRED = 'EXPIRED',
  PAYMENT_IN_PROGRESS = 'PAYMENT_IN_PROGRESS',
}

export enum INVOICE_TYPE {
  REGULAR = 'REGULAR',
  PAYMENT_LINK = 'PAYMENT_LINK',
}

export class InvoiceSender {
  @ApiProperty()
  @Prop({ type: String })
  image: string;

  @ApiProperty()
  @Prop({ type: String })
  name: string;

  @ApiProperty()
  @Prop({ type: String })
  address: string;

  @ApiProperty()
  @Prop({ type: String })
  phone: string;
}

export class InvoiceReceiver {
  @ApiProperty()
  @Prop({ type: String })
  id: string;

  @ApiProperty()
  @Prop({ type: String })
  name: string;

  @ApiProperty()
  @Prop({ type: String })
  phone: string;

  @ApiProperty()
  @Prop({ type: String })
  email: string;
}

export class InvoiceItem {
  @ApiProperty()
  @Prop({ type: String })
  id: string;

  @ApiProperty()
  @Prop({ type: String })
  name: string;

  @ApiProperty()
  @Prop({ type: Number })
  price: number;

  @ApiProperty()
  @Prop({ type: Number })
  quantity: number;
}

export class InvoiceFee {
  @ApiProperty()
  @Prop({ type: Number })
  amount: number;

  @ApiProperty()
  @Prop({ enum: [FEE_TYPES.DELIVERY, FEE_TYPES.DISCOUNT, FEE_TYPES.VAT, FEE_TYPES.DISCOUNT, FEE_TYPES.OTHERS] })
  type: FEE_TYPES;
}

@Schema({ timestamps: true })
export class Invoice {
  @ApiProperty()
  id: string;

  @ApiProperty()
  @Prop({ type: String })
  invoice_id: string;

  @ApiProperty()
  @Prop({ type: String })
  number: string;

  @ApiProperty()
  @Prop({ type: String })
  title: string;

  @ApiProperty()
  @Prop({ type: InvoiceSender })
  sender: InvoiceSender;

  @ApiProperty()
  @Prop({ type: InvoiceReceiver })
  receiver: InvoiceReceiver;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  payments_enabled: boolean;

  @ApiProperty()
  @Prop({ type: Date })
  date_created: Date;

  @ApiProperty()
  @Prop({ type: Date })
  date_due: Date;

  @ApiProperty()
  @Prop({ enum: ['PENDING', 'PAID', 'EXPIRED', 'PAYMENT_IN_PROGRESS'] })
  status: INVOICE_STATUSES;

  @ApiProperty()
  @Prop({ enum: [INVOICE_TYPE.REGULAR, INVOICE_TYPE.PAYMENT_LINK], default: INVOICE_TYPE.REGULAR })
  type: INVOICE_TYPE;

  @ApiProperty()
  @Prop({
    type: CURRENCIES,
    enum: [
      CURRENCIES.GHC,
      CURRENCIES.NGN,
      CURRENCIES.USD,
      CURRENCIES.GBP,
      CURRENCIES.EUR,
      CURRENCIES.ZAR,
      CURRENCIES.CAD,
    ],
    default: CURRENCIES.NGN,
  })
  currency: CURRENCIES = CURRENCIES.NGN;

  @ApiProperty()
  @Prop({ type: Number })
  total_amount: number;

  @ApiProperty()
  @Prop({ type: [] })
  items: InvoiceItem[];

  @ApiProperty()
  @Prop({ type: [] })
  fees: InvoiceFee[];

  @ApiProperty()
  @Prop({ type: Boolean })
  is_draft: boolean;

  @ApiProperty()
  @Prop({ type: String })
  pdf_link: string;

  @ApiProperty()
  @Prop({ type: String })
  store: string;

  @ApiProperty()
  @Prop({ type: String })
  order: string;

  @ApiProperty()
  @Prop({ type: Date })
  paid_at: Date;

  @ApiProperty()
  @Prop({ type: String })
  receipt?: string;

  @ApiProperty()
  @Prop({ type: String, required: false })
  payment_id?: string;
}

export const InvoiceSchema = SchemaFactory.createForClass(Invoice);
