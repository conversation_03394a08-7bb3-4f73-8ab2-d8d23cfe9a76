import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString } from 'class-validator';

export class PaginatedQueryDto {
  @ApiProperty({ required: false })
  page: number;

  @ApiProperty({ required: false })
  per_page: number;

  @ApiProperty({ required: false, type: 'string', enum: ['ASC', 'DESC'] })
  sort?: any;
}

export class InvoiceEmailDto {
  @ApiProperty({ required: true })
  @IsEmail()
  email: string;

  @ApiProperty({ required: true })
  @IsString()
  message: string;
}
