import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import {
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayInit,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { parse } from 'cookie';
import { Server, Socket } from 'socket.io';
import { BrokerTransportService } from '../../broker/broker-transport.service';
import { JwtConfig } from '../../config/types/jwt.config';
import { bridge, ping_records } from './event.bridge';

@WebSocketGateway({ cors: true })
export class WebsocketGateway implements OnGatewayConnection, OnGatewayInit {
  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly brokerTransport: BrokerTransportService,
  ) {}

  afterInit(server: Server) {
    bridge.on('data', (data) => {
      console.log(server.emit(data.id, data.data));
    });
  }

  handleConnection(client: any, ...args: any[]) {
    return;
  }

  @SubscribeMessage('handshake')
  async authHandshake(@MessageBody() data: string, @ConnectedSocket() client: Socket) {
    const user = await this.getUserFromSocket(data);

    if (!user) return;

    client.emit('connect-id', user);
    return user;
  }

  private async getUserFromSocket(
    token: string,
  ): Promise<{ id: string; store: { id: string; subscription: string } } | undefined> {
    if (!token || token === 'undefined') return;

    const payload = this.jwtService.verify(token, {
      secret: this.configService.get<JwtConfig>('jwtConfiguration').secret,
    });

    return payload;
  }
}
