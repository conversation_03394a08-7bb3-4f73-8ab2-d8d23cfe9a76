import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model } from 'mongoose';
import { Analytic, AnalyticDocument } from './analytic.schema';
import { CreateAnalyticDto } from './dto/analytic.dto';
import { BrokerTransportService } from '../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { Store } from '../store/store.schema';
import { ResendRepository } from '../../repositories/resend.repository';

@Injectable()
export class AnalyticService {
  constructor(
    private readonly brokerTransport: BrokerTransportService,
    private readonly resend: ResendRepository,
    private readonly logger: Logger,
    @InjectModel(Analytic.name)
    private readonly analyticModel: Model<AnalyticDocument>,
  ) {
    this.logger.setContext('AnalyticService');
  }

  async create(body: CreateAnalyticDto) {
    body.time = Date.now().toString();
    if (body.item) {
      await this.brokerTransport.send(BROKER_PATTERNS.ITEM.ADD_VIEW, { _id: body.item }).toPromise();
    } else if (body.store) {
      const store = await this.brokerTransport
        .send<Store>(BROKER_PATTERNS.STORE.ADD_VIEW, { _id: body.store })
        .toPromise();
      // this.sendMilestoneEmail(store)
    }

    return await this.analyticModel.create(body as any);
  }

  async totalStores() {
    return this.brokerTransport.send<number>(BROKER_PATTERNS.ANALYTICS.TOTAL_NUMBER_OF_STORES, {}).toPromise();
  }

  async totalPaidStores() {
    return this.brokerTransport.send<number>(BROKER_PATTERNS.ANALYTICS.TOTAL_PAID_STORES, {}).toPromise();
  }

  async totalStoresCreatedToday() {
    return this.brokerTransport.send<number>(BROKER_PATTERNS.ANALYTICS.TOTAL_STORES_CREATED_TODAY, {}).toPromise();
  }

  async totalStoreVisits() {
    return this.analyticModel.count({
      store: { $exists: true, $ne: null },
      item: { $exists: false },
    });
  }

  async totalUsers() {
    return this.brokerTransport.send<number>(BROKER_PATTERNS.ANALYTICS.TOTAL_USERS, {}).toPromise();
  }

  async getStoreVisits(filter: FilterQuery<Analytic>) {
    return await this.analyticModel
      .find(filter, { time: 1 })
      .lean()
      .then((analytics) => analytics.map((a) => ({ time: a.time, value: a._id })));
  }

  getModel() {
    return this.analyticModel;
  }

  async sendMilestoneEmail(store: Store) {
    const { total_visits, owner } = store;
    let milestoneData = undefined;

    const milestones = [
      {
        broker_pattern: BROKER_PATTERNS.MAIL.HUNDRED_STORE_VISITS,
        email_subject: `Hey ${owner.name}, congratulations on 100 store visits 🎉`,
        value: 100,
      },
      {
        broker_pattern: BROKER_PATTERNS.MAIL.FIVE_HUNDRED_STORE_VISITS,
        email_subject: `Hey ${owner.name}, congratulations on 500 store visits 🎉`,
        value: 500,
      },
      {
        broker_pattern: BROKER_PATTERNS.MAIL.THOUSAND_STORE_VISITS,
        email_subject: `Hey ${owner.name}, congratulations on 1000 store visits 🎉`,
        value: 1000,
      },
    ];

    for (let m of milestones) {
      if (total_visits === m.value) {
        milestoneData = m;
      }
    }

    if (milestoneData !== undefined) {
      await this.resend.sendEmail(milestoneData.broker_pattern, {
        to: owner.email,
        subject: milestoneData.email_subject,
        data: {
          name: owner.name,
        },
      });
    }
  }
}
