import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Document, Types } from 'mongoose';
import { Store } from '../store/store.schema';
import { Item } from '../item/item.schema';

export type AnalyticDocument = Analytic & Document;

export enum PAGE_TYPE {
  STORE = 'STORE',
  ITEM = 'ITEM',
}

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class Analytic {
  @ApiProperty()
  id: string;

  @ApiProperty()
  @Prop({ type: String })
  ip: string;

  @ApiProperty()
  @Prop({ type: String })
  client: string;

  @ApiProperty()
  @Prop({ type: String })
  ref: string;

  @ApiProperty()
  @Prop({ type: Date })
  time: Date;

  @ApiProperty()
  @Prop({ type: Types.ObjectId, ref: 'Store' })
  store: Store;

  @ApiProperty()
  @Prop({ type: Types.ObjectId, ref: 'Item' })
  item: Item;

  @ApiProperty()
  @Prop({ type: String, enum: [PAGE_TYPE.STORE, PAGE_TYPE.ITEM] })
  page_type: PAGE_TYPE;
}

export const AnalyticSchema = SchemaFactory.createForClass(Analytic);
