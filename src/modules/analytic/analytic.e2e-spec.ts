import { Test, TestingModule } from '@nestjs/testing';
import { AppModule } from '../../app.module';
import { Transport } from '@nestjs/microservices';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { TEST_ITEM_ID, TEST_STORE_ID } from '../../tools/testdata';

describe('Analytic Module', () => {
  let app: INestApplication;

  beforeEach(async () => {
    jest.setTimeout(30000);

    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = module.createNestApplication();
    app.connectMicroservice({
      transport: Transport.NATS,
      options: {
        url: process.env.NATS_URL || 'nats://localhost:4222',
      },
    });

    await app.startAllMicroservicesAsync();
    await app.init();
  });

  xit('should create analytics for item', async () => {
    return request(app.getHttpServer())
      .post('/analytics')
      .send({ item: TEST_ITEM_ID })
      .expect(201)
      .then(({ body: { message } }) => {
        expect(message).toBeTruthy();
      });
  });

  xit('should create analytics for store', async () => {
    return request(app.getHttpServer())
      .post('/analytics')
      .send({ store: TEST_STORE_ID })
      .expect(201)
      .then(({ body: { message } }) => {
        expect(message).toBeTruthy();
      });
  });
});
