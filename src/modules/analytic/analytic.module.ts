import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { AnalyticService } from './analytic.service';
import { AnalyticController } from './analytic.controller';
import { MongooseModule } from '@nestjs/mongoose';
import setIdPlugin from '../../mongoose-plugins/set-id.mongoose-plugin';
import jsonHookPlugin from '../../mongoose-plugins/json-hook.mongoose-plugin';
import { SharedModule } from '../../shared.module';
import { Analytic, AnalyticSchema } from './analytic.schema';
import { AnalyticBroker } from './analytic.broker';

@Module({
  imports: [
    MongooseModule.forFeatureAsync([
      {
        name: Analytic.name,
        useFactory: () => {
          AnalyticSchema.plugin(setIdPlugin());
          AnalyticSchema.plugin(jsonHookPlugin(['_id', '__v']));
          return AnalyticSchema;
        },
      },
    ]),
    SharedModule,
  ],
  controllers: [AnalyticController, AnalyticBroker],
  providers: [AnalyticService],
  exports: [AnalyticService],
})
export class AnalyticModule {}
