import {
  BadRequestException,
  Body,
  Controller,
  Get,
  InternalServerErrorException,
  Logger,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AnalyticService } from './analytic.service';
import { ApiCreatedResponse, ApiExcludeEndpoint, ApiTags } from '@nestjs/swagger';
import { CreateAnalyticDto } from './dto/analytic.dto';
import { IRequest } from '../../interfaces/request.interface';
import { JwtAuthGuard } from '../../jwt/jwt-auth.guard';
import { InternalApiJWTGuard } from '../../guards/api.guard';

@ApiTags('Analytics')
@Controller('analytics')
export class AnalyticController {
  constructor(private readonly analyticService: AnalyticService, private readonly logger: Logger) {
    this.logger.setContext('AnalyticController');
  }

  @Post('')
  @ApiCreatedResponse({
    type: 'object',
    schema: {
      properties: {
        message: {
          type: 'string',
          example: 'Stored successfully',
        },
      },
    },
  })
  async create(@Req() req: IRequest, @Body() body: CreateAnalyticDto) {
    try {
      body.ip = req.headers['x-real-ip'] as string;
      body.client = req.headers['user-agent'];

      await this.analyticService.create(body);
      return {
        message: 'Analytic stored successfully',
      };
    } catch (err) {
      this.logger.error(err);
      if (!(err instanceof BadRequestException)) {
        throw new InternalServerErrorException(err);
      }
    }
  }

  @Get('internal/stores')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async storeAnalytics() {
    const [paidStores, storeVisits, totalStores, storesCreatedToday, totalUsers] = await Promise.all([
      this.analyticService.totalPaidStores(),
      this.analyticService.totalStoreVisits(),
      this.analyticService.totalStores(),
      this.analyticService.totalStoresCreatedToday(),
      this.analyticService.totalUsers(),
    ]);

    return {
      message: 'Internal store analytics fetched successfully',
      data: {
        paidStores,
        storeVisits,
        totalStores,
        storesCreatedToday,
        totalUsers,
      },
    };
  }
}
