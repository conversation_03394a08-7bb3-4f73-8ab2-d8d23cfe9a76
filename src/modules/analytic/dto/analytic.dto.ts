import { ApiProperty } from '@nestjs/swagger';
import { IsMongoId, IsOptional, IsString } from 'class-validator';
import { PAGE_TYPE } from '../analytic.schema';

export class CreateAnalyticDto {
  id?: string;

  ip?: string;

  client?: string;

  time?: string;

  @ApiProperty()
  @IsString()
  @IsMongoId()
  @IsOptional()
  store?: string;

  @ApiProperty()
  @IsString()
  @IsMongoId()
  @IsOptional()
  item?: string;

  @ApiProperty({ type: String, enum: [PAGE_TYPE.ITEM, PAGE_TYPE.STORE] })
  @IsString()
  @IsOptional()
  page_type?: PAGE_TYPE;

  @ApiProperty({ type: String })
  @IsString()
  @IsOptional()
  ref?: string;
}
