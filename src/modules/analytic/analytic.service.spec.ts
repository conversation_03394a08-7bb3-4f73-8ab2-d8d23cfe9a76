import { Test, TestingModule } from '@nestjs/testing';
import { AnalyticService } from './analytic.service';
import { TEST_ITEM_ID, TEST_STORE_ID } from '../../tools/testdata';
import { AppModule } from '../../app.module';
import { Transport } from '@nestjs/microservices';
import { ItemService } from '../item/item.service';
import { StoreService } from '../store/services/store.service';

describe('AnalyticService', () => {
  let analyticService: AnalyticService;
  let itemService: ItemService;
  let storeSerivce: StoreService;
  let id: string;

  beforeAll(async () => {
    jest.setTimeout(30000);

    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    const app = module.createNestApplication();
    app.connectMicroservice({
      transport: Transport.NATS,
      options: {
        url: process.env.NATS_URL || 'nats://localhost:4222',
      },
    });

    await app.startAllMicroservicesAsync();
    await app.init();

    analyticService = app.get<AnalyticService>(AnalyticService);
    itemService = app.get<ItemService>(ItemService);
    storeSerivce = app.get<StoreService>(StoreService);
  });

  afterAll(async () => {
    await analyticService.getModel().findByIdAndDelete(id);
  });

  it('should create an analytic for item', async () => {
    const originalItem = await itemService.getItem({ _id: TEST_ITEM_ID });
    const analyticObj = {
      item: TEST_ITEM_ID,
      ip: '127.0.0.1',
      client: 'chrome',
    };
    const analytic = await analyticService.create(analyticObj);
    expect(analytic.toJSON()).toEqual(
      expect.objectContaining({
        client: expect.any(String),
        ip: expect.any(String),
        item: TEST_ITEM_ID,
        time: expect.any(Date),
        id: expect.any(String),
      }),
    );
    // Expect items view to be incremented
    const updatedItem = await itemService.getItem({ _id: TEST_ITEM_ID });
    expect(updatedItem.views - originalItem.views).toBeGreaterThanOrEqual(1);
  });

  it('should create an analytic for store', async () => {
    const originalStore = await storeSerivce.getStore({ _id: TEST_STORE_ID });
    const analyticObj = {
      store: TEST_STORE_ID,
      ip: '127.0.0,1',
      client: 'Chrome',
    };
    const analytic = await analyticService.create(analyticObj);
    expect(analytic.toJSON()).toEqual(
      expect.objectContaining({
        client: expect.any(String),
        store: TEST_STORE_ID,
        ip: expect.any(String),
        time: expect.any(Date),
        id: expect.any(String),
      }),
    );
    // Expect store total visits to increment by one
    const updatedStore = await storeSerivce.getStore({ _id: TEST_STORE_ID });
    expect(updatedStore.total_visits - originalStore.total_visits).toBeGreaterThanOrEqual(1);
  });
});
