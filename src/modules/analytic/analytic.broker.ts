import { Controller } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model } from 'mongoose';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { Analytic, AnalyticDocument } from './analytic.schema';
import { SkipThrottle } from '@nestjs/throttler';
import { AnalyticService } from './analytic.service';

@SkipThrottle()
@Controller()
export class AnalyticBroker {
  constructor(
    private readonly analyticService: AnalyticService, // private readonly analyticModel: Model<AnalyticDocument>,
  ) {}

  @MessagePattern(BROKER_PATTERNS.ANALYTICS.GET_STORE_VISITS)
  async getStoreVisits(filter: FilterQuery<Analytic>) {
    return this.analyticService.getStoreVisits(filter);
  }
}
