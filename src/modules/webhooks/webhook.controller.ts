import {
  BadRequestException,
  Controller,
  HttpCode,
  Logger,
  Next,
  Post,
  Req,
  Res,
  Response as _Res,
  UnauthorizedException,
  Get,
  Query,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import { Request, Response } from 'express';
import { BrokerTransportService } from '../../broker/broker-transport.service';
import { MonoConfig } from '../../config/types/mono.config';
import { PaystackConfig } from '../../config/types/paystack.config';
import { SquadConfig } from '../../config/types/squad.config';
import { ZillaConfig } from '../../config/types/zilla.config';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { registerErrors } from '../../utils/errors.util';
import { PaymentService } from '../payment/services/index.service';
import { ThepeerConfig } from '../../config/types/thepeer.config';
import { CURRENCIES } from '../country/country.schema';
import { BlocHQConfig } from '../../config/types/bloc.config';
import { ShipbubbleConfig } from '../../config/types/shipbubble.config';
import { FlutterWaveConfig } from '../../config/types/flutterwave.config';
import { WhatsappConfig } from '../../config/types/whatsapp.config';
import { WhatsappMessageDto } from '../../interfaces/whatsapp.interface';
import { wrapWaMessage } from '../whatsapp-bot/utils/functions.utils';
import { getAppEnv, isProduction } from '../../utils';
import Axios from 'axios';
import { ALLOWED_PHONE_NOS, WA_BOT_PHONE_NOS } from '../../utils/wabot-constants';
import axios, { AxiosRequestHeaders } from 'axios';
import fetch from 'node-fetch';
import {
  CHOWDECK_EVENTS_STATUS_MAP,
  DELIVERY_PROVIDERS,
  FEZ_DELIVERY_EVENTS_STATUS_MAP,
  SHAQ_EXPRESS_EVENTS_STATUS_MAP,
} from '../../enums/deliveries';
import { KoraPayConfig } from '../../config/types/korapay.config';
import { ZeepayConfig } from '../../config/types/zeepay.config';
import { PayazaConfig } from '../../config/types/payaza.config';
import { FincraConfig } from '../../config/types/fincra.config';
import { extractErrorDetails } from '../../utils/axios-errors';
import { StartbuttonConfig } from '../../config/types/startbutton.config';
import { StripeConfig } from '../../config/types/stripe.config';
import { StripeRepository } from '../../repositories/stripe.repository';
import { LeatherbackConfig } from '../../config/types/leatherback.config';
import { PaystackRepository } from '../../repositories/paystack.repository';
import { SkipThrottle } from '@nestjs/throttler';
import { SudoRepository } from '../../repositories/sudo.repository';
import { SudoConfig } from '../../config/types/sudo.config';
import { ZeepayWebhookPayload, ZeepayWebhookV2Payload } from '../../repositories/zeepay.repository';
import { toKobo } from '../../utils/functions';

@Controller('webhooks')
@SkipThrottle()
export class WebhookController {
  // private readonly paystackConfig: PaystackConfig;

  constructor(
    private readonly logger: Logger,
    private readonly configService: ConfigService,
    private readonly brokerTransport: BrokerTransportService,
    private readonly stripe: StripeRepository,
    private readonly paystackRepository: PaystackRepository,
    private readonly sudoRepository: SudoRepository,
  ) {
    this.logger.setContext('payment.controller.ts');
  }

  @Post('monnify')
  @HttpCode(200)
  async monnifyWebhook(@Req() req: Request) {
    try {
      switch (req.body.eventType) {
        case 'SUCCESSFUL_TRANSACTION':
          await this.brokerTransport
            .send(BROKER_PATTERNS.PAYMENT.WEBHOOKS.MONNIFY_TRANSFER_RECEIVED, req.body.eventData)
            .toPromise();
          break;
      }
    } catch (err) {
      this.logger.error(err);
      registerErrors(err);
    }

    return {};
  }

  @Post('paystack')
  @HttpCode(200)
  async paystackWebhook(@Req() req: Request) {
    const paystackConfig = this.configService.get<PaystackConfig>('paystackConfiguration');

    this.logger.log(`Running paystack log ${req.body}`);
    this.logger.log(req.body);

    const privateKey = this.paystackRepository.getPrivateKey(req.body?.data?.currency);

    const hash = crypto
      .createHmac('sha512', privateKey as string)
      .update(JSON.stringify(req.body))
      .digest('hex');

    if (hash != req.headers['x-paystack-signature']) {
      this.logger.log(`Hash header didn't match, hash:${hash}, headers: ${req.headers}`);
      throw new BadRequestException("Header hash doesn't match");
    }

    try {
      switch (req.body.event) {
        case 'charge.success':
          await this.brokerTransport
            .send<void>(BROKER_PATTERNS.PAYMENT.WEBHOOKS.RESOLVE_PAYSTACK_PAYMENTS, req.body)
            .toPromise(); // Payment through paystack
          break;
        case 'transfer.success': // Payout success
          await this.brokerTransport
            .send<void>(BROKER_PATTERNS.PAYMENT.WEBHOOKS.WITHDRAWAL_RESPONSE, {
              reference: req.body.data.reference,
              success: true,
            })
            .toPromise();
          break;
        case 'transfer.reversed': // Payout reversed
          await this.brokerTransport
            .send<void>(BROKER_PATTERNS.PAYMENT.WEBHOOKS.WITHDRAWAL_RESPONSE, {
              success: false,
              reference: req.body.data.reference,
            })
            .toPromise();
          break;
        case 'transfer.failed': // Payout failed
          await this.brokerTransport
            .send<void>(BROKER_PATTERNS.PAYMENT.WEBHOOKS.WITHDRAWAL_RESPONSE, {
              success: false,
              reference: req.body.data.reference,
            })
            .toPromise();
          break;
        case 'direct_debit.authorization.created':
          await this.brokerTransport
            .send<void>(BROKER_PATTERNS.PAYMENT.WEBHOOKS.PAYSTACK_DD_AUTH_CREATED, req.body?.data)
            .toPromise();
          break;
        case 'direct_debit.authorization.active':
          await this.brokerTransport
            .send<void>(BROKER_PATTERNS.PAYMENT.WEBHOOKS.PAYSTACK_DD_AUTH_CREATED, req.body?.data)
            .toPromise();
      }
    } catch (err) {
      this.logger.error(err);
      registerErrors(err);
    }

    return {};
  }

  @Post('zilla')
  @HttpCode(200)
  async zillaWebhook(@Req() req: Request) {
    const zillaConfig = this.configService.get<ZillaConfig>('zillaConfiguration');

    const hash = crypto
      .createHmac('sha512', zillaConfig.hashKey as string)
      .update(req.body.data)
      .digest('hex');

    if (typeof req.body.data == 'string') req.body.data = JSON.parse(req.body.data);

    if (req.body.signature !== hash) {
      this.logger.log(`Hash header didn't match, hash: ${hash}, signature: ${req.body.signature}`);
      throw new BadRequestException("Header hash doesn't match");
    }

    try {
      switch (req.body.eventType) {
        case 'successful_payment':
          await this.brokerTransport
            .send(BROKER_PATTERNS.PAYMENT.WEBHOOKS.ZILLA_PAYMENT_CONFIRMED, req.body.data)
            .toPromise();
          break;
      }
    } catch (err) {
      this.logger.error(err);
      registerErrors(err);
    }

    return {};
  }

  @Post('flutterwave')
  @HttpCode(200)
  async flutterwaveWebhook(@Req() req: Request) {
    const isProd = process.env.NODE_ENV === 'production';
    const fwConfig = this.configService.get<FlutterWaveConfig>('flutterwaveConfiguration');

    if (req.headers['verif-hash'] !== fwConfig.secretHash) {
      throw new UnauthorizedException();
    }

    const webhook = req.body;

    switch (webhook['event.type']) {
      case 'BANK_TRANSFER_TRANSACTION':
        const successCheck = isProd ? webhook?.data?.status === 'successful' : webhook?.status === 'successful';
        if (successCheck) {
          await this.brokerTransport
            .emit(BROKER_PATTERNS.PAYMENT.WEBHOOKS.FLW_TRANSFER_RECIEVED, isProd ? webhook.data : webhook)
            .toPromise();
        }
        break;
      default:
        break;
    }

    return {};
  }

  @Post('korapay')
  @HttpCode(200)
  async korapayWebhook(@Req() req: Request) {
    const kpCofig = this.configService.get<KoraPayConfig>('korapayConfiguration');
    const secretKey = kpCofig.privateKey;

    const hash = crypto.createHmac('sha256', secretKey).update(JSON.stringify(req.body.data)).digest('hex');

    if (hash !== req.headers['x-korapay-signature']) {
      this.logger.log(`Hash header didn't match, hash: ${hash}, signature: ${req.headers['x-korapay-signature']}`);
      throw new BadRequestException("Header hash doesn't match");
    }

    const webhook = req.body;

    try {
      switch (webhook['event']) {
        case 'charge.success':
          await this.brokerTransport
            .send(BROKER_PATTERNS.PAYMENT.WEBHOOKS.KORAPAY_TRANSFER_RECEIVED, webhook.data)
            .toPromise();
          break;
      }
    } catch (err) {
      this.logger.error(err);
      registerErrors(err);
    }

    return {};
  }

  @Post('stripe')
  @HttpCode(200)
  async stripeWebhook(@Req() req: Request) {
    const endpointSecret = this.configService.get<StripeConfig>('stripeConfiguration').webhookSecret;
    const signature = req.headers['stripe-signature'];

    // Retrieve the raw body
    const rawBody = req?.headers.bodyRaw as string;

    try {
      // Verify the signature
      const authenticRequest = verifyStripeWebhook(signature as string, rawBody, endpointSecret);

      if (!authenticRequest) {
        this.logger.log(`Invalid Stripe webhook signature`);
        throw new BadRequestException('Invalid Signature');
      }

      // Parse the event
      const event = JSON.parse(rawBody);

      switch (event.type) {
        case 'charge.updated':
          this.handleStripeWebhookData(event);
          break;
        case 'charge.succeeded':
          this.handleStripeWebhookData(event);
          break;
      }
    } catch (error) {
      console.log('STRIPE WEBHOOK ERROR');
      this.logger.error(error);
      registerErrors(error);
    }

    return {};
  }

  async handleStripeWebhookData(event: any) {
    const charge = event.data.object;
    const { id, amount, currency, metadata } = charge;

    // Check if the charge is newly succeeded
    if (
      charge.status === 'succeeded' &&
      charge.balance_transaction &&
      event.data.previous_attributes?.status !== 'succeeded' &&
      metadata?.payment_reference
    ) {
      const balanceTransactionId = charge.balance_transaction;

      try {
        const balanceTransaction = await this.stripe.fetchBalanceTransaction(balanceTransactionId);

        if (!balanceTransaction.data) {
          throw new BadRequestException('Failed to fetch balance transaction');
        }

        // Extract relevant data

        const { fee, net } = balanceTransaction.data;

        console.log('Stripe payment body'.toLocaleUpperCase(), {
          id,
          amount,
          reference: metadata.payment_reference,
          fee,
          settled: net,
        });

        await this.brokerTransport
          .emit(BROKER_PATTERNS.PAYMENT.WEBHOOKS.STRIPE_PAYMENT_RECEIVED, {
            id,
            amount,
            reference: metadata.payment_reference,
            fee,
            settled: net,
          })
          .toPromise();
      } catch (error) {
        throw new BadRequestException('Failed to fetch balance transaction');
      }
    } else {
      console.log('This is not a newly succeeded charge:', charge.id);
      return {};
    }
  }

  @Post('leatherback')
  @HttpCode(200)
  async leatherbackWebhook(@Req() req: Request) {
    const lbConfig = this.configService.get<LeatherbackConfig>('leatherbackConfiguration');
    const hash = crypto.createHmac('sha512', lbConfig.webhookSecret).update(JSON.stringify(req.body));
    const hashText = hash.digest('hex');

    if (hashText === req.headers['lb-x-signature'].toString().toLocaleLowerCase()) {
      console.log('LEATHERBACK WEBHOOK RECEIVED', req.body);
      await this.brokerTransport
        .send(BROKER_PATTERNS.PAYMENT.WEBHOOKS.LEATHERBACK_PAYMENT_RECEIVED, req.body?.Data)
        .toPromise();
    }

    return {};
  }

  @Post('startbutton')
  @HttpCode(200)
  async startbuttonWebhook(@Req() req: Request) {
    const sbConfig = this.configService.get<StartbuttonConfig>('startbuttonConfiguration');
    const secretKey = sbConfig.privateKey;

    const hash = crypto.createHmac('sha512', secretKey).update(JSON.stringify(req.body)).digest('hex');

    if (hash !== req.headers['x-startbutton-signature']) {
      this.logger.log(`Hash header didn't match, hash: ${hash}, signature: ${req.headers['x-startbutton-signature']}`);
      throw new BadRequestException("Header hash doesn't match");
    }

    const webhook = req.body;

    try {
      switch (webhook['event']) {
        case 'collection.verified':
          await this.brokerTransport
            .send(BROKER_PATTERNS.PAYMENT.WEBHOOKS.STARTBUTTON_PAYMENT_RECEIVED, webhook)
            .toPromise();
          break;
        case 'collection.completed':
          await this.brokerTransport
            .send(BROKER_PATTERNS.PAYMENT.WEBHOOKS.STARTBUTTON_PAYMENT_RECEIVED, webhook)
            .toPromise();
          break;
        case 'transfer.successful':
          await this.brokerTransport
            .send<void>(BROKER_PATTERNS.PAYMENT.WEBHOOKS.WITHDRAWAL_RESPONSE, {
              reference: webhook?.data?.transaction?.transactionReference,
              success: true,
            })
            .toPromise();
          break;
        case 'transfer.failed':
          await this.brokerTransport
            .send<void>(BROKER_PATTERNS.PAYMENT.WEBHOOKS.WITHDRAWAL_RESPONSE, {
              reference: webhook?.data?.transaction?.transactionReference,
              success: false,
            })
            .toPromise();
          break;
        case 'transfer.reversed':
          await this.brokerTransport
            .send<void>(BROKER_PATTERNS.PAYMENT.WEBHOOKS.WITHDRAWAL_RESPONSE, {
              reference: webhook?.data?.transaction?.transactionReference,
              success: false,
            })
            .toPromise();
          break;
      }
    } catch (err) {
      this.logger.error(err);
      registerErrors(err);
    }

    return {};
  }

  @Post('mono')
  @HttpCode(200)
  async monoWebhook(@Req() req: Request) {
    const monoConfig = this.configService.get<MonoConfig>('monoConfiguration');

    if (req.headers['mono-webhook-secret'] !== monoConfig.webhookKey) {
      throw new UnauthorizedException();
    }

    const webhook = req.body;

    switch (webhook.event) {
      // case 'issuing.virtual_account_created':
      //   console.log(webhook.data);
      //   this.brokerTransport
      //     .send<void>(BROKER_PATTERNS.WALLET.VIRTUAL_ACCOUNT_CREATED, {
      //       accountId: webhook.data.id,
      //     })
      //     .toPromise();
      //   break;
      // case 'issuing.transfer_received':
      //   console.log(webhook.data);
      //   this.brokerTransport
      //     .send<void>(BROKER_PATTERNS.PAYMENT.WEBHOOKS.MONO_TRANSFER_RECIEVED, {
      //       amount: webhook.data.amount,
      //       narration: webhook.data.narration,
      //       account_id: webhook.data.account,
      //       reference: webhook.data.id,
      //       currency: webhook.data.currency,
      //       type: webhook.data.type,
      //       date: webhook.data.date,
      //       source: webhook.data.source,
      //     })
      //     .toPromise();
      //   break;
      case 'direct_debit.payment_successful':
        this.brokerTransport
          .send<void>(BROKER_PATTERNS.PAYMENT.WEBHOOKS.MONO_PAYMENT_CONFIRMED, {
            amount: webhook.data.object.amount,
            fee: webhook.data.object.fee,
            status: webhook.data.object.status,
            description: webhook.data.object.description,
            reference: webhook.data.object.reference,
          })
          .toPromise();
        break;
      case 'issuing.transfer_failed':
        // console.log(webhook.data);
        break;
    }

    return {};
  }

  @Post('squad')
  @HttpCode(200)
  async squadWebhook(@Req() req: Request) {
    const squadConfig = this.configService.get<SquadConfig>('squadConfiguration');
    const hash = crypto.createHmac('sha512', squadConfig.privateKey).update(JSON.stringify(req.body));
    const hashText = hash.digest('hex');

    this.logger.log(hashText + ', ' + req.headers['x-squad-signature'] + JSON.stringify(req.body));

    if (hashText === req.headers['x-squad-signature']) {
      this.brokerTransport.emit(BROKER_PATTERNS.PAYMENT.WEBHOOKS.SQUAD_TRANSFER_RECIEVED, req.body).toPromise();

      //Race condition test
      // const fakeTxRef = req.body.transaction_reference + String(Math.floor(Math.random() * 900) + 100);
      // const fakeBody = {
      //   ...req.body,
      //   transaction_reference: fakeTxRef,
      // };

      // const reqs = [
      //   () => this.brokerTransport.emit(BROKER_PATTERNS.PAYMENT.WEBHOOKS.SQUAD_TRANSFER_RECIEVED, req.body).toPromise(),
      //   () => this.brokerTransport.emit(BROKER_PATTERNS.PAYMENT.WEBHOOKS.SQUAD_TRANSFER_RECIEVED, fakeBody).toPromise(),
      // ];
      // const res = await Promise.all(reqs.map((fn) => fn()));
    }

    return {};
  }

  @Post('blochq')
  @HttpCode(200)
  async blochqWebhook(@Req() req: Request) {
    const blochqConfig = this.configService.get<BlocHQConfig>('blochqConfiguration');
    const hash = crypto.createHmac('sha256', blochqConfig.webhookKey).update(JSON.stringify(req.body));
    const hashText = hash.digest('hex');

    this.logger.log(hashText + ', ' + req.headers['x-bloc-webhook'] + JSON.stringify(req.body));

    if (hashText == req.headers['x-bloc-webhook']) {
      switch (req.body.event) {
        case 'transaction.new':
          if (req.body.data.drcr === 'CR') {
            await this.brokerTransport
              .emit(BROKER_PATTERNS.PAYMENT.WEBHOOKS.BLOCHQ_TRANSFER_RECEIVED, req.body.data)
              .toPromise();
          }
          break;

        case 'transaction.updated':
          if (req.body.data.drcr === 'DR') {
            await this.brokerTransport
              .emit(BROKER_PATTERNS.PAYMENT.WEBHOOKS.BLOCHQ_WITHDRAWAL, {
                reference: req.body.data.reference,
                success: req.body.data.status === 'successful',
              })
              .toPromise();
          }
          break;
        default:
          break;
      }
    }

    return {};
  }

  @Post('thepeer')
  @HttpCode(200)
  async thepeerWebhook(@Req() req: Request) {
    const thepeerConfig = this.configService.get<ThepeerConfig>('thepeerConfiguration');
    const hash = crypto.createHmac('sha1', thepeerConfig.secretKey).update(JSON.stringify(req.body));
    const hashText = hash.digest('hex');

    if (hashText === req.headers['x-thepeer-signature']) {
      await this.brokerTransport.emit(BROKER_PATTERNS.PAYMENT.WEBHOOKS.THEPEER_PAYMENT_RECEIVED, req.body).toPromise();
    }

    return {};
  }

  @Post('shipbubble')
  @HttpCode(200)
  async shibubbleWebhook(@Req() req: Request) {
    const shipbubbleConfig = this.configService.get<ShipbubbleConfig>('shipbubbleConfiguration');

    const hash512 = crypto.createHmac('sha512', shipbubbleConfig.apiKey).update(JSON.stringify(req.body)).digest('hex');
    const hash256 = crypto.createHmac('sha256', shipbubbleConfig.apiKey).update(JSON.stringify(req.body)).digest('hex');

    if (hash512 === req.headers['x-ship-signature'] || hash256 === req.headers['x-ship-signature']) {
      const body = req.body;
      if (body.status && body.order_id) {
        await this.brokerTransport
          .emit(BROKER_PATTERNS.DELVERIES.UPDATE_STATUS, {
            filter: { tracking_code: body.order_id },
            update: { status: (body.status as string).toUpperCase() },
          })
          .toPromise();
      }
    }
    return {};
  }

  @Post('fez-delivery')
  @HttpCode(200)
  async fezDeliveryWebhook(@Req() req: Request) {
    this.logger.log('FEZ DELIVERY WEBHOOK RECEIVED', req.body);
    let body = req.body;

    // Check if the body is in the unexpected format
    if (body && typeof body === 'object' && Object.keys(body).length === 1 && body[Object.keys(body)[0]] === '') {
      const key = Object.keys(body)[0];
      try {
        // Parse the key as JSON
        body = JSON.parse(key);
      } catch (error) {
        console.error('Failed to parse body:', error);
        return {};
      }
    }

    if (body.orderNumber && body.status) {
      const newStatus = FEZ_DELIVERY_EVENTS_STATUS_MAP[body.status];

      this.logger.log('NEW STATUS', newStatus);

      if (!newStatus) return {};

      await this.brokerTransport
        .emit(BROKER_PATTERNS.DELVERIES.UPDATE_STATUS, {
          filter: { 'meta.fez_order_no': body.orderNumber },
          update: { status: newStatus },
        })
        .toPromise();
    }
    return {};
  }

  @Post('shaq')
  @HttpCode(200)
  async shaqWebhook(@Req() req: Request) {
    this.logger.log('FEZ DELIVERY WEBHOOK RECEIVED', req.body);
    let body = req.body;

    if (body.tracking_number && body.status) {
      const newStatus = SHAQ_EXPRESS_EVENTS_STATUS_MAP[body.status];

      this.logger.log('NEW STATUS', newStatus);

      if (!newStatus) return {};

      await this.brokerTransport
        .emit(BROKER_PATTERNS.DELVERIES.UPDATE_STATUS, {
          filter: { 'meta.shaq_tracking_number': body.tracking_number, provider: DELIVERY_PROVIDERS.SHAQ_EXPRESS },
          update: { status: newStatus },
        })
        .toPromise();
    }
    return {};
  }

  @Post('zeepay')
  @HttpCode(200)
  async zeepayWebhook(@Req() req: Request) {
    // Verify IP address
    const requestIp = req.headers['x-forwarded-for'] || req.connection.remoteAddress;
    // if (requestIp !== zeepayConfig.ip_address) {
    //   this.logger.log(`Invalid IP address: ${requestIp}, expected: ${zeepayConfig.ip_address}`);
    //   throw new BadRequestException('Invalid IP address');
    // }

    // Check if status is 'Success'
    if (req.body.status !== 'Success') {
      this.logger.log(`Payment not successful, status: ${req.body.status}`);
      throw new BadRequestException('Payment not successful');
    }

    try {
      // Handle successful payment
      await this.brokerTransport.send(BROKER_PATTERNS.PAYMENT.WEBHOOKS.RESOLVE_ZEEPAY_PAYMENT, req.body).toPromise();
    } catch (err) {
      this.logger.error(err);
      registerErrors(err);
    }

    return {};
  }

  @Post('zeepay/v2')
  @HttpCode(200)
  async zeepayWebhookV2(@Req() req: Request) {
    // Verify IP address
    const requestIp = req.headers['x-forwarded-for'] || req.connection.remoteAddress;
    // if (requestIp !== zeepayConfig.ip_address) {
    //   this.logger.log(`Invalid IP address: ${requestIp}, expected: ${zeepayConfig.ip_address}`);
    //   throw new BadRequestException('Invalid IP address');
    // }

    const data = req.body as ZeepayWebhookV2Payload;

    if (data.transaction.status !== 1 || data.transaction.type !== 'Cr') {
      this.logger.log(`Payment not successful, status: ${data.transaction.status}`);
      throw new BadRequestException('Payment not successful');
    }

    try {
      // Handle successful payment

      const chargedAmount = toKobo(Number(data.transaction.amount));
      const fee = toKobo(Number(data.transaction.amount_details.cc));
      const payload: ZeepayWebhookPayload = {
        zeepay_id: Number(data.transaction.zeepay_id),
        reference: data.transaction.extra,
        status: 'Success',
        message: data.transaction.status_message,
        amount: chargedAmount,
        fee_charged: fee,
        amount_settled: chargedAmount - fee,
      };

      await this.brokerTransport.send(BROKER_PATTERNS.PAYMENT.WEBHOOKS.RESOLVE_ZEEPAY_PAYMENT, payload).toPromise();
    } catch (err) {
      this.logger.error(err);
      registerErrors(err);
    }

    return {};
  }

  @Post('chowdeck')
  @HttpCode(200)
  async chowdeckWebhook(@Req() req: Request) {
    const body = req.body;
    // console.log({ body });
    if (body.category && body.payload.id) {
      const newStatus = CHOWDECK_EVENTS_STATUS_MAP[body.category];

      if (!newStatus) return {};

      await this.brokerTransport
        .emit(BROKER_PATTERNS.DELVERIES.UPDATE_STATUS, {
          filter: { 'meta.chowdeck_order_id': body.payload.id },
          update: { status: CHOWDECK_EVENTS_STATUS_MAP[body.category] },
        })
        .toPromise();
    }
    return {};
  }

  @Post('whatsapp')
  @HttpCode(200)
  async whatsappWebhook(@Req() req: Request, @Res() res: Response) {
    const body = req.body as WhatsappMessageDto;
    const data = wrapWaMessage(body);

    const number = data?.getWaId() ?? data?.getStatusRecipient();
    const allowedPhoneNos = ALLOWED_PHONE_NOS[getAppEnv()];

    if (allowedPhoneNos && allowedPhoneNos.length > 0 && !allowedPhoneNos.includes(number)) {
      res.sendStatus(200);
      return {};
    }

    if (false && process.env.WHATSAPP_FORWARD_TEST_CLIENTS && !isProduction()) {
      const allowedClients = process.env.WHATSAPP_FORWARD_TEST_CLIENTS.split('||')?.reduce((acc, curr) => {
        const [phones, url] = curr?.split('-->');
        const numbers = phones?.split(',');
        acc.push({
          allowedNumbers: numbers,
          endpoint: url,
        });
        return acc;
      }, []);

      // const number = data?.getWaId() ?? data?.getStatusRecipient();

      const forwardTo = allowedClients?.find((a) => a.allowedNumbers.includes(number))?.endpoint as string;

      if (forwardTo) {
        const headerCopy = { ...req.headers };
        delete headerCopy.bodyRaw;
        const url = new URL(forwardTo);
        const host = url.hostname;

        headerCopy.host = host;
        headerCopy['x-forwarded-host'] = host;

        const client = Axios.create({ headers: headerCopy as any });

        try {
          await client.post(`${forwardTo}/webhooks/whatsapp`, body);
          res.sendStatus(200);
          this.logger.log(`Fowarding request:: endpoint: ${forwardTo} number: ${number}`);
          return {};
        } catch (e) {
          this.logger.error(e);
        }
      }
    }

    const whatsappConfig = this.configService.get<WhatsappConfig>('whatsappConfiguration');
    const hash256 = crypto
      .createHmac('sha256', whatsappConfig.key)
      .update(req?.headers.bodyRaw as string)
      .digest('hex');
    const canProcess = WA_BOT_PHONE_NOS[getAppEnv()]?.includes(data.getBotPhone());
    const isVerified = hash256 === req.headers['x-hub-signature-256']?.toString()?.replace('sha256=', '');

    let requestType: 'message' | 'status';
    if (data.getMessages()?.length > 0) requestType = 'message';
    if (data.getStatuses()?.length > 0) requestType = 'status';

    if (isVerified && canProcess) {
      switch (requestType) {
        case 'message':
          const messageTypeToBrokerPattern = {
            text: BROKER_PATTERNS.WHATSAPP_BOT.HANDLE_TEXT_MESSAGE,
            interactive: BROKER_PATTERNS.WHATSAPP_BOT.HANDLE_INTERACTIVE_MESSAGE,
            button: BROKER_PATTERNS.WHATSAPP_BOT.HANDLE_INTERACTIVE_MESSAGE,
            location: BROKER_PATTERNS.WHATSAPP_BOT.HANDLE_INTERACTIVE_MESSAGE,
          };
          try {
            this.brokerTransport.emit(messageTypeToBrokerPattern[data.getMessageType()], { ...body });
          } catch (e) {
            res.sendStatus(200);
          }
          break;
        case 'status':
          try {
            this.brokerTransport.emit(BROKER_PATTERNS.WHATSAPP_BOT.HANDLE_MESSAGE_STATUS, { ...body });
          } catch (e) {
            res.sendStatus(200);
          }
          break;
      }
    }

    res.sendStatus(200);
    return {};
  }

  @Post('payaza')
  @HttpCode(200)
  async payazaWebhook(@Req() req: Request) {
    const payazaConfig = this.configService.get<PayazaConfig>('payazaConfiguration');
    const publicKey = payazaConfig.publicKey;

    try {
      // Decode the authentication header
      const authHeader = req.headers['authentication'];

      if (!authHeader || Array.isArray(authHeader)) {
        this.logger.warn('Invalid or missing authentication header');
        return;
      }

      const decodedAuthHeader = Buffer.from(authHeader, 'base64').toString('utf-8');

      // Verify the decoded value matches your public key
      if (decodedAuthHeader !== publicKey) {
        this.logger.warn('Invalid authentication header');
        return;
      }

      // Process the webhook based on the transaction status
      switch (req.body.transaction_status) {
        case 'Funds Received':
          await this.brokerTransport
            .send(BROKER_PATTERNS.PAYMENT.WEBHOOKS.PAYAZA_PAYMENT_RECEIVED, req.body)
            .toPromise();
          break;
      }
      return {};
    } catch (err) {
      this.logger.error(err);
      registerErrors(err);
    }
  }

  @Post('fincra')
  @HttpCode(200)
  async fincraWebhook(@Req() req: Request) {
    const fincraConfig = this.configService.get<FincraConfig>('fincraConfiguration');

    this.logger.log('Running fincra webhook', req.body);

    const webhookSecretKey = fincraConfig.webhookSecretKey;
    const webhookSignature = req.headers['signature'];
    const webhookSignatureHash = crypto
      .createHmac('sha512', webhookSecretKey)
      .update(JSON.stringify(req.body))
      .digest('hex');
    if (webhookSignature !== webhookSignatureHash) {
      this.logger.log("Hash header didn't match, hash: " + webhookSignatureHash + ', signature: ' + webhookSignature);
      return;
    }
    try {
      const data = req.body;
      this.logger.log(data, 'Fincra conversion webhook received');
      await this.brokerTransport.send(BROKER_PATTERNS.PAYMENT.WEBHOOKS.FINCRA_CONVERSION, data).toPromise();
      return {};
    } catch (err) {
      const errorDetails = extractErrorDetails(err);
      this.logger.error(errorDetails, 'Fincra webhook failed');
      return {};
    }
  }

  @Get('whatsapp')
  @HttpCode(200)
  async whatsappGetWebhook(@Req() req: Request, @Query() query: any, @Res() res: Response) {
    // const body = req.body;
    // console.log(body, query);
    res.status(200).send(query['hub.challenge']);
    return {};
  }

  @Get('instagram')
  @HttpCode(200)
  async instagramGetWebhook(@Req() req: Request, @Query() query: any, @Res() res: Response) {
    // const body = req.body;
    // console.log(body, query);
    res.status(200).send(query['hub.challenge']);
    return {};
  }

  @Post('instagram')
  @HttpCode(200)
  async instagramWebhook(@Req() req: Request, @Res() res: Response) {
    const body = req.body;
    console.log(body);
    res.sendStatus(200);
    return {};
  }

  @Post('sudo')
  @HttpCode(200)
  async sudoWebhook(@Req() req: Request) {
    try {
      this.logger.log('Sudo webhook received', JSON.stringify(req.body));

      // Currently Safehaven doesn't provide a webhook signature, but we should verify once available
      // const sudoConfig = this.configService.get<SudoConfig>('sudoConfiguration');
      // const isValid = this.sudoRepository.verifyWebhookSignature(req.body, req.headers['x-sudo-signature']);
      // if (!isValid) {
      //   this.logger.warn('Invalid Sudo webhook signature');
      //   throw new UnauthorizedException('Invalid signature');
      // }

      // Process the webhook
      const isValidPayload = await this.sudoRepository.processWebhookPayload(req.body);

      if (isValidPayload && req.body.type === 'virtualAccount.transfer') {
        const { data } = req.body;

        // Forward to the payment webhook service
        await this.brokerTransport
          .send(BROKER_PATTERNS.PAYMENT.WEBHOOKS.SUDO_TRANSFER_RECEIVED, {
            data: {
              virtualAccount: data.virtualAccount,
              amount: data.amount,
              reference: data.externalReference,
              status: data.status === 'Completed' && data.responseCode === '00' ? 'successful' : 'failed',
              metadata: {
                sessionId: data.sessionId,
                accountName: data.debitAccountName,
                accountNumber: data.debitAccountNumber,
                narration: data.narration,
              },
            },
          })
          .toPromise();
      } else if (isValidPayload && req.body.type === 'transfer') {
        const { data } = req.body;

        // This is a withdrawal transfer
        if (data.status === 'Completed' && data.responseCode === '00') {
          await this.brokerTransport
            .send(BROKER_PATTERNS.PAYMENT.WEBHOOKS.SUDO_WITHDRAWAL, {
              reference: data.paymentReference,
              success: true,
              nipReference: data.sessionId,
            })
            .toPromise();
        } else {
          await this.brokerTransport
            .send(BROKER_PATTERNS.PAYMENT.WEBHOOKS.SUDO_WITHDRAWAL, {
              reference: data.paymentReference,
              success: false,
            })
            .toPromise();
        }
      }
    } catch (err) {
      this.logger.error('Error processing Sudo webhook');
      this.logger.log(err);
      registerErrors(err);
    }

    return {};
  }
}

/**
 * Verifies the signature of the webhook request.
 */
function verifyStripeWebhook(signature: string, requestBody: any, webhookSecret: string) {
  // Split the signature into it's parts
  const parts = signature.split(',').map((part) => part.split('='));

  // The timestamp the request was fired at
  const timestamp = parts.find((part) => part[0] === 't');

  // The actual signature to compare against
  const v1 = parts.find((part) => part[0] === 'v1');

  // Don't authorize the request if neither of those exist
  if (timestamp?.length < 1 || v1?.length < 1) {
    return false;
  }

  const integerTimestamp = parseInt(timestamp[1], 10);

  // Check if the timestamp is too old
  if (!verifyTimestamp(integerTimestamp)) {
    return false;
  }

  // Construct the payload to hash
  const handleString = `${integerTimestamp}.${requestBody}`;

  // Hash the string with the webhook secret as the key with nodes included crypto library
  const hash = crypto.createHmac('SHA256', webhookSecret).update(handleString).digest('hex');

  /*
   * Load Stripes and our signature into a buffer, respectively.
   * Compare both with nodes timingSafeEqual function to prevent timing attacks.
   */
  return crypto.timingSafeEqual(Buffer.from(hash) as any, Buffer.from(v1[1]) as any);
}

/**
 * Verifies that the timestamp is within the last 5 minutes to prevent relay attacks.
 * 5 minutes is what Stripe uses in their SDK as a default, change it if need be.
 */
function verifyTimestamp(timestamp: number) {
  return Math.floor(Date.now() / 1000) - timestamp <= 60 * 60 * 24;
}
