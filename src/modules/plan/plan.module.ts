import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { PlanController } from './plan.controller';
import { PlanService } from './plan.service';
import { SharedModule } from '../../shared.module';
import { MongooseModule } from '@nestjs/mongoose';
import { Plan, PlanSchema } from './plan.schema';
import setIdPlugin from '../../mongoose-plugins/set-id.mongoose-plugin';
import jsonHookPlugin from '../../mongoose-plugins/json-hook.mongoose-plugin';
import { PlanBroker } from './plan.broker';
import { PlanOption, PlanOptionSchema } from './plan-options/plan-options.schema';

@Module({
  imports: [
    MongooseModule.forFeatureAsync([
      {
        name: Plan.name,
        useFactory: () => {
          PlanSchema.plugin(setIdPlugin());
          PlanSchema.plugin(jsonHookPlugin(['_id', '__v']));
          return PlanSchema;
        },
      },
      {
        name: PlanOption.name,
        useFactory: () => {
          PlanOptionSchema.plugin(setIdPlugin());
          PlanOptionSchema.plugin(jsonHookPlugin(['_id', '__v']));
          return PlanOptionSchema;
        },
      },
    ]),
    SharedModule,
  ],
  controllers: [PlanController, PlanBroker],
  providers: [PlanService],
})
export class PlanModule {}
