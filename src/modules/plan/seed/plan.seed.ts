import { ISeed } from '../../../interfaces/seed/seed.interface';
import { Collection, Connection } from 'mongoose';
import { planMock } from '../../../tools/testdata';

export class PlanSeed implements ISeed {
  private readonly collection: Collection;

  constructor(db: Connection) {
    this.collection = db.collection('plans');
  }

  async runDevSeed(): Promise<any> {
    if (!(await this.collection.findOne({ _id: planMock._id }))) {
      return this.collection.insertOne(planMock);
    }
  }

  runProdSeed(): Promise<any> {
    return Promise.resolve(undefined);
  }
}
