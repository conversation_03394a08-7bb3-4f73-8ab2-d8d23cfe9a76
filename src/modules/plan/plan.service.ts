import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { CreatePlanDto, CreatePlanOptionDto, UpdatePlanDto } from '../../models/dtos/PlanDtos';
import { InjectModel } from '@nestjs/mongoose';
import { Plan, PlanDocument } from './plan.schema';
import mongoose, { Model } from 'mongoose';
import { BrokerTransportService } from '../../broker/broker-transport.service';
import { PaymentMethod } from '../paymentmethod/paymentmethod.schema';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { COUNTRY_CODE, COUNTRY_CURRENCY_MAP, CURRENCY_COUNTRY_MAP, Country } from '../country/country.schema';
import { PLAN_FEATURES } from '../../utils/constants';
import { PLAN_OPTION_VERSION, PlanOption, PlanOptionDocument } from './plan-options/plan-options.schema';
import { PLAN_TYPE } from '../../enums/plan.enum';
import { getDocId } from '../../utils/functions';

@Injectable()
export class PlanService {
  constructor(
    @InjectModel(Plan.name) private readonly planModel: Model<PlanDocument>,
    @InjectModel(PlanOption.name) private planOptionModel: Model<PlanOptionDocument>,
    private readonly logger: Logger,
    private readonly brokerTransport: BrokerTransportService,
  ) {
    this.logger.setContext('plan.service.ts');
  }

  async create(planReq: CreatePlanDto) {
    return this.planModel.create(planReq);
  }

  async update(id: string, planReq: UpdatePlanDto) {
    let plan = await this.planModel.findById(id);

    if (!plan) {
      throw new BadRequestException('Plan with id does not exists');
    }

    plan = await this.planModel.findByIdAndUpdate(id, {
      name: planReq?.name ?? plan.name,
      amount: planReq?.amount ?? plan.amount,
      country: planReq?.country ?? plan.country,
      type: planReq?.type ?? plan.type,
      interval: planReq?.interval ?? plan?.interval,
      interval_text: planReq?.interval_text ?? plan.interval_text,
      discount: planReq?.discount ?? plan.discount,
    });

    plan.description = [...PLAN_FEATURES[plan.type]];

    return plan;
  }

  async getPlanForAdmin(id: string) {
    const plan: PlanDocument & { country?: COUNTRY_CODE | Country } = await this.planModel
      .findById(id)
      .populate('options');

    return plan;
  }

  async getPlanById(id, version?: PLAN_OPTION_VERSION) {
    const plan: PlanDocument & { country?: COUNTRY_CODE | Country } = await this.planModel
      .findById(id)
      .populate('options');

    if (!plan) {
      throw new BadRequestException('Plan with id does not exist');
    }

    plan.description = [...PLAN_FEATURES[plan.type]];

    return await this.resolvePlan({ plan });
  }

  async getAllForAdmin() {
    const plans: (PlanDocument & { country?: COUNTRY_CODE | Country })[] = await this.planModel
      .find()
      .populate('options');

    return plans;
  }

  async getAll() {
    const plans: (PlanDocument & { country?: COUNTRY_CODE | Country })[] = await this.planModel
      .find()
      .populate('options');

    const paymentMethods = await this.brokerTransport
      .send<PaymentMethod[]>(BROKER_PATTERNS.PAYMENT.GET_ALL_PAYMENT_METHODS, {})
      .toPromise();

    let responsePlans = [];

    for (let plan of plans) {
      plan = plan.toJSON();
      plan.country = plan.country || COUNTRY_CODE.NG;
      plan.description = [...PLAN_FEATURES[plan.type]];

      const response = await this.resolvePlan({ plan });
      responsePlans = [...responsePlans, ...response];
    }

    return {
      plans: responsePlans,
      paymentMethods,
    };
  }

  async getPlansByCountry(country: COUNTRY_CODE) {
    const plans: (PlanDocument & { country?: COUNTRY_CODE | Country })[] = await this.planModel
      .find()
      .populate('options');
    const paymentMethods = await this.brokerTransport
      .send<PaymentMethod[]>(BROKER_PATTERNS.PAYMENT.GET_ALL_PAYMENT_METHODS, {})
      .toPromise();

    let responsePlans = [];

    for (let plan of plans) {
      plan = plan.toJSON();
      plan.country = plan.country || COUNTRY_CODE.NG;
      plan.description = [...PLAN_FEATURES[plan.type]];

      const response = await this.resolvePlan({ plan, filterByCountry: true, country });
      responsePlans = [...responsePlans, ...response];
    }

    return {
      plans: responsePlans,
      paymentMethods,
    };
  }

  async resolvePlan(data: { plan: PlanDocument; filterByCountry?: boolean; country?: COUNTRY_CODE }) {
    const { plan, filterByCountry, country } = data;
    let { options, ...planWithoutOption } = plan;

    const countries = await this.brokerTransport
      .send<Country[]>(BROKER_PATTERNS.COUNTRY.GET_ALL_COUNTRIES, {})
      .toPromise();

    if (filterByCountry) {
      options = options.filter((o) => o.country === country);
    }

    return options.map((o) => {
      const { id, ...rest } = o;
      return {
        ...rest,
        ...planWithoutOption,
        plan_option_id: id,
        country: countries.find((c) => c.code === o.country),
      };
    });
  }

  async getPlans(filter) {
    let plans = await this.planModel.find(filter).populate('options');

    return plans.map((plan) => {
      plan.description = [...PLAN_FEATURES[plan.type]];
      return plan;
    });
  }

  async getPlansLean(filter) {
    return await this.planModel.find(filter, { name: 1, type: 1 }).populate('options').lean();
  }

  async getPlan(data: mongoose.FilterQuery<PlanDocument>) {
    const plan = await this.planModel.findOne(data).populate('options');

    if (!plan) {
      throw new BadRequestException('Plan with id does not exist');
    }

    plan.description = [...PLAN_FEATURES[plan.type]];

    return plan;
  }

  /*  async changePlan(planReq: ChangePlanDto) {
    const plan = await this.planModel.findById(planReq.plan);
    if (!plan) {
      throw new BadRequestException('Plan with id does not exist');
    }

    if (plan.type !== PLAN_TYPE.STARTER) {
      throw new BadRequestException('You can only switch to the free plan');
    }

    const subscription = await this.brokerTransport
      .send<Subscription>(BROKER_PATTERNS.PAYMENT.UPDATE_SUBSCRIPTION, {
        filter: { owner: planReq.userId },
        update: { plan: plan.id },
      })
      .toPromise();

    if (!subscription) {
      this.logger.error("User doesn't have an active subscription");
      throw new BadRequestException(
        "User doesn't have an activer subscription",
      );
    }

    const store = await this.brokerTransport
      .send(BROKER_PATTERNS.STORE.REVERT_SLUG, { owner: planReq.userId })
      .toPromise();

    return {};
  } */

  async updateCountries() {
    await this.planModel.updateMany(
      {
        $or: [{ country: { $exists: false } }, { country: { $eq: null } }],
      },
      {
        country: COUNTRY_CODE.NG,
      },
    );

    return {};
  }

  async restructurePlans() {
    await this.planModel.updateMany(
      {},
      {
        $unset: { description: '', methods: '' },
      },
    );
  }

  async createOption(id: string, planOptionReq: CreatePlanOptionDto) {
    const plan = await this.planModel.findById(id);
    if (!plan) {
      throw new BadRequestException('Plan with id does not exist');
    }

    const option = await this.planOptionModel.create(planOptionReq);

    await this.planModel.findByIdAndUpdate(id, { $push: { options: option.id } });

    return option;
  }

  async updateOption(id: string, optionId: string, planOptionReq: CreatePlanOptionDto) {
    const option = await this.planOptionModel.findByIdAndUpdate(optionId, planOptionReq, { new: true });

    return option;
  }

  async migratePlans(): Promise<PlanDocument[]> {
    // Retrieve all existing plans
    const existingPlans = await this.planModel.find();

    // Group plans by name and type
    const groupedPlans = existingPlans.reduce((acc, plan) => {
      const key = plan.type;
      if (!acc[key]) {
        acc[key] = {
          name: plan.name,
          type: plan.type,
          is_paid_plan: plan.type !== 'STARTER', // Assuming 'STARTER' is a constant from PLAN_TYPE enum
          options: [],
        };
      }

      // Build option from existing plan details
      acc[key].options.push({
        interval: plan.interval,
        interval_text: plan.interval_text,
        country: plan.country,
        amount: plan.amount,
        discount: plan.discount,
        chowbotTokens: 0, // Default value
        plan_type: plan.type,
      });

      return acc;
    }, {});

    const newlyCreatedPlans: PlanDocument[] = [];

    // Insert new plan structure with options
    for (const key in groupedPlans) {
      const groupedPlan = groupedPlans[key];

      const optionsDocs = await this.planOptionModel.insertMany(groupedPlan.options);

      const newPlan = new this.planModel({
        name: groupedPlan.name,
        type: groupedPlan.type,
        is_paid_plan: groupedPlan.is_paid_plan,
        options: Array.isArray(optionsDocs) ? [...optionsDocs].map((doc) => doc._id) : [optionsDocs._id],
      });

      await newPlan.save();

      newlyCreatedPlans.push(newPlan);
    }

    return newlyCreatedPlans;
  }

  async addSAandKEPlans() {
    const durationConfigs = {
      Forever: { interval: 365, discount: 0 },
      Monthly: { interval: 30, discount: 0 },
      Quarterly: { interval: 90, discount: 8 },
      'Bi-Annually': { interval: 180, discount: 12 },
    };

    const plans = {
      [COUNTRY_CODE.ZA]: {
        [PLAN_TYPE.STARTER]: {
          Forever: 0,
        },
        [PLAN_TYPE.BASIC]: {
          Monthly: 99,
          Quarterly: 270,
          'Bi-Annually': 520,
        },
        [PLAN_TYPE.BUSINESS_PLUS]: {
          Monthly: 199,
          Quarterly: 545,
          'Bi-Annually': 1050,
        },
      },
      [COUNTRY_CODE.KE]: {
        [PLAN_TYPE.STARTER]: {
          Forever: 0,
        },
        [PLAN_TYPE.BASIC]: {
          Monthly: 600,
          Quarterly: 1650,
          'Bi-Annually': 3165,
        },
        [PLAN_TYPE.BUSINESS_PLUS]: {
          Monthly: 1200,
          Quarterly: 3310,
          'Bi-Annually': 6330,
        },
      },
    };

    const allPlanOptions = Object.keys(plans)
      .map((c) =>
        Object.keys(plans[c])
          .map((pG) =>
            Object.keys(plans[c][pG]).map((int) => ({
              interval: durationConfigs[int].interval,
              interval_text: int,
              country: c,
              amount: Math.ceil(plans[c][pG][int] * (1 - durationConfigs[int].discount / 100)),
              discount: durationConfigs[int].discount,
              chowbotTokens: 0,
              plan_type: pG,
              actual_amount: plans[c][pG][int],
              currency: COUNTRY_CURRENCY_MAP[c],
            })),
          )
          .flat(),
      )
      .flat();

    const planOptions = await this.planOptionModel.insertMany(allPlanOptions);
    const planOptionsIdsGroupedByPlanType = planOptions.reduce((acc, option) => {
      if (!acc[option.plan_type]) {
        acc[option.plan_type] = [];
      }
      acc[option.plan_type].push(getDocId(option));
      return acc;
    }, {});

    for (let index = 0; index < Object.keys(planOptionsIdsGroupedByPlanType).length; index++) {
      const planType = Object.keys(planOptionsIdsGroupedByPlanType)[index];
      const planOptionIds = planOptionsIdsGroupedByPlanType[planType];
      await this.planModel.findOneAndUpdate(
        { type: planType as PLAN_TYPE },
        { $push: { options: { $each: planOptionIds } } },
      );
    }

    return planOptions;
  }

  async createKitchenPlans() {
    const basePrice = 0;
    const originalTokenPrice = 35;

    const config = [
      {
        tokens: 250,
        pricePerToken: 35,
      },
      {
        tokens: 500,
        pricePerToken: 32,
      },
      {
        tokens: 1000,
        pricePerToken: 30,
      },
      {
        tokens: 1500,
        pricePerToken: 28,
      },
      {
        tokens: 2500,
        pricePerToken: 26,
      },
      {
        tokens: 5000,
        pricePerToken: 24,
      },
      {
        tokens: 10000,
        pricePerToken: 22,
      },
    ];

    const planOptions: PlanOption[] = await this.planOptionModel.insertMany(
      config.map((c) => ({
        plan_type: PLAN_TYPE.KITCHEN,
        price_per_token: c.pricePerToken,
        chowbot_tokens: c.tokens,
        country: COUNTRY_CODE.NG,
        amount: basePrice + c.pricePerToken * c.tokens,
        interval: 30,
        interval_text: 'Monthly',
        discount: Math.ceil(((originalTokenPrice - c.pricePerToken) / originalTokenPrice) * 100),
      })),
    );

    const plan = new this.planModel({
      type: PLAN_TYPE.KITCHEN,
      name: 'Kitchen',
      is_paid_plan: true,
      options: planOptions.map((o) => o.id),
    });

    await plan.save();

    return plan;
  }
}
