import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { COUNTRY_CODE, CURRENCIES, Country } from '../../country/country.schema';
import { PLANS, PLAN_TYPE } from '../../../enums/plan.enum';

export type PlanOptionDocument = PlanOption & Document;

export enum PLAN_OPTION_VERSION {
  OLD = 'OLD',
  NEW = 'NEW',
}

@Schema({ timestamps: true })
export class PlanOption {
  @ApiProperty()
  id: string;

  @ApiProperty()
  @Prop({ required: true })
  interval: number;

  @ApiProperty()
  @Prop({ type: String, required: true })
  interval_text: string;

  @ApiProperty({ type: String, enum: [COUNTRY_CODE.NG, COUNTRY_CODE.GH, COUNTRY_CODE.ZA, COUNTRY_CODE.KE] })
  @Prop({ type: String, enum: [COUNTRY_CODE.NG, COUNTRY_CODE.GH, COUNTRY_CODE.ZA, COUNTRY_CODE.KE] })
  country: COUNTRY_CODE | Country;

  @ApiProperty({ type: String, enum: [CURRENCIES.NGN, CURRENCIES.ZAR, CURRENCIES.KES, CURRENCIES.GHC] })
  @Prop({ type: String, enum: [CURRENCIES.NGN, CURRENCIES.ZAR, CURRENCIES.KES, CURRENCIES.GHC] })
  currency: CURRENCIES;

  // @ApiProperty()
  // @Prop({ required: true })
  // currency: string;

  @ApiProperty({ type: 'string', enum: PLANS.TYPES })
  @Prop({ type: String, enum: PLANS.TYPES })
  plan_type: PLAN_TYPE;

  @ApiProperty()
  @Prop({ type: Number, required: true })
  amount: number;

  @ApiProperty()
  @Prop({ type: Number, required: true })
  actual_amount: number;

  @ApiProperty()
  @Prop({ type: Number, required: false })
  price_per_token?: number;

  @ApiProperty({ type: 'number' })
  @Prop({ type: Number, required: false })
  discount?: number;

  @ApiProperty({ default: 0 })
  @Prop({ default: 0 })
  chowbot_tokens?: number;

  @ApiProperty({ type: 'string', enum: PLAN_OPTION_VERSION })
  @Prop({ type: String, enum: Object.values(PLAN_OPTION_VERSION), default: PLAN_OPTION_VERSION.OLD })
  version: PLAN_OPTION_VERSION;
}

export const PlanOptionSchema = SchemaFactory.createForClass(PlanOption);
