import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { PAYMENT_METHODS } from '../../enums/payment.enum';
import { PLANS, PLAN_TYPE } from '../../enums/plan.enum';
import { COUNTRY_CODE, Country } from '../country/country.schema';
import { PlanOption } from './plan-options/plan-options.schema';

export type PlanDocument = Plan & Document;

@Schema({ timestamps: true })
export class Plan {
  @ApiProperty()
  id: string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  name: string;

  @ApiProperty()
  @Prop({ type: Number, required: false })
  amount?: number;

  @ApiProperty()
  @Prop({ type: [String] })
  description?: any[];

  @ApiProperty()
  @Prop({ type: Number, required: false })
  interval?: number;

  @ApiProperty()
  @Prop({ type: String, required: false })
  interval_text?: string;

  @ApiProperty()
  @Prop({ type: String, required: false })
  hook?: string;

  @ApiProperty({ type: 'string', enum: PLANS.TYPES })
  @Prop({ type: String, enum: PLANS.TYPES })
  type: PLAN_TYPE;

  @ApiProperty({ type: String, enum: [COUNTRY_CODE.NG, COUNTRY_CODE.GH, COUNTRY_CODE.ZA, COUNTRY_CODE.KE] })
  @Prop({ type: String, enum: [COUNTRY_CODE.NG, COUNTRY_CODE.GH, COUNTRY_CODE.KE, COUNTRY_CODE.ZA], required: false })
  country?: COUNTRY_CODE | Country;

  @ApiProperty({ type: 'number' })
  @Prop({ type: Number, required: false })
  discount: number;

  @ApiProperty({ type: () => Array })
  @Prop({
    type: [{ type: Types.ObjectId, ref: 'PlanOption' }],
    default: [],
  })
  options: PlanOption[];

  @ApiProperty()
  @Prop({ type: Boolean, required: true })
  is_paid_plan: boolean;

  @ApiProperty({
    type: 'array',
    items: {
      type: 'object',
      properties: {
        code: {
          type: 'string',
        },
        enum: {
          type: 'string',
        },
        total_amount: {
          type: 'number',
        },
        percent: {
          type: 'number',
        },
      },
    },
  })
  @Prop({
    type: [
      {
        code: String,
        total_amount: Number,
        percent: Number,
        type: {
          type: String,
          enum: [
            PAYMENT_METHODS.PAYSTACK,
            PAYMENT_METHODS.TRANSFER,
            PAYMENT_METHODS.TRF_MOMO,
            PAYMENT_METHODS.ZILLA,
            PAYMENT_METHODS.STARTBUTTON,
            PAYMENT_METHODS.MOMO,
          ],
        },
      },
    ],
  })
  methods?: {
    code: string;
    type: string;
    total_amount: number;
    percent: number;
  }[];
}

export const PlanSchema = SchemaFactory.createForClass(Plan);
