import {
  BadRequestException,
  HttpException,
  Injectable,
  NotFoundException,
  ServiceUnavailableException,
} from '@nestjs/common';
import axios from 'axios';
import fs from 'fs';
import buffer from 'buffer';
import { exec } from 'child_process';
import { BrokerTransportService } from '../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { GptResponse, OpenaiRepository } from '../../repositories/openai.respository';
import { PROMPT_TEMPLATES } from '../../utils/prompts';
import { PDFMargin } from 'puppeteer';
import { MenuImageDto } from '../../models/dtos/file.dto';
import vision from '@google-cloud/vision';
import { uploadImage } from '../store/kyc/utils/uploadImage';
import { S3Repository } from '../../repositories/s3.repositories';
import { objectToQueryParams, stripBase64Strings } from '../../utils/functions';
import path from 'path';
import { takeScreenshotsFromWebpage } from '../../utils/take-screenshots';
import { YearWrap } from '../store/year-wrap/year-wrap.schema';
import * as mime from 'mime-types';

const PER_PAGE = 500;

@Injectable()
export class UtilsService {
  constructor(
    protected readonly brokerTransport: BrokerTransportService,
    protected readonly openai: OpenaiRepository,
    private readonly s3: S3Repository,
  ) {}

  async convertImageUrlsToStrings(images: string[]) {
    const imageStrings = images.map(async (image) => {
      const imageRes = await axios.get(image, {
        responseType: 'arraybuffer',
      });
      const buffer = Buffer.from(imageRes.data);
      const imageString = 'data:image/jpeg;base64,' + buffer.toString('base64');
      return imageString;
    });

    return Promise.all(imageStrings);
  }

  async getInstagramMetatags(username: string) {
    const unauthenticatedHeaders = {};
    try {
      const response = await axios.get(
        `https://www.instagram.com/api/v1/users/web_profile_info/?username=${username}`,
        {
          headers: {
            'User-Agent':
              'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            Cookie:
              'csrftoken=nZAMBFTvr6nQSaMUQ9aYqdY4xh29B0ta; ig_did=3B9AB38B-0D05-4179-BF27-B72534385FB9; ig_nrcb=1; mid=ZLhacgAEAAFgSKuReLyJlwoMPbIz; datr=cFq4ZFAEyJ_Pob050o_FW8vD',
            'X-Csrftoken': 'nZAMBFTvr6nQSaMUQ9aYqdY4xh29B0ta',
            'X-Ig-App-Id': '936619743392459',
            'X-Asbd-Id': '129477',
            'Viewport-Width': '1284',
            'X-Ig-Www-Claim': '0',
            'X-Requested-With': 'XMLHttpRequest',
            Referer: `https://www.instagram.com/${username}/`,
          },
        },
      );

      const data = response.data.data;
      const imageRes = await axios.get(data.user.profile_pic_url, {
        responseType: 'arraybuffer',
      });
      const buffer = Buffer.from(imageRes.data);

      const metatags = {
        description: data.user.biography ?? "This username probably doesn't exist",
        title: data.user.full_name ?? 'Unknown Username',
        image: 'data:image/jpeg;base64,' + buffer.toString('base64'),
        fullData: data.user,
      };

      return metatags;
    } catch (error) {
      // Handle any error that might occur during the request
      console.log(error);
      throw new BadRequestException('Instagram Bounced!');
    }
  }

  async getInstagramMetatagsWithCurl(username: string) {
    try {
      const endpoint = `https://www.instagram.com/api/v1/users/web_profile_info/?username=${username}`;

      const headers = {
        'User-Agent':
          'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        Cookie:
          'csrftoken=nZAMBFTvr6nQSaMUQ9aYqdY4xh29B0ta; ig_did=3B9AB38B-0D05-4179-BF27-B72534385FB9; ig_nrcb=1; mid=ZLhacgAEAAFgSKuReLyJlwoMPbIz; datr=cFq4ZFAEyJ_Pob050o_FW8vD',
        'X-Csrftoken': 'nZAMBFTvr6nQSaMUQ9aYqdY4xh29B0ta',
        'X-Ig-App-Id': '936619743392459',
        'X-Asbd-Id': '129477',
      };

      let curlCommand = `curl -X GET '${endpoint}'`;

      // Add headers to the curl command
      for (const [header, value] of Object.entries(headers)) {
        curlCommand += ` -H '${header}: ${value}'`;
      }

      // console.log(curlCommand);

      let response;

      await exec(curlCommand, (error, stdout, stderr) => {
        if (error) {
          console.error('Error:', error.message);
          return;
        }

        if (stderr) {
          console.error('stderr:', stderr);
          return;
        }

        response = stdout;
        console.log('Response:', stdout);
      });

      // console.log('DATATAAAA!!');
      // console.log(response.data);

      // console.log('REQUEST HEADERS');
      // console.log(response?.request);

      // const data = response.data.data;
      // const imageRes = await axios.get(data.user.profile_pic_url, {
      //   responseType: 'arraybuffer',
      // });
      // const buffer = Buffer.from(imageRes.data);

      // const metatags = {
      //   description: data.user.biography ?? "This username probably doesn't exist",
      //   title: data.user.full_name ?? 'Unknown Username',
      //   image: 'data:image/jpeg;base64,' + buffer.toString('base64'),
      //   fullData: data.user,
      // };

      // return metatags;
      return response;
    } catch (error) {
      // Handle any error that might occur during the request
      console.log(error);
      throw new BadRequestException('Instagram Bounced!');
    }
  }

  async getSitemapPages() {
    const storesCount = await this.brokerTransport
      .send<number>(BROKER_PATTERNS.STORE.COUNT_STORES, { disabled: { $ne: true }, reference: { $exists: false } })
      .toPromise();

    const itemsCount = await this.brokerTransport
      .send<number>(BROKER_PATTERNS.ITEM.GET_TOTAL, { reference: { $exists: false } })
      .toPromise();

    return {
      stores: Math.ceil(storesCount / PER_PAGE),
      items: Math.ceil(itemsCount / PER_PAGE),
    };
  }

  async getSitemapItems(page: number) {
    const items = await this.brokerTransport
      .send<any>(BROKER_PATTERNS.ITEM.GET_SITEMAP_PAGE, { page, per_page: PER_PAGE })
      .toPromise();

    return items;
  }

  async getSitemapStores(page: number) {
    const stores = await this.brokerTransport
      .send<any>(BROKER_PATTERNS.STORE.GET_SITEMAP_PAGE, { page, per_page: PER_PAGE })
      .toPromise();

    return stores;
  }

  async getProductDetailsFromCaption(caption: string) {
    const res = await this.openai.getJsonResponseFromPrompt(PROMPT_TEMPLATES.GET_PRODUCT_DETAILS_FROM_CAPTION, caption);
    const data = res?.data as GptResponse;
    const message = data?.choices?.[0]?.message?.content;
    try {
      return JSON.parse(Array.from(message.matchAll(/{(\n|.)*}/g), (m) => m[0])[0]);
    } catch (e) {}
  }

  async getProductDetailsFromMultipleCaptions(captions: { caption: string; key: string }[]) {
    const reqs = captions.map(
      (c) =>
        new Promise(async (resolve) => {
          const res = await this.openai.getJsonResponseFromPrompt(
            PROMPT_TEMPLATES.GET_PRODUCT_DETAILS_FROM_CAPTION,
            decodeURIComponent(encodeURIComponent(c.caption)),
          );

          resolve(res);
        }),
    );

    try {
      const res = await Promise.all(reqs);
      const results = [];

      if (res) {
        res.forEach((r, index) => {
          // const data = (r as any)?.data as GptResponse;
          // const message = data?.choices?.[0]?.message?.content;

          try {
            // const parsedMessage = JSON.parse(Array.from(message.matchAll(/{(\n|.)*}/g), (m) => m[0])[0]);
            results.push({
              data: r,
              key: captions[index].key,
            });
          } catch (error) {
            results.push(null);
          }
        });
      }

      return results;
    } catch (error) {
      throw new ServiceUnavailableException("Couldn't extract product information");
    }
  }

  async extractItemsFromMenuImage(image: MenuImageDto) {
    //Create a google cloud client
    const client = new vision.ImageAnnotatorClient({
      keyFilename: path.join(__dirname, '../../config/vision-ai-key.json'),
    });

    // Upload restaurant menu image
    const menuImage = await uploadImage(stripBase64Strings(image.menu_image), this.s3, 'MENU_IMAGE');
    let detections;

    try {
      // Perform text detection on the image file
      const [result] = await client.documentTextDetection(menuImage.Location);
      detections = result.textAnnotations;
    } catch (error) {
      console.log(error);
      throw new BadRequestException('Something went wrong');
    }

    const textFromImage = detections?.[0]?.description;
    if (textFromImage) {
      try {
        const res = await this.openai.getJsonResponseFromPrompt(
          PROMPT_TEMPLATES.GET_MENU_ITEMS_FROM_IMAGE_TEXT,
          textFromImage,
          false,
        );
        // const response = JSON.parse(Array.from(message.matchAll(/{(\n|.)*}/g), (m) => m[0])[0]);

        console.log({ res });
        const returnData = Array.isArray(res) ? res : Array.isArray(Object.values(res)[0]) ? Object.values(res)[0] : [];

        return returnData;
      } catch (e) {
        console.log(e);
        throw new HttpException(`Error processing image, try again`, 400);
      }
    }
    throw new HttpException(`Error processing image, try again`, 400);
  }

  async generateMainSitemap(): Promise<string> {
    const baseApiUrl = process.env.CATLOG_API + '/utils';
    const baseFrontendUrl = process.env.CATLOG_WWW;
    const pagesCount = await this.getSitemapPages();

    const stores = Array.from(Array(pagesCount.stores)).map((_, index) => `${baseApiUrl}/sitemaps/stores/${index + 1}`);
    const items = Array.from(Array(pagesCount.items)).map((_, index) => `${baseApiUrl}/sitemaps/products/${index + 1}`);

    const pages = [`${baseFrontendUrl}/sitemaps/marketing.xml`, ...stores, ...items];

    const stringData = `<?xml version="1.0" encoding="UTF-8"?>
      <sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
        ${pages
          .map((url) => `<sitemap><loc>${url}</loc><lastmod>${new Date().toISOString()}</lastmod></sitemap>`)
          .join('')}
      </sitemapindex>`;

    return stringData;
  }

  async generateStoreSitemap(page: number): Promise<string> {
    const baseUrl = process.env.CATLOG_WWW;
    const stores = await this.getSitemapStores(page); // Adjust the per_page value as needed

    return `<?xml version="1.0" encoding="UTF-8"?>
      <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
        ${stores
          .map(
            (store) =>
              `<url><loc>${baseUrl}/${
                store.slug
              }</loc><lastmod>${new Date().toISOString()}</lastmod><changefreq>weekly</changefreq><priority>0.75</priority></url>`,
          )
          .join('')}
      </urlset>`;
  }

  async generateProductSitemap(page: number): Promise<string> {
    const baseUrl = process.env.CATLOG_WWW;
    const items = await this.getSitemapItems(page); // Adjust the per_page value as needed

    return `<?xml version="1.0" encoding="UTF-8"?>
      <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
        ${items
          .map(
            (item) =>
              `<url><loc>${baseUrl}/products/${
                item.slug
              }</loc><lastmod>${new Date().toISOString()}</lastmod><changefreq>weekly</changefreq><priority>0.75</priority></url>`,
          )
          .join('')}
      </urlset>`;
  }

  async generateManifestFile(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // Read the image URLs from the JSON file
        const imageUrls: string[] = JSON.parse(fs.readFileSync('prod.items-and-order-items.image-urls.json', 'utf8'));

        // Prepare an array to hold the CSV lines
        const csvLines: string[] = [];

        // Add the header line (optional for S3 Batch Operations)
        // For S3 Batch Operations, the header is not required, so we can omit it
        // csvLines.push('Bucket,Key');

        for (const url of imageUrls) {
          // Parse the URL to extract the bucket and key
          const parsedUrl = new URL(url);

          // Extract the bucket name from the hostname
          // Assuming the URL is in the format: https://catlog-1.s3.eu-west-2.amazonaws.com/path/to/object.jpg
          const hostname = parsedUrl.hostname;
          const bucket = hostname.split('.')[0]; // 'catlog-1'

          // Extract the object key (path) from the pathname
          // Decode URI components to handle any encoded characters
          const key = decodeURIComponent(parsedUrl.pathname.substring(1)); // Remove the leading '/'

          // Add a line to the CSV in the required format
          csvLines.push(`${bucket},${key}`);
        }

        // Write the CSV lines to a manifest file
        fs.writeFileSync('items.manifest.csv', csvLines.join('\n'), 'utf8');

        console.log('Manifest file generated successfully.');
        resolve();
      } catch (error) {
        console.error('Error generating manifest file:', error);
        reject(error);
      }
    });
  }

  async getWrappedScreenshots(cards: string[], storeSlug: string) {
    const wrappedData: YearWrap | { error: string } = await this.brokerTransport
      .send(BROKER_PATTERNS.STORE.GET_YEAR_WRAP, { slug: storeSlug })
      .toPromise();

    //check if data exists or not

    if (!wrappedData || (wrappedData as any)?.error) {
      return [];
    }

    const cardsArray = cards.map((card) => {
      const cardData = wrappedCardsData[card];

      if (cardData) {
        const params = {};

        cardData.params.forEach((param) => {
          params[param] = wrappedData[param];
        });

        const base64 = Buffer.from(JSON.stringify(params)).toString('base64');

        return {
          path: `${process.env.CATLOG_APP}/wrapped/cards/${card}?data=${base64}`,
          filename: card,
        };
      }

      return null;
    });

    const filePaths = await takeScreenshotsFromWebpage(cardsArray, this.s3);
    return filePaths;
  }

  async downloadFile(url: string) {
    try {
      const response = await axios.get(url, {
        responseType: 'arraybuffer',
        validateStatus: (status) => status < 400,
      });

      const contentType = response.headers['content-type'];
      const contentDisposition = response.headers['content-disposition'];

      // Try to get filename from content-disposition header
      let filename = '';
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, '');
        }
      }

      // If no filename in content-disposition, try to get it from URL
      if (!filename) {
        filename = url.split('/').pop() || 'downloaded-file';
      }

      // Ensure filename has proper extension based on content type
      const mimeType = contentType || mime.lookup(filename) || 'application/octet-stream';
      const extension = mime.extension(mimeType);

      if (extension && !filename.endsWith(`.${extension}`)) {
        filename = `${filename}.${extension}`;
      }

      return {
        buffer: Buffer.from(response.data),
        filename,
        mimeType,
      };
    } catch (error) {
      if (error.response?.status === 404) {
        throw new NotFoundException('File not found');
      }
      throw new BadRequestException('Failed to download file');
    }
  }

  async getUrlHeadInfo(url: string) {
    try {
      const response = await axios.head(url, {
        validateStatus: (status) => status < 400,
      });

      const headers = response.headers;
      const contentDisposition = headers['content-disposition'];

      // Try to get filename from content-disposition header
      let filename = '';
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, '');
        }
      }

      // If no filename in content-disposition, try to get it from URL
      if (!filename) {
        filename = url.split('/').pop() || 'unknown-file';
      }

      // Get mime type and extension
      const mimeType = headers['content-type'] || mime.lookup(filename) || 'application/octet-stream';
      const extension = mime.extension(mimeType);

      return {
        headers: {
          'content-type': headers['content-type'],
          'content-length': headers['content-length'],
          'content-disposition': headers['content-disposition'],
          'last-modified': headers['last-modified'],
          etag: headers['etag'],
        },
        filename,
        mimeType,
        extension,
        size: headers['content-length'] ? parseInt(headers['content-length'], 10) : null,
        lastModified: headers['last-modified'] ? new Date(headers['last-modified']).toISOString() : null,
      };
    } catch (error) {
      if (error.response?.status === 404) {
        throw new NotFoundException('File not found');
      }
      throw new BadRequestException('Failed to get file information');
    }
  }
}

type WrappedDataKeys = keyof YearWrap;

const wrappedCardsData: { [key: string]: { params: WrappedDataKeys[] } } = {
  store_visits: {
    params: ['no_of_store_visits'],
  },
  orders_processed: {
    params: ['total_order_volume', 'total_order_count', 'store_currency'],
  },
  payments_processed: {
    params: ['total_payments_count', 'total_payments_volume', 'store_currency'],
  },
  best_month: {
    params: ['month_with_highest_orders'],
  },
  best_product: {
    params: ['top_product'],
  },
  top_location: {
    params: ['top_orders_location', 'no_of_orders_for_location'],
  },
  referrals: {
    params: ['no_of_referrals'],
  },
  summary: {
    params: [
      'total_order_count',
      'store_currency',
      'no_of_store_visits',
      'month_with_highest_orders',
      'top_product',
      'top_orders_location',
      'total_payments_volume',
    ],
  },
  metaphor: {
    params: ['metaphor'],
  },
};
