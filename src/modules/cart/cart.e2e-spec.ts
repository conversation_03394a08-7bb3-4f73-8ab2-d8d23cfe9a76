import { Test, TestingModule } from '@nestjs/testing';
import { AppModule } from '../../app.module';
import { JwtStrategy } from '../../jwt/jwt.strategy';
import { MockJwtStrategy } from '../../tools/test-helpers';
import { LowercasePipe } from '../../pipe/lowercase.pipe';
import { Transport } from '@nestjs/microservices';
import { CartModule } from './cart.module';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { mockCart } from './text-data';
import { CartService } from './cart.service';

describe('Cart Module', () => {
  let app: INestApplication;

  // const expectedCartRes = {
  //   id: expect.any(String),
  //   hash: expect.any(String),
  //   items: expect.arrayContaining([expect.objectContaining(mockCart.items[0])]),
  //   updated_at: expect.any(String),
  //   created_at: expect.any(String),
  // };

  beforeAll(async () => {
    jest.setTimeout(30000);
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule, CartModule],
    })
      .overrideProvider(JwtStrategy)
      .useClass(MockJwtStrategy)
      .compile();

    app = module.createNestApplication();
    app.useGlobalPipes(new LowercasePipe());
    app.connectMicroservice({
      transport: Transport.NATS,
      options: {
        url: process.env.NATS_URL || 'nats://localhost:4222',
      },
    });

    await app.startAllMicroservicesAsync();
    await app.init();
  });

  afterAll(async () => {
    const cartService = app.get<CartService>(CartService);
    await cartService.remove(mockCart.id);
  });

  it('should call api to create a cart', async () => {
    delete mockCart.id;
    return request(app.getHttpServer())
      .post('/carts')
      .send(mockCart)
      .expect(201)
      .then(({ body: { data } }) => {
        expect(data.id).toBeDefined();
        expect(data.items[0].object).toEqual(
          expect.objectContaining({
            id: expect.any(String),
            views: expect.any(Number),
            available: expect.any(Boolean),
          }),
        );
        expect(data.items[0].quantity).toStrictEqual(4);
        mockCart.id = data.id;
        mockCart.hash = data.hash;
      });
  });

  it('should call api to update cart', async () => {
    mockCart.items[0].quantity = 3;
    return request(app.getHttpServer())
      .put(`/carts/${mockCart.id}`)
      .send(mockCart)
      .expect(200)
      .then(({ body: { data } }) => {
        expect(data.items[0].quantity).toStrictEqual(3);
        expect.objectContaining({
          id: expect.any(String),
          views: expect.any(Number),
          available: expect.any(Boolean),
        });
      });
  });

  it('should call api to get cart by id', async () => {
    return request(app.getHttpServer())
      .get(`/carts/${mockCart.id}`)
      .expect(200)
      .then(({ body: { data } }) => {
        expect(data.items[0].quantity).toStrictEqual(3);
        expect(data.items[0].object).toEqual(
          expect.objectContaining({
            id: expect.any(String),
            views: expect.any(Number),
            available: expect.any(Boolean),
          }),
        );
      });
  });

  it('should call api to get cart by hash', async () => {
    return request(app.getHttpServer())
      .get(`/carts/${mockCart.hash}`)
      .expect(200)
      .then(({ body: { data } }) => {
        expect(data.items[0].quantity).toStrictEqual(3);
        expect(data.items[0].object).toEqual(
          expect.objectContaining({
            id: expect.any(String),
            views: expect.any(Number),
            available: expect.any(Boolean),
          }),
        );
      });
  });

  // it('should call api to get all carts by store id', async () => {
  //   return request(app.getHttpServer())
  //     .get(`/carts?store=${mockCart.store}`)
  //     .expect(200)
  //     .then(({ body: { data } }) => {
  //       expect(Array.isArray(data));
  //       expect(data.length).toBeGreaterThanOrEqual(1);
  //       let item;
  //       data.forEach((cart) => {
  //         const findItem = cart.items.find((item) => item.quantity === 3);
  //         if (findItem) {
  //           item = findItem;
  //         }
  //       });
  //       expect(item).toBeTruthy();
  //       expect(item.quantity).toStrictEqual(3);
  //       expect(item.object).toEqual(
  //         expect.objectContaining({
  //           id: expect.any(String),
  //           views: expect.any(Number),
  //           available: expect.any(Boolean),
  //         }),
  //       );
  //     });
  // });
});
