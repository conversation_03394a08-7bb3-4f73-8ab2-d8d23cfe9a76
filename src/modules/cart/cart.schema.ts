import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Store } from '../store/store.schema';
import { Item } from '../item/item.schema';
import { Customer } from '../orders/customers/customer.schema';
import { VisitorLocation } from '../ip-address/ip-address.schema';
import { CURRENCIES } from '../country/country.schema';

export interface CartItem {
  _id: mongoose.Schema.Types.ObjectId | string;
  item_id: string;
  object: Item;
  snapshot?: Item;
  quantity: number;
  variant_id: string;
  variant: any;
  is_deleted: any;
}

export type CartDocument = Cart & Document;
@Schema({ timestamps: { updatedAt: 'updated_at', createdAt: 'created_at' } })
export class Cart {
  @ApiProperty()
  id?: string;

  _id?: string;

  @ApiProperty()
  @Prop({ type: String })
  hash: string;

  @ApiProperty()
  @Prop({ type: String })
  ip_address?: string;

  @ApiProperty({
    type: 'array',
    items: {
      properties: {
        item_id: {
          type: 'string',
        },
        quantity: {
          type: 'number',
        },
      },
    },
  })
  @Prop({
    type: [
      {
        item_id: mongoose.Schema.Types.ObjectId,
        quantity: Number,
        object: { type: mongoose.Schema.Types.ObjectId, ref: 'Item' },
        variant_id: mongoose.Schema.Types.ObjectId,
      },
    ],
  })
  items: CartItem[];

  @ApiProperty({
    type: 'object',
    properties: {
      chowdeck: {
        type: 'object',
        properties: {
          city: { type: 'string' },
          latitude: { type: 'number' },
          longitude: { type: 'number' },
          ip: { type: 'string' },
          country_code: { type: 'string' },
          country_name: { type: 'string' },
          region_code: { type: 'string' },
          region_name: { type: 'string' },
          zip_code: { type: 'string' },
          time_zone: { type: 'string' },
        },
      },
    },
  })
  @Prop({
    type: {
      city: String,
      latitude: Number,
      longitude: Number,
      ip: String,
      country_code: String,
      country_name: String,
      region_code: String,
      region_name: String,
      zip_code: String,
      time_zone: String,
    },
  })
  location: VisitorLocation;

  @ApiProperty({
    type: 'string',
  })
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Store' })
  store: Store;

  @ApiProperty({
    type: 'string',
  })
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Customer', required: false })
  customer?: Customer;

  @ApiProperty({
    type: 'string',
  })
  @Prop({ type: String, required: false })
  order?: String;

  @ApiProperty()
  @Prop({
    type: String,
    enum: [
      CURRENCIES.NGN,
      CURRENCIES.USD,
      CURRENCIES.EUR,
      CURRENCIES.GBP,
      CURRENCIES.GHC,
      CURRENCIES.ZAR,
      CURRENCIES.KES,
    ],
    default: CURRENCIES.NGN,
  })
  currency?: CURRENCIES;
}

export const CartSchema = SchemaFactory.createForClass(Cart);
