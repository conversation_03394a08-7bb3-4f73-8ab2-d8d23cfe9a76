import { Mo<PERSON><PERSON> } from '@nestjs/common';
import { CartService } from './cart.service';
import { CartController } from './cart.controller';
import { MongooseModule } from '@nestjs/mongoose';
import mongooseLeanVirtuals from 'mongoose-lean-virtuals';
import setIdPlugin from '../../mongoose-plugins/set-id.mongoose-plugin';
import jsonHookPlugin from '../../mongoose-plugins/json-hook.mongoose-plugin';
import { Cart, CartSchema } from './cart.schema';
import { CartBroker } from './cart.broker';
import { SharedModule } from '../../shared.module';
import mongoosePaginate from 'mongoose-paginate-v2';

@Module({
  imports: [
    MongooseModule.forFeatureAsync([
      {
        name: Cart.name,
        useFactory: () => {
          CartSchema.plugin(setIdPlugin());
          CartSchema.plugin(mongooseLeanVirtuals);
          CartSchema.plugin(mongoosePaginate);
          CartSchema.plugin(jsonHookPlugin(['__v', '_id']));
          return CartSchema;
        },
      },
    ]),
    SharedModule,
  ],
  controllers: [CartController, CartBroker],
  providers: [CartService],
  exports: [CartService],
})
export class CartModule {}
