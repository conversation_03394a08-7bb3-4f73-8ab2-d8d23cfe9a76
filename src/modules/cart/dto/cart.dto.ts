import { ApiProperty } from '@nestjs/swagger';
import { Cart } from '../cart.schema';
import { PaginatedQueryWithDataDto } from '../../../models/dtos/PaginatedDto';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { CURRENCIES } from '../../country/country.schema';

export class CreateCartDto {
  @ApiProperty()
  id?: string;

  @ApiProperty()
  customer?: string;

  @ApiProperty()
  ip?: string;

  @ApiProperty({ example: 'store_id' })
  store: string;

  @ApiProperty({
    type: 'object',
    properties: {
      item_id: {
        type: 'string',
      },
      quantity: {
        type: 'number',
      },
    },
  })
  items: { item_id: string; quantity: number }[];

  @IsString()
  @IsNotEmpty()
  currency?: CURRENCIES;
}

export class UpdateCartDto {
  @ApiProperty()
  id?: string;

  @ApiProperty()
  customer?: string;

  @ApiProperty({ example: 'store_id', required: false })
  store?: string;

  @ApiProperty({
    type: 'object',
    properties: {
      item_id: {
        type: 'string',
      },
      quantity: {
        type: 'number',
      },
    },
    required: false,
  })
  items?: { item_id: string; quantity: number }[];

  @ApiProperty()
  ip?: string;

  @IsOptional()
  @IsString()
  currency?: CURRENCIES;
}

export class CartResponseDto {
  @ApiProperty()
  message: string;

  @ApiProperty({ type: Cart })
  data: Cart;
}

export class CartResponseDtoCarts {
  @ApiProperty()
  message: string;

  @ApiProperty({ type: [Cart] })
  data: Cart[];
}

export class GetCartPaginatedDto extends PaginatedQueryWithDataDto<Cart> {
  @ApiProperty({
    type: [Cart],
  })
  data: [Cart];
}
