import { Controller } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { MongooseFilterQuery } from 'mongoose';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { CartDocument } from './cart.schema';
import { CartService } from './cart.service';
import { SkipThrottle } from '@nestjs/throttler';

@SkipThrottle()
@Controller()
export class CartBroker {
  constructor(private readonly cartService: CartService) {}

  @MessagePattern(BROKER_PATTERNS.CART.GET_TOTAL)
  async getCartStoreTotal(data: MongooseFilterQuery<CartDocument>) {
    return this.cartService.getTotal(data);
  }

  @MessagePattern(BROKER_PATTERNS.CART.GET_CART)
  async getCart(data: { id: string }) {
    return this.cartService.findOne(data.id);
  }

  @MessagePattern(BROKER_PATTERNS.CART.CREATE_CART)
  async createCart(data) {
    return this.cartService.create(data);
  }

  @MessagePattern(BROKER_PATTERNS.CART.UPDATE_CART)
  async updateCart(data: { id: string; data: any }) {
    return this.cartService.update(data.id, data.data);
  }
}
