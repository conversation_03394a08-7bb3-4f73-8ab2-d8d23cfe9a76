import { CartService } from './cart.service';
import { Test, TestingModule } from '@nestjs/testing';
import { AppModule } from '../../app.module';
import { CartModule } from './cart.module';
import { TEST_ITEM_ID, TEST_STORE_ID } from '../../tools/testdata';
import { Transport } from '@nestjs/microservices';

describe('cart.service.ts', () => {
  let cartService: CartService;
  const mockCart = {
    id: undefined,
    store: TEST_STORE_ID,
    items: [{ item_id: TEST_ITEM_ID, quantity: 4 }],
    hash: undefined,
  };

  beforeAll(async () => {
    jest.setTimeout(30000);

    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule, CartModule],
    }).compile();

    const app = module.createNestApplication();
    app.connectMicroservice({
      transport: Transport.NATS,
      options: {
        url: process.env.NATS_URL || 'nats://localhost:4222',
      },
    });

    await app.startAllMicroservicesAsync();
    await app.init();

    cartService = app.get<CartService>(CartService);
  });

  afterAll(async () => {
    await cartService.remove(mockCart.id);
  });

  it('should create a cart', async () => {
    delete mockCart.id;
    const cart = await cartService.create(mockCart);
    expect(cart.items[0].item_id.toString()).toStrictEqual(
      mockCart.items[0].item_id,
    );
    expect(cart.items[0].object).toEqual(
      expect.objectContaining({
        id: expect.any(String),
        views: expect.any(Number),
        available: expect.any(Boolean),
      }),
    );
    mockCart.id = cart.id;
    mockCart.hash = cart.hash;
  });

  it('should find all cart tied to a store', async () => {
    const carts = await cartService.findAll(mockCart.store);
    expect(carts.length).toBeGreaterThanOrEqual(1);
    expect(carts[0].items[0].item_id).toStrictEqual(mockCart.items[0].item_id);
    expect(carts[0].items[0].quantity).toStrictEqual(
      mockCart.items[0].quantity,
    );
    expect(carts[0].items[0].object).toEqual(
      expect.objectContaining({
        id: expect.any(String),
        views: expect.any(Number),
        available: expect.any(Boolean),
      }),
    );
  });

  it('should update a cart', async () => {
    mockCart.items[0].quantity = 3;
    const cart = await cartService.update(mockCart.id, mockCart);
    expect(cart.items[0].quantity).toEqual(3);
    expect(cart.items[0].object).toEqual(
      expect.objectContaining({
        id: expect.any(String),
        views: expect.any(Number),
        available: expect.any(Boolean),
      }),
    );
  });

  it('should get cart by id', async () => {
    const cart = await cartService.findOne(mockCart.id);
    expect(cart.id).toStrictEqual(mockCart.id);
    expect(cart.items[0].item_id).toStrictEqual(mockCart.items[0].item_id);
    expect(cart.items[0].object).toEqual(
      expect.objectContaining({
        id: expect.any(String),
        views: expect.any(Number),
        available: expect.any(Boolean),
      }),
    );
  });

  it('should get cart by hash', async () => {
    const cart = await cartService.findOne(mockCart.hash);
    expect(cart.id).toStrictEqual(mockCart.id);
    expect(cart.items[0].object).toEqual(
      expect.objectContaining({
        id: expect.any(String),
        views: expect.any(Number),
        available: expect.any(Boolean),
      }),
    );
  });
});
