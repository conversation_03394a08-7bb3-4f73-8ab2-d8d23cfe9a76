import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  Req,
  UseGuards,
} from '@nestjs/common';
import { CartService } from './cart.service';
import {
  CartResponseDto,
  CartResponseDtoCarts,
  CreateCartDto,
  GetCartPaginatedDto,
  UpdateCartDto,
} from './dto/cart.dto';
import { ApiCreatedResponse, ApiOkResponse, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Cart } from './cart.schema';
import { IRequest } from '../../interfaces/request.interface';
import { getAppEnv } from '../../utils';
import { JwtAuthGuard } from '../../jwt/jwt-auth.guard';
import { PaginatedQueryDto } from '../wallets/dtos/search.dto';

@ApiTags('Cart')
@Controller('carts')
export class CartController {
  constructor(private readonly cartService: CartService) {}

  @Post()
  @ApiOperation({
    summary: 'Creates a cart by taking an array object containing the item id and quantity',
  })
  @ApiCreatedResponse({
    status: HttpStatus.CREATED,
    type: Cart,
  })
  @HttpCode(HttpStatus.CREATED)
  async create(@Req() request: IRequest, @Body() createCartDto: CreateCartDto) {
    const ip = request.headers['x-real-ip'] as string;
    createCartDto.ip = ip ? ip : getAppEnv() === 'local' ? '**************' : undefined;

    const data = await this.cartService.create(createCartDto);
    return {
      message: 'Cart created successfully',
      data,
    };
  }

  // @Get('')
  // @ApiOkResponse({ type: CartResponseDtoCarts })
  // async findAll(@Query('store') storeId: string) {
  //   const data = await this.cartService.findAll(storeId);
  //   return {
  //     message: 'Carts fetched successfully',
  //     data,
  //   };
  // }

  @ApiOperation({ summary: 'Get cart using either cart id or hash' })
  @ApiOkResponse({ type: CartResponseDto })
  @Get(':id')
  async findOne(@Param('id') id: string) {
    const data = await this.cartService.findOne(id);
    return {
      message: 'Cart fetched successfully',
      data,
    };
  }

  @ApiOkResponse({ type: CartResponseDto })
  @Put(':id')
  async update(@Req() request: IRequest, @Param('id') id: string, @Body() cartReq: UpdateCartDto) {
    const ip = request.headers['x-real-ip'] as string;
    cartReq.ip = ip ? ip : getAppEnv() === 'local' ? '**************' : undefined;

    const data = await this.cartService.update(id, cartReq);
    return {
      message: 'Cart updated successfully',
      data,
    };
  }

  @ApiOkResponse({ type: CartResponseDto })
  @Delete(':id')
  async remove(@Param('id') id: string) {
    const data = await this.cartService.remove(id);
    return {
      message: 'Cart deleted successfully',
      data,
    };
  }

  @Get('')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  @ApiResponse({
    status: HttpStatus.OK,
    type: GetCartPaginatedDto,
  })
  async getItems(@Query() query: PaginatedQueryDto, @Req() req: IRequest) {
    const items = await this.cartService.paginateCarts(req.user.store.id, query);

    return {
      message: 'Carts fetched successfully',
      data: items,
    };
  }
}
