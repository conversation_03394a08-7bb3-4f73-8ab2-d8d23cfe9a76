import {
  ArrayMinSize,
  ArrayNotEmpty,
  IsArray,
  IsBoolean,
  IsEnum,
  IsIn,
  IsInstance,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { PAYMENT_METHODS, PAYMENT_TYPES } from '../../../enums/payment.enum';
import { Type } from 'class-transformer';
import { COUNTRY_CODE, CURRENCIES } from '../../country/country.schema';

export class PaymentMethodMeta {
  @ApiProperty()
  network?: string;

  @ApiProperty()
  momo_number?: string;

  @ApiProperty()
  account_name?: string;
}

export class CreatePaymentMethodDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @IsBoolean()
  @IsNotEmpty()
  active: boolean;

  @ApiProperty()
  @ValidateNested()
  @Type(() => PaymentMethodMeta)
  // @IsInstance(PaymentMethodMeta)
  meta: PaymentMethodMeta;

  @IsEnum(PAYMENT_METHODS)
  @IsString()
  // @IsIn([
  //   PAYMENT_METHODS.PAYSTACK,
  //   PAYMENT_METHODS.MONNIFY_TRANSFER,
  //   PAYMENT_METHODS.TRF_MOMO,
  //   PAYMENT_METHODS.ZILLA,
  //   PAYMENT_METHODS.MONO_DIRECT_PAY,
  //   PAYMENT_METHODS.THEPEER,
  //   PAYMENT_METHODS.CATLOG_CREDIT,
  //   PAYMENT_METHODS.WALLET,
  // ])
  type: PAYMENT_METHODS;

  @ApiProperty({
    type: [String],
  })
  @IsEnum(COUNTRY_CODE, { each: true })
  countries: COUNTRY_CODE[];

  @ApiProperty()
  @IsEnum(CURRENCIES, { each: true })
  @ArrayNotEmpty()
  @ArrayMinSize(1)
  @IsString({ each: true })
  currencies: CURRENCIES[];

  @ApiProperty({
    type: [String],
  })
  @IsEnum(PAYMENT_TYPES, { each: true })
  payment_types: PAYMENT_TYPES[];
}
