import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, Post, UseGuards } from '@nestjs/common';
import { PaymentMethodService } from './paymentmethod.service';
import { ApiExcludeEndpoint, ApiHeader } from '@nestjs/swagger';
import { CreatePaymentMethodDto } from './dtos/create';
import { PAYMENT_METHODS } from '../../enums/payment.enum';
import { InternalApiKeyGuard } from '../../guards/internal-api-key.guard';
import { JwtAuthGuard } from '../../jwt/jwt-auth.guard';
import { InternalApiJWTGuard } from '../../guards/api.guard';

@Controller('payment-methods')
export class PaymentMethodController {
  constructor(private readonly paymentMethodService: PaymentMethodService) {}

  @Get('')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @HttpCode(HttpStatus.OK)
  async getAllMethods() {
    return {
      data: await this.paymentMethodService.getAllMethods({}),
    };
  }

  @Post('')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'internal-api-key' })
  @HttpCode(HttpStatus.OK)
  async createPaymentMethod(@Body() body: CreatePaymentMethodDto) {
    const data = await this.paymentMethodService.createPaymentMethod(body);
    return {
      data,
    };
  }

  @Delete('/:type')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'internal-api-key' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deletePaymentMethod(@Param('type') type: PAYMENT_METHODS) {
    await this.paymentMethodService.deletePaymentMethod(type);
    return {
      message: 'Method successfully deleted',
    };
  }

  @Get('/type/:type')
  @HttpCode(HttpStatus.OK)
  async getPaymentMethod(@Param('type') type: PAYMENT_METHODS) {
    const data = await this.paymentMethodService.getPaymentMethod(type);
    return {
      message: 'Method fetched successfully',
      data,
    };
  }

  @Post('/migrate-payment-methods')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async migratePaymentMethods() {
    return this.paymentMethodService.migrateAllPaymentMethods();
  }

  @Post('/migrate-transfer-payment-method')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async migrateTransferPaymentMethod() {
    return this.paymentMethodService.migratePaymentMethodsToAddBaseTransferType();
  }
}
