import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { jsonHookMongoosePlugin, setIdMongoosePlugin } from '../../mongoose-plugins';
import { SharedModule } from '../../shared.module';
import { PaymentMethodBroker } from './paymentmethod.broker';
import { PaymentMethodController } from './paymentmethod.controller';
import { PaymentMethod, PaymentMethodSchema } from './paymentmethod.schema';
import { PaymentMethodService } from './paymentmethod.service';

@Module({
  imports: [
    SharedModule,
    MongooseModule.forFeatureAsync([
        {
            name: PaymentMethod.name,
            useFactory: () => {
                PaymentMethodSchema.plugin(setIdMongoosePlugin());
                PaymentMethodSchema.plugin(jsonHookMongoosePlugin(['__v', '_id']));
                return PaymentMethodSchema;
            },
        },
    ])
  ],
  providers: [PaymentMethodService],
  controllers: [PaymentMethodController, PaymentMethodBroker]
})
export class PaymentMethodModule {}
