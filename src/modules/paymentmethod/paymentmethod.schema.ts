import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { PAYMENT_METHODS, PAYMENT_TYPES } from '../../enums/payment.enum';
import { PaymentMethodMeta } from './dtos/create';
import { COUNTRY_CODE, CURRENCIES } from '../country/country.schema';

export type PaymentMethodDocument = PaymentMethod & Document;

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class PaymentMethod {
  @ApiProperty()
  id: string;

  @ApiProperty()
  @Prop({ type: String })
  name: string;

  @ApiProperty()
  @Prop({ type: PaymentMethodMeta })
  meta: PaymentMethodMeta;

  @ApiProperty()
  @Prop({ type: [String] })
  countries: [COUNTRY_CODE];

  @ApiProperty()
  @Prop({ type: [String], enum: Object.values(CURRENCIES) })
  currencies: [CURRENCIES];

  @ApiProperty()
  @Prop({ type: [String] })
  payment_types: [PAYMENT_TYPES];

  @ApiProperty()
  @Prop({ type: String })
  @ApiProperty({
    type: String,
    enum: Object.values(PAYMENT_METHODS),
  })
  @Prop({
    type: String,
    enum: Object.values(PAYMENT_METHODS),
  })
  type: PAYMENT_METHODS;

  @ApiProperty({ type: Boolean })
  @Prop({ type: Boolean, default: true })
  enabled: boolean;
}
export const PaymentMethodSchema = SchemaFactory.createForClass(PaymentMethod);
