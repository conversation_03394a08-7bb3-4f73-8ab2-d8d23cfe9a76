import { Controller } from '@nestjs/common';
import { PaymentMethodService } from './paymentmethod.service';
import { MessagePattern } from '@nestjs/microservices';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { SkipThrottle } from '@nestjs/throttler';

@SkipThrottle()
@Controller()
export class PaymentMethodBroker {
  constructor(private readonly paymentMethodSerivce: PaymentMethodService) {}

  @MessagePattern(BROKER_PATTERNS.PAYMENT.GET_ALL_PAYMENT_METHODS)
  async getPaymentMethods(filter: any = {}) {
    return this.paymentMethodSerivce.getAllMethods(filter);
  }
}
