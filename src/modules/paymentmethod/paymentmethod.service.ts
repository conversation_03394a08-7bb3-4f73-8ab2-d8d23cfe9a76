import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { PaymentMethod, PaymentMethodDocument } from './paymentmethod.schema';
import { Model } from 'mongoose';
import { CreatePaymentMethodDto } from './dtos/create';
import { formatString, genChars } from '../../utils';
import { PAYMENT_METHODS } from '../../enums/payment.enum';

@Injectable()
export class PaymentMethodService {
  constructor(
    @InjectModel(PaymentMethod.name)
    protected readonly paymentMethodModel: Model<PaymentMethodDocument>,
  ) {}

  async createPaymentMethod(body: CreatePaymentMethodDto) {
    return this.paymentMethodModel.create({ ...body, enabled: true });
  }

  async deletePaymentMethod(type: PAYMENT_METHODS) {
    return this.paymentMethodModel.deleteOne({ type });
  }

  async getPaymentMethod(type: PAYMENT_METHODS) {
    return this.paymentMethodModel.findOne({ type }).exec();
  }

  async getAllMethods(filter) {
    const res = await this.paymentMethodModel.find(filter);
    // console.log('R:', res);
    return res;
  }

  async migrateAllPaymentMethods() {
    const methods = await this.getAllMethods({});

    for (const m of methods) {
      if (m.enabled == undefined) {
        m.enabled = true;
        m.meta = {};
      }

      await m.save();
    }

    return methods;
  }

  async migratePaymentMethodsToAddBaseTransferType() {
    return await this.paymentMethodModel.findOneAndUpdate(
      {
        type: PAYMENT_METHODS.MONNIFY_TRANSFER,
      },
      {
        name: 'Transfer',
        type: PAYMENT_METHODS.TRANSFER,
      },
      { new: true },
    );
  }

  genRef() {
    return formatString(genChars(16, true));
  }
}
