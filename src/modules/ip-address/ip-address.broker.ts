import { Controller } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model } from 'mongoose';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { IpAddress, IpAddressDocument } from './ip-address.schema';
import { IpAddressService } from './ip-address.service';
import { SkipThrottle } from '@nestjs/throttler';

@SkipThrottle()
@Controller()
export class IpAddressBroker {
  constructor(private readonly IpAddressService: IpAddressService) {}

  @MessagePattern(BROKER_PATTERNS.IP_ADDRESS.RESOLVE_IP)
  async getOrRecordIPAddress(ip: string) {
    return await this.IpAddressService.getOrRecordIPAddress(ip);
  }
}
