import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export interface VisitorLocation {
  city?: string;
  latitude?: number;
  longitude?: number;
  ip?: string;
  country_code?: string;
  country_name?: string;
  region_code?: string;
  region_name?: string;
  zip_code?: string;
  time_zone?: string;
}

export type IpAddressDocument = IpAddress & Document;
@Schema({ timestamps: { updatedAt: 'updated_at', createdAt: 'created_at' } })
export class IpAddress {
  @ApiProperty()
  id?: string;

  _id?: string;

  @ApiProperty()
  @Prop({ type: String })
  ip: string;

  @ApiProperty({
    type: 'object',
    properties: {
      chowdeck: {
        type: 'object',
        properties: {
          city: { type: 'string' },
          latitude: { type: 'number' },
          longitude: { type: 'number' },
          ip: { type: 'string' },
          country_code: { type: 'string' },
          country_name: { type: 'string' },
          region_code: { type: 'string' },
          region_name: { type: 'string' },
          zip_code: { type: 'string' },
          time_zone: { type: 'string' },
        },
      },
    },
  })
  @Prop({
    type: {
      city: String,
      latitude: Number,
      longitude: Number,
      ip: String,
      country_code: String,
      country_name: String,
      region_code: String,
      region_name: String,
      zip_code: String,
      time_zone: String,
    },
  })
  location: VisitorLocation;
}

export const IpAddressSchema = SchemaFactory.createForClass(IpAddress);
