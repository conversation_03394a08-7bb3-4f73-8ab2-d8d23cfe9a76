import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import setIdPlugin from '../../mongoose-plugins/set-id.mongoose-plugin';
import jsonHookPlugin from '../../mongoose-plugins/json-hook.mongoose-plugin';
import { SharedModule } from '../../shared.module';
import { IpAddressBroker } from './ip-address.broker';
import { IpAddressController } from './ip-address.controller';
import { IpAddressService } from './ip-address.service';
import { IpAddress, IpAddressSchema } from './ip-address.schema';

@Module({
  imports: [
    MongooseModule.forFeatureAsync([
      {
        name: IpAddress.name,
        useFactory: () => {
          IpAddressSchema.plugin(setIdPlugin());
          IpAddressSchema.plugin(jsonHookPlugin(['_id', '__v']));
          return IpAddressSchema;
        },
      },
    ]),
    SharedModule,
  ],
  controllers: [IpAddressController, IpAddressBroker],
  providers: [IpAddressService],
})
export class IpAddressModule {}
