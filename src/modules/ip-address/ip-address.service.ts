import { Injectable } from '@nestjs/common';
import { IpAddress, IpAddressDocument, VisitorLocation } from './ip-address.schema';
import axios from 'axios';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { BrokerTransportService } from '../../broker/broker-transport.service';

interface IpGeolocationResponse {
  query: string;
  status: string;
  continent: string;
  continentCode: string;
  country: string;
  countryCode: string;
  region: string;
  regionName: string;
  city: string;
  district: string;
  zip: string;
  lat: number;
  lon: number;
  timezone: string;
  offset: number;
  currency: string;
  isp: string;
  org: string;
  as: string;
  asname: string;
  mobile: boolean;
  proxy: boolean;
  hosting: boolean;
}

@Injectable()
export class IpAddressService {
  constructor(
    @InjectModel(IpAddress.name) private readonly ipAddressModel: Model<IpAddressDocument>,
    private readonly brokerTransport: BrokerTransportService,
  ) {}

  async getOrRecordIPAddress(ip: string) {
    const ipAddress = await this.ipAddressModel.findOne({ ip: ip });
    if (ipAddress && ipAddress.location) {
      return ipAddress;
    }

    const ipLocation = await this.resolveIpLocation(ip);

    if (ipAddress) {
      ipAddress.location = ipLocation;
      await ipAddress.save();
      return ipAddress;
    }

    const newIpAddress = await this.ipAddressModel.create({ ip: ip, location: ipLocation });
    return newIpAddress;
  }

  async resolveIpLocation(ip: string): Promise<VisitorLocation> {
    try {
      const response = await axios.get(`http://ip-api.com/json/${ip}`);

      const data = response.data as IpGeolocationResponse;

      return {
        city: data.city,
        country_name: data.country,
        country_code: data.countryCode,
        latitude: data.lat,
        longitude: data.lon,
        zip_code: data.zip,
        region_code: data.region,
        region_name: data.regionName,
        time_zone: data.timezone,
        ip: ip,
      };
    } catch (error) {
      return null;
    }
  }
}
