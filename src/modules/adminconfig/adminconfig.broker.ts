import { Controller } from '@nestjs/common';
import { AdminConfigService } from './adminconfig.service';
import { MessagePattern } from '@nestjs/microservices';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { SkipThrottle } from '@nestjs/throttler';

@SkipThrottle()
@Controller()
export class AdminConfigBroker {
  constructor(private adminConfigService: AdminConfigService) {}

  @MessagePattern(BROKER_PATTERNS.CONFIG.GET_CONFIG)
  async getActiveConfig(name: string) {
    return this.adminConfigService.getConfig(name);
  }
}
