import { Mo<PERSON><PERSON> } from "@nestjs/common";
import { SharedModule } from "../../shared.module";
import { MongooseModule } from "@nestjs/mongoose";
import { AdminConfig, AdminConfigSchema } from "./adminconfig.schema";
import { jsonHookMongoosePlugin, setIdMongoosePlugin } from '../../mongoose-plugins';
import { AdminConfigController } from "./adminconfig.controller";
import { AdminConfigBroker } from "./adminconfig.broker";
import { AdminConfigService } from "./adminconfig.service";

@Module({
  imports: [
    MongooseModule.forFeatureAsync([
      {
        name: AdminConfig.name,
        useFactory: () => {
          AdminConfigSchema.plugin(setIdMongoosePlugin());
          AdminConfigSchema.plugin(jsonHookMongoosePlugin(['_id', '__v']));
          return AdminConfigSchema;
        },
      },
    ]),
    SharedModule,
  ],
  controllers: [AdminConfigController, AdminConfigBroker],
  providers: [AdminConfigService]
})
export class AdminConfigModule {};