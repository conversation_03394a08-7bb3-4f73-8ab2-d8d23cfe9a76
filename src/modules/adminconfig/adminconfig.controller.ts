import { Body, Controller, Get, Post, UseGuards } from '@nestjs/common';
import { AdminConfigService } from './adminconfig.service';
import { InternalApiJWTGuard } from '../../guards/api.guard';
import { JwtAuthGuard } from '../../jwt/jwt-auth.guard';
import { AddAdminConfigDto } from '../../models/dtos/admincfg.dto';
import {
  ApiExcludeEndpoint,
  ApiOperation,
  ApiResponse,
  ApiSecurity,
  ApiTags,
  ApiBody,
  ApiProperty,
} from '@nestjs/swagger';
import { FeatureFlags } from './adminconfig.schema';

class AppConfigurationResponse {
  @ApiProperty({
    description: 'Feature flags configuration',
    example: { subscriptions: true, deliveries: false },
  })
  featureFlags: FeatureFlags;

  @ApiProperty({
    description: 'Minimum required app version',
    example: '1.2.3',
  })
  minimumRequiredAppVersion: string;
}

class UpdateFeatureFlagsDto {
  @ApiProperty({
    description: 'Enable/disable subscriptions feature',
    example: true,
  })
  subscriptions: boolean;

  @ApiProperty({
    description: 'Enable/disable deliveries feature',
    example: false,
  })
  deliveries: boolean;
}

class UpdateVersionDto {
  @ApiProperty({
    description: 'New minimum required app version',
    example: '1.2.3',
  })
  version: string;
}

@ApiTags('Admin Configuration')
@Controller('/admin')
export class AdminConfigController {
  constructor(private adminConfigService: AdminConfigService) {}

  @Get('/configs')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async getConfigs() {
    return this.adminConfigService.getConfigs();
  }

  @Post('/configs')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async addConfig(@Body() req: AddAdminConfigDto) {
    return this.adminConfigService.addConfg(req.name, req.value);
  }

  @Get('/app-configuration')
  @ApiOperation({
    summary: 'Get app configuration',
    description: 'Returns the current app configuration including feature flags and minimum required version',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns app configuration',
    type: AppConfigurationResponse,
  })
  async getAppConfiguration() {
    return this.adminConfigService.getAppConfiguration();
  }

  @Post('/feature-flags')
  @ApiSecurity('bearer')
  @ApiOperation({
    summary: 'Update feature flags',
    description: 'Updates the feature flags configuration. Requires admin authentication.',
  })
  @ApiBody({ type: UpdateFeatureFlagsDto })
  @ApiResponse({
    status: 200,
    description: 'Feature flags updated successfully',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token',
  })
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async updateFeatureFlags(@Body() featureFlags: FeatureFlags) {
    return this.adminConfigService.updateFeatureFlags(featureFlags);
  }

  @Post('/minimum-version')
  @ApiSecurity('bearer')
  @ApiOperation({
    summary: 'Update minimum required app version',
    description: 'Updates the minimum required app version. Requires admin authentication.',
  })
  @ApiBody({ type: UpdateVersionDto })
  @ApiResponse({
    status: 200,
    description: 'Minimum version updated successfully',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token',
  })
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async updateMinimumVersion(@Body('version') version: string) {
    return this.adminConfigService.updateMinimumAppVersion(version);
  }
}
