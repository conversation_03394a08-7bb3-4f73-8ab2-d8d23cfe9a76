import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { Link, LinkDocument } from "./url-shortener.schema";

@Injectable()
export class UrlShortenerService {
    constructor(
        @InjectModel(Link.name)
        private readonly linkModel: Model<LinkDocument>
    ){}

    randFunc() {
        const randString = 'aA1bB2cC3dD4eE5fF6gG7hH8iI9jJ0kK1lL2mM3nN4oO5pP6qQ7rR8sS9tT0uU9vV8wW7xX6yY5zZ4';
        let r = '';
        let i = 0;
        while (i < 4) {
            r += randString[Math.round(Math.random() * (randString.length - 1))];
            i++;
        }
    
        return r;
    }

    async getLink(short_url: string): Promise<LinkDocument | null> {
        return await this.linkModel.findOne({short_url});
    }

    async generateLink(long_url: string): Promise<LinkDocument> {
        let itemLink = await this.linkModel.findOne({long_url});

        if (!itemLink) {
            let short = this.randFunc();
            let itemLinkCopy = await this.linkModel.findOne({short_url: short});

            while (itemLinkCopy) {
                short = this.randFunc();
                itemLinkCopy = await this.linkModel.findOne({short_url: short});
            }

            itemLink = new this.linkModel({
                short_url: short,
                long_url: long_url,
                views: 0
            });

            await itemLink.save();
        }

        return itemLink;
    }
}