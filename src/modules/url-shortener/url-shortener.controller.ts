import { Body, Controller, Get, NotFoundException, Param, Post, Redirect, Res, UseGuards } from '@nestjs/common';
import { InternalApiJWTGuard } from '../../guards/api.guard';
import { InternalApiKeyGuard } from '../../guards/internal-api-key.guard';
import { JwtAuthGuard } from '../../jwt/jwt-auth.guard';
import { GenerateLinkDto } from './dtos/make-link';
import { UrlShortenerService } from './url-shortener.service';
import { ApiExcludeEndpoint } from '@nestjs/swagger';

@Controller('url-shortener')
export class UrlShortenerController {
  constructor(private readonly urlShortenerService: UrlShortenerService) {}

  @Post('')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async generateLink(@Body() { url }: GenerateLinkDto) {
    const link = await this.urlShortenerService.generateLink(url);

    return {
      message: 'Link generated',
      data: link.toJSON(),
    };
  }

  @Get('/:id')
  @Redirect()
  async getLink(@Param('id') short: string) {
    const link = await this.urlShortenerService.getLink(short);

    if (link) {
      await link.updateOne({
        views: link.views + 1,
      });

      return {
        url: link.long_url,
        statusCode: 301,
      };
    } else {
      throw new NotFoundException();
    }
  }

  @Get('/:id/full')
  async getLinkData(@Param('id') short: string) {
    const link = await this.urlShortenerService.getLink(short);

    return {
      full_url: link.long_url,
    };
  }
}
