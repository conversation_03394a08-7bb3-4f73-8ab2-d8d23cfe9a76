import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type LinkDocument = Link & Document;

@Schema()
export class Link {
  _id: any;

  @ApiProperty()
  @Prop({ type: String, required: true})
  short_url: string;

  @ApiProperty()
  @Prop({ type: String, required: true})
  long_url: string;

  @ApiProperty()
  @Prop({ type: Number, required: true})
  views: string;
}

export const LinkSchema = SchemaFactory.createForClass(Link);
