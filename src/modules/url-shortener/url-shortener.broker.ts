import { Controller } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { UrlShortenerService } from './url-shortener.service';
import { SkipThrottle } from '@nestjs/throttler';

@SkipThrottle()
@Controller()
export class UrlShortenerBroker {
  constructor(private readonly shortenerService: UrlShortenerService) {}

  @MessagePattern(BROKER_PATTERNS.URL_SHORTENER.SHORTEN)
  async shorten(data: { url: string; referrer: string }) {
    return (
      process.env.CATLOG_URL_SHORTENER +
      '/' +
      (await this.shortenerService.generateLink(data.url + '?ref=' + data.referrer)).short_url
    );
  }
}
