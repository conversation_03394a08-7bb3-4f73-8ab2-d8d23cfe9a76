import { Modu<PERSON> } from '@nestjs/common';
import { SharedModule } from '../../shared.module';
import { MongooseModule } from '@nestjs/mongoose';
import { Link, LinkSchema } from './url-shortener.schema';
import { jsonHookMongoosePlugin, setIdMongoosePlugin } from '../../mongoose-plugins';
import { UrlShortenerService } from './url-shortener.service';
import { UrlShortenerController } from './url-shortener.controller';
import { UrlShortenerBroker } from './url-shortener.broker';

@Module({
  imports: [
    MongooseModule.forFeatureAsync([
      {
        name: Link.name,
        useFactory: () => {
          LinkSchema.plugin(setIdMongoosePlugin());
          LinkSchema.plugin(jsonHookMongoosePlugin(['_id', '__v']));
          return LinkSchema;
        },
      },
    ]),
    SharedModule,
  ],
  controllers: [UrlShortenerController, UrlShortenerBroker],
  providers: [UrlShortenerService],
})
export class UrlShortenerModule {}
