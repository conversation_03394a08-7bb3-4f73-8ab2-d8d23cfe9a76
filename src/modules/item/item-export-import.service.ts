import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { PaginateModel } from 'mongoose';
import { Item, ItemDocument } from './item.schema';
import { getDocId } from '../../utils/functions';
import * as XLSX from 'xlsx';
import { ResendRepository } from '../../repositories/resend.repository';
import { CustomerIoRepository } from '../../repositories/customer-io.repository';
import { BrokerTransportService } from '../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { Store } from '../store/store.schema';
import { User } from '../user/user.schema';
import { amountFormat } from '../../utils';
import xlsx from 'json-as-xlsx';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { JOBS, QUEUES } from '../../enums/queues.enum';

@Injectable()
export class ItemExportImportService {
  constructor(
    @InjectModel(Item.name)
    private readonly itemModel: PaginateModel<ItemDocument>,
    private readonly resend: ResendRepository,
    private readonly customerIo: CustomerIoRepository,
    private readonly brokerTransport: BrokerTransportService,
    private readonly logger: Logger,
    @InjectQueue(QUEUES.ITEM) private itemQueue: Queue,
  ) {
    this.logger.setContext('ItemExportImportService');
  }

  /**
   * Export items to Excel format
   * @param storeId The store ID
   * @param itemIds Array of item IDs or 'all' to export all items
   * @returns Buffer containing the Excel file
   */
  async exportItems(storeId: string, itemIds: string[] | 'all'): Promise<Buffer> {
    try {
      // Build the filter query
      const filterQuery: any = { store: getDocId(storeId), is_deleted: { $ne: true } };

      if (itemIds !== 'all') {
        filterQuery._id = { $in: itemIds.map((id) => getDocId(id)) };
      }

      // Fetch items with their variants
      const items = await this.itemModel.find(filterQuery).lean();

      if (!items.length) {
        throw new BadRequestException('No items found to export');
      }

      // Transform items into the export format
      const exportData = items.flatMap((item) => {
        // If item has no variants, create a single row
        if (!item.variants || !item.variants.options || item.variants.options.length === 0) {
          return [
            {
              id: getDocId(item).toString(),
              option_id: '',
              name: item.name,
              option_name: '',
              option_image: '',
              option_price: amountFormat(0),
              base_price: amountFormat(item.price),
              option_quantity: 0,
              total_quantity: item.quantity || 0,
              infinite_quantity: item.is_always_available ? 'Yes' : 'No',
            },
          ];
        }

        // If item has variants, create a row for each variant
        return item.variants.options.map((option) => {
          // Create option name from variant options
          let optionName = '';
          if (option.values) {
            optionName = Object.entries(option.values)
              .map(([key, value]) => `${key}: ${value}`)
              .join(', ');
          }

          return {
            id: getDocId(item).toString(),
            option_id: getDocId(option),
            name: item.name,
            option_name: optionName,
            option_image: option.image || '',
            option_price: amountFormat(option.price),
            base_price: amountFormat(item.price),
            option_quantity: option.quantity || 0,
            total_quantity: item.quantity || 0,
            infinite_quantity: item.is_always_available ? 'Yes' : 'No',
          };
        });
      });

      // Create Excel workbook
      const spreadSheetData = [
        {
          sheet: 'Items',
          columns: Object.keys(exportData[0]).map((field) => ({
            label: field.split('_').join(' ').toUpperCase(),
            value: field,
          })),
          content: exportData,
        },
      ];

      return xlsx(spreadSheetData as any, {
        extraLength: 3,
        writeOptions: {
          type: 'buffer',
          bookType: 'xlsx',
        },
      });
    } catch (error) {
      this.logger.error('Error exporting items:', error);
      throw new BadRequestException('Failed to export items');
    }
  }

  /**
   * Convert a string to lowercase snake case
   * @param str The string to convert
   * @returns The converted string
   */
  private toSnakeCase(str: string): string {
    return str
      .toLowerCase()
      .replace(/\s+/g, '_')
      .replace(/[^a-z0-9_]/g, '');
  }

  /**
   * Convert object keys to snake case
   * @param obj The object to convert
   * @returns A new object with snake case keys
   */
  private convertKeysToSnakeCase(obj: any): any {
    if (Array.isArray(obj)) {
      return obj.map((item) => this.convertKeysToSnakeCase(item));
    }

    if (obj !== null && typeof obj === 'object') {
      const result = {};
      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          const snakeKey = this.toSnakeCase(key);
          result[snakeKey] = this.convertKeysToSnakeCase(obj[key]);
        }
      }
      return result;
    }

    return obj;
  }

  /**
   * Validate the import file data
   * @param data The parsed Excel/CSV data
   * @returns Validation result with errors if any
   */
  private validateImportData(data: any[]): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!data.length) {
      errors.push('No data found in the uploaded file');
      return { isValid: false, errors };
    }

    // Check for required fields
    const requiredFields = ['id', 'base_price', 'total_quantity', 'infinite_quantity'];
    const missingFields = requiredFields.filter((field) => !(field in data[0]));

    if (missingFields.length > 0) {
      errors.push(`Missing required fields: ${missingFields.join(', ')}`);
      return { isValid: false, errors };
    }

    // Validate data types and formats
    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      const rowNum = i + 1;

      // Validate ID
      if (!row.id || typeof row.id !== 'string') {
        errors.push(`Row ${rowNum}: Invalid or missing ID`);
      }

      // Validate price
      if (isNaN(parseFloat(row.base_price))) {
        errors.push(`Row ${rowNum}: Invalid price value`);
      }

      // Validate quantity
      if (isNaN(parseInt(row.total_quantity, 10))) {
        errors.push(`Row ${rowNum}: Invalid quantity value`);
      }

      // Validate infinite_quantity
      if (!['yes', 'no'].includes(row.infinite_quantity.toLowerCase())) {
        errors.push(`Row ${rowNum}: infinite_quantity must be 'Yes' or 'No'`);
      }

      // If option_id is provided, validate it
      if (row.option_id && typeof row.option_id !== 'string') {
        errors.push(`Row ${rowNum}: Invalid option_id`);
      }

      // If option_price is provided, validate it
      if (row.option_price && isNaN(parseFloat(row.option_price))) {
        errors.push(`Row ${rowNum}: Invalid option_price value`);
      }
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * Pre-process the import file and add to queue
   * @param storeId The store ID
   * @param file The uploaded file buffer
   * @param user The user performing the import
   * @returns Pre-processing results
   */
  async preProcessImport(storeId: string, file: Buffer, user: User): Promise<any> {
    try {
      // Parse the Excel/CSV file
      const workbook = XLSX.read(file, { type: 'buffer' });
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const rawData = XLSX.utils.sheet_to_json(worksheet);

      // Convert keys to snake case
      const data = this.convertKeysToSnakeCase(rawData);

      // Validate the data
      const validationResult = this.validateImportData(data);
      if (!validationResult.isValid) {
        return {
          success: false,
          errors: validationResult.errors,
        };
      }

      // Get store information
      const store = await this.brokerTransport
        .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: getDocId(storeId) })
        .toPromise();

      if (!store) {
        throw new BadRequestException('Store not found');
      }

      // Add to queue for processing
      await this.itemQueue.add(
        QUEUES.ITEM,
        {
          type: JOBS.IMPORT_ITEMS,
          data: {
            storeId,
            data,
            userId: user.id,
            storeName: store.name,
          },
        },
        {
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 5000,
          },
        },
      );

      return {
        success: true,
        message: 'Import job added to queue successfully',
      };
    } catch (error) {
      this.logger.error('Error pre-processing import:', error);
      throw new BadRequestException('Failed to pre-process import');
    }
  }

  /**
   * Process the import job from the queue
   * @param jobData The job data from the queue
   * @returns Import results
   */
  async processImportJob(jobData: any): Promise<any> {
    const { storeId, data, userId, storeName } = jobData;

    try {
      // Get user information
      const user = await this.brokerTransport
        .send<User>(BROKER_PATTERNS.USER.GET_USER, { _id: getDocId(userId) })
        .toPromise();

      if (!user) {
        throw new BadRequestException('User not found');
      }

      // Get store information
      const store = await this.brokerTransport
        .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: getDocId(storeId) })
        .toPromise();

      if (!store) {
        throw new BadRequestException('Store not found');
      }

      // Group data by item ID to process all variants together
      const itemGroups = new Map<string, any[]>();

      for (const row of data) {
        const itemId = row['id'] as string;
        if (!itemGroups.has(itemId)) {
          itemGroups.set(itemId, []);
        }
        itemGroups.get(itemId).push(row);
      }

      // Process each item group
      const results = {
        successful: 0,
        failed: 0,
        errors: [] as string[],
        failedItems: [] as Array<{ name: string; option_name?: string; error?: string }>,
      };

      for (const [itemId, rows] of itemGroups.entries()) {
        try {
          // Validate item exists
          const item = await this.itemModel.findOne({ _id: getDocId(itemId), store: getDocId(storeId) as any });
          if (!item) {
            results.failed++;
            const errorMessage = `Item with ID ${itemId} not found`;
            results.errors.push(errorMessage);

            // Add to failed items with name from the import data
            const itemName = rows[0]?.name || 'Unknown Item';
            results.failedItems.push({
              name: itemName,
              error: errorMessage,
            });

            continue;
          }

          let totalQuantity = 0;
          let isAlwaysAvailable = false;
          let price = 0;
          let hasOptions = false;

          // Process each row for this item
          for (const row of rows) {
            const optionId = row['option_id'] as string;
            price = parseFloat(row['base_price'] as string);
            const optionPrice = row['option_price'] ? parseFloat(row['option_price'] as string) : 0;
            const quantity = parseInt(row['total_quantity'] as string, 10);
            const optionQuantity = row['option_quantity'] ? parseInt(row['option_quantity'] as string, 10) : 0;
            isAlwaysAvailable = row['infinite_quantity'].toLowerCase() === 'yes';

            if (optionId) {
              hasOptions = true;
              // Update variant
              if (!item.variants || !item.variants.options) {
                results.failed++;
                const errorMessage = `Item with ID ${itemId} does not have variants`;
                results.errors.push(errorMessage);

                // Add to failed items
                results.failedItems.push({
                  name: item.name,
                  error: errorMessage,
                });

                continue;
              }

              const optionIndex = item.variants.options.findIndex((opt) => getDocId(opt).toString() === optionId);

              if (optionIndex === -1) {
                results.failed++;
                const errorMessage = `Option with ID ${optionId} not found for item ${itemId}`;
                results.errors.push(errorMessage);

                // Add to failed items with option name from the import data
                const optionName = row['option_name'] || 'Unknown Option';
                results.failedItems.push({
                  name: item.name,
                  option_name: optionName,
                  error: errorMessage,
                });

                continue;
              }

              // Update the variant with the new price and quantity
              item.variants.options[optionIndex].price = optionPrice;
              item.variants.options[optionIndex].quantity = optionQuantity;

              // Add to total quantity
              totalQuantity += optionQuantity;
            } else {
              // For items without variants, use the quantity directly
              totalQuantity = quantity;
            }
          }

          await this.itemModel.updateOne(
            { _id: getDocId(itemId) },
            { quantity: totalQuantity, is_always_available: isAlwaysAvailable, price, variants: item.variants },
          );
          results.successful++;
        } catch (error) {
          results.failed++;
          const errorMessage = `${error.message}`;
          results.errors.push(errorMessage);

          // Add to failed items
          const itemName = rows[0]?.name || 'Unknown Item';
          results.failedItems.push({
            name: itemName,
            error: errorMessage,
          });
        }
      }

      // Send email notification
      await this.sendImportUpdateEmail(user, store, results);

      return results;
    } catch (error) {
      this.logger.error('Error processing import job:', error);
      throw new BadRequestException('Failed to process import job');
    }
  }

  /**
   * Send email notification for item import update
   */
  private async sendImportUpdateEmail(user: User, store: Store, results: any): Promise<void> {
    try {
      const emailData = {
        name: user.name,
        products_count: results.successful.toString(),
        failed_count: results.failed.toString(),
        cta_link: `${process.env.CATLOG_APP}/products`,
        source: 'Excel/CSV Import',
        failed_items: results.failedItems,
      };

      await this.resend.sendEmail(BROKER_PATTERNS.MAIL.PRODUCT_IMPORT_UPDATE_SUCCESSFUL, {
        to: user.email,
        subject: 'Items Updates Imported Successfully 🎉',
        data: emailData,
      });

      // Update user in Customer.io
      if (user.primary_store && getDocId(store).toString() === getDocId(user.primary_store).toString()) {
        await this.customerIo.createOrUpdateUser({
          id: getDocId(user),
          email: user.email,
          products_count: results.successful,
        });
      }
    } catch (error) {
      this.logger.error('Error sending import update email:', error);
    }
  }
}
