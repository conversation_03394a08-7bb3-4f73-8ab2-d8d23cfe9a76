import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import type { Document, Types } from 'mongoose';
import { Item, ItemDocument } from './item.schema';
import { OpenaiRepository } from '../../repositories/openai.respository';

// Include _id in the lean document type since it's always present
type LeanDoc<T> = Omit<T, keyof Document> & { _id: Types.ObjectId };

@Injectable()
export class ItemRecommendationsService {
  private readonly logger = new Logger(ItemRecommendationsService.name);

  constructor(
    @InjectModel(Item.name) private itemModel: Model<ItemDocument>,
    private openaiRepository: OpenaiRepository,
  ) {}

  private calculateCosineSimilarity(embedding1: number[], embedding2: number[]): number {
    if (!embedding1 || !embedding2 || embedding1.length !== embedding2.length) {
      this.logger.warn('Invalid embeddings for similarity calculation');
      return 0;
    }
    const dotProduct = embedding1.reduce((acc, val, i) => acc + val * embedding2[i], 0);
    const norm1 = Math.sqrt(embedding1.reduce((acc, val) => acc + val * val, 0));
    const norm2 = Math.sqrt(embedding2.reduce((acc, val) => acc + val * val, 0));
    if (norm1 === 0 || norm2 === 0) {
      return 0;
    }
    return dotProduct / (norm1 * norm2);
  }

  private async generateEmbeddingForItem(itemId: string): Promise<boolean> {
    try {
      const item = await this.itemModel.findById(itemId);
      if (!item) return false;

      const embedding = await this.openaiRepository.generateTextEmbedding(item);
      if (embedding) {
        await this.itemModel.findByIdAndUpdate(itemId, { embedding });
        return true;
      }
      return false;
    } catch (error) {
      this.logger.error(`Failed to generate embedding for item ${itemId}: ${error.message}`);
      return false;
    }
  }

  private async ensureEmbeddings(items: (ItemDocument | LeanDoc<ItemDocument>)[]): Promise<void> {
    const itemsNeedingEmbeddings = items.filter((item) => !item.embedding);

    if (itemsNeedingEmbeddings.length > 0) {
      this.logger.log(`Generating embeddings for ${itemsNeedingEmbeddings.length} items`);

      const generatePromises = itemsNeedingEmbeddings.map((item) => this.generateEmbeddingForItem(item._id.toString()));
      await Promise.all(generatePromises);
    }
  }

  async findRelatedProducts(itemId: string, limit: number = 5): Promise<ItemDocument[]> {
    try {
      const sourceItem = await this.itemModel.findById(itemId).select('+embedding');
      if (!sourceItem) {
        this.logger.warn(`Source item ${itemId} not found.`);
        return [];
      }

      // If the source item doesn't have an embedding, generate it
      if (!sourceItem.embedding) {
        const embeddingSuccess = await this.generateEmbeddingForItem(itemId);
        if (!embeddingSuccess) {
          this.logger.warn(`Could not generate embedding for source item ${itemId}`);
          return [];
        }
      }

      // Get the source item with embedding
      const sourceItemWithEmbedding = await this.itemModel.findById(itemId).select('+embedding');
      if (!sourceItemWithEmbedding?.embedding) {
        this.logger.warn(`Source item embedding not found for ${itemId}`);
        return [];
      }

      // Use MongoDB's $vectorSearch aggregation to find similar items
      const similarItems = await this.itemModel
        .aggregate([
          {
            $vectorSearch: {
              index: 'embedding',
              path: 'embedding',
              queryVector: sourceItemWithEmbedding.embedding,
              numCandidates: 100,
              limit: limit + 1, // +1 because we'll need to filter out the source item itself
            },
          },
          {
            $match: {
              store: sourceItemWithEmbedding.store,
              _id: { $ne: sourceItemWithEmbedding._id },
              available: true,
              is_deleted: { $ne: true },
            },
          },
          {
            $limit: limit,
          },
        ])
        .exec();

      return similarItems;
    } catch (error) {
      // Check if the error is related to missing vector search capability
      if (
        error.message &&
        (error.message.includes('$vectorSearch') ||
          error.message.includes('index not found') ||
          error.message.includes('embedding_index'))
      ) {
        // Fall back to the original method if vector search is not available
        this.logger.warn(`Vector search not available, falling back to manual calculation: ${error.message}`);
        return this.findRelatedProductsManually(itemId, limit);
      }

      this.logger.error(`Error finding related products: ${error.message}`);
      return [];
    }
  }

  // Keep the original implementation as a fallback method
  private async findRelatedProductsManually(itemId: string, limit: number = 4): Promise<ItemDocument[]> {
    const sourceItem = await this.itemModel.findById(itemId).select('+embedding');
    if (!sourceItem) {
      this.logger.warn(`Source item ${itemId} not found.`);
      return [];
    }

    // Get all potential related items from the same store
    const storeItems = (await this.itemModel
      .find({
        store: sourceItem.store,
        _id: { $ne: sourceItem._id },
        available: true,
        is_deleted: { $ne: true },
      })
      .select('+embedding')) as ItemDocument[];

    // Check and generate embeddings if needed
    await this.ensureEmbeddings([sourceItem, ...storeItems]);

    // Refresh source item to get the embedding if it was just generated
    const refreshedSourceItem = (await this.itemModel
      .findById(itemId)
      .select('+embedding')
      .lean()) as LeanDoc<ItemDocument>;
    if (!refreshedSourceItem?.embedding) {
      this.logger.warn(`Could not generate embedding for source item ${itemId}`);
      return [];
    }

    const sourceEmbedding = refreshedSourceItem.embedding;

    const itemsWithSimilarity = storeItems
      .map((item) => {
        if (!item.embedding) return null;
        const similarity = this.calculateCosineSimilarity(sourceEmbedding, item.embedding);
        return { item, similarity };
      })
      .filter(Boolean);

    return itemsWithSimilarity
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, limit)
      .map(({ item }) => item);
  }
}
