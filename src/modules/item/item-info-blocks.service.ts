import { forwardRef, Inject, Injectable, NotFoundException } from '@nestjs/common';
import { StoreContentService } from '../store/services/store-content.service';
import { ItemService } from './item.service';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Item, ItemDocument } from './item.schema';

@Injectable()
export class ItemInfoBlocksService {
  constructor(
    @Inject(forwardRef(() => StoreContentService))
    private readonly storeContentService: StoreContentService,
    @Inject(forwardRef(() => ItemService))
    private readonly itemService: ItemService,
    @InjectModel(Item.name)
    private readonly itemModel: Model<ItemDocument>,
  ) {}

  /**
   * Get all info blocks for a specific item
   */
  async getInfoBlocksForItem(itemId: string) {
    // Verify item exists
    const item = await this.itemService.findById(itemId);
    if (!item) {
      throw new NotFoundException('Item not found');
    }

    // If the item has no info blocks, return empty array
    if (!item.info_blocks || item.info_blocks.length === 0) {
      return [];
    }

    // Get each info block by ID
    const infoBlocks = [];
    for (const blockId of item.info_blocks) {
      try {
        const infoBlock = await this.storeContentService.getInfoBlock(blockId.toString());
        infoBlocks.push(infoBlock);
      } catch (error) {
        // Skip if info block not found
      }
    }

    return infoBlocks;
  }

  /**
   * Get all info blocks for an item with a specific tag
   */
  async getInfoBlocksForItemByTag(itemId: string, tagKey: string, tagValue?: any) {
    const infoBlocks = await this.getInfoBlocksForItem(itemId);

    if (tagValue !== undefined) {
      return infoBlocks.filter((block) => block.tag && block.tag[tagKey] === tagValue);
    } else {
      return infoBlocks.filter((block) => block.tag && block.tag[tagKey] !== undefined);
    }
  }

  async addInfoBlocksToItems(infoBlockId: string, itemIds: string[]) {
    const items = await this.itemModel.updateMany(
      { _id: { $in: itemIds } },
      { $addToSet: { info_blocks: infoBlockId } },
    );

    return items;
  }

  async removeInfoBlocksFromItems(infoBlockId: string, itemIds: string[]) {
    const items = await this.itemModel.updateMany({ _id: { $in: itemIds } }, { $pull: { info_blocks: infoBlockId } });

    return items;
  }

  /**
   * Assign an info block to an item
   */
  async assignInfoBlockToItem(infoBlockId: string, itemId: string) {
    // Verify item exists
    const item = await this.itemService.findById(itemId);
    if (!item) {
      throw new NotFoundException('Item not found');
    }

    // Verify info block exists and belongs to the same store as the item
    const infoBlock = await this.storeContentService.getInfoBlock(infoBlockId);
    if (!infoBlock) {
      throw new NotFoundException('Info block not found');
    }

    if (infoBlock.store.toString() !== item.store.toString()) {
      throw new NotFoundException("Info block does not belong to the item's store");
    }

    // Add info block ID to item if not already present
    if (!item.info_blocks) {
      item.info_blocks = [];
    }

    if (!(item.info_blocks as string[]).includes(infoBlockId)) {
      // Use updateOne to avoid race conditions
      await this.itemModel.updateOne({ _id: itemId }, { $addToSet: { info_blocks: infoBlockId } });

      // Update the info block's items array using StoreContentService
      await this.storeContentService.addItemToInfoBlock(infoBlockId, itemId);
    }

    return this.itemService.findById(itemId);
  }

  /**
   * Remove an info block from an item
   */
  async removeInfoBlockFromItem(infoBlockId: string, itemId: string) {
    // Verify item exists
    const item = await this.itemService.findById(itemId);
    if (!item) {
      throw new NotFoundException('Item not found');
    }

    // Use updateOne to avoid race conditions
    await this.itemModel.updateOne({ _id: itemId }, { $pull: { info_blocks: infoBlockId } });

    // Update the info block's items array using StoreContentService
    await this.storeContentService.removeItemFromInfoBlock(infoBlockId, itemId);

    return this.itemService.findById(itemId);
  }
}
