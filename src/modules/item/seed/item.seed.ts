import { ISeed } from '../../../interfaces/seed/seed.interface';
import { Connection } from 'mongoose';
import { itemMock } from '../../../tools/testdata';
import { Seed } from '../../../base-seed/seed';

export class ItemSeed extends Seed implements ISeed {
  constructor(db: Connection) {
    super(db);
    this.collection = db.collection('items');
  }

  async runDevSeed() {
    console.log('[HERE]', await this.collection.findOne({ _id: itemMock._id }));
    if (!(await this.collection.findOne({ _id: itemMock._id }))) {
      return this.collection.insertOne(itemMock);
    }
  }

  runProdSeed(): Promise<any> {
    return Promise.resolve(undefined);
  }
}
