import mongoose, { Fi<PERSON>Q<PERSON>y, Model, PaginateModel } from 'mongoose';

import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Item, ItemDocument } from '../item.schema';
import { CreateDiscountDto, DeleteDiscountDto, UpdateDiscountDto } from '../../../models/dtos/ItemDtos';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { PaginatedQueryDto } from '../../../models/dtos/PaginatedDto';
import { Discount, DiscountDocument } from './discounts-coupons.schema';
import { convertToObjectIds } from '../../../utils';
import { IRequest } from '../../../interfaces/request.interface';
import computeItemsDiscount from '../utils';

@Injectable()
export class DiscountService {
  constructor(
    private readonly brokerTransport: BrokerTransportService,
    @InjectModel(Item.name)
    private readonly itemModel: PaginateModel<ItemDocument>,
    @InjectModel(Discount.name)
    private readonly discountModel: PaginateModel<DiscountDocument>,
    private readonly logger: Logger,
  ) {
    this.logger.setContext('item.service.ts');
  }

  async createDiscount(req: IRequest, data: CreateDiscountDto) {
    const user = req.user;
    const { items } = data;
    if (items && items.length > 0) {
      const discount = await this.discountModel.create({
        ...data,
        store: user.store.id,
      });
      const itemData = { discount: discount.id };
      const ids = convertToObjectIds(items as string[]);

      await this.itemModel.updateMany({ _id: { $in: [...ids] } }, itemData);
      return discount;
    }
    throw new BadRequestException('Cannot create discount without item data');
  }

  async getDiscounts(
    store: string,
    filterQuery?: FilterQuery<DiscountDocument> & { search: string },
    paginationQuery?: PaginatedQueryDto,
  ) {
    let { search, ...filter } = { ...filterQuery };

    filter = {
      ...filter,
      is_deleted: {
        $ne: true,
      },
      store,
    };

    if (search) {
      filter.$or = [{ label: new RegExp(search, 'ig') }];
    }

    if (paginationQuery) {
      const result = await this.discountModel.paginate(filter, {
        page: paginationQuery.page || 1,
        limit: paginationQuery.per_page || 50,
        lean: true,
        sort: { createdAt: paginationQuery?.sort === 'ASC' ? 1 : -1 },
      });

      return {
        items: result.docs,
        page: result.page,
        next_page: result.nextPage,
        prev_page: result.prevPage,
        total: result.totalDocs,
        total_pages: result.totalPages,
        per_page: result.limit,
      };
    }

    return { items: this.discountModel.find({ store }) };
  }

  async getDiscountItems(id: string, store: string) {
    const discount = await this.discountModel.findOne({ _id: id, store });

    if (!discount) {
      throw new NotFoundException('Discount does not exist');
    }

    const items = await this.itemModel
      .find({ _id: { $in: discount.items } }, { images: 1, name: 1, price: 1, thumbnail: 1 })
      .lean();

    return items;
  }

  async updateDiscount(data: UpdateDiscountDto, store: string) {
    const { id: _id, items } = data;
    delete data.id;

    const prevDiscount = await this.discountModel.findOne({ _id, store });
    if (!prevDiscount) throw new BadRequestException('Discount does not exitst');

    if (items && items.length > 0) {
      const updatedDiscount = await this.discountModel.findOneAndUpdate({ _id }, { ...data }, { new: true });
      const prevItemsIds = convertToObjectIds(prevDiscount.items as string[]);
      const currItemsIds = convertToObjectIds(updatedDiscount.items as string[]);
      const prevItemsUpdate = { discount: undefined, discount_price: null };
      const currItemsUpdate = {
        discount: updatedDiscount.id,
        discount_price: null,
      };

      await this.itemModel.updateMany({ _id: { $in: [...prevItemsIds] } }, prevItemsUpdate);
      await this.itemModel.updateMany({ _id: { $in: [...currItemsIds] } }, currItemsUpdate);

      return updatedDiscount;
    }
    return await this.discountModel.findOneAndUpdate({ _id }, { ...data }, { new: true });
  }

  async deleteDiscount(data: DeleteDiscountDto) {
    await this.itemModel.updateMany({ discount: data.id }, { discount: undefined });
    return this.discountModel.findOneAndDelete({
      _id: data.id,
    });
  }
}
