import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import mongoose from 'mongoose';
import { Store } from '../../store/store.schema';
import { Item } from '../item.schema';
import { Document } from 'mongoose';
import { Order } from '../../orders/order.schema';
import { Customer } from '../../orders/customers/customer.schema';

export type DiscountDocument = Discount & Document;
export type CouponDocument = Coupon & Document;

@Schema({ timestamps: true })
export class Discount {
  _id: string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  label: string;

  @ApiProperty()
  @Prop({ type: Date, required: true })
  start_date: string | Date;

  @ApiProperty()
  @Prop({ type: Date, required: false })
  end_date?: string | Date;

  @ApiProperty()
  @Prop({ type: Boolean, required: true, default: true })
  active: boolean;

  @ApiProperty()
  @Prop({ type: Number, required: false })
  discount_cap?: number;

  @ApiProperty()
  @Prop({ type: Number, required: true })
  percentage: number;

  @ApiProperty()
  @Prop({ type: [mongoose.Schema.Types.ObjectId], required: true })
  items: string[] | Item[];

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, required: true })
  store: string | Store;
}

@Schema({ timestamps: true })
export class Coupon {
  _id: string;

  @ApiProperty({ enum: ['percentage', 'fixed'] })
  @Prop({ type: String, required: true, enum: ['percentage', 'fixed'] })
  type: 'percentage' | 'fixed';

  @ApiProperty()
  @Prop({ type: String, required: true })
  coupon_code: string;

  @ApiProperty()
  @Prop({ type: Date, required: true })
  end_date: string | Date;

  @ApiProperty()
  @Prop({ type: Boolean, required: true, default: true })
  active: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, required: false, default: false })
  sponsored_by_catlog?: boolean;

  @ApiProperty()
  @Prop({ type: Number, required: false })
  discount_cap?: number;

  @ApiProperty()
  @Prop({ type: Number, required: false })
  discount_amount?: number;

  @ApiProperty()
  @Prop({ type: Number, required: true })
  quantity: number;

  @ApiProperty()
  @Prop({ type: Number, required: false })
  percentage?: number;

  @ApiProperty()
  @Prop({ type: Number, required: false })
  minimum_order_amount?: number;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, required: true })
  store: string | Store;

  @ApiProperty()
  @Prop({ type: [String], required: false, ref: 'Order' })
  orders?: Order[] | string[];

  @ApiProperty()
  @Prop({ type: [String], required: false, ref: 'Customer' })
  used_by?: Customer[] | string[];
}

export const DiscountSchema = SchemaFactory.createForClass(Discount);
export const CouponSchema = SchemaFactory.createForClass(Coupon);
