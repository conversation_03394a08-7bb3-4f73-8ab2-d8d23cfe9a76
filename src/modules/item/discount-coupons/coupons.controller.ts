import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBody,
  ApiCreatedResponse,
  ApiExcludeEndpoint,
  ApiExtraModels,
  ApiResponse,
  ApiSecurity,
  ApiTags,
} from '@nestjs/swagger';
import { PlanPermissions, RolePermissions } from '../../../decorators/permission.decorator';
import { PlanGuard } from '../../../guards/plan.guard';
import { SCOPES } from '../../../utils/permissions.util';
import { IRequest } from '../../../interfaces/request.interface';
import { JwtAuthGuard } from '../../../jwt/jwt-auth.guard';
import {
  CreateCatlogSponsoredCouponsDto,
  CreateCouponDto,
  CreateDiscountResponse,
  DiscountResponse,
  UpdateCouponDto,
  UpdateDiscountResponse,
  VerifyCouponDto,
} from '../../../models/dtos/ItemDtos';
import { Coupon } from './discounts-coupons.schema';
import { PaginatedQueryDto } from '../custom-items/dtos/get-custom-item';
import { CouponService } from './coupons.service';
import { InternalApiJWTGuard } from '../../../guards/api.guard';

@Controller('coupons')
@ApiTags('Coupons')
@ApiExtraModels(Coupon)
export class CouponsController {
  constructor(private readonly couponService: CouponService, private readonly logger: Logger) {}

  @Post('')
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_COUPONS)
  @UseGuards(JwtAuthGuard, PlanGuard)
  @ApiCreatedResponse({
    type: CreateDiscountResponse,
  })
  @ApiBody({ type: CreateCouponDto })
  @ApiSecurity('bearer')
  async createCoupon(@Req() req: IRequest, @Body() couponReq: CreateCouponDto) {
    try {
      const data = await this.couponService.createCoupon(req.user?.store?.id, couponReq);
      return {
        message: 'Coupon created successfully',
        data: data,
      };
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException(err.message);
    }
  }

  @Get('')
  @UseGuards(JwtAuthGuard)
  @ApiCreatedResponse({
    type: DiscountResponse,
  })
  @ApiSecurity('bearer')
  async getCoupons(@Req() req: IRequest, @Query('filter') filter: any, @Query() query: PaginatedQueryDto) {
    try {
      // filter = filter ?? { search: '' };
      const data = await this.couponService.getCoupons(req.user, filter, query);
      return {
        message: 'Coupons retrieved successfully',
        data: data,
      };
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException(err.message);
    }
  }

  @Post('verify')
  async verifyCoupon(@Body() body: VerifyCouponDto) {
    const data = await this.couponService.verifyCoupon(body);
    return {
      message: 'Coupon was successfully verified',
      data,
    };
  }

  @Get(':code')
  @UseGuards(JwtAuthGuard)
  @ApiCreatedResponse({
    type: DiscountResponse,
  })
  @ApiSecurity('bearer')
  async getCouponByCode(@Param('code') code: string, @Req() req: IRequest) {
    try {
      const data = await this.couponService.getCouponByCode(req.user, code);
      return {
        message: 'Coupon was successfully retrieved',
        data,
      };
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException(err.message);
    }
  }

  @Put('')
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_COUPONS)
  @UseGuards(JwtAuthGuard, PlanGuard)
  @ApiCreatedResponse({
    type: UpdateDiscountResponse,
  })
  @ApiBody({ type: UpdateCouponDto })
  @ApiSecurity('bearer')
  async updateCoupon(@Req() req: IRequest, @Body() discountReq: UpdateCouponDto) {
    try {
      const data = await this.couponService.updateCoupon(req.user, discountReq);
      return {
        message: 'Coupon updated successfully',
        data: data,
      };
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException(err.message);
    }
  }

  @Delete(':id')
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_COUPONS)
  @UseGuards(JwtAuthGuard, PlanGuard)
  @ApiSecurity('bearer')
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteCoupon(@Param('id') id: string) {
    try {
      const data = await this.couponService.deleteCoupon({ id });
      return {
        message: 'Coupon deleted successfully',
        data: data,
      };
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException(err.message);
    }
  }

  @Post('/admin/create-catlog-coupons')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async createCatlogSponsoredCoupons(@Body() body: CreateCatlogSponsoredCouponsDto) {
    const data = await this.couponService.createCatlogSponsoredCoupons(body);

    return {
      message: 'Created Coupons',
      data,
    };
  }
}
