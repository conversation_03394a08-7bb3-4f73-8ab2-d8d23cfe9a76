import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsMongoId,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
  ArrayMinSize,
} from 'class-validator';

export class HighlightVideoDto {
  @ApiProperty()
  @IsString({ message: 'Please ensure video uploaded successfully before saving' })
  @IsNotEmpty({ message: 'Video URL is required' })
  url: string;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  active?: boolean;

  @ApiProperty()
  @IsString()
  @IsNotEmpty({ message: 'Thumbnail is required for every video' })
  thumbnail: string;

  @ApiProperty()
  @IsArray()
  @ArrayMinSize(1, { message: 'Please ensure every video has at least one linked product' })
  @IsMongoId({ each: true, message: 'Some selected products are invalid' })
  products: string[];
}

export class CreateHighlightDto {
  @ApiProperty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => HighlightVideoDto)
  videos: HighlightVideoDto[];

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  title: string;
}

export class UpdateHighlightDto {
  @ApiProperty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => HighlightVideoDto)
  @IsOptional()
  videos?: HighlightVideoDto[];

  @ApiProperty()
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  active?: boolean;
}

export class HighlightResponse {
  @ApiProperty()
  message: string;

  @ApiProperty()
  data: any;
}

export class CreateHighlightResponse {
  @ApiProperty()
  message: string;

  @ApiProperty()
  data: any;
}

export class UpdateHighlightResponse {
  @ApiProperty()
  message: string;

  @ApiProperty()
  data: any;
}
