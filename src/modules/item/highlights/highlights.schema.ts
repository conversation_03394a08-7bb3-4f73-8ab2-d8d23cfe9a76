import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import mongoose from 'mongoose';
import { Document } from 'mongoose';
import { Item } from '../item.schema';
import { Store } from '../../store/store.schema';

export type HighlightDocument = Highlight & Document;

export interface HighlightVideo {
  url: string;
  active: boolean;
  thumbnail: string;
  products: string[] | Item[];
}

@Schema({ timestamps: true })
export class Highlight {
  _id: string;

  id?: string;

  @ApiProperty()
  @Prop({
    type: [
      {
        url: { type: String, required: true },
        active: { type: Boolean, default: false },
        thumbnail: { type: String, required: true },
        products: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Item' }],
      },
    ],
    default: [],
  })
  videos: HighlightVideo[];

  @ApiProperty()
  @Prop({ type: String, required: true })
  title: string;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  active: boolean;

  @ApiProperty()
  @Prop({ type: String, required: true })
  slug: string;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Store', required: true })
  store: Store | string;
}

export const HighlightSchema = SchemaFactory.createForClass(Highlight);
