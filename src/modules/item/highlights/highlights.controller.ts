import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBody, ApiCreatedResponse, ApiExtraModels, ApiResponse, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { IRequest } from '../../../interfaces/request.interface';
import { JwtAuthGuard } from '../../../jwt/jwt-auth.guard';
import { PaginatedQueryDto } from '../../../models/dtos/PaginatedDto';
import { HighlightsService } from './highlights.service';
import { Highlight } from './highlights.schema';
import {
  CreateHighlightDto,
  CreateHighlightResponse,
  HighlightResponse,
  UpdateHighlightDto,
  UpdateHighlightResponse,
} from './dtos/highlight.dto';
import { checkIfUserOwnsStore } from '../../../utils';
import { Store } from '../../store/store.schema';
import { PlanGuard } from '../../../guards/plan.guard';
import { PlanPermissions } from '../../../decorators/permission.decorator';
import { SCOPES } from '../../../utils/permissions.util';

@Controller('item-highlights')
@ApiTags('Item Highlights')
@ApiExtraModels(Highlight)
export class HighlightsController {
  constructor(private readonly highlightsService: HighlightsService, private readonly logger: Logger) {}

  @Post('')
  @UseGuards(JwtAuthGuard, PlanGuard)
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_STORE_HIGHLIGHTS)
  @ApiCreatedResponse({
    type: CreateHighlightResponse,
  })
  @ApiBody({ type: CreateHighlightDto })
  @ApiSecurity('bearer')
  async createHighlight(@Req() req: IRequest, @Body() highlightReq: CreateHighlightDto) {
    try {
      const data = await this.highlightsService.createHighlight(highlightReq, req.user.store.id);
      return {
        message: 'Highlight created successfully',
        data: data,
      };
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException(err.message);
    }
  }

  @Get('')
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_STORE_HIGHLIGHTS)
  @UseGuards(JwtAuthGuard, PlanGuard)
  @ApiCreatedResponse({
    type: HighlightResponse,
  })
  @ApiSecurity('bearer')
  async getHighlights(@Req() req: IRequest, @Query('filter') filter: any, @Query() query: PaginatedQueryDto) {
    try {
      const data = await this.highlightsService.getHighlights(req.user?.store.id, filter, query, false);
      return {
        message: 'Highlights retrieved successfully',
        data: data,
      };
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException(err.message);
    }
  }

  @Get('public')
  @ApiCreatedResponse({
    type: HighlightResponse,
  })
  async getHighlightsPublic(@Query('filter') filter: any, @Query('store') store: string) {
    try {
      const data = await this.highlightsService.getHighlights(store, filter, null, true);
      return {
        message: 'Highlights retrieved successfully',
        data: data,
      };
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException(err.message);
    }
  }

  @Get(':id')
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_STORE_HIGHLIGHTS)
  @UseGuards(JwtAuthGuard, PlanGuard)
  @ApiCreatedResponse({
    type: HighlightResponse,
  })
  @ApiSecurity('bearer')
  async getHighlightById(@Req() req: IRequest, @Param('id') id: string) {
    try {
      const data = await this.highlightsService.getHighlightById(id, req.user?.store.id);
      return {
        message: 'Highlight retrieved successfully',
        data,
      };
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException(err.message);
    }
  }

  @Get('slug/:slug')
  @ApiCreatedResponse({
    type: HighlightResponse,
  })
  async getHighlightBySlug(@Param('slug') slug: string) {
    try {
      const data = await this.highlightsService.getHighlightBySlug(slug);
      return {
        message: 'Highlight retrieved successfully',
        data,
      };
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException(err.message);
    }
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard, PlanGuard)
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_STORE_HIGHLIGHTS)
  @ApiCreatedResponse({
    type: UpdateHighlightResponse,
  })
  @ApiBody({ type: UpdateHighlightDto })
  @ApiSecurity('bearer')
  async updateHighlight(@Req() req: IRequest, @Param('id') id: string, @Body() highlightReq: UpdateHighlightDto) {
    try {
      const data = await this.highlightsService.updateHighlight(id, highlightReq, req.user?.store.id);
      return {
        message: 'Highlight updated successfully',
        data: data,
      };
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException(err.message);
    }
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, PlanGuard)
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_STORE_HIGHLIGHTS)
  @ApiSecurity('bearer')
  async deleteHighlight(@Req() req: IRequest, @Param('id') id: string) {
    try {
      await this.highlightsService.deleteHighlight(id, req.user?.store.id);
      return {
        message: 'Highlight deleted successfully',
      };
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException(err.message);
    }
  }
}
