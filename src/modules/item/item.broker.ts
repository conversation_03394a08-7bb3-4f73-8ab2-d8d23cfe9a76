import { Controller } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { ItemService } from './item.service';
import { ClientSession, FilterQuery, Model, Mongoose, MongooseFilterQuery } from 'mongoose';
import { Item, ItemDocument } from './item.schema';
import { DiscountService } from './discount-coupons/discounts.service';
import { CouponService } from './discount-coupons/coupons.service';
import { CreateItemDto, CreateItemDtoItem, UpdateItemDto, VerifyCouponDto } from '../../models/dtos/ItemDtos';
import { InjectModel } from '@nestjs/mongoose';
import { SkipThrottle } from '@nestjs/throttler';
import { ItemInfoBlocksService } from './item-info-blocks.service';
import { HighlightsService } from './highlights/highlights.service';
@SkipThrottle()
@Controller()
export class ItemBroker {
  constructor(
    private readonly itemService: ItemService,
    private readonly discountService: DiscountService,
    private readonly couponService: CouponService,
    private readonly itemInfoBlocksService: ItemInfoBlocksService,
    private readonly highlightsService: HighlightsService,
    @InjectModel(Item.name)
    private readonly itemModel: Model<ItemDocument>,
  ) {}

  @MessagePattern(BROKER_PATTERNS.ITEM.GET_TOP_ITEMS)
  async getTopItems(data: FilterQuery<ItemDocument>) {
    return this.itemService.getTopItems(data);
  }

  @MessagePattern(BROKER_PATTERNS.ITEM.GENERATE_STORE_ITEM_EMBEDDINGS)
  async generateStoreEmbeddings(data: { storeId: string; batchSize?: number; limit?: number }) {
    return this.itemService.generateEmbeddingsForExistingItems(data.storeId, data.batchSize || 100, data.limit);
  }

  @MessagePattern(BROKER_PATTERNS.ITEM.ADD_VIEW)
  async addView(filter: FilterQuery<ItemDocument>) {
    return this.itemService.incrementItemView(filter);
  }

  @MessagePattern(BROKER_PATTERNS.ITEM.ADD_TOTAL_ORDERS)
  async addTotalOrders(data: { filter: FilterQuery<ItemDocument>; total_orders: number }) {
    return this.itemService.incrementItemTotalOrder(data.filter, data.total_orders);
  }

  @MessagePattern(BROKER_PATTERNS.ITEM.GET_TOTAL)
  async getItemsTotal(data: MongooseFilterQuery<ItemDocument>) {
    return this.itemService.countItems(data);
  }

  @MessagePattern(BROKER_PATTERNS.ITEM.GET_SITEMAP_PAGE)
  async getSitemapPage(data: { page: number; per_page: number }) {
    return this.itemService.getSitemapPage(data.page, data.per_page);
  }

  @MessagePattern(BROKER_PATTERNS.ITEM.GET_ITEM)
  async getItem(filter: FilterQuery<ItemDocument>) {
    return this.itemService.getItem(filter);
  }

  @MessagePattern(BROKER_PATTERNS.ITEM.GET_ITEMS)
  async getItems(filter: FilterQuery<ItemDocument>) {
    return this.itemService.getItems(filter);
  }

  @MessagePattern(BROKER_PATTERNS.ITEM.GET_ITEMS_WITH_DISCOUNTS)
  async getItemsWithDiscounts(filter: FilterQuery<ItemDocument>) {
    return this.itemService.getItemsWithResolvedDiscounts(filter);
  }

  @MessagePattern(BROKER_PATTERNS.ITEM.GET_MATCHING_ITEMS_WITH_NAME)
  async getMatchingItems(data: { storeId: string; names: string[]; otherQuery: any }) {
    return await this.itemModel
      .find({
        store: data.storeId as any,
        $or: [{ name: { $in: data.names.map((name) => new RegExp(name, 'i')) } }, data.otherQuery],
      })
      .populate('categories')
      .exec();
  }

  @MessagePattern(BROKER_PATTERNS.ITEM.GET_SORTED_ITEMS)
  async getSortedItems(filter: FilterQuery<ItemDocument>) {
    return this.itemService.getSortedItems(filter);
  }

  @MessagePattern(BROKER_PATTERNS.ITEM.GET_STORE_ITEMS_COUNT)
  async getStoreItems(storeId: string) {
    return this.itemService.getStoreItemsCount(storeId);
  }

  @MessagePattern(BROKER_PATTERNS.ITEM.GET_COUPON)
  async getCoupon(data: { couponCode: string; user: any }) {
    return this.couponService.getCouponByCode(data.user, data.couponCode);
  }

  // @MessagePattern(BROKER_PATTERNS.ITEM.VERIFY_COUPON)
  // async verifyCoupon(data) {
  //   return this.couponService.verifyCoupon(data);
  // }

  @MessagePattern(BROKER_PATTERNS.ITEM.TRACK_ORDER_COUPON)
  async trackOrderCoupon(data) {
    return this.couponService.trackOrderCoupon(data);
  }

  @MessagePattern(BROKER_PATTERNS.ITEM.TRACK_ITEM_QUANTITIES)
  async trackItemQuantities(data: { items: any[]; isCancelledOrder?: boolean }) {
    return this.itemService.trackItemQuantities(data);
  }

  @MessagePattern(BROKER_PATTERNS.ITEM.GET_DISCOUNTS)
  async getStoreDiscounts(data: { store: string }) {
    return (await this.discountService.getDiscounts(data.store, { search: '' })).items;
  }

  @MessagePattern(BROKER_PATTERNS.ITEM.CHECK_ITEM_QUANTITIES)
  async checkItemQuantites(data: { store: string }) {
    return this.itemService.checkItemQuantities(data.store);
  }

  @MessagePattern(BROKER_PATTERNS.ITEM.UPDATE_ITEM_WEIGHTS)
  async upadateItemWeights(data: { items: { id: string; weight: number; isCustomItem: boolean }[] }) {
    return this.itemService.updateItemWeights(data.items);
  }

  @MessagePattern(BROKER_PATTERNS.ITEM.UPDATE_ITEMS)
  async updateItems(data: { filter: FilterQuery<Item>; payload: UpdateItemDto }[]) {
    return this.itemService.updateItems(data);
  }

  @MessagePattern(BROKER_PATTERNS.ITEM.UPDATE_STORE_ITEMS)
  async upadateStoreItems(data: { store: string; payload: UpdateItemDto }) {
    return this.itemService.updateStoreItems(data);
  }

  @MessagePattern(BROKER_PATTERNS.ITEM.CREATE_ITEMS)
  async createItems(data: CreateItemDto) {
    return this.itemService.createItems(data);
  }

  @MessagePattern(BROKER_PATTERNS.ITEM.VERIFY_COUPON)
  async verifyCoupon(data: VerifyCouponDto) {
    return this.couponService.verifyCoupon(data, true);
  }

  @MessagePattern(BROKER_PATTERNS.ITEM.ADD_DEFAULT_QUANTITIES)
  async addDefaultQuantities(storeId) {
    return this.itemService.addDefaultQuantities(storeId);
  }

  @MessagePattern(BROKER_PATTERNS.ITEM.UPDATE_INFO_BLOCKS)
  async updateInfoBlocks(data: { infoBlockId: string; itemIds: string[]; type: 'add' | 'remove' }) {
    if (data.type === 'add') {
      return this.itemInfoBlocksService.addInfoBlocksToItems(data.infoBlockId, data.itemIds);
    } else {
      return this.itemInfoBlocksService.removeInfoBlocksFromItems(data.infoBlockId, data.itemIds);
    }
  }

  @MessagePattern(BROKER_PATTERNS.ITEM.HIGHLIGHTS.COUNT_HIGHLIGHTS)
  async countHighlights(filter: any) {
    return this.highlightsService.countHighlights(filter);
  }
}
