import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import mongoose from 'mongoose';
import { Document } from 'mongoose';

export type CustomItemDocument = CustomItem & Document;

@Schema({ timestamps: true })
export class CustomItem {
  _id: string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  name: string;

  @ApiProperty()
  @Prop({ type: Number, required: true })
  price: number;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Store' })
  store: string;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  is_deleted: boolean;

  @ApiProperty()
  @Prop({ type: Number })
  weight?: number;
}

export const CustomItemSchema = SchemaFactory.createForClass(CustomItem);
