import { ApiProperty, OmitType } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsArray,
  IsOptional,
  IsString,
  IsBoolean,
  IsObject,
} from 'class-validator';

export class CreateCustomItemSubItemsDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  price: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  weight?: string;
}

export class CreateCustomItemsDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  price: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  weight?: number;
}

export class CreateCustomItemDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  store: string;

  @ApiProperty({
    type: [CreateCustomItemSubItemsDto],
  })
  @IsNotEmpty()
  @IsArray()
  items: [CreateCustomItemSubItemsDto];
}

export class UpdateCustomItemDto {
  @ApiProperty({ type: String })
  @IsString()
  @IsOptional()
  name: string;

  @ApiProperty({ type: Number })
  @IsOptional()
  @IsNumber()
  price: number;

  @ApiProperty({ type: Number })
  @IsString()
  @IsOptional()
  weight: number;
}
