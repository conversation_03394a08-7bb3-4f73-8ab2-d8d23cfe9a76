import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, PaginateModel } from 'mongoose';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { PLANS } from '../../../enums/plan.enum';
import { MailchimpRepository } from '../../../repositories/mailchimp.repository';
import { Plan } from '../../plan/plan.schema';
import { Store, StoreDocument } from '../../store/store.schema';
import { User } from '../../user/user.schema';
import { CustomItem, CustomItemDocument } from './custom-item.schema';
import { CreateCustomItemDto, UpdateCustomItemDto, CreateCustomItemsDto } from './dtos/create-custom-item';
import { PaginatedQueryDto } from './dtos/get-custom-item';

@Injectable()
export class CustomItemService {
  constructor(
    @InjectModel(CustomItem.name)
    private readonly customItemModel: PaginateModel<CustomItemDocument>,
    private readonly brokerTransport: BrokerTransportService,
    private readonly logger: Logger,
  ) {}

  async createCustomItem(store: string, item: CreateCustomItemsDto) {
    return new this.customItemModel({ ...item, store: store }).save();
  }

  async create(user: User, itemReq: CreateCustomItemDto) {
    const store = await this.getStore({ _id: itemReq.store as any });

    const itemsCount = await this.customItemModel.count({
      store: store.id,
      is_deleted: { $ne: true },
    });

    if (!user.subscription) {
      if (itemsCount >= PLANS.MAX_UPLOADS.STARTER) {
        throw new BadRequestException(
          `You've reached the product limit for your plan you cannot upload more than 10 products`,
        );
      }
    } else if (itemsCount >= PLANS.MAX_UPLOADS[(user.subscription.plan as Plan).type]) {
      throw new BadRequestException(
        `You've reached the product limit for your plan you cannot upload more than ${
          PLANS.MAX_UPLOADS[(user.subscription.plan as Plan).type]
        } products`,
      );
    }

    return Promise.all(
      itemReq.items.map((item) => {
        return new this.customItemModel({ ...item, store: store.id }).save();
      }),
    );
  }

  async getItems(
    storeId: string,
    { search, ...filter }: FilterQuery<CustomItemDocument> & { search: string },
    paginationQuery: PaginatedQueryDto,
  ) {
    filter = {
      ...filter,
      is_deleted: {
        $ne: true,
      },
      store: storeId,
    };

    if (search) {
      filter.$or = [{ name: new RegExp(search, 'ig') }];
    }

    const result = await this.customItemModel.paginate(filter, {
      page: paginationQuery.page || 1,
      limit: paginationQuery.per_page || 50,
      lean: true,
    });

    return {
      data: {
        store: storeId,
        items: result.docs,
      },
      page: result.page,
      next_page: result.nextPage,
      prev_page: result.prevPage,
      total: result.totalDocs,
      total_pages: result.totalPages,
      per_page: result.limit,
    };
  }

  async updateItem(itemId: string, update: UpdateCustomItemDto) {
    return this.customItemModel.findByIdAndUpdate(itemId, update, {
      new: true,
    });
  }

  async deleteItem(itemId: string) {
    return this.customItemModel.findByIdAndUpdate(itemId, { is_deleted: true });
  }

  async getStore(filter: FilterQuery<StoreDocument>) {
    const store = await this.brokerTransport.send<Store>(BROKER_PATTERNS.STORE.GET_STORE, filter).toPromise();

    if (!store) {
      throw new BadRequestException('store does not exist');
    }
    return store;
  }
}
