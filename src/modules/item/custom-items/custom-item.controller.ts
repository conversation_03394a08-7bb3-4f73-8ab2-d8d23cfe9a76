import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from '../../../jwt/jwt-auth.guard';
import { CustomItemService } from './custom-item.service';
import {
  CreateCustomItemDto,
  UpdateCustomItemDto,
  CreateCustomItemsDto,
} from './dtos/create-custom-item';
import { IRequest as Request } from '../../../interfaces/request.interface';
import { PaginatedQueryDto } from './dtos/get-custom-item';

@Controller('custom-items')
export class CustomItemController {
  constructor(private readonly customItemService: CustomItemService) {}

  @Post('')
  @UseGuards(JwtAuthGuard)
  async create(@Req() req: Request, @Body() item: CreateCustomItemsDto) {
    
    const store = req?.user?.store;
    const data = await this.customItemService.createCustomItem(store?.id, item);

    return {
      message: 'Item created successfully',
      data,
    };
  }

  @Post('old-create')
  @UseGuards(JwtAuthGuard)
  async oldCreate(@Req() req: Request, @Body() item: CreateCustomItemDto) {
    const data = await this.customItemService.create(req.user, item);

    return {
      message: 'Items created successfully',
      data,
    };
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard)
  async update(@Param('id') id: string, @Body() update: UpdateCustomItemDto) {
    const data = await this.customItemService.updateItem(id, update);

    return {
      message: 'Item updated successfully',
      data,
    };
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  async delete(@Param('id') id: string) {
    const data = await this.customItemService.deleteItem(id);

    return {
      message: 'Item deleted successfully',
      data,
    };
  }

  @Get('')
  @UseGuards(JwtAuthGuard)
  async getItems(
    @Query('filter') filter: any,
    @Query() query: PaginatedQueryDto,
    @Req() req: Request,
  ) {
    filter = filter ?? { search: '' };
    const items = await this.customItemService.getItems(
      req.user.store.id,
      filter,
      query,
    );

    return {
      message: 'Items fetched successfully',
      ...items,
    };
  }
}
