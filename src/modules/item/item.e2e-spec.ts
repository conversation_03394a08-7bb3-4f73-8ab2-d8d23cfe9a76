import { INestApplication } from '@nestjs/common';
import { Transport } from '@nestjs/microservices';
import { Test, TestingModule } from '@nestjs/testing';
import request from 'supertest';
import * as faker from 'faker';
import { AppModule } from '../../app.module';
import { JwtStrategy } from '../../jwt/jwt.strategy';
import { MockJwtStrategy } from '../../tools/test-helpers';
import { ItemModule } from './item.module';
import { storeMock, TEST_CATEGORY_ID, TEST_STORE_ID, TOKENS } from '../../tools/testdata';
import { ItemService } from './item.service';
import { LowercasePipe } from '../../pipe/lowercase.pipe';

describe('Item Module', () => {
  let app: INestApplication;

  let item = {
    id: undefined,
    name: faker.commerce.productName(),
    price: faker.commerce.price(),
    description: faker.commerce.productDescription(),
    price_unit: 'Plate',
    store: TEST_STORE_ID,
    category: TEST_CATEGORY_ID,
    slug: faker.commerce.productName().replace(/\s/g, '_'),
  } as any;

  const expectedItemResponse = {
    name: expect.any(String),
    views: expect.any(Number),
    price: expect.any(Number),
    description: expect.any(String),
    price_unit: expect.any(String),
    slug: expect.any(String),
  };

  beforeAll(async () => {
    jest.setTimeout(30000);
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule, ItemModule],
    })
      .overrideProvider(JwtStrategy)
      .useClass(MockJwtStrategy)
      .compile();

    app = module.createNestApplication();
    app.useGlobalPipes(new LowercasePipe());
    app.connectMicroservice({
      transport: Transport.NATS,
      options: {
        url: process.env.NATS_URL || 'nats://localhost:4222',
      },
    });

    await app.startAllMicroservicesAsync();
    await app.init();
  });

  afterAll(async () => {
    const itemService = app.get<ItemService>(ItemService);
    await itemService.getItemModel().findByIdAndDelete(item.id);
  });

  it('should create item', () => {
    return request(app.getHttpServer())
      .post('/items')
      .send({ items: [item], store: TEST_STORE_ID })
      .set('Authorization', `Bearer ${TOKENS.WITH_STORE_AND_STARTER_SUBSCRIPTION}`)
      .then(({ body }) => {
        expect(body.data).toEqual(expect.arrayContaining([expect.objectContaining(expectedItemResponse)]));
        expect(body).toEqual(
          expect.objectContaining({
            message: expect.any(String),
            data: expect.any(Array),
          }),
        );
        item.id = body.data[0].id;
        item.slug = body.data[0].slug;

        console.log({ body });
      });
  });

  it('should update item', () => {
    const updateItem = {
      name: faker.commerce.productName(),
      description: faker.commerce.productDescription(),
      available: false,
    };

    return request(app.getHttpServer())
      .put(`/items/${item.id}`)
      .send(updateItem)
      .set('Authorization', `Bearer ${TOKENS.WITH_STORE_AND_STARTER_SUBSCRIPTION}`)
      .expect(200)
      .then(({ body }) => {
        item = { ...item, ...updateItem };
        expect(body).toEqual(
          expect.objectContaining({
            message: expect.any(String),
            data: expect.any(Object),
          }),
        );
        expect(body.data).toEqual(expect.objectContaining(expectedItemResponse));
        item.slug = body.data.slug;
      });
  });

  it('should fetch an item by id', () => {
    return request(app.getHttpServer())
      .get(`/items/${item.id}`)
      .set('Authorization', `Bearer ${TOKENS.WITH_STORE_AND_STARTER_SUBSCRIPTION}`)
      .expect(200)
      .then(({ body: { data } }) => {
        expect(data).toEqual(expect.objectContaining(expectedItemResponse));
        expect(data.category).toBeTruthy();
        expect(data.category).toEqual({
          name: expect.any(String),
          id: expect.any(String),
        });
      });
  });

  it('should fetch an item by slug', () => {
    return request(app.getHttpServer())
      .get(`/items/${item.slug}`)
      .set('Authorization', `Bearer ${TOKENS.WITH_STORE_AND_STARTER_SUBSCRIPTION}`)
      .expect(200)
      .then(({ body: { data } }) => {
        expect(data).toEqual(expect.objectContaining(expectedItemResponse));
      });
  });

  xit('fetch items by store id', () => {
    return request(app.getHttpServer())
      .get(`/items/public?filter[store]=${TEST_STORE_ID}&page=1&per_page=5`)
      .expect(200)
      .then(({ body }) => {
        expect(body).toEqual(
          expect.objectContaining({
            message: expect.any(String),
            page: 1,
            per_page: 5,
            total_pages: expect.any(Number),
            sort: expect.any(String),
            data: expect.objectContaining({
              items: [
                expect.objectContaining({
                  ...expectedItemResponse,
                  store: expect.any(String),
                  category: expect.objectContaining({
                    id: expect.any(String),
                    name: expect.any(String),
                  }),
                }),
              ],
            }),
          }),
        );
      });
  });

  xit('fetch items by store slug and filter by category', () => {
    return request(app.getHttpServer())
      .get(`/items/public?filter[store]=${storeMock.slug}&filter[category]=${TEST_CATEGORY_ID}&page=1&per_page=5`)
      .expect(200)
      .then(({ body }) => {
        expect(body).toEqual(
          expect.objectContaining({
            message: expect.any(String),
            page: 1,
            per_page: 5,
            total_pages: expect.any(Number),
            sort: expect.any(String),
            data: expect.objectContaining({
              items: [
                expect.objectContaining({
                  ...expectedItemResponse,
                  category: expect.objectContaining({
                    id: expect.any(String),
                    name: expect.any(String),
                  }),
                }),
              ],
            }),
          }),
        );
      });
  });

  it('search for items by store id ', () => {
    return request(app.getHttpServer())
      .get(
        `/items/public?filter[store]=${TEST_STORE_ID}&filter[search]=${item.description
          .split(/\s/)
          .slice(0, 2)
          .join(' ')}&page=1&per_page=5`,
      )
      .expect(200);
  });

  it('delete an item', () => {
    return request(app.getHttpServer())
      .delete(`/items/${item.id}`)
      .set('Authorization', `Bearer ${TOKENS.WITH_STORE_AND_STARTER_SUBSCRIPTION}`)
      .expect(204);
  });
});
