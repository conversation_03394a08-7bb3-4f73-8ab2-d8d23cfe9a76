import { Discount, DiscountDocument } from './discount-coupons/discounts-coupons.schema';
import { Item, ItemDocument } from './item.schema';

const computeItemsDiscount = (itemDocs: Item[], discounts: DiscountDocument[]) => {
  const items = [...itemDocs];

  if (!discounts || discounts.length === 0) {
    return items;
  }

  const discountMap = new Map<string, DiscountDocument>();
  for (let discount of discounts) {
    discountMap.set(discount._id.toString(), discount);
  }

  const itemsWithDiscounts = items.map((item) => {
    const { price, discount, variants } = item;

    const itemDiscount = discount ? discountMap.get(discount.toString()) : undefined;

    if (itemDiscount) {
      if (variants) {
        for (let option of variants.options) {
          option.discount_price = getDiscountPrice(option.price, itemDiscount);
        }
      }
      item.discount_price = getDiscountPrice(price, itemDiscount);
    } else {
      item.discount_price <= 0 && delete item.discount_price;
    }

    delete item.discount;

    return item;
  });

  return itemsWithDiscounts;
};

export function getDiscountPrice(price: number, discount: DiscountDocument) {
  let discount_price;
  const discountAmount = (price * discount.percentage) / 100;
  const discountExceeded = discount.discount_cap ? discountAmount > discount.discount_cap : false;

  const discountExpired = discount.end_date ? new Date(discount.end_date) < new Date() : false;

  discountExceeded ? (discount_price = price - discount.discount_cap) : (discount_price = price - discountAmount);

  discountExpired || !discount.active ? (discount_price = undefined) : undefined;

  return discount_price;
}

export default computeItemsDiscount;
