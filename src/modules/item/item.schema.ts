import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import mongoose from 'mongoose';

import { InfoBlock, Store } from '../store/store.schema';
import { Document } from 'mongoose';
import { Discount } from './discount-coupons/discounts-coupons.schema';
import { ITEM_SOURCE } from '../../enums/item.enum';
import { IsNotEmpty } from 'class-validator';
import { TieredPricing } from './tiered-pricing/tiered-pricing.schema';

export type ItemDocument = Item & Document;

export interface ItemVariants {
  type: string;
  is_template?: boolean;
  options: {
    values: { [key: string]: string };
    image?: string;
    is_available: boolean;
    price: number;
    original_price?: number;
    discount_price?: number;
    quantity?: number;
    // is_always_available?: boolean;
    _id: any;
    id?: string;
  }[];
}

export enum ThumbnailTypes {
  VIDEO = 'video',
  IMAGE = 'image'
}

export class ProductVideo {
  name: string;
  url: string;
  thumbnail: string;
}

@Schema({ timestamps: true })
export class Item {
  _id: string;

  id?: string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  name: string;

  @ApiProperty()
  @Prop({ type: Number, required: true })
  price: number;

  @ApiProperty()
  @Prop({ type: Number, required: false })
  original_price?: number;

  @ApiProperty()
  @Prop({ type: String })
  price_unit?: string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  description: string;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, required: false })
  category: string | Store['categories'][0];

  @ApiProperty()
  @Prop({ type: [mongoose.Schema.Types.ObjectId], required: false, default: [] })
  tags: string[] | Store['categories'];

  @ApiProperty()
  @Prop({ type: String, default: '' })
  slug: string;

  @ApiProperty()
  @Prop({ type: Boolean, default: true })
  available?: boolean;

  @ApiProperty()
  @Prop({ type: [String], default: [] })
  images: string[];

  @ApiProperty()
  @Prop({ type: Array<ProductVideo>, default: [] })
  videos?: ProductVideo[];

  @ApiProperty()
  @Prop({ type: String })
  featuredImage?: string;

  @ApiProperty()
  @Prop({ type: Number })
  thumbnail?: number;

  @ApiProperty()
  @Prop({ type: String, enum: Object.values(ThumbnailTypes), default: ThumbnailTypes.IMAGE })
  thumbnail_type?: ThumbnailTypes;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Store' })
  store: Store;

  @ApiProperty()
  @Prop({ type: Number, default: 0 })
  views: number;

  @ApiProperty()
  @Prop({ type: Number, default: 0 })
  total_orders: number;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  is_deleted: boolean;

  @ApiProperty({
    properties: {
      type: {
        type: 'string',
      },
      options: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            value: {
              type: 'object',
            },
            image: {
              type: 'string',
            },
            price: {
              type: 'number',
            },
            is_available: {
              type: 'boolean',
            },
            quantity: {
              type: 'number',
            },
            is_always_available: {
              type: 'boolean',
            },
          },
        },
      },
    },
  })
  @Prop({
    type: {
      options: [
        {
          _id: mongoose.Schema.Types.ObjectId,
        },
      ],
    },
  })
  variants?: ItemVariants;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, required: false })
  discount?: string | Discount;

  @ApiProperty()
  @Prop({ type: Number, required: false })
  discount_price?: number;

  @ApiProperty()
  @Prop({ type: Number, required: false })
  quantity?: number;

  @ApiProperty()
  @Prop({ type: Number, required: false })
  minimum_order_quantity?: number;

  @ApiProperty()
  @Prop({ type: Number, required: false })
  cost_price?: number;

  @ApiProperty()
  @Prop({ type: Boolean, required: false })
  is_always_available?: boolean;

  @ApiProperty()
  @Prop({ type: Number, required: false })
  weight?: number;

  @ApiProperty()
  @Prop({ type: Number, required: false })
  sort_index?: number;

  @ApiProperty()
  @Prop({ type: Boolean, required: false, default: false })
  is_featured?: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, required: false })
  is_menu_item?: boolean;

  @ApiProperty()
  @Prop({ type: Object, default: {} })
  meta?: {
    [key: string]: any;
  };

  @ApiProperty()
  @Prop({ type: Date })
  last_chowdeck_update: Date;

  @ApiProperty({
    type: String,
    enum: Object.values(ITEM_SOURCE),
  })
  @Prop({ type: String, enum: Object.values(ITEM_SOURCE), default: ITEM_SOURCE.MANUAL })
  upload_source: ITEM_SOURCE;

  @ApiProperty()
  @Prop({ type: [Number], index: '2dsphere', select: false})
  embedding?: number[];

  @ApiProperty()
  @Prop({ type: Date, required: false })
  expiry_date?: string | Date;

  @ApiProperty({ type: [String], required: false })
  @Prop({ type: [{ type: mongoose.Schema.Types.ObjectId, ref: 'InfoBlock' }], default: [] })
  info_blocks?: string[] | InfoBlock[];

  @ApiProperty({ required: false })
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'TieredPricing', required: false })
  tiered_pricing?: string | TieredPricing;
}

export const ItemSchema = SchemaFactory.createForClass(Item);

ItemSchema.pre(/^find/, function() {
  this.populate('tiered_pricing');
});

// Add a note about the vector search index
// The actual vector search index is created via the script/create-vector-index.js script
// This is just a reminder that the embedding field is used for vector search
ItemSchema.index({ store: 1 }); // Add an index for store lookups

// Optionally add a compound index if needed later for performance
// ItemSchema.index({ store: 1, embedding: '2dsphere' });
