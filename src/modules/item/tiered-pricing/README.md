# Tiered Pricing Module

This module provides comprehensive tiered pricing functionality for items, allowing stores to create discount rules based on quantity thresholds.

## Features

- **Tiered Pricing Rules**: Create pricing rules with multiple tiers based on quantity
- **Percentage-based Discounts**: Apply percentage discounts to items based on quantity
- **Bulk Operations**: Efficiently assign/remove multiple items to/from pricing rules
- **Validation**: Comprehensive validation for business rules and data integrity
- **Performance Optimized**: Indexed queries and bulk operations for scalability

## Schema

### TieredPricing

```typescript
{
  _id: string;
  label: string;                    // Human-readable name for the rule
  active: boolean;                  // Whether the rule is active
  tiers: PricingTier[];            // Array of pricing tiers
  items: string[];                 // Array of item IDs assigned to this rule
  store: string;                   // Store ID that owns this rule
  created_at: Date;
  updated_at: Date;
}
```

### PricingTier

```typescript
{
  minimum_quantity: number; // Minimum quantity for this tier (must be unique)
  discount_percentage: number; // Discount percentage (0-100)
}
```

## Business Rules

### Validation Rules

1. **Minimum Quantity**: Must be a positive integer (≥ 1)
2. **Discount Percentage**: Must be between 0 and 100
3. **Unique Tiers**: Each tier must have a unique minimum quantity
4. **Ordered Tiers**: Tiers must be ordered by minimum quantity (ascending)
5. **At Least One Tier**: Each pricing rule must have at least one tier

### Price Calculation Logic

1. **Active Check**: Only active rules are applied
2. **Tier Selection**: Find the highest applicable tier based on quantity
3. **Discount Application**: Apply percentage discount to base price
4. **Fallback**: Return original price if no applicable tier

## API Endpoints

### Create Tiered Pricing Rule

```
POST /tiered-pricing
```

**Request Body:**

```json
{
  "label": "Bulk Discount",
  "active": true,
  "tiers": [
    { "minimum_quantity": 5, "discount_percentage": 10 },
    { "minimum_quantity": 10, "discount_percentage": 15 }
  ],
  "items": ["item1", "item2"]
}
```

### Get All Tiered Pricing Rules

```
GET /tiered-pricing?page=1&limit=10&active=true
```

### Get Active Tiered Pricing Rules

```
GET /tiered-pricing/active
```

### Get Specific Tiered Pricing Rule

```
GET /tiered-pricing/:id
```

### Update Tiered Pricing Rule

```
PUT /tiered-pricing/:id
```

**Request Body:**

```json
{
  "label": "Updated Bulk Discount",
  "active": false,
  "tiers": [
    { "minimum_quantity": 3, "discount_percentage": 5 },
    { "minimum_quantity": 8, "discount_percentage": 12 }
  ],
  "items_to_add": ["item3"],
  "items_to_remove": ["item1"]
}
```

### Delete Tiered Pricing Rule

```
DELETE /tiered-pricing/:id
```

### Assign Items to Tiered Pricing Rule

```
POST /tiered-pricing/:id/assign-items
```

**Request Body:**

```json
{
  "items": ["item1", "item2", "item3"]
}
```

### Remove Items from Tiered Pricing Rule

```
POST /tiered-pricing/:id/remove-items
```

**Request Body:**

```json
{
  "items": ["item1", "item2"]
}
```

### Get Assigned Items

```
GET /tiered-pricing/:id/items
```

### Calculate Price

```
POST /tiered-pricing/calculate-price
```

**Request Body:**

```json
{
  "base_price": 100,
  "quantity": 5,
  "tiered_pricing_id": "tiered1"
}
```

**Response:**

```json
{
  "data": {
    "original_price": 500,
    "discounted_price": 450,
    "discount_amount": 50,
    "discount_percentage": 10,
    "applied_tier": {
      "minimum_quantity": 5,
      "discount_percentage": 10
    }
  }
}
```

## Performance Considerations

### Database Indexes

The module includes the following indexes for optimal performance:

1. **Store + Active Index**: `{ store: 1, active: 1 }`
2. **Items Index**: `{ items: 1 }`
3. **Tier Minimum Quantity Index**: `{ 'tiers.minimum_quantity': 1 }`

### Bulk Operations

- **Item Assignment**: Uses `updateMany` for efficient bulk updates
- **Item Removal**: Uses `$unset` and `$pull` operations
- **Pagination**: Supports pagination for large datasets

### Caching Strategy

Consider implementing caching for:

- Frequently accessed tiered pricing rules
- Price calculation results
- Store-specific active rules

## Integration Points

### Item Schema Updates

The item schema has been updated to include:

```typescript
tiered_pricing?: string;  // Reference to TieredPricing rule
```

### Cart Integration

When calculating cart totals, check for `tiered_pricing` on each item and apply the appropriate discount based on quantity.

### Order Processing

Ensure tiered pricing is applied during order creation and that the calculated prices are stored with the order.

## Error Handling

### Common Error Scenarios

1. **Validation Errors**: Invalid tier configuration, duplicate quantities
2. **Not Found**: Tiered pricing rule doesn't exist
3. **Store Mismatch**: Items don't belong to the same store
4. **Business Logic**: Inactive rules, no applicable tiers

### Error Responses

```json
{
  "statusCode": 400,
  "message": "Tiers must have unique minimum quantities",
  "error": "Bad Request"
}
```

## Testing

The module includes comprehensive unit tests covering:

- **CRUD Operations**: Create, read, update, delete operations
- **Validation**: Business rule validation
- **Price Calculation**: Various scenarios and edge cases
- **Error Handling**: Exception scenarios

Run tests with:

```bash
npm test tiered-pricing.service.spec.ts
```

## Migration Guide

### Adding Tiered Pricing to Existing Items

1. **Database Migration**: Add `tiered_pricing` field to items collection
2. **Default Values**: Set existing items' `tiered_pricing` to `null`
3. **Backward Compatibility**: Ensure existing code handles `null` values

### Example Migration Script

```javascript
// Add tiered_pricing field to existing items
db.items.updateMany({ tiered_pricing: { $exists: false } }, { $set: { tiered_pricing: null } });
```

## Security Considerations

1. **Store Isolation**: Users can only access tiered pricing rules for their own store
2. **Input Validation**: All inputs are validated using class-validator
3. **Authorization**: JWT authentication required for all endpoints
4. **Data Integrity**: Business rules prevent invalid configurations

## Future Enhancements

1. **Time-based Rules**: Support for date ranges and scheduling
2. **Customer-specific Rules**: Different rules for different customer types
3. **Product Category Rules**: Apply rules to entire product categories
4. **Advanced Discounts**: Support for fixed amount discounts
5. **Analytics**: Track usage and effectiveness of tiered pricing rules
