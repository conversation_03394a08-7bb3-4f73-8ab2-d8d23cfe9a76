import {
  Controller,
  Post,
  Put,
  Delete,
  Get,
  Body,
  Param,
  Query,
  UseGuards,
  Req,
  HttpStatus,
  HttpCode,
  BadRequestException,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiSecurity, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../jwt/jwt-auth.guard';
import { TieredPricingService } from './tiered-pricing.service';
import {
  CreateTieredPricingDto,
  UpdateTieredPricingDto,
  AssignItemsDto,
  RemoveItemsDto,
  CalculatePriceDto,
} from './dtos/tiered-pricing.dto';
import { IRequest } from '../../../interfaces/request.interface';
import { checkIfUserOwnsStore } from '../../../utils';
import { TieredPricing } from './tiered-pricing.schema';
import { PaginatedQueryDto } from '../../../models/dtos/PaginatedDto';
import { PlanPermissions } from '../../../decorators/permission.decorator';
import { SCOPES } from '../../../utils/permissions.util';
import { PlanGuard } from '../../../guards/plan.guard';

@Controller('tiered-pricing')
@ApiTags('Tiered Pricing')
@UseGuards(JwtAuthGuard)
@ApiSecurity('bearer')
export class TieredPricingController {
  constructor(private readonly tieredPricingService: TieredPricingService, private readonly logger: Logger) {
    this.logger.setContext('tieredPricingController');
  }

  @PlanPermissions( SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_TIERED_PRICING)
  @UseGuards(JwtAuthGuard, PlanGuard)
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new tiered pricing rule' })
  @ApiBody({ type: CreateTieredPricingDto })
  @ApiResponse({
    status: 201,
    description: 'Tiered pricing rule created successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Tiered pricing rule created successfully' },
        data: { $ref: '#/components/schemas/TieredPricing' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request - validation error' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async create(@Req() req: IRequest, @Body() createDto: CreateTieredPricingDto) {
    const data = await this.tieredPricingService.create(req.user.store.id, createDto);
    return {
      message: 'Tiered pricing rule created successfully',
      data,
    };
  }

  @PlanPermissions( SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_TIERED_PRICING)
  @UseGuards(JwtAuthGuard, PlanGuard)
  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiResponse({ status: HttpStatus.OK })
  @ApiQuery({
    name: 'filter',
    required: false,
    schema: {
      type: 'object',
      properties: {
        active: {
          type: 'boolean',
          description: 'Filter by active status',
        },
      },
    },
  })
  async findAll(@Query('filter') filter: any, @Query() query: PaginatedQueryDto, @Req() req: IRequest) {
    try {
      if (!filter) {
        filter = {};
      }
      filter.store = req.user.store.id;
      const active = filter.active !== undefined ? filter.active : undefined;
      const result = await this.tieredPricingService.findByStorePaginated(
        req.user.store.id,
        query.page || 1,
        query.per_page || 10,
        active,
      );
      return {
        message: 'Tiered pricing rules fetched successfully',
        ...result,
      };
    } catch (err) {
      this.logger.error(`error occurred fetching list of tiered pricing rules`, err);
      if (!(err instanceof BadRequestException)) {
        throw new InternalServerErrorException();
      }
      throw err;
    }
  }

  @PlanPermissions( SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_TIERED_PRICING)
  @UseGuards(JwtAuthGuard, PlanGuard)
  @Get('active')
  @ApiOperation({ summary: 'Get all active tiered pricing rules for the store' })
  @ApiResponse({
    status: 200,
    description: 'Active tiered pricing rules retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/TieredPricing' },
        },
      },
    },
  })
  async findActive(@Req() req: IRequest) {
    const data = await this.tieredPricingService.findActiveByStore(req.user.store.id);
    return { data };
  }

  @PlanPermissions( SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_TIERED_PRICING)
  @UseGuards(JwtAuthGuard, PlanGuard)
  @Get(':id')
  @ApiOperation({ summary: 'Get a specific tiered pricing rule by ID' })
  @ApiParam({ name: 'id', description: 'Tiered pricing rule ID' })
  @ApiResponse({
    status: 200,
    description: 'Tiered pricing rule retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: { $ref: '#/components/schemas/TieredPricing' },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Tiered pricing rule not found' })
  async findOne(@Param('id') id: string) {
    const data = await this.tieredPricingService.findById(id);
    return { data };
  }

  @PlanPermissions( SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_TIERED_PRICING)
  @UseGuards(JwtAuthGuard, PlanGuard)
  @Put(':id')
  @ApiOperation({ summary: 'Update a tiered pricing rule' })
  @ApiParam({ name: 'id', description: 'Tiered pricing rule ID' })
  @ApiBody({ type: UpdateTieredPricingDto })
  @ApiResponse({
    status: 200,
    description: 'Tiered pricing rule updated successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Tiered pricing rule updated successfully' },
        data: { $ref: '#/components/schemas/TieredPricing' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request - validation error' })
  @ApiResponse({ status: 404, description: 'Tiered pricing rule not found' })
  async update(@Param('id') id: string, @Body() updateDto: UpdateTieredPricingDto) {
    const data = await this.tieredPricingService.update(id, updateDto);
    return {
      message: 'Tiered pricing rule updated successfully',
      data,
    };
  }

  @PlanPermissions( SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_TIERED_PRICING)
  @UseGuards(JwtAuthGuard, PlanGuard)
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a tiered pricing rule' })
  @ApiParam({ name: 'id', description: 'Tiered pricing rule ID' })
  @ApiResponse({ status: 204, description: 'Tiered pricing rule deleted successfully' })
  @ApiResponse({ status: 404, description: 'Tiered pricing rule not found' })
  async delete(@Param('id') id: string) {
    await this.tieredPricingService.delete(id);
    return { message: 'Tiered pricing rule deleted successfully' };
  }

  @PlanPermissions( SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_TIERED_PRICING)
  @UseGuards(JwtAuthGuard, PlanGuard)
  @Post(':id/assign-items')
  @ApiOperation({ summary: 'Assign items to a tiered pricing rule' })
  @ApiParam({ name: 'id', description: 'Tiered pricing rule ID' })
  @ApiBody({ type: AssignItemsDto })
  @ApiResponse({
    status: 200,
    description: 'Items assigned successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Items assigned successfully' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request - validation error' })
  @ApiResponse({ status: 404, description: 'Tiered pricing rule not found' })
  async assignItems(@Param('id') id: string, @Body() assignDto: AssignItemsDto) {
    await this.tieredPricingService.assignItems(id, assignDto);
    return { message: 'Items assigned successfully' };
  }

  @PlanPermissions( SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_TIERED_PRICING)
  @UseGuards(JwtAuthGuard, PlanGuard)
  @Post(':id/remove-items')
  @ApiOperation({ summary: 'Remove items from a tiered pricing rule' })
  @ApiParam({ name: 'id', description: 'Tiered pricing rule ID' })
  @ApiBody({ type: RemoveItemsDto })
  @ApiResponse({
    status: 200,
    description: 'Items removed successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Items removed successfully' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request - validation error' })
  @ApiResponse({ status: 404, description: 'Tiered pricing rule not found' })
  async removeItems(@Param('id') id: string, @Body() removeDto: RemoveItemsDto) {
    await this.tieredPricingService.removeItems(id, removeDto);
    return { message: 'Items removed successfully' };
  }

  @Get(':id/items')
  @ApiOperation({ summary: 'Get items assigned to a tiered pricing rule' })
  @ApiParam({ name: 'id', description: 'Tiered pricing rule ID' })
  @ApiResponse({
    status: 200,
    description: 'Assigned items retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/Item' },
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Tiered pricing rule not found' })
  async getAssignedItems(@Param('id') id: string) {
    const data = await this.tieredPricingService.getAssignedItems(id);
    return { data };
  }

  @Post('calculate-price')
  @ApiOperation({ summary: 'Calculate price based on quantity and tiered pricing' })
  @ApiBody({ type: CalculatePriceDto })
  @ApiResponse({
    status: 200,
    description: 'Price calculated successfully',
    schema: {
      type: 'object',
      properties: {
        original_price: { type: 'number' },
        discounted_price: { type: 'number' },
        discount_amount: { type: 'number' },
        discount_percentage: { type: 'number' },
        applied_tier: {
          type: 'object',
          properties: {
            minimum_quantity: { type: 'number' },
            discount_percentage: { type: 'number' },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request - validation error' })
  @ApiResponse({ status: 404, description: 'Tiered pricing rule not found' })
  async calculatePrice(@Body() calculateDto: CalculatePriceDto) {
    const data = await this.tieredPricingService.calculatePrice(calculateDto);
    return { data };
  }
}
