import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import mongoose from 'mongoose';
import { Store } from '../../store/store.schema';
import { Item } from '../item.schema';
import { Document } from 'mongoose';

export type TieredPricingDocument = TieredPricing & Document;

export interface PricingTier {
  minimum_quantity: number;
  discount_percentage: number;
}

@Schema({ timestamps: true })
export class TieredPricing {
  _id: string;

  @ApiProperty()
  @Prop({ type: String, required: true, trim: true, maxlength: 100 })
  label: string;

  @ApiProperty()
  @Prop({ type: Boolean, required: true, default: true })
  active: boolean;

  @ApiProperty({ type: [Object] })
  @Prop({
    type: [
      {
        minimum_quantity: {
          type: Number,
          required: true,
          min: 1,
          validate: {
            validator: function (value: number) {
              return Number.isInteger(value) && value > 0;
            },
            message: 'Minimum quantity must be a positive integer',
          },
        },
        discount_percentage: {
          type: Number,
          required: true,
          min: 0,
          max: 100,
          validate: {
            validator: function (value: number) {
              return value >= 0 && value <= 100;
            },
            message: 'Discount percentage must be between 0 and 100',
          },
        },
      },
    ],
    required: true,
    validate: [
      {
        validator: function (tiers: PricingTier[]) {
          if (!Array.isArray(tiers) || tiers.length === 0) {
            return false;
          }
          return true;
        },
        message: 'At least one tier is required',
      },
      {
        validator: function (tiers: PricingTier[]) {
          // Ensure tiers are sorted by minimum_quantity and have unique minimum_quantity
          const quantities = tiers.map((t) => t.minimum_quantity).sort((a, b) => a - b);
          const uniqueQuantities = new Set(quantities);
          return quantities.length === uniqueQuantities.size;
        },
        message: 'Tiers must have unique minimum quantities',
      },
      {
        validator: function (tiers: PricingTier[]) {
          // Ensure tiers are properly ordered (ascending by minimum_quantity)
          for (let i = 1; i < tiers.length; i++) {
            if (tiers[i].minimum_quantity <= tiers[i - 1].minimum_quantity) {
              return false;
            }
          }
          return true;
        },
        message: 'Tiers must be ordered by minimum quantity (ascending)',
      },
    ],
  })
  tiers: PricingTier[];

  @ApiProperty()
  @Prop({ type: [mongoose.Schema.Types.ObjectId], ref: 'Item', default: [] })
  items: string[] | Item[];

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Store', required: true, index: true })
  store: string | Store;

  @ApiProperty()
  @Prop({ type: Date, default: Date.now })
  created_at: Date;

  @ApiProperty()
  @Prop({ type: Date, default: Date.now })
  updated_at: Date;
}

export const TieredPricingSchema = SchemaFactory.createForClass(TieredPricing);

// Add indexes for performance
TieredPricingSchema.index({ store: 1, active: 1 });
TieredPricingSchema.index({ items: 1 });
TieredPricingSchema.index({ 'tiers.minimum_quantity': 1 });

// Pre-save middleware to ensure proper ordering
TieredPricingSchema.pre('save', function (next) {
  if (this.tiers && this.tiers.length > 0) {
    // Sort tiers by minimum_quantity to ensure proper ordering
    this.tiers.sort((a, b) => a.minimum_quantity - b.minimum_quantity);
  }
  next();
});
