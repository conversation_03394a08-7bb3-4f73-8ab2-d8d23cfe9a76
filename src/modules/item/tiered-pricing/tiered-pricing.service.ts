import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { TieredPricing, TieredPricingDocument, PricingTier } from './tiered-pricing.schema';
import { Item, ItemDocument } from '../item.schema';
import { getDocId } from '../../../utils/functions';
import {
  CreateTieredPricingDto,
  UpdateTieredPricingDto,
  AssignItemsDto,
  RemoveItemsDto,
  CalculatePriceDto,
} from './dtos/tiered-pricing.dto';

@Injectable()
export class TieredPricingService {
  private readonly logger = new Logger(TieredPricingService.name);

  constructor(
    @InjectModel(TieredPricing.name) private tieredPricingModel: Model<TieredPricingDocument>,
    @InjectModel(Item.name) private itemModel: Model<ItemDocument>,
  ) {}

  /**
   * Create a new tiered pricing rule
   */
  async create(storeId: string, createDto: CreateTieredPricingDto): Promise<TieredPricing> {
    try {
      // Validate tiers before creating
      this.validateTiers(createDto.tiers);

      await this.removeItemsFromAllTieredPricing(createDto.items, storeId);
      const tieredPricing = new this.tieredPricingModel({
        ...createDto,
        store: storeId,
        active: createDto.active ?? true,
      });

      const saved = await tieredPricing.save();

      // If items are provided, update them in bulk
      if (createDto.items && createDto.items.length > 0) {
        await this.updateItemsWithTieredPricing(createDto.items, saved._id);
      }

      this.logger.log(`Created tiered pricing rule: ${saved._id} for store: ${storeId}`);
      return saved;
    } catch (error) {
      this.logger.error(`Error creating tiered pricing: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update an existing tiered pricing rule
   */
  async update(id: string, updateDto: UpdateTieredPricingDto): Promise<TieredPricing> {
    try {
      const tieredPricing = await this.tieredPricingModel.findById(id);
      if (!tieredPricing) {
        throw new NotFoundException('Tiered pricing rule not found');
      }

      // Validate tiers if provided
      if (updateDto.tiers) {
        this.validateTiers(updateDto.tiers);
      }

      // Handle item assignments - compare with current items in DB
      if (updateDto.items !== undefined) {
        await this.removeItemsFromAllTieredPricing(updateDto.items, tieredPricing.store as string);

        const currentItems = (tieredPricing.items || []).map((item) => getDocId(item));
        const newItems = updateDto.items || [];

        // Find items to remove (in current but not in new)
        const itemsToRemove = currentItems.filter((item) => !newItems.includes(item));
        if (itemsToRemove.length > 0) {
          await this.removeTieredPricingFromItems(itemsToRemove);
        }

        // Find items to add (in new but not in current)
        const itemsToAdd = newItems.filter((item) => !currentItems.includes(item));
        if (itemsToAdd.length > 0) {
          await this.updateItemsWithTieredPricing(itemsToAdd, id);
        }
      }

      // Update the tiered pricing rule
      const updated = await this.tieredPricingModel.findByIdAndUpdate(
        id,
        {
          $set: {
            ...updateDto,
            updated_at: new Date(),
          },
        },
        { new: true, runValidators: true },
      );

      this.logger.log(`Updated tiered pricing rule: ${id}`);
      return updated;
    } catch (error) {
      this.logger.error(`Error updating tiered pricing: ${error.message}`);
      throw error;
    }
  }

  /**
   * Delete a tiered pricing rule and clean up item references
   */
  async delete(id: string): Promise<void> {
    try {
      const tieredPricing = await this.tieredPricingModel.findById(id);
      if (!tieredPricing) {
        throw new NotFoundException('Tiered pricing rule not found');
      }

      // Remove tiered_pricing reference from all items in bulk
      if (tieredPricing.items && tieredPricing.items.length > 0) {
        await this.removeItemsFromTieredPricing(tieredPricing.items as string[], id);
      }

      await this.tieredPricingModel.findByIdAndDelete(id);
      this.logger.log(`Deleted tiered pricing rule: ${id}`);
    } catch (error) {
      this.logger.error(`Error deleting tiered pricing: ${error.message}`);
      throw error;
    }
  }

  /**
   * Find tiered pricing rule by ID
   */
  async findById(id: string): Promise<TieredPricing> {
    const tieredPricing = await this.tieredPricingModel.findById(id);
    if (!tieredPricing) {
      throw new NotFoundException('Tiered pricing rule not found');
    }
    return tieredPricing;
  }

  /**
   * Find all tiered pricing rules for a store
   */
  async findByStore(storeId: string): Promise<TieredPricing[]> {
    return this.tieredPricingModel.find({ store: storeId }).sort({ created_at: -1 });
  }

  /**
   * Find active tiered pricing rules for a store
   */
  async findActiveByStore(storeId: string): Promise<TieredPricing[]> {
    return this.tieredPricingModel.find({ store: storeId, active: true }).sort({ created_at: -1 });
  }

  /**
   * Assign items to a tiered pricing rule
   */
  async assignItems(id: string, assignDto: AssignItemsDto): Promise<void> {
    try {
      const tieredPricing = await this.findById(id);

      // Validate that all items exist and belong to the same store
      await this.validateItemsExist(assignDto.items, tieredPricing.store as string);

      await this.addItemsToTieredPricing(assignDto.items, id);
      await this.updateItemsWithTieredPricing(assignDto.items, id);

      this.logger.log(`Assigned ${assignDto.items.length} items to tiered pricing: ${id}`);
    } catch (error) {
      this.logger.error(`Error assigning items to tiered pricing: ${error.message}`);
      throw error;
    }
  }

  /**
   * Remove items from a tiered pricing rule
   */
  async removeItems(id: string, removeDto: RemoveItemsDto): Promise<void> {
    try {
      await this.findById(id); // Verify tiered pricing exists
      await this.removeItemsFromTieredPricing(removeDto.items, id);

      this.logger.log(`Removed ${removeDto.items.length} items from tiered pricing: ${id}`);
    } catch (error) {
      this.logger.error(`Error removing items from tiered pricing: ${error.message}`);
      throw error;
    }
  }

  /**
   * Calculate price based on quantity and tiered pricing
   */
  async calculatePrice(
    calculateDto: CalculatePriceDto,
  ): Promise<{
    original_price: number;
    discounted_price: number;
    discount_amount: number;
    discount_percentage: number;
    applied_tier?: PricingTier;
  }> {
    try {
      const tieredPricing = await this.findById(calculateDto.tiered_pricing_id);

      if (!tieredPricing.active) {
        return {
          original_price: calculateDto.base_price * calculateDto.quantity,
          discounted_price: calculateDto.base_price * calculateDto.quantity,
          discount_amount: 0,
          discount_percentage: 0,
        };
      }

      // Find the applicable tier
      const applicableTier = this.findApplicableTier(tieredPricing.tiers, calculateDto.quantity);

      if (!applicableTier) {
        return {
          original_price: calculateDto.base_price * calculateDto.quantity,
          discounted_price: calculateDto.base_price * calculateDto.quantity,
          discount_amount: 0,
          discount_percentage: 0,
        };
      }

      const originalPrice = calculateDto.base_price * calculateDto.quantity;
      const discountedPrice =
        calculateDto.base_price * (1 - applicableTier.discount_percentage / 100) * calculateDto.quantity;
      const discountAmount = originalPrice - discountedPrice;

      return {
        original_price: originalPrice,
        discounted_price: discountedPrice,
        discount_amount: discountAmount,
        discount_percentage: applicableTier.discount_percentage,
        applied_tier: applicableTier,
      };
    } catch (error) {
      this.logger.error(`Error calculating price: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get items assigned to a tiered pricing rule
   */
  async getAssignedItems(id: string): Promise<Item[]> {
    const tieredPricing = await this.findById(id);
    return this.itemModel.find({ _id: { $in: tieredPricing.items as string[] } });
  }

  /**
   * Validate tiers for business rules
   */
  private validateTiers(tiers: any[]): void {
    if (!Array.isArray(tiers) || tiers.length === 0) {
      throw new BadRequestException('At least one tier is required');
    }

    // Check for unique minimum quantities
    const quantities = tiers.map((t) => t.minimum_quantity);
    const uniqueQuantities = new Set(quantities);
    if (quantities.length !== uniqueQuantities.size) {
      throw new BadRequestException('Tiers must have unique minimum quantities');
    }

    // Check for proper ordering
    for (let i = 1; i < tiers.length; i++) {
      if (tiers[i].minimum_quantity <= tiers[i - 1].minimum_quantity) {
        throw new BadRequestException('Tiers must be ordered by minimum quantity (ascending)');
      }
    }

    // Validate individual tier properties
    tiers.forEach((tier, index) => {
      if (!Number.isInteger(tier.minimum_quantity) || tier.minimum_quantity < 1) {
        throw new BadRequestException(`Tier ${index + 1}: Minimum quantity must be a positive integer`);
      }
      if (tier.discount_percentage < 0 || tier.discount_percentage > 100) {
        throw new BadRequestException(`Tier ${index + 1}: Discount percentage must be between 0 and 100`);
      }
    });
  }

  /**
   * Find the applicable tier for a given quantity
   */
  private findApplicableTier(tiers: PricingTier[], quantity: number): PricingTier | null {
    if (!tiers || tiers.length === 0) return null;

    // Sort tiers by minimum_quantity in descending order to find the highest applicable tier
    const sortedTiers = [...tiers].sort((a, b) => b.minimum_quantity - a.minimum_quantity);

    return sortedTiers.find((tier) => quantity >= tier.minimum_quantity) || null;
  }

  /**
   * Validate that items exist and belong to the same store
   */
  private async validateItemsExist(itemIds: string[], storeId: string): Promise<void> {
    const items = await this.itemModel.find({
      _id: { $in: itemIds },
      store: storeId as any,
    });

    if (items.length !== itemIds.length) {
      throw new BadRequestException('Some items do not exist or do not belong to this store');
    }
  }

  /**
   * Assign items to tiered pricing rule (bulk operation)
   */
  async assignItemsToTieredPricing(itemIds: string[], tieredPricingId: string): Promise<void> {
    // Update items to reference this tiered pricing rule
    await this.itemModel.updateMany({ _id: { $in: itemIds } }, { $set: { tiered_pricing: tieredPricingId } });

    // Add items to the tiered pricing rule
    await this.tieredPricingModel.findByIdAndUpdate(tieredPricingId, {
      $addToSet: { items: { $each: itemIds } },
    });
  }

  async updateItemsWithTieredPricing(itemIds: string[], tieredPricingId: string): Promise<void> {
    await this.itemModel.updateMany({ _id: { $in: itemIds } }, { $set: { tiered_pricing: tieredPricingId } });
  }

  async removeTieredPricingFromItems(itemIds: string[]): Promise<void> {
    await this.itemModel.updateMany({ _id: { $in: itemIds } }, { $unset: { tiered_pricing: 1 } });
  }

  async addItemsToTieredPricing(itemIds: string[], tieredPricingId: string): Promise<TieredPricingDocument> {
    return await this.tieredPricingModel.findByIdAndUpdate(tieredPricingId, {
      $addToSet: { items: { $each: itemIds } },
    });
  }

  /**
   * Remove items from tiered pricing rule (bulk operation)
   */
  private async removeItemsFromTieredPricing(itemIds: string[], tieredPricingId: string): Promise<void> {
    // Remove tiered_pricing reference from items
    await this.itemModel.updateMany({ _id: { $in: itemIds } }, { $unset: { tiered_pricing: 1 } });

    // Remove items from the tiered pricing rule
    await this.tieredPricingModel.findByIdAndUpdate(tieredPricingId, {
      $pull: { items: { $in: itemIds } },
    });
  }

  /**
   * Remove items from any tiered pricing rules they're assigned to
   */
  async removeItemsFromAllTieredPricing(itemIds: string[], storeId: string): Promise<void> {
    try {
      if (!itemIds || itemIds.length === 0) {
        return;
      }

      // Remove items from all tiered pricing rules that contain them in a single operation
      const result = await this.tieredPricingModel.updateMany(
        { items: { $in: itemIds }, store: storeId },
        { $pull: { items: { $in: itemIds } } },
      );

      this.logger.log(`Removed ${itemIds.length} items from ${result.modifiedCount} tiered pricing rules`);
    } catch (error) {
      this.logger.error(`Error removing items from all tiered pricing: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get tiered pricing rules with pagination
   */
  async findByStorePaginated(
    storeId: string,
    page: number = 1,
    limit: number = 10,
    sort: string = 'DESC',
    active?: boolean,
  ): Promise<{ data: TieredPricing[]; total: number; page: number; limit: number }> {
    const query: any = { store: storeId };
    if (active !== undefined) {
      query.active = active;
    }

    const [data, total] = await Promise.all([
      this.tieredPricingModel
        .find(query)
        .sort({ created_at: sort === 'ASC' ? 1 : -1 })
        .skip((page - 1) * limit)
        .limit(limit),
      this.tieredPricingModel.countDocuments(query),
    ]);

    return { data, total, page, limit };
  }
}
