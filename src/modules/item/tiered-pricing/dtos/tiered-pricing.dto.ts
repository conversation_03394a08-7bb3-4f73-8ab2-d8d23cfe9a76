import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsBoolean,
  IsArray,
  IsOptional,
  ValidateNested,
  Min,
  Max,
  IsInt,
  ArrayMinSize,
  Validate,
  IsMongoId,
} from 'class-validator';
import { Type } from 'class-transformer';

export class PricingTierDto {
  @ApiProperty({
    description: 'Minimum quantity for this tier',
    example: 5,
    minimum: 1,
  })
  @IsInt()
  @Min(1, { message: 'Minimum quantity must be at least 1' })
  minimum_quantity: number;

  @ApiProperty({
    description: 'Discount percentage for this tier',
    example: 10,
    minimum: 0,
    maximum: 100,
  })
  @Min(0, { message: 'Discount percentage must be at least 0' })
  @Max(100, { message: 'Discount percentage cannot exceed 100' })
  discount_percentage: number;
}

export class CreateTieredPricingDto {
  @ApiProperty({
    description: 'Label for the tiered pricing rule',
    example: 'Bulk Discount',
    maxLength: 100,
  })
  @IsString()
  label: string;

  @ApiProperty({
    description: 'Whether the tiered pricing rule is active',
    example: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  active?: boolean;

  @ApiProperty({
    type: [PricingTierDto],
    description: 'Array of pricing tiers',
    example: [
      { minimum_quantity: 5, discount_percentage: 10 },
      { minimum_quantity: 10, discount_percentage: 15 },
    ],
  })
  @IsArray()
  @ArrayMinSize(1, { message: 'At least one tier is required' })
  @ValidateNested({ each: true })
  @Type(() => PricingTierDto)
  tiers: PricingTierDto[];

  @ApiProperty({
    type: [String],
    description: 'Array of item IDs to assign to this tiered pricing rule',
    required: false,
    example: ['507f1f77bcf86cd799439011', '507f1f77bcf86cd799439012'],
  })
  @IsArray()
  @IsOptional()
  @IsMongoId({ each: true, message: 'Each ID must be a valid product ID' })
  items?: string[];
}

export class UpdateTieredPricingDto {
  @ApiProperty({
    description: 'Label for the tiered pricing rule',
    example: 'Bulk Discount',
    maxLength: 100,
    required: false,
  })
  @IsString()
  @IsOptional()
  label?: string;

  @ApiProperty({
    description: 'Whether the tiered pricing rule is active',
    example: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  active?: boolean;

  @ApiProperty({
    type: [PricingTierDto],
    description: 'Array of pricing tiers',
    example: [
      { minimum_quantity: 5, discount_percentage: 10 },
      { minimum_quantity: 10, discount_percentage: 15 },
    ],
    required: false,
  })
  @IsArray()
  @IsOptional()
  @ArrayMinSize(1, { message: 'At least one tier is required' })
  @ValidateNested({ each: true })
  @Type(() => PricingTierDto)
  tiers?: PricingTierDto[];

  @ApiProperty({
    type: [String],
    description: 'Array of item IDs assigned to this tiered pricing rule',
    required: false,
    example: ['507f1f77bcf86cd799439011', '507f1f77bcf86cd799439012'],
  })
  @IsArray()
  @IsOptional()
  @IsMongoId({ each: true, message: 'Each ID must be a valid product ID' })
  items?: string[];
}

export class AssignItemsDto {
  @ApiProperty({
    type: [String],
    description: 'Array of item IDs to assign to this tiered pricing rule',
    example: ['507f1f77bcf86cd799439011', '507f1f77bcf86cd799439012'],
  })
  @IsArray()
  @IsMongoId({ each: true, message: 'Each ID must be a valid product ID' })
  items: string[];
}

export class RemoveItemsDto {
  @ApiProperty({
    type: [String],
    description: 'Array of item IDs to remove from this tiered pricing rule',
    example: ['507f1f77bcf86cd799439011', '507f1f77bcf86cd799439012'],
  })
  @IsArray()
  @IsMongoId({ each: true, message: 'Each ID must be a valid product ID' })
  items: string[];
}

export class CalculatePriceDto {
  @ApiProperty({
    description: 'Base price of the item',
    example: 100,
  })
  @Min(0, { message: 'Base price must be non-negative' })
  base_price: number;

  @ApiProperty({
    description: 'Quantity to calculate price for',
    example: 5,
  })
  @IsInt()
  @Min(1, { message: 'Quantity must be at least 1' })
  quantity: number;

  @ApiProperty({
    description: 'Tiered pricing rule ID',
    example: '507f1f77bcf86cd799439011',
  })
  @IsMongoId({ message: 'ID must be a valid pricing ID' })
  tiered_pricing_id: string;
}
