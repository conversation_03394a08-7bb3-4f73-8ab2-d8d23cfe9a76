import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  UseGuards,
  Req,
  Res,
  BadRequestException,
  UseInterceptors,
  UploadedFile,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiConsumes, ApiBody, ApiSecurity, ApiExcludeEndpoint } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../jwt/jwt-auth.guard';
import { IRequest, IResponse } from '../../interfaces/request.interface';
import { ItemExportImportService } from './item-export-import.service';
import { Readable } from 'stream';
import { InternalApiJWTGuard } from '../../guards/api.guard';
import { SCOPES } from '../../utils/permissions.util';
import { PlanGuard } from '../../guards/plan.guard';
import { PlanPermissions } from '../../decorators/permission.decorator';

@ApiTags('Item Export Import')
@Controller('item-files')
export class ItemExportImportController {
  constructor(private readonly itemExportImportService: ItemExportImportService) {}

  @Get('export')
  @PlanPermissions(SCOPES.PRODUCTS.EXPORT_PRODUCTS)
  @UseGuards(JwtAuthGuard, PlanGuard)
  @ApiSecurity('bearer')
  async exportItems(@Req() req: IRequest, @Res() res: IResponse, @Query() query: { items?: string }) {
    try {
      const items = query.items === 'all' ? 'all' : query.items.split(',');
      const buffer = await this.itemExportImportService.exportItems(req.user.store.id, items);

      // Set response headers
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', 'attachment; filename=items-export.xlsx');

      // Create a readable stream from the buffer
      const stream = new Readable();
      stream._read = () => {};
      stream.push(buffer);
      stream.push(null);

      // Pipe the stream to the response
      stream.pipe(res);

      return {};
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Post('import')
  @PlanPermissions(SCOPES.PRODUCTS.IMPORT_PRODUCTS)
  @UseGuards(JwtAuthGuard, PlanGuard)
  @ApiSecurity('bearer')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  @HttpCode(HttpStatus.OK)
  async importItems(@UploadedFile() file: Express.Multer.File, @Req() req: IRequest) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    try {
      const results = await this.itemExportImportService.preProcessImport(req.user.store.id, file.buffer, req.user);

      if (!results.success) {
        return {
          success: false,
          message: 'Validation failed',
          errors: results.errors,
        };
      }

      return {
        success: true,
        message: results.message,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Post('migrate-item-image-urls')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiExcludeEndpoint()
  async updateItemImageUrls() {
    // This is just a placeholder to match the pattern in the codebase
    return {
      message: 'Migration process started successfully',
    };
  }
}
