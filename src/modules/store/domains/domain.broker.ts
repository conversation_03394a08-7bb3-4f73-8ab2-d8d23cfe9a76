import { Controller } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { DomainService } from './domain.service';
import { SkipThrottle } from '@nestjs/throttler';
import { PAYMENT_METHODS } from '../../../enums/payment.enum';

@SkipThrottle()
@Controller()
export class DomainBroker {
  constructor(private readonly domainService: DomainService) {}

  @MessagePattern(BROKER_PATTERNS.DOMAIN.CHECK_AVAILABILITY)
  async checkDomainAvailability({ domain, country }) {
    return this.domainService.checkDomainAvailability(domain, country);
  }

  @MessagePattern(BROKER_PATTERNS.DOMAIN.INITIATE_PURCHASE)
  async initiateDomainPurchase({ storeId, domain, contactInfo, nameservers }) {
    return this.domainService.initiateDomainPurchase(storeId, domain, contactInfo, nameservers);
  }

  @MessagePattern(BROKER_PATTERNS.DOMAIN.COMPLETE_PURCHASE)
  async completeDomainPurchase({ purchaseId, paymentId, nameservers }) {
    return this.domainService.completeDomainPurchase(purchaseId, paymentId, nameservers);
  }

  @MessagePattern(BROKER_PATTERNS.DOMAIN.GET_PURCHASES)
  async getDomainPurchases({ storeId }) {
    return this.domainService.getDomainPurchases(storeId);
  }

  @MessagePattern(BROKER_PATTERNS.DOMAIN.GET_PURCHASE)
  async getDomainPurchase({ purchaseId }) {
    return this.domainService.getDomainPurchase(purchaseId);
  }

  @MessagePattern(BROKER_PATTERNS.DOMAIN.CREATE_PAYMENT)
  async createDomainPurchasePayment({ purchaseId, paymentMethods, storeId }) {
    return this.domainService.createDomainPurchasePayment(purchaseId, paymentMethods, storeId);
  }

  @MessagePattern(BROKER_PATTERNS.DOMAIN.QUEUE_DNS_SETUP)
  async queueDomainDnsSetup({ domainId }) {
    return this.domainService.queueDomainDnsSetup(domainId);
  }
}
