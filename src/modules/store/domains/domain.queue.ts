import { Process, Processor, OnQueueActive, OnQueueCompleted, OnQueueFailed } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Job } from 'bull';
import { Model } from 'mongoose';
import { Go54Repository } from '../../../repositories/go54.repository';
import { DomainnameapiRepository } from '../../../repositories/domainnameapi.repository';
import { QUEUES, JOBS } from '../../../enums/queues.enum';
import { StoreDomain, StoreDomainDocument } from './store-domain.schema';
import { StoreService } from '../services/store.service';

// Enum to select domain provider (matching domain.service.ts)
enum DOMAIN_PROVIDER {
  GO54 = 'go54',
  DOMAINNAMEAPI = 'domainnameapi',
}

interface DomainDnsSetupJob {
  domainId: string;
  attempts: number;
  dnsSetupComplete?: boolean;
}

@Processor(QUEUES.DOMAIN)
@Injectable()
export class DomainQueue {
  private readonly logger = new Logger(DomainQueue.name);
  private readonly currentProvider: DOMAIN_PROVIDER;

  constructor(
    @InjectModel(StoreDomain.name) private readonly storeDomainModel: Model<StoreDomainDocument>,
    private readonly go54Repository: Go54Repository,
    private readonly domainnameapiRepository: DomainnameapiRepository,
    private readonly storeService: StoreService,
    private readonly configService: ConfigService,
  ) {
    // Set the current provider based on environment variable or default to domainnameapi
    this.currentProvider =
      process.env.DOMAIN_PROVIDER === 'go54' ? DOMAIN_PROVIDER.GO54 : DOMAIN_PROVIDER.DOMAINNAMEAPI;
    this.logger.log(`Domain Queue using provider: ${this.currentProvider}`);
  }

  /**
   * Get the current domain repository based on configuration
   */
  private getCurrentDomainRepository() {
    switch (this.currentProvider) {
      case DOMAIN_PROVIDER.GO54:
        return this.go54Repository;
      case DOMAIN_PROVIDER.DOMAINNAMEAPI:
        return this.domainnameapiRepository;
      default:
        return this.domainnameapiRepository; // Default fallback
    }
  }

  @OnQueueActive()
  onActive(job: Job) {
    this.logger.debug(`Processing job ${job.id} of type ${job.name}`);
  }

  @OnQueueFailed()
  onFailed(job: Job, error: Error) {
    this.logger.error(`Failed to process job ${job.id} of type ${job.name}: ${error.message}`, error.stack);
  }

  @OnQueueCompleted()
  onCompleted(job: Job) {
    this.logger.debug(`Completed job ${job.id} of type ${job.name}`);
  }

  @Process({ name: JOBS.DOMAIN_DNS_SETUP, concurrency: 1 })
  async handleDomainDnsSetup(
    job: Job<DomainDnsSetupJob>,
  ): Promise<{
    success: boolean;
    message: string;
    dnsResult?: any;
  }> {
    const { domainId, attempts, dnsSetupComplete } = job.data;
    const MAX_ATTEMPTS = 10;

    try {
      // Get domain document
      const domain = await this.storeDomainModel.findById(domainId);
      if (!domain) {
        this.logger.error(`Domain with ID ${domainId} not found`);
        return { success: false, message: 'Domain not found' };
      }

      this.logger.log(`Processing domain ${domain.domain}, attempt ${attempts + 1}`);

      // Step 1: DNS setup (skip if already completed)
      if (!dnsSetupComplete) {
        this.logger.log(`Setting up DNS for domain ${domain.domain}`);

        const serverIp = process.env.SERVER_IP;
        if (!serverIp) {
          throw new Error('SERVER_IP environment variable not set');
        }

        // Add A record
        let dnsResult;
        if (this.currentProvider === DOMAIN_PROVIDER.GO54) {
          // DNS update is only available for Go54 provider
          dnsResult = await this.go54Repository.updateDnsRecord(
            domain.domain,
            'A',
            '@', // Root domain
            14400, // TTL: 4 hours
          );
        } else {
          // For other providers, we'll assume DNS is managed externally or use a different approach
          this.logger.log(`DNS update not implemented for ${this.currentProvider} provider, skipping...`);
          dnsResult = { data: { status: true, message: 'DNS setup skipped for this provider' } };
        }

        if (dnsResult.error) {
          this.logger.error(`Failed to set up DNS A record: ${dnsResult.error}`);

          // Retry logic for DNS setup
          if (attempts < MAX_ATTEMPTS) {
            await job.queue.add(
              JOBS.DOMAIN_DNS_SETUP,
              {
                domainId,
                attempts: attempts + 1,
                dnsSetupComplete: false,
              },
              {
                delay: 30 * 60 * 1000, // 30 minutes delay
                attempts: 1,
                backoff: {
                  type: 'exponential',
                  delay: 30 * 60 * 1000,
                },
              },
            );
            return { success: false, message: 'DNS setup failed, retrying later' };
          }

          return { success: false, message: 'Failed to set up DNS A record after multiple attempts' };
        }

        this.logger.log(`Successfully added A record for ${domain.domain}`);
      } else {
        this.logger.log(`DNS already set up for ${domain.domain}, skipping to certificate generation`);
      }

      // Step 2: Certificate generation
      const certificateResult = await this.storeService.generateSslCertificate(domainId);

      if (!certificateResult.success) {
        this.logger.warn(`SSL certificate generation not successful: ${certificateResult.message}`);

        // Retry logic specifically for certificate generation
        if (attempts < MAX_ATTEMPTS) {
          await job.queue.add(
            JOBS.DOMAIN_DNS_SETUP,
            {
              domainId,
              attempts: attempts + 1,
              dnsSetupComplete: true, // DNS is already set up
            },
            {
              delay: 60 * 60 * 1000, // 1 hour delay for SSL
              attempts: 1,
              backoff: {
                type: 'exponential',
                delay: 60 * 60 * 1000,
              },
            },
          );
          return { success: false, message: 'SSL certificate generation failed, retrying later' };
        }

        return {
          success: false,
          message: 'DNS setup successful but SSL certificate generation failed after multiple attempts',
        };
      }

      // Success! Update domain status
      this.logger.log(`Successfully generated SSL certificate for ${domain.domain}`);
      domain.certificate_issued = true;
      domain.certificate_issued_at = new Date();
      await domain.save();

      return {
        success: true,
        message: 'Domain DNS and SSL certificate setup completed successfully',
      };
    } catch (error) {
      this.logger.error(`Error in domain DNS setup: ${error.message}`, error.stack);

      // Retry logic
      if (attempts < MAX_ATTEMPTS) {
        // Re-queue the job with increased attempt count - preserve DNS setup status
        await job.queue.add(
          JOBS.DOMAIN_DNS_SETUP,
          {
            domainId,
            attempts: attempts + 1,
            dnsSetupComplete: job.data.dnsSetupComplete, // Preserve DNS setup status
          },
          {
            delay: 30 * 60 * 1000, // 30 minutes delay
            attempts: 1,
            backoff: {
              type: 'exponential',
              delay: 30 * 60 * 1000,
            },
          },
        );
      }

      return {
        success: false,
        message: `Error in domain setup: ${error.message}`,
      };
    }
  }
}
