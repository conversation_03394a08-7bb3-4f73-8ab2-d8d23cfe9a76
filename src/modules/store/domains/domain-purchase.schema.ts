import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { Store } from '../store.schema';
import { CURRENCIES } from '../../country/country.schema';
import { User } from '../../user/user.schema';

export type DomainPurchaseDocument = DomainPurchase & Document;

export enum DOMAIN_PURCHASE_STATUS {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
}

@Schema({ timestamps: true })
export class DomainPurchase {
  @Prop({ type: String, required: true })
  domain: string;

  @Prop({ type: Types.ObjectId, ref: 'Store', required: true })
  store: string | Store;

  @Prop({ type: String, enum: Object.values(DOMAIN_PURCHASE_STATUS), default: DOMAIN_PURCHASE_STATUS.PENDING })
  status: DOMAIN_PURCHASE_STATUS;

  @Prop({ type: Number, required: true })
  amount: number;

  @Prop({ type: String, enum: Object.values(CURRENCIES), default: CURRENCIES.NGN })
  currency: CURRENCIES;

  @Prop({ type: Types.ObjectId, ref: 'Payment' })
  payment_id?: string;

  @Prop({ type: String })
  registration_id?: string;

  @Prop({ type: Date })
  expires_at?: Date;

  @Prop({ type: [String] })
  nameservers?: string[];

  @Prop({ type: Types.ObjectId, ref: 'User' })
  owner: string | User;

  @Prop({ type: Object })
  meta?: Record<string, any>;
}

export const DomainPurchaseSchema = SchemaFactory.createForClass(DomainPurchase);
