import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { businessTLDs, catlogInfo, Go54Repository, UserInfo } from '../../../repositories/go54.repository';
import { DomainnameapiRepository } from '../../../repositories/domainnameapi.repository';
import { getDocId, toNaira } from '../../../utils/functions';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { PAYMENT_METHODS, PAYMENT_STATUS, PAYMENT_TYPES } from '../../../enums/payment.enum';
import { StoreDomain, StoreDomainDocument } from './store-domain.schema';
import { DomainPurchase, DomainPurchaseDocument, DOMAIN_PURCHASE_STATUS } from './domain-purchase.schema';
import { Store } from '../store.schema';
import { COUNTRY_CODE, COUNTRY_CURRENCY_MAP, CURRENCIES } from '../../country/country.schema';
import { HostApiRepository } from '../../../repositories/host-api.repository';
import { QUEUES, JOBS } from '../../../enums/queues.enum';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { CacheService } from '../../shared/cache/cache.service';
import { DOMAIN_PURCHASE_MARKUPS } from '../../../utils/constants';
import { formatDate } from '../../../utils/time';
import { toCurrency } from '../../../utils';
import { ResendRepository } from '../../../repositories/resend.repository';
import { SlackRepository } from '../../../repositories/slack.respository';

// Enum to select domain provider
enum DOMAIN_PROVIDER {
  GO54 = 'go54',
  DOMAINNAMEAPI = 'domainnameapi',
}

@Injectable()
export class DomainService {
  // Default TLDs array with common options
  private readonly ratesCacheKey = 'domain_rates';
  private readonly currentProvider: DOMAIN_PROVIDER;

  constructor(
    @InjectModel(StoreDomain.name) private readonly storeDomainModel: Model<StoreDomainDocument>,
    @InjectModel(DomainPurchase.name) private readonly domainPurchaseModel: Model<DomainPurchaseDocument>,
    private readonly go54Repository: Go54Repository,
    private readonly domainnameapiRepository: DomainnameapiRepository,
    private readonly hostApiRepository: HostApiRepository,
    private readonly resend: ResendRepository,
    private readonly brokerTransport: BrokerTransportService,
    private readonly cacheService: CacheService,
    private readonly logger: Logger,
    @InjectQueue(QUEUES.DOMAIN) private readonly domainQueue: Queue,
    private readonly slackRepository: SlackRepository,
  ) {
    this.logger.setContext('DomainService');

    // Set the current provider based on environment variable or default to domainnameapi
    this.currentProvider =
      process.env.DOMAIN_PROVIDER === 'go54' ? DOMAIN_PROVIDER.GO54 : DOMAIN_PROVIDER.DOMAINNAMEAPI;
    this.logger.log(`Using domain provider: ${this.currentProvider}`);
  }

  /**
   * Get the current domain repository based on configuration
   */
  private getCurrentDomainRepository() {
    switch (this.currentProvider) {
      case DOMAIN_PROVIDER.GO54:
        return this.go54Repository;
      case DOMAIN_PROVIDER.DOMAINNAMEAPI:
        return this.domainnameapiRepository;
      default:
        return this.domainnameapiRepository; // Default fallback
    }
  }

  /**
   * Apply minimum price for Domainnameapi domains
   * @param price Original price from provider
   * @param sourceCurrency Source currency of the price
   * @returns Adjusted price with minimum applied
   */
  private applyMinimumPrice(price: number, sourceCurrency: CURRENCIES): number {
    // Only apply minimum price for Domainnameapi (USD pricing)
    if (this.currentProvider === DOMAIN_PROVIDER.DOMAINNAMEAPI && sourceCurrency === CURRENCIES.USD) {
      const MINIMUM_USD_PRICE = 4; // $4 USD minimum
      if (price < MINIMUM_USD_PRICE) {
        this.logger.log(`Domain price ${price} USD is below minimum. Adjusting to ${MINIMUM_USD_PRICE} USD`);
        return MINIMUM_USD_PRICE;
      }
    }
    return price;
  }

  /**
   * Check domain availability and pricing through the configured provider
   */
  async checkDomainAvailability(domain: string, storeCountry: COUNTRY_CODE) {
    // Normalize the domain
    domain = this.normalizeDomain(domain);

    // Validate domain format
    if (!this.validateDomainFormat(domain)) {
      throw new BadRequestException('Invalid domain format');
    }

    const domainRepository = this.getCurrentDomainRepository();
    const result = await domainRepository.checkDomainAvailability(domain, storeCountry);

    if (result.error) {
      this.logger.error(`Failed to check domain availability: ${JSON.stringify(result.error)}`);
      throw new BadRequestException(result.error);
    }

    // Determine source currency based on provider
    const sourceCurrency = this.currentProvider === DOMAIN_PROVIDER.DOMAINNAMEAPI ? CURRENCIES.USD : CURRENCIES.NGN;
    const targetCurrency = COUNTRY_CURRENCY_MAP[storeCountry];

    let { recommended, alternatives } = await result.data.alternatives
      .filter((alt) => alt.available)
      .sort((a, b) => a.price - b.price)
      .reduce(async (accPromise, curr) => {
        const acc = await accPromise;

        // Apply minimum price before conversion
        const adjustedPrice = this.applyMinimumPrice(curr.price, sourceCurrency);
        const convertedPrice = await this.convertDomainPriceToStoreCurrency(
          adjustedPrice,
          sourceCurrency,
          targetCurrency,
        );

        const domainEntry = {
          ...curr,
          price: convertedPrice,
          currency: targetCurrency,
        };

        if (businessTLDs.some((suffix) => curr.domain.endsWith(suffix))) {
          acc.recommended.push(domainEntry);
        } else {
          acc.alternatives.push(domainEntry);
        }

        return acc;
      }, Promise.resolve({ recommended: [], alternatives: [] }));

    // Apply minimum price to main domain before conversion
    const adjustedMainPrice = this.applyMinimumPrice(result.data.price, sourceCurrency);
    const price = await this.convertDomainPriceToStoreCurrency(adjustedMainPrice, sourceCurrency, targetCurrency);

    return {
      domain: result.data.domain,
      available: result.data.available,
      price,
      currency: targetCurrency,
      message: result.data.message,
      alternatives,
      recommended,
    };
  }

  /**
   * Initiate domain purchase process
   */
  async initiateDomainPurchase(storeId: string, domain: string, contactInfo?: any, nameservers?: string[]) {
    // Normalize the domain
    domain = this.normalizeDomain(domain);

    // Validate domain format
    if (!this.validateDomainFormat(domain)) {
      throw new BadRequestException('Invalid domain format');
    }

    // Check if domain is available for purchase - use efficient single domain check
    const availabilityResult = await this.getCurrentDomainRepository().checkSingleDomain(domain);

    if (availabilityResult.error) {
      this.logger.error(`Failed to check domain availability: ${JSON.stringify(availabilityResult.error)}`);
      throw new BadRequestException('Failed to check domain availability');
    }

    if (!availabilityResult.data.available) {
      throw new BadRequestException(`Domain ${domain} is not available for purchase`);
    }

    // Get store details including owner information
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
      .toPromise();

    if (!store) {
      throw new NotFoundException('Store not found');
    }

    // Determine source currency based on provider
    const sourceCurrency = this.currentProvider === DOMAIN_PROVIDER.DOMAINNAMEAPI ? CURRENCIES.USD : CURRENCIES.NGN;

    // Apply minimum price before conversion
    const adjustedPrice = this.applyMinimumPrice(availabilityResult.data.price, sourceCurrency);
    const amount = await this.convertDomainPriceToStoreCurrency(
      adjustedPrice,
      sourceCurrency,
      store.currencies.default,
    );

    // Create a domain purchase record
    const domainPurchase = await this.domainPurchaseModel.create({
      domain,
      store: storeId,
      status: DOMAIN_PURCHASE_STATUS.PENDING,
      amount,
      currency: store.currencies.default,
      nameservers: nameservers,
      owner: getDocId(store.owner),
    });

    return domainPurchase;
  }

  /**
   * Complete domain purchase after payment
   */
  async completeDomainPurchase(
    purchaseId: string,
    paymentId?: string,
    nameservers?: string[],
    fromBroker: boolean = true,
  ) {
    const domainPurchase = await this.domainPurchaseModel.findById(purchaseId);

    if (!domainPurchase) {
      this.handleError('Domain purchase record not found', fromBroker);
    }

    if (!paymentId && !domainPurchase.payment_id) {
      this.handleError('Payment ID is required', fromBroker);
    }

    if (!paymentId) {
      paymentId = domainPurchase.payment_id;
    }

    // Get payment details to verify payment was successful
    const payment = await this.brokerTransport
      .send(BROKER_PATTERNS.PAYMENT.GET_PAYMENT, { _id: paymentId })
      .toPromise();

    if (!payment || payment.status !== PAYMENT_STATUS.SUCCESS) {
      this.handleError('Payment was not successful', fromBroker);
    }

    // Get owner details
    const owner = await this.brokerTransport
      .send<any>(BROKER_PATTERNS.USER.GET_USER, { _id: getDocId(domainPurchase.owner) })
      .toPromise();

    if (!owner) {
      this.handleError('We could not find the record of the user tied to this domain purchase', fromBroker);
    }

    // Get store details for Slack notification
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: getDocId(domainPurchase.store) })
      .toPromise();

    if (!store) {
      this.handleError('Store not found', fromBroker);
    }

    // // Prepare contact info from store and owner details
    const ownerName = owner.name.split(' ');
    const firstName = ownerName[0] || '';
    const lastName = ownerName.length > 1 ? ownerName.slice(1).join(' ') : '';

    const contactInfo: UserInfo = {
      firstName: firstName,
      lastName: lastName || firstName, // Use firstName as lastName if lastName is empty
      email: owner.email,
      phone: owner.phone.replace('-', ''),
    };

    // Register the domain with Go54/Domainnameapi
    const registrationResult = await this.getCurrentDomainRepository().purchaseDomain(
      domainPurchase.domain,
      contactInfo,
      nameservers,
    );

    // Send Slack notification for Domainnameapi purchases (requires manual DNS setup)
    if (this.currentProvider === DOMAIN_PROVIDER.DOMAINNAMEAPI) {
      try {
        await this.slackRepository.sendDomainDnsSetupNotification({
          name: owner.name,
          country: store.country.toString(),
          store_name: store.name,
          store_slug: store.slug,
          whatsapp: owner.phone,
          domain: domainPurchase.domain,
          amount: toCurrency(toNaira(payment.amount), payment.currency),
          currency: payment.currency,
          provider: this.currentProvider,
          payment_reference: payment.reference,
          // registration_id: registrationResult.data.registrationId,
          // expires_at: registrationResult.data.expiresAt,
        });
        this.logger.log(`DNS setup notification sent for domain ${domainPurchase.domain} via Domainnameapi`);
      } catch (error) {
        this.logger.error(`Failed to send DNS setup notification: ${error.message}`, error.stack);
        // Don't fail the entire process if Slack notification fails
      }
    }

    if (registrationResult.error) {
      this.logger.error(`Failed to register domain: ${JSON.stringify(registrationResult.error)}`);

      // Update the domain purchase status to failed
      await this.domainPurchaseModel.findByIdAndUpdate(purchaseId, {
        status: DOMAIN_PURCHASE_STATUS.COMPLETED,
        payment_id: paymentId,
        meta: {
          error: registrationResult.error,
        },
      });

      // this.handleError(registrationResult.error ?? "Couldn't register domain please try again later", fromBroker);
    } else {
      // Update the domain purchase record
      await this.domainPurchaseModel.findByIdAndUpdate(purchaseId, {
        status: DOMAIN_PURCHASE_STATUS.COMPLETED,
        payment_id: paymentId,
        registration_id: registrationResult.data.registrationId,
        expires_at: new Date(registrationResult.data.expiresAt),
      });
    }

    // Add the domain to the store's domains
    const verificationCode = `catlog-verify=${this.generateRandomString(16)}`;

    const domainDoc = await this.storeDomainModel.create({
      domain: domainPurchase.domain,
      store_id: new Types.ObjectId(getDocId(domainPurchase.store)),
      verification_code: verificationCode,
      verified: false,
      certificate_issued: false,
    });

    // Queue domain DNS and certificate setup
    if (!registrationResult.error) {
      this.queueDomainDnsSetup(getDocId(domainDoc));
    }

    //Send Email
    await this.resend.sendEmail(BROKER_PATTERNS.MAIL.DOMAIN_PURCHASE_SUCCESSFUL, {
      to: owner.email,
      subject: 'Your domain purchase was successful ✅',
      data: {
        name: owner.name.split(' ')[0],
        store_name: store.name,
        domain: domainPurchase.domain,
        purchase_date: formatDate(new Date()),
        preview_text: 'Your domain purchase was successful!',
      },
    });

    return {
      domain_purchase: domainPurchase,
      domain: domainDoc,
      registration_response: registrationResult.data,
    };
  }

  /**
   * Get all domain purchases for a store
   */
  async getDomainPurchases(storeId: string) {
    return this.domainPurchaseModel.find({ store_id: storeId }).sort({ createdAt: -1 }).exec();
  }

  /**
   * Get a single domain purchase
   */
  async getDomainPurchase(purchaseId: string) {
    return this.domainPurchaseModel.findById(purchaseId).exec();
  }

  /**
   * Create payment for domain purchase
   */
  async createDomainPurchasePayment(purchaseId: string, paymentMethods: PAYMENT_METHODS[], storeId: string) {
    const domainPurchase = await this.domainPurchaseModel.findById(purchaseId);

    if (!domainPurchase) {
      throw new NotFoundException('Domain purchase record not found');
    }

    if (domainPurchase.status !== DOMAIN_PURCHASE_STATUS.PENDING) {
      throw new BadRequestException('This domain purchase is not in pending status');
    }

    // Get store details
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
      .toPromise();

    if (!store) {
      throw new NotFoundException('Store not found');
    }

    // Convert amount to store's currency if different from domain purchase currency
    let amount = domainPurchase.amount;
    let currency = domainPurchase.currency;

    if (store.currencies && store.currencies.default !== domainPurchase.currency) {
      try {
        const convertedAmount = await this.convertDomainPriceToStoreCurrency(
          domainPurchase.amount,
          domainPurchase.currency,
          store.currencies.default,
        );
        amount = convertedAmount;
        currency = store.currencies.default;
        this.logger.log(
          `Converted domain price from ${domainPurchase.amount} ${domainPurchase.currency} to ${amount} ${currency}`,
        );
      } catch (error) {
        this.logger.error(`Failed to convert currency: ${error.message}`, error.stack);
        // If conversion fails, use original currency and amount
      }
    }

    // Create payment using the payment service
    const paymentData = {
      type: PAYMENT_TYPES.DOMAIN_PURCHASE,
      payment_methods: paymentMethods,
      currency: currency,
      amount: amount,
      domain_purchase: getDocId(domainPurchase),
    };

    const paymentResult = await this.brokerTransport
      .send(BROKER_PATTERNS.PAYMENT.CREATE_PAYMENT_FOR_DOMAIN, { body: paymentData, storeId })
      .toPromise();

    return paymentResult;
  }

  /**
   * Convert domain price from one currency to another using currency rates
   * @param amount The amount to convert
   * @param fromCurrency The source currency (usually USD for Domainnameapi, NGN for Go54)
   * @param toCurrency The target currency (store's currency)
   * @returns Converted amount in target currency
   */
  private async convertDomainPriceToStoreCurrency(
    amount: number,
    fromCurrency: CURRENCIES,
    toCurrency: CURRENCIES,
  ): Promise<number> {
    if (fromCurrency === toCurrency) {
      return amount + toNaira(DOMAIN_PURCHASE_MARKUPS[toCurrency]); // No conversion needed
    }

    try {
      // First get rates for the base currency (fromCurrency) using broker pattern
      let rates = await this.cacheService.get(this.ratesCacheKey);

      if (!rates) {
        rates = await this.brokerTransport
          .send(BROKER_PATTERNS.WALLET.GET_CURRENCY_RATES_FOR_CURRENCY, { baseCurrency: fromCurrency })
          .toPromise();
        await this.cacheService.setWithLock(this.ratesCacheKey, rates, 30 * 60); // 30 minutes
      }

      if (!rates || !rates[toCurrency]) {
        // If direct conversion rate not available, try using USD as intermediate
        if (fromCurrency !== CURRENCIES.USD && toCurrency !== CURRENCIES.USD) {
          const usdToFromRate = await this.getExchangeRate(CURRENCIES.USD, fromCurrency);
          const usdToToRate = await this.getExchangeRate(CURRENCIES.USD, toCurrency);

          if (usdToFromRate && usdToToRate) {
            // Convert from source currency to USD, then USD to target currency
            const amountInUsd = amount / usdToFromRate;
            return amountInUsd * usdToToRate + toNaira(DOMAIN_PURCHASE_MARKUPS[toCurrency]);
          }
        }

        throw new Error(`Currency conversion rate not available from ${fromCurrency} to ${toCurrency}`);
      }

      // Direct conversion from fromCurrency to toCurrency
      return amount * rates[toCurrency] + toNaira(DOMAIN_PURCHASE_MARKUPS[toCurrency]);
    } catch (error) {
      this.logger.error(`Currency conversion error: ${error.message}`, error.stack);
      throw new Error(`Failed to convert currency: ${error.message}`);
    }
  }

  /**
   * Helper method to get exchange rate between two currencies using broker pattern
   */
  private async getExchangeRate(fromCurrency: CURRENCIES, toCurrency: CURRENCIES): Promise<number | null> {
    try {
      const rates = await this.brokerTransport
        .send(BROKER_PATTERNS.WALLET.GET_CURRENCY_RATES_FOR_CURRENCY, { baseCurrency: fromCurrency })
        .toPromise();
      return rates[toCurrency] || null;
    } catch (error) {
      this.logger.warn(`Could not get exchange rate from ${fromCurrency} to ${toCurrency}: ${error.message}`);
      return null;
    }
  }

  /**
   * Normalize domain by removing protocol, www prefix and trailing slashes
   */
  private normalizeDomain(domain: string | any): string {
    if (!domain || typeof domain !== 'string') {
      throw new BadRequestException('Invalid domain format: domain must be a string');
    }

    // Remove protocol
    let normalizedDomain = domain.replace(/^https?:\/\//, '');

    // Remove www. prefix
    normalizedDomain = normalizedDomain.replace(/^www\./, '');

    // Remove trailing slash
    normalizedDomain = normalizedDomain.replace(/\/$/, '');

    // Check if domain already has a TLD
    const hasTld = /\.[a-zA-Z]{2,}$/.test(normalizedDomain);

    // If no TLD is present, append the first default TLD
    if (!hasTld) {
      normalizedDomain = normalizedDomain + '.store';
    }

    // Validate the domain format after normalization
    if (!this.validateDomainFormat(normalizedDomain)) {
      throw new BadRequestException('Invalid domain format after normalization');
    }

    return normalizedDomain.toLowerCase();
  }

  /**
   * Validate domain format
   */
  private validateDomainFormat(domain: string): boolean {
    // Basic domain format validation
    const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z]{2,})+$/;
    return domainRegex.test(domain);
  }

  /**
   * Generate a random string for verification code
   */
  private generateRandomString(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * Test Go54 login functionality
   */
  async testGo54Login() {
    try {
      // This method is specific to Go54Repository
      const loginResult = await this.go54Repository.login();

      if (loginResult.error) {
        this.logger.error(`Go54 login failed: ${JSON.stringify(loginResult.error)}`);
        return { error: loginResult.error };
      }

      return { data: loginResult.data };
    } catch (error) {
      this.logger.error(`Exception during Go54 login: ${error.message}`, error.stack);
      return {
        error: {
          message: error.message,
        },
      };
    }
  }

  /**
   * Queue domain DNS setup and certificate generation
   * @param domainId ID of the StoreDomain document
   */
  async queueDomainDnsSetup(domainId: string) {
    // Get domain details
    const domain = await this.storeDomainModel.findById(domainId);

    if (!domain) {
      throw new NotFoundException('Domain not found');
    }

    // Add job to queue for DNS setup and certificate generation
    await this.domainQueue.add(
      JOBS.DOMAIN_DNS_SETUP,
      {
        domainId: getDocId(domain),
        attempts: 0,
        dnsSetupComplete: false, // DNS setup needs to be done initially
      },
      {
        delay: 15 * 60 * 1000, // 15 minutes delay
        attempts: 5,
        backoff: {
          type: 'exponential',
          delay: 30 * 60 * 1000, // 30 minutes between retries
        },
      },
    );

    this.logger.log(`Queued DNS setup for domain ${domain.domain} with ID ${domainId}`);
    return { success: true, message: 'Domain DNS setup queued successfully' };
  }

  handleError(message: string, fromBroker: boolean = true) {
    if (fromBroker) {
      return { error: message };
    } else {
      throw new BadRequestException(message);
    }
  }
}
