import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import mongoose, { Model } from 'mongoose';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { IRequest } from '../../../interfaces/request.interface';
import { AcceptInviteDto, CreateInviteDto, UpdateTeamMemberDto } from '../../../models/dtos/StoreDtos';
import { Subscription } from '../../subscription/subscription.schema';
import { Plan } from '../../../modules/plan/plan.schema';
import { User } from '../../../modules/user/user.schema';
import { Invite, InviteDocument } from './invites.schema';
import { Store, StoreDocument } from '../store.schema';
import { Country } from '../../../modules/country/country.schema';
import { getDocId } from '../../../utils/functions';
import { Referrals } from '../../user/referrals/referrals.schema';
import { ResendRepository } from '../../../repositories/resend.repository';

@Injectable()
export class TeamsService {
  constructor(
    @InjectModel(Store.name) private readonly storeModel: Model<StoreDocument>,
    @InjectModel(Invite.name)
    private readonly inviteModel: Model<InviteDocument>,
    private readonly brokerTransport: BrokerTransportService,
    private readonly resend: ResendRepository,
  ) {}

  async getPendingInvite(invite_id: string) {
    const invite =
      mongoose.Types.ObjectId.isValid(invite_id) &&
      (await this.inviteModel.findOne({ _id: invite_id, status: { $ne: 'ACCEPTED' } }).populate('store', 'name'));
    const isExpired = new Date(invite.expires_at) <= new Date(Date.now());
    if (invite && !isExpired) return invite;
    throw new BadRequestException('Invite has expired or invalid');
  }

  async updateTeamMember(store: string, updateReq: UpdateTeamMemberDto) {
    if (updateReq.invite_id !== undefined) {
      return await this.inviteModel.updateOne({ _id: updateReq.invite_id }, { role: updateReq.role });
    }
    return await this.storeModel.updateOne(
      { _id: store },
      {
        $set: {
          'owners.$[owner].role': updateReq.role,
        },
      },
      {
        arrayFilters: [
          {
            'owner.user': {
              $eq: updateReq.user_id,
            },
          },
        ],
      },
    );
  }

  async acceptInvite(inviteReq: AcceptInviteDto) {
    const invite =
      mongoose.Types.ObjectId.isValid(inviteReq.id) &&
      (await this.inviteModel.findOne({
        _id: inviteReq.id,
        status: { $ne: 'ACCEPTED' },
      }));

    if (invite == undefined || new Date(invite.expires_at) < new Date() || invite.status === 'ACCEPTED') {
      throw new BadRequestException('Invite has expired or invalid');
    }

    let user;

    if (!invite.user) {
      const res = await this.brokerTransport
        .send<{ user: User }>(BROKER_PATTERNS.USER.CREATE_USER, {
          phone: invite.phone ?? inviteReq.phone,
          email: invite.email ?? inviteReq.email,
          password: inviteReq?.password,
          name: inviteReq?.name,
          recaptcha_token: inviteReq?.recaptcha_token,
        })
        .toPromise();

      user = res.user;
    } else {
      user = await this.brokerTransport
        .send<User>(BROKER_PATTERNS.USER.GET_USER, { _id: invite.user })
        .toPromise();

      if (!user) throw new BadRequestException('User in request does not exist');
    }

    const store = await this.storeModel
      .findByIdAndUpdate(
        invite.store,
        {
          $addToSet: {
            owners: {
              user: user.id,
              role: invite.role,
              email: user.email,
              name: user.name,
            },
          },
        },
        { useFindAndModify: false, new: true },
      )
      .populate({
        path: 'stores',
        populate: {
          path: 'subscription',
          populate: {
            path: 'plan last_payment',
          },
        },
      });

    await this.inviteModel.findOneAndUpdate(
      { _id: invite.id },
      { status: 'ACCEPTED', user: user._id },
      { useFindAndModify: false },
    );

    user = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.ADD_STORE, {
        id: getDocId(user),
        storeId: invite.store,
      })
      .toPromise();

    if (!invite.user) {
      const storeData = store.toJSON();

      // storeData.item_count = await this.brokerTransport
      //   .send<number>(BROKER_PATTERNS.ITEM.GET_TOTAL, { store: store.id })
      //   .toPromise();

      storeData.country = await this.brokerTransport
        .send<Country>(BROKER_PATTERNS.COUNTRY.GET_COUNTRY, {
          code: store.country,
        })
        .toPromise();

      const token = await this.brokerTransport
        .send<string>(BROKER_PATTERNS.USER.GENERATE_JWT_TOKEN, {
          user,
          storeId: store.id,
        })
        .toPromise();

      return {
        user: { ...user, stores: [storeData] },
        token: token,
      };
    }

    return {};
  }

  async deleteInvite(invite_id: string) {
    const invite = await this.inviteModel.findByIdAndDelete({ _id: invite_id });
    await this.storeModel.update({ _id: invite.store }, { $pull: { invites: { id: invite_id } } });
    return invite;
  }
  getExpiryDate() {
    const date = new Date();
    date.setDate(date.getDate() + 7);
    return date;
  }

  async createInvite(userReq: IRequest['user'], inviteReq: CreateInviteDto, origin: string) {
    const store = userReq.store.id;
    const { phone, email, role } = inviteReq;
    const user = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, {
        $or: [{ email: { $eq: email ?? '' } }, { phone: { $eq: phone ?? '' } }],
      })
      .toPromise();

    const isSameUser = user && userReq.id === user?.id;
    const isStoreOwner = user && user?.stores.findIndex((val) => val?.id === userReq.store.id) >= 0;
    if (isStoreOwner || isSameUser) throw new BadRequestException('User is already a store owner');

    /* CHECK IF USER EXISTS OR NOT */
    if (user) {
      const isInvalidInvite = await this.inviteModel.exists({
        user: user.id,
        store,
      });
      if (!isInvalidInvite) {
        const { phone, email } = user;

        const invite = await new this.inviteModel({
          status: 'PENDING',
          store,
          phone,
          email,
          user: user.id,
          role,
          expires_at: this.getExpiryDate(),
        }).save();

        const dbStore = await this.storeModel.findByIdAndUpdate(
          store,
          { $push: { invites: invite } },
          { useFindAndModify: false, new: true },
        );

        const emailData = {
          to: user.email,
          subject: `You've been invited to join ${dbStore.name} 👋🏽`,
          data: {
            store_name: dbStore.name,
            invite_link: `${origin}/join-store?invite=${invite._id}`,
            name: user.name,
          },
        };

        await this.resend.sendEmail(BROKER_PATTERNS.MAIL.ACCEPT_STORE_INVITE, emailData);

        // const invites = await this.storeModel.findByIdAndUpdate(
        //   store,
        //   { $push: { invites: invite } },
        //   { useFindAndModify: false },
        // );

        return invite;
      } else throw new BadRequestException('invite with user already exists');
    } else {
      const isInvalidInvite = await this.inviteModel.exists({
        $or: [{ email: { $eq: email ?? '' } }, { phone: { $eq: phone ?? '' } }],
        store,
      });

      if (!isInvalidInvite) {
        const invite = await new this.inviteModel({
          status: 'PENDING',
          store,
          phone,
          email,
          role,
          expires_at: this.getExpiryDate(),
        }).save();

        const dbStore = await this.storeModel.findByIdAndUpdate(
          store,
          { $push: { invites: invite } },
          { useFindAndModify: false, new: true },
        );

        if (inviteReq.email) {
          const emailData = {
            to: inviteReq.email,
            subject: `You've been invited to join ${dbStore.name} 👋🏽`,
            data: {
              store_name: dbStore.name,
              invite_link: `${origin}/join-store?invite=${invite._id}`,
              name: '',
            },
          };

          await this.resend.sendEmail(BROKER_PATTERNS.MAIL.ACCEPT_STORE_INVITE, emailData);
        }

        return invite;
      } else throw new BadRequestException('invite with user already exists');
    }
  }

  async getStoreOwners(store_id: string) {
    const { invites, owners, owner } = await this.storeModel.findById(store_id).populate('invites').populate('owner');

    const storeCreator = {
      id: owner.id,
      name: owner.name,
      email: owner.email,
      role: 'OWNER',
    };

    const pending = invites?.filter((invite) => invite.status !== 'ACCEPTED');

    return [storeCreator, ...owners, ...pending];
  }

  async removeStoreOwner(user: any, owner_id: string) {
    if (owner_id !== user.id && owner_id !== user.store.owner) {
      await this.brokerTransport
        .send<User>(BROKER_PATTERNS.USER.REMOVE_STORE, {
          id: owner_id,
          storeId: user.store.id,
        })
        .toPromise();

      await this.inviteModel.findOneAndDelete({
        user: user.id,
        store: user.store.id,
        status: 'ACCEPTED',
      });

      return await this.storeModel.findOneAndUpdate(
        { _id: user.store.id },
        { $pull: { owners: { user: owner_id } } },
        { useFindAndModify: false, new: true },
      );
    }
    throw new BadRequestException('invalid user id');
  }
}
