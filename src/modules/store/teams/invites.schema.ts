import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../user/user.schema';
import { Store } from '../store.schema';
import mongoose, { Document } from 'mongoose';
import { STORE_ROLES } from '../../../utils/permissions.util';

export type InviteDocument = Invite & Document;

@Schema({ timestamps: true })
export class Invite {
  _id: string;

  @ApiProperty()
  id: string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  status: 'PENDING' | 'ACCEPTED';

  @ApiProperty()
  @Prop({ type: String, required: false })
  email: string;

  @ApiProperty()
  @Prop({ type: String, required: false })
  phone: string;

  @ApiProperty({ type: () => User })
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  user?: User | string;

  @ApiProperty({ type: () => Store })
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Store' })
  store: Store | string;

  @ApiProperty()
  @Prop({
    type: String,
    enum: [STORE_ROLES.ADMIN, STORE_ROLES.OWNER, STORE_ROLES.OPERATOR, STORE_ROLES.STAFF],
    required: true,
  })
  role: string;

  @ApiProperty()
  @Prop({ type: Date, required: true })
  expires_at: Date;
}
export const InviteSchema = SchemaFactory.createForClass(Invite);
