import { Body, Controller, Delete, Get, Param, Post, Put, Req, UseGuards } from '@nestjs/common';
import { ApiCreatedResponse, ApiExtraModels, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { PlanGuard } from '../../../guards/plan.guard';
import { SCOPES } from '../../../utils/permissions.util';
import { PlanPermissions, RolePermissions } from '../../../decorators/permission.decorator';
import { RoleGuard } from '../../../guards/role.guard';
import { IRequest } from '../../../interfaces/request.interface';
import { JwtAuthGuard } from '../../../jwt/jwt-auth.guard';
import {
  AcceptInviteDto,
  CreateInviteDto,
  InviteResponseDto,
  UpdateTeamMemberDto,
} from '../../../models/dtos/StoreDtos';
import { Store } from '.././store.schema';
import { TeamsService } from '.././teams/teams.service';

@Controller('stores')
@ApiTags('Store')
@ApiExtraModels(Store)
export class TeamsController {
  constructor(private readonly teamService: TeamsService) {}

  /* INVITES */
  @Post('/invites')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_TEAM_MEMBERS)
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_TEAMS)
  @UseGuards(JwtAuthGuard, RoleGuard, PlanGuard)
  @ApiCreatedResponse({ type: InviteResponseDto })
  async createInvite(@Req() req: IRequest, @Body() inviteReq: CreateInviteDto) {
    const data = await this.teamService.createInvite(req.user, inviteReq, req.headers.origin);
    return {
      message: 'successfully created invite',
      data,
    };
  }

  @Put('/invites/:id')
  @ApiCreatedResponse({ type: InviteResponseDto })
  async acceptInvite(@Param('id') id: string, @Body() inviteReq: AcceptInviteDto) {
    const data = await this.teamService.acceptInvite({ ...inviteReq, id });
    return {
      message: 'successfully accepted invite',
      data,
    };
  }

  @Get('/invites/:id')
  @ApiCreatedResponse({ type: InviteResponseDto })
  async getInvite(@Param('id') inviteId: string) {
    const data = await this.teamService.getPendingInvite(inviteId);
    return {
      message: 'successfully fetched invite',
      data,
    };
  }

  @RolePermissions(SCOPES.SETTINGS.UPDATE_TEAM_MEMBERS)
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_TEAMS)
  @UseGuards(JwtAuthGuard, RoleGuard, PlanGuard)
  @Delete('/invites/:id')
  @ApiSecurity('bearer')
  @ApiCreatedResponse({ type: InviteResponseDto })
  async deleteInvite(@Param('id') inviteId: string) {
    const data = await this.teamService.deleteInvite(inviteId);
    return {
      message: 'successfully deleted invite',
      data,
    };
  }

  /* TEAMS */
  @Put('teams/')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_TEAM_MEMBERS)
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_TEAMS)
  @UseGuards(JwtAuthGuard, RoleGuard, PlanGuard)
  @ApiSecurity('bearer')
  @ApiCreatedResponse({ type: InviteResponseDto })
  async updateTeamMember(@Req() req: IRequest, @Body() updateReq: UpdateTeamMemberDto) {
    const data = await this.teamService.updateTeamMember(req.user.store.id, updateReq);
    return {
      message: 'successfully updated team member',
      data,
    };
  }

  @Get('/teams/')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.VIEW_TEAM_MEMBERS)
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_TEAMS)
  @UseGuards(JwtAuthGuard, RoleGuard, PlanGuard)
  @ApiCreatedResponse({ type: InviteResponseDto })
  async getTeamMember(@Req() req: IRequest) {
    const data = await this.teamService.getStoreOwners(req?.user?.store?.id);
    return {
      message: 'successfully fetched team members',
      data,
    };
  }

  @Delete('/teams/:id')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_TEAM_MEMBERS)
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_TEAMS)
  @UseGuards(JwtAuthGuard, RoleGuard, PlanGuard)
  @ApiCreatedResponse({ type: InviteResponseDto })
  async removeTeamMember(@Req() req: IRequest, @Param('id') owner_id: string) {
    const data = await this.teamService.removeStoreOwner(req?.user, owner_id);
    return {
      message: 'successfully deleted team member',
      data,
    };
  }
}
