import { Test, TestingModule } from '@nestjs/testing';
import request from 'supertest';
import { StoreModule } from './store.module';
import { AppModule } from '../../app.module';
import { INestApplication } from '@nestjs/common';
import { JwtStrategy } from '../../jwt/jwt.strategy';
import { MockJwtStrategy } from '../../tools/test-helpers';
import { Transport } from '@nestjs/microservices';
import * as faker from 'faker';
import { StoreService } from './services/store.service';
import { TEST_STORE_ID, TOKENS } from '../../tools/testdata';
import { Store } from './store.schema';
import { LowercasePipe } from '../../pipe/lowercase.pipe';
import { ICountry } from '../country/country.service';

describe('StoreController', () => {
  let app: INestApplication;
  let storeId: string;
  let categoryId: string;
  let switchedStoreToken: string;

  const store = {
    name: faker.company.companyName(),
    phone: faker.phone.phoneNumberFormat(0),
    country: 'NG',
    description: faker.lorem.paragraph(),
  } as Store;

  const storeExpects = expect.objectContaining({
    id: expect.any(String),
    name: expect.any(String),
    phone: expect.any(String),
    description: expect.any(String),
    slug: expect.any(String),
    address: expect.any(String),
    categories: [],
    state: expect.any(String),
    country: expect.objectContaining({
      name: expect.any(String),
      currency: expect.any(String),
      code: expect.any(String),
      dial_code: expect.any(String),
      emoji: expect.any(String),
      id: expect.any(String),
    }),
    delivery_locations: '',
    hero_image: '',
    socials: {
      twitter: '',
      instagram: '',
      facebook: '',
      snapchat: '',
      whatsapp: '',
    },
  });

  const storeResponseObject = {
    message: expect.any(String),
    data: storeExpects,
  };

  const storeCreateResponseObject = {
    message: expect.any(String),
    data: expect.objectContaining({
      id: expect.any(String),
      name: expect.any(String),
      phone: expect.any(String),
      description: expect.any(String),
      slug: expect.any(String),
      address: expect.any(String),
      categories: [],
      state: expect.any(String),
      delivery_locations: '',
      hero_image: '',
      socials: {
        twitter: '',
        instagram: '',
        facebook: '',
        snapchat: '',
        whatsapp: '',
        tiktok: '',
      },
    }),
  };

  const category = {
    id: '',
    name: faker.commerce.department(),
  };

  beforeAll(async () => {
    jest.setTimeout(30000);
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule, StoreModule],
    })
      .overrideProvider(JwtStrategy)
      .useClass(MockJwtStrategy)
      .compile();

    app = module.createNestApplication();
    app.useGlobalPipes(new LowercasePipe());
    app.connectMicroservice({
      transport: Transport.NATS,
      options: {
        url: process.env.NATS_URL || 'nats://localhost:4222',
      },
    });

    await app.startAllMicroservicesAsync();
    await app.init();
  });

  afterAll(async () => {
    const storeService = app.get<StoreService>(StoreService);
    await storeService.getStoreModel().findByIdAndDelete({ _id: storeId }).exec();
  });

  it('Create store', async () => {
    return request(app.getHttpServer())
      .post('/stores')
      .send(store)
      .set('Accept', 'application/json')
      .set('Authorization', `Bearer ${TOKENS.NO_STORE}`)
      .expect(201)
      .expect(({ body }) => {
        const data = body.data.stores[0];
        expect({}).toEqual(expect.objectContaining({}));
        storeId = data.id;

        store.slug = data.slug;
      });
  });

  xit('Get Stores', async () => {
    return request(app.getHttpServer())
      .get('/stores')
      .set('Accept', 'application/json')
      .expect(200)
      .then(({ body }) => {
        expect(body).toEqual(
          expect.objectContaining({
            ...storeResponseObject,
            data: expect.arrayContaining([storeResponseObject.data]),
          }),
        );
      });
  });

  it('Update Store', async () => {
    const name = faker.name.firstName();
    const phone = faker.phone.phoneNumberFormat(0);
    const description = faker.lorem.paragraph();
    return request(app.getHttpServer())
      .put(`/stores/${storeId}`)
      .send({
        name,
        phone,
        description,
      })
      .set('Accept', 'application/json')
      .set('Authorization', `Bearer ${TOKENS.WITH_STORE_AND_STARTER_SUBSCRIPTION}`)
      .expect(200)
      .then(({ body }) => {
        expect(body).toEqual(expect.objectContaining(storeCreateResponseObject));
        expect(body.data).toEqual(
          expect.objectContaining({
            name: name,
            phone: phone,
            description: description,
          }),
        );
      });
  });

  // it('Get a Store', async () => {
  //   return request(app.getHttpServer())
  //     .get(`/stores/${storeId}`)
  //     .set('Accept', 'application/json')
  //     .set('Authorization', `Bearer ${TOKENS.WITH_STORE_AND_STARTER_SUBSCRIPTION}`)
  //     .expect(200)
  //     .then(({ body }) => {
  //       expect(body.data).toEqual(expect.objectContaining(storeResponseObject));
  //     });
  // });

  it('Get a Store by slug', async () => {
    return request(app.getHttpServer())
      .get(`/stores/public/${store.slug}`)
      .expect(200)
      .then(({ body }) => {
        expect(body).toEqual(storeCreateResponseObject);
      });
  });

  it('Switch stores', async () => {
    return request(app.getHttpServer())
      .get(`/users/refresh-jwt_token/${storeId}`)
      .set('Authorization', `Bearer ${TOKENS.NO_STORE}`)
      .expect(200)
      .then(({ body }) => {
        console.log('BDY', body);
        switchedStoreToken = body.data.token;
      });
  });

  xit('add and update a store category', async () => {
    const newCategoryName = faker.commerce.department();
    const category1 = {
      name: faker.commerce.department(),
    };

    console.log('UPD', switchedStoreToken);

    return request(app.getHttpServer())
      .post(`/stores/${storeId}/categories`)
      .send([{ ...category, name: newCategoryName }, category1])
      .set('Accept', 'application/json')
      .set('Authorization', `Bearer ${switchedStoreToken}`)
      .expect(200)
      .then(({ body }) => {
        expect(body.data).toEqual(
          expect.arrayContaining([
            {
              name: newCategoryName,
              id: expect.any(String),
              items_count: expect.any(Number),
            },
          ]),
        );
        category.name = newCategoryName;
        expect(body.data).toEqual(
          expect.arrayContaining([
            {
              name: category.name,
              id: expect.any(String),
              items_count: expect.any(Number),
            },
            {
              name: category1.name,
              id: expect.any(String),
              items_count: expect.any(Number),
            },
          ]),
        );
      });
  });

  xit('get store categories', async () => {
    return request(app.getHttpServer())
      .get(`/stores/${storeId}/categories`)
      .set('Authorization', `Bearer ${switchedStoreToken}`)
      .set('Accept', 'application/json')
      .expect(200)
      .then(({ body }) => {
        expect(body.data).toEqual(
          expect.arrayContaining([
            {
              name: category.name,
              id: expect.any(String),
              items_count: expect.any(Number),
            },
          ]),
        );
      });
  });

  xit('get store statistics', async () => {
    return request(app.getHttpServer())
      .get(`/stores/${TEST_STORE_ID}/statistics`)
      .set('Authorization', `Bearer ${switchedStoreToken}`)
      .set('Accept', 'application/json')
      .expect(200)
      .then(({ body }) => {
        expect(body.data).toEqual({
          total_visits: expect.any(Number),
          total_items: expect.any(Number),
          total_carts: expect.any(Number),
          top_items: [
            expect.objectContaining({
              name: expect.any(String),
              description: expect.any(String),
              slug: expect.any(String),
              views: expect.any(Number),
            }),
          ],
          visits: [
            expect.objectContaining({
              time: expect.any(String),
              id: expect.any(String),
            }),
          ],
          orders: [
            expect.objectContaining({
              time: expect.any(String),
              id: expect.any(String),
            }),
          ],
        });
      });
  });

  xit('delete store category', async () => {
    return request(app.getHttpServer())
      .delete(`/stores/${storeId}/categories/${categoryId}`)
      .set('Authorization', `Bearer ${switchedStoreToken}`)
      .set('Accept', 'application/json')
      .expect(204);
  });
});
