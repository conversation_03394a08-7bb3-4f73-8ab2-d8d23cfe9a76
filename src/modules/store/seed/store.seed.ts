import { ISeed } from '../../../interfaces/seed/seed.interface';
import { Collection, Connection } from 'mongoose';
import { storeMock } from '../../../tools/testdata';

export class StoreSeed implements ISeed {
  private readonly collection: Collection;

  constructor(db: Connection) {
    this.collection = db.collection('stores');
  }
  async runDevSeed() {
    if (!(await this.collection.findOne({ _id: storeMock._id }))) {
      return this.collection.insertOne(storeMock);
    }
  }

  runProdSeed(): Promise<any> {
    return Promise.resolve(undefined);
  }
}
