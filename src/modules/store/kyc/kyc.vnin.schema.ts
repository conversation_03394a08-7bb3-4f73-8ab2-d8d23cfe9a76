import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { ApiProperty } from "@nestjs/swagger";
import { Document } from "mongoose";

export type VNinDocument = VNin & Document;

@Schema({timestamps: true})
export class VNin {
  public id: string;

  @ApiProperty()
  @Prop({type: String, unique: true})
  vnin: string;

  @ApiProperty()
  @Prop({type: String})
  image: string;
  
  @ApiProperty()
  @Prop({type: String})
  first_name: string;
  
  @ApiProperty()
  @Prop({type: String})
  middle_name: string;
  
  @ApiProperty()
  @Prop({type: String})
  last_name: string;
  
  @ApiProperty()
  @Prop({type: String})
  gender: string;
}

export const VNinSchema = SchemaFactory.createForClass(VNin);