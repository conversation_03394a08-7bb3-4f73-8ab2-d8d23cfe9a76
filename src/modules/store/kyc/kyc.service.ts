import {
  BadRequestException,
  HttpCode,
  HttpException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  PreconditionFailedException,
  ServiceUnavailableException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import md5 from 'md5';
import { DocumentQuery, FilterQuery, Model } from 'mongoose';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import {
  KycBasicInfoDto,
  KycLookupDto,
  KycResendVerificationToken,
  KycVerifyDto,
  KycVerifyIdDto,
} from '../../../models/dtos/kyc.dto';
import { DojahRepository, Entity, Error } from '../../../repositories/dojah.repository';
import { BvnResponse, MonoRepository } from '../../../repositories/mono.repository';
import { S3Repository } from '../../../repositories/s3.repositories';
import { generateNumber, formatPhoneNumber } from '../../../utils';
import { Wallet } from '../../wallets/wallet.schema';
import { User, UserDocument } from '../../user/user.schema';
import { BVN_PROVIDER, Bvn, BvnDocument } from './kyc.bvn.schema';
import { Kyc, KycAddressType, KycDocument, KycIDAnalysis, KycIDAnalysisDocument, KycStatus } from './kyc.schema';
import { uploadImage } from './utils/uploadImage';
import { Store } from '../store.schema';
import { maskEmail, maskPhoneNumber } from './utils/maskDetails';
import { UserMessagingRepository } from '../../../repositories/user-messaging.repository';
import { YouVerifyRepository } from '../../../repositories/youverify.repository';
import { VNin, VNinDocument } from './kyc.vnin.schema';
import { MailchimpRepository } from '../../../repositories/mailchimp.repository';
import { IRequest } from '../../../interfaces/request.interface';
import { PaginatedQueryDto } from '../../../models/dtos/PaginatedDto';
import { COUNTRY_CODE } from '../../country/country.schema';
import { Exclusive } from '../../../utils/types';
import { COUNTRY_VERIFICATION_METHODS, VERIFICATION_METHODS } from '../../../enums/kycmethods.enum';
import { COUNTRY_CODE_MAP, COUNTRY_CODE_NAME_MAP, COUNTRY_PHONE_LENGTHS } from '../../../utils/constants';
import { BrevoRepository } from '../../../repositories/brevo.repository';
import { ADMIN_CONFIG, AdminConfig } from '../../adminconfig/adminconfig.schema';
import { normalizeBvnData } from './utils/normalizeBvnData';
import * as fs from 'fs';
import * as JSONStream from 'JSONStream';
import * as es from 'event-stream';
import { CustomerIoRepository } from '../../../repositories/customer-io.repository';
import { getDocId } from '../../../utils/functions';
import { ResendRepository } from '../../../repositories/resend.repository';
import { SlackRepository } from '../../../repositories/slack.respository';

@Injectable()
export class KYCService {
  constructor(
    private logger: Logger,
    private readonly s3: S3Repository,
    private readonly mono: MonoRepository,
    private readonly dojah: DojahRepository,
    private readonly youverify: YouVerifyRepository,
    private readonly whatsapp: UserMessagingRepository,
    private readonly brevo: BrevoRepository,
    private readonly customerIo: CustomerIoRepository,
    private readonly brokerTransport: BrokerTransportService,
    private readonly resend: ResendRepository,
    private readonly slack: SlackRepository,
    @InjectModel(Bvn.name) private readonly bvnModel: Model<BvnDocument>,
    @InjectModel(Kyc.name) private readonly kycModel: Model<KycDocument>,
    @InjectModel(VNin.name) private readonly vninModel: Model<VNinDocument>,
    @InjectModel(KycIDAnalysis.name) private readonly kycIdAnalysisModel: Model<KycIDAnalysisDocument>,
  ) {}

  async setBasicInfo(user: IRequest['user'], info: KycBasicInfoDto): Promise<Kyc> {
    let kyc: KycDocument;
    const storeId = user?.store.id;
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
      .toPromise();

    kyc = await this.kycModel.findOne({ store: storeId });

    let phone = '';

    if (kyc) {
      //UPDATING KYC
      kyc.first_name = info.first_name.trim();
      kyc.last_name = info.last_name.trim();

      kyc.save();
    } else {
      //CREATING KYC
      if (store.country.toString() === COUNTRY_CODE.GH && user.phone_verified && user.phone.startsWith('+233')) {
        phone = user.phone;
      }

      kyc = await new this.kycModel({
        first_name: info.first_name.trim(),
        last_name: info.last_name.trim(),
        phone: phone,
        address: {
          address_line1: '',
          lga: '',
          city: '',
          state: '',
        },
        entity: 'INDIVIDUAL',
        identity: {
          type: '',
          number: '',
          url: '',
          filename: '',
          verified_at: null,
        },
        token: '',
        status: 'IN-PROGRESS',
        store: store.id,
        stores: [store.id],
        country: store.country,
        phone_verified: !!phone,
      }).save();

      store.kyc = kyc._id;

      await this.brokerTransport
        .send<void>(BROKER_PATTERNS.STORE.UPDATE_STORE, {
          filter: { _id: storeId },
          update: store,
        })
        .toPromise();

      this.brevo.kycStatusUpdate(user.email, 'IN-PROGRESS');
      this.customerIo.createOrUpdateUser({
        id: getDocId(user),
        email: user?.email,
        kyc_status: kyc.status,
      });
    }

    return kyc.toFilteredJSON();
  }

  async updateKycDateOfBirth(user: IRequest['user'], body: { dob: string }) {
    const kyc = await this.kycModel.findOne({ store: user.store.id });

    if (!kyc) {
      throw new PreconditionFailedException('Kyc not found');
    }

    if (kyc.status === 'APPROVED' || kyc.status === 'DENIED') {
      throw new PreconditionFailedException('Can not update kyc info');
    }

    kyc.dob = body.dob;
    await kyc.save();

    return kyc.toFilteredJSON();
  }

  async sendTokenToPhoneNumber(user: IRequest['user'], body: { phone: string; dob: string }) {
    const kyc = await this.kycModel.findOne({ store: user.store.id });
    const isValidPhoneNumber = COUNTRY_CODE_MAP[kyc.country] === body.phone.split('-')[0];
    const correctPhoneLength = body.phone.split('-')[1].length === COUNTRY_PHONE_LENGTHS[kyc.country];

    if (!isValidPhoneNumber || !correctPhoneLength) {
      throw new PreconditionFailedException(
        `Please provide a valid ${COUNTRY_CODE_NAME_MAP[kyc.country].demonym} phone number`,
      );
    }

    if (!kyc) {
      throw new PreconditionFailedException('Kyc not found');
    }

    const token = generateNumber(6);
    await this.whatsapp.sendVerificationTokenMessage(body.phone, token);

    kyc.bvn_token = token;
    kyc.phone = body.phone;
    kyc.token_expires = Date.now() + 1000 * 60 * 10;
    kyc.dob = body.dob;
    await kyc.save();

    return kyc.toFilteredJSON();
  }

  async verifyPhoneNumber(user: IRequest['user'], body: { token: string }) {
    const kyc = await this.kycModel.findOne({ store: user.store.id });

    if (!kyc) {
      throw new PreconditionFailedException('Kyc not found');
    }

    if (kyc.bvn_token != body.token || Date.now() > kyc.token_expires) {
      throw new PreconditionFailedException('Invalid token');
    }

    kyc.phone_verified = true;
    await kyc.save();

    kyc.toJSON();

    return kyc.toFilteredJSON();
  }

  async lookupBvn(storeId: string, data: KycLookupDto) {
    const bvn_hash = md5(data.bvn);

    let bvn = await this.bvnModel.findOne({ hash: bvn_hash });
    let kycDoc = await this.kycModel.findOne({ bvn: data.bvn });

    if (bvn && kycDoc) {
      if (!kycDoc.stores.includes(storeId)) {
        throw new BadRequestException('BVN has been used by another store');
      }
    } else {
      kycDoc = await this.kycModel.findOne({ stores: storeId });
    }

    if (!kycDoc) {
      throw new BadRequestException('Please reload page');
    }

    if (!bvn) {
      const config = await this.brokerTransport
        .send<AdminConfig | undefined>(BROKER_PATTERNS.CONFIG.GET_CONFIG, ADMIN_CONFIG.BVN_PROVIDER)
        .toPromise();

      const provider = !config?.value ? BVN_PROVIDER.DOJAH : config.value;
      let bvnResponse;
      if (provider === BVN_PROVIDER.DOJAH) {
        const res = await this.dojah.lookupBvn(data.bvn);
        bvnResponse = res.entity;
      } else if (provider === BVN_PROVIDER.MONO) {
        const res = await this.mono.lookupBvn(data.bvn);
        if (res?.error || !res) {
          throw new BadRequestException('Invalid BVN');
        }
        bvnResponse = res.data;
      }

      const bvnData = normalizeBvnData(bvnResponse);

      bvn = new this.bvnModel({
        ...bvnData,
      });

      bvn.hash = bvn_hash;
      bvn.full_name = `${bvn?.first_name} ${bvn?.middle_name} ${bvn?.last_name}`;

      await bvn.save();
    }

    const dobSplit = bvn.dob.split('-'); //because of mono oooh
    const reversedDob = `${dobSplit[2]}-${dobSplit[1]}-${dobSplit[0]}`;

    this.logger.log({
      dateFromMono: bvn.dob,
      providedDob: data.dob,
      reversedDob,
    });

    if (bvn.dob !== data.dob && data.dob !== reversedDob) {
      throw new BadRequestException('Incorrect date of birth');
    }

    const fullName = bvn?.full_name.toLocaleLowerCase();

    if (
      fullName.indexOf(kycDoc.first_name.toLocaleLowerCase()) === -1 ||
      fullName.indexOf(kycDoc.last_name.toLocaleLowerCase()) === -1
    ) {
      throw new BadRequestException("BVN name doesn't match name provided in previous step");
    }

    if (!bvn.phone && !bvn.email) {
      throw new HttpException(
        {
          statusCode: '412',
          message: 'Email and phone not found for this BVN please contact support',
          error: 'Bad request',
        },
        HttpStatus.PRECONDITION_FAILED,
      );
    }

    // const user = await this.brokerTransport.send<User>(BROKER_PATTERNS.USER.GET_USER, {_id: userId}).toPromise();
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
      .toPromise();
    kycDoc = await this.kycModel.findOne({ _id: store.kyc });

    if (!kycDoc) {
      throw new BadRequestException('KYC information not found');
    }

    kycDoc.bvn_token = generateNumber(6);
    kycDoc.token_expires = Date.now() + 1000 * 60 * 10; // +10 minutes

    kycDoc.proposed_bvn = {
      bvn: data.bvn,
      name: bvn.first_name + ' ' + bvn.middle_name + ' ' + bvn.last_name,
      phone: bvn.phone,
    };

    await kycDoc.save();

    if (bvn.phone) {
      const formattedPhone = bvn.phone.startsWith('234') ? `+${bvn.phone}` : `+234${bvn.phone.slice(1)}`;
      await this.whatsapp.sendBvnVerificationTokenMessage(formattedPhone, {
        code: kycDoc.bvn_token,
        name: bvn.full_name,
      });
    }

    if (bvn.email) {
      await this.resend.sendEmail(BROKER_PATTERNS.MAIL.BVN_VERIFICATION, {
        to: bvn.email,
        subject: `Your BVN verification code is ${kycDoc.bvn_token} 🔒`,
        data: {
          name: bvn.first_name,
          code: kycDoc.bvn_token,
        },
      });
    }

    return {
      email: bvn?.email ? maskEmail(bvn.email) : null,
      phone: bvn?.phone ? maskPhoneNumber(bvn.phone) : null,
      kyc: kycDoc.toFilteredJSON() as Kyc,
    };
  }

  async verifyBvn(verification: KycVerifyDto) {
    const kyc = await this.kycModel.findOne({ stores: verification.storeId });
    const bvnDoc = await this.bvnModel.findOne({ hash: md5(verification.bvn) });

    if (!kyc) {
      throw new NotFoundException('Please reload page');
    }

    if (!bvnDoc) {
      throw new NotFoundException('BVN information not found');
    }

    if (verification.token !== kyc.bvn_token || Date.now() > kyc.token_expires) {
      throw new PreconditionFailedException('Token is invalid or expired');
    }

    //do a name verification

    kyc.phone = bvnDoc.phone;
    kyc.address = {
      address_line1: bvnDoc?.address_line_1 ?? '',
      lga: bvnDoc?.lga_of_residence ?? '',
      state: bvnDoc?.state_of_residence?.replace(' State', '') ?? '',
      city: '',
    };
    kyc.bvn = verification.bvn;
    kyc.dob = bvnDoc.dob;
    kyc.bvn_verified_at = new Date();
    kyc.gender = bvnDoc.gender;

    kyc.save();

    return kyc.toFilteredJSON();
  }

  async resendVerificationCode(verification: KycResendVerificationToken) {
    const bvn = await this.bvnModel.findOne({ hash: md5(verification.bvn) });
    const kycDoc = await this.kycModel.findOne({ stores: verification.storeId });

    kycDoc.bvn_token = generateNumber(6);
    kycDoc.token_expires = Date.now() + 1000 * 60 * 10; // +10 minutes

    await kycDoc.save();

    if (bvn.phone) {
      const formattedPhone = bvn.phone.indexOf('234') === 0 ? `+${bvn.phone}` : `+234${bvn.phone.slice(1)}`;
      await this.whatsapp.sendBvnVerificationTokenMessage(formattedPhone, {
        code: kycDoc.bvn_token,
        name: bvn.full_name,
      });
      // await this.whatsapp.sendVerificationTokenMessage('+234' + bvn.phone.slice(1), kycDoc.bvn_token);
    }

    if (bvn.email) {
      await this.resend.sendEmail(BROKER_PATTERNS.MAIL.BVN_VERIFICATION, {
        to: bvn.email,
        subject: `Your BVN verification code is ${kycDoc.bvn_token} 🔒`,
        data: {
          name: bvn.first_name + ' ' + bvn.last_name,
          code: kycDoc.bvn_token,
        },
      });
    }

    return {
      email: maskEmail(bvn.email),
      number: maskPhoneNumber(bvn.phone),
      kyc: kycDoc.toFilteredJSON() as Kyc,
    };
  }

  async doVerification(user: IRequest['user'], body: KycVerifyIdDto) {
    let data;
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, {
        _id: user.store.id,
      })
      .toPromise();

    if (!store) {
      throw new PreconditionFailedException('Store does not exist');
    }

    const kyc = await this.kycModel.findOne({ store: user.store.id });

    if (!kyc) {
      throw new PreconditionFailedException('KYC does not exist');
    }

    if (!COUNTRY_VERIFICATION_METHODS[kyc.country].includes(body.id_type)) {
      throw new PreconditionFailedException('Unable to process ' + body.id_type + 'verification at the moment');
    }

    if (body.id_type === VERIFICATION_METHODS.NIN) {
      data = await this.youverifyVnin({
        ...body,
        storeId: user.store.id,
      });
    } else {
      data = await this.dojahIdVerification({
        ...body,
        storeId: user.store.id,
      });
    }

    return data;
  }

  async youverifyVnin(idVerification: KycVerifyIdDto) {
    let errors: string[] = [];
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, {
        _id: idVerification.storeId,
      })
      .toPromise();
    const storeKyc = await this.kycModel.findOne({ _id: store.kyc });

    if (!storeKyc) {
      throw new BadRequestException('Please reload page');
    }

    // if (storeKyc.id_verified) throw new HttpException({ message: 'ID has been previously verified' }, 400);

    if (idVerification.selfie.startsWith('data'))
      idVerification.selfie = idVerification.selfie.split('data:image/jpeg;base64,')[1];

    let vnin = await this.vninModel.findOne({ vnin: idVerification.vnin });

    if (!vnin) {
      let r;

      try {
        r = await this.dojah.vninLookup(idVerification.vnin);
        // r = await this.dojah.vninFacialVerification(idVerification.vnin, idVerification.selfie);
        // r = await this.youverify.verifyVnin(idVerification.vnin);

        // console.log({ response: r });
      } catch (e) {
        console.log('DOJAH VNIN ERROR');
        console.log(e);
        throw new BadRequestException(e?.error ?? 'Something went wrong');
      }

      if (!r) {
        throw new BadRequestException('Something went wrong');
      }

      vnin = new this.vninModel({
        first_name: r.entity.firstname,
        middle_name: r.entity.middlename,
        last_name: r.entity.surname,
        image: r.entity.photo,
        gender: r.entity.gender,
        vnin: idVerification.vnin,
      });

      vnin.save();
    }

    const fullName =
      vnin.first_name.toLowerCase() + ' ' + vnin.middle_name.toLowerCase() + ' ' + vnin.last_name.toLowerCase();

    if (
      fullName.indexOf(storeKyc.first_name.toLocaleLowerCase()) === -1 ||
      fullName.indexOf(storeKyc.last_name.toLocaleLowerCase()) === -1
    ) {
      errors = ["Your name didn't match your ID", 'Your selfie was unclear', 'Your ID Image was unclear'];
      throw new HttpException(
        {
          errors,
          message: 'Your request failed for one of the following reasons',
        },
        400,
      );
    }

    const photoId = await uploadImage(
      idVerification.photo_id.startsWith('data')
        ? idVerification.photo_id.split('data:image/jpeg;base64,')[1]
        : idVerification.photo_id,
      this.s3,
      'KYC/PHOTO_ID/VNIN',
    );
    const idUserPhoto = await uploadImage(vnin.image, this.s3, 'KYC/USER/VNIN');
    const selfie = await uploadImage(idVerification.selfie, this.s3, 'KYC/SELFIE/VNIN');

    const selfieVerificationR = await this.youverify.compareImages(idUserPhoto.Location, selfie.Location);

    if (selfieVerificationR.error) {
      this.logger.error('YOU VERIFY IMAGE COMPARISON FAILED', selfieVerificationR.error);
      throw new InternalServerErrorException('Something went wrong');
    }

    if (!selfieVerificationR.data.imageComparison.match) {
      errors = [
        "Your selfie doesn't match provided ID",
        'Your selfie was unclear',
        'Your ID Image was unclear',
        'You were wearing a pair of glasses',
      ];
    }

    if (errors.length > 0) {
      throw new HttpException(
        {
          errors,
          message: 'Your request failed for one of the following reasons',
        },
        400,
      );
    }

    storeKyc.identity = {
      type: idVerification.id_type,
      number: idVerification.id_number,
      url: photoId.Location,
      filename: idVerification.filename,
      verified_at: new Date(),
    };
    storeKyc.id_verified = true;

    await storeKyc.save();

    return storeKyc.toFilteredJSON();
  }

  async dojahVerifyVNin(idVerification: KycVerifyIdDto) {
    let errors: string[] = [];
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, {
        _id: idVerification.storeId,
      })
      .toPromise();
    const storeKyc = await this.kycModel.findOne({ _id: store.kyc });

    if (!storeKyc) {
      throw new BadRequestException('Please reload page');
    }

    if (storeKyc.id_verified) throw new HttpException({ message: 'ID verified' }, 400);

    if (idVerification.photo_id.startsWith('data'))
      idVerification.photo_id = idVerification.photo_id.split('data:image/jpeg;base64,')[1];

    let vnin = await this.vninModel.findOne({ vnin: idVerification.vnin });

    if (!vnin) {
      const r = await this.dojah.vninLookup(idVerification.vnin);

      if (r.error) {
        throw new BadRequestException('Something went wrong');
      }

      vnin = new this.vninModel({
        first_name: r.entity.firstname,
        middle_name: r.entity.middlename,
        last_name: r.entity.surname,
        image: r.entity.photo,
        gender: r.entity.gender,
        vnin: idVerification.vnin,
      });

      vnin.save();
    }

    const fullName =
      vnin.first_name.toLowerCase() + ' ' + vnin.middle_name.toLowerCase() + ' ' + vnin.last_name.toLowerCase();

    if (
      fullName.indexOf(storeKyc.first_name.toLocaleLowerCase()) === -1 ||
      fullName.indexOf(storeKyc.last_name.toLocaleLowerCase()) === -1
    ) {
      errors = ["Your name didn't match your ID", 'Your selfie was unclear', 'Your ID Image was unclear'];

      throw new HttpException(
        {
          errors,
          message: 'Your request failed for one of the following reasons',
        },
        400,
      );
    }

    const selfieVerificationR = await this.dojah.verifySelfie(idVerification.selfie, vnin.image);

    if (selfieVerificationR.error) {
      throw new BadRequestException('Something went wrong');
    }

    if (!selfieVerificationR.entity.match) {
      errors = ['Your selfie was unclear', 'Your ID Image was unclear', 'You were wearing a pair of glasses'];
    }

    if (errors.length > 0) {
      throw new HttpException(
        {
          errors,
          message: 'Your request failed for one of the following reasons',
        },
        400,
      );
    }

    const res = await uploadImage(idVerification.photo_id, this.s3, 'KYC/PHOTO_ID/VNIN');
    storeKyc.identity = {
      type: idVerification.id_type,
      number: idVerification.id_number,
      url: res.Location,
      filename: idVerification.filename,
      verified_at: new Date(),
    };
    storeKyc.id_verified = true;

    await storeKyc.save();

    return storeKyc.toFilteredJSON();
  }

  async youVerifyIdVerification(idVerification: KycVerifyIdDto) {
    let errors: string[] = [];
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, {
        _id: idVerification.storeId,
      })
      .toPromise();
    const storeKyc = await this.kycModel.findOne({ _id: store.kyc });

    if (!storeKyc) {
      throw new BadRequestException('Please reload page');
    }

    if (storeKyc.id_verified) throw new HttpException({ message: 'ID verified' }, 400);

    if (idVerification.photo_id.startsWith('data'))
      idVerification.photo_id = idVerification.photo_id.split('data:image/jpeg;base64,')[1];

    let fullName;

    switch (idVerification.id_type) {
      case VERIFICATION_METHODS.INTERNATIONAL_PASSPORT:
        const passportRes = await this.youverify.verifyPassport(
          idVerification.id_number,
          storeKyc.last_name,
          idVerification.selfie,
        );

        if (passportRes.error) throw new HttpException({ message: 'Error verifying ID' }, 400);

        fullName = passportRes.data.firstName.toLowerCase() + ' ' + passportRes.data.lastName.toLowerCase();

        if (
          fullName.indexOf(storeKyc.first_name.toLocaleLowerCase()) === -1 ||
          fullName.indexOf(storeKyc.last_name.toLocaleLowerCase()) === -1
        ) {
          errors = ["Your name didn't match your ID", 'Your selfie was unclear', 'Your ID Image was unclear'];
        } else if (!passportRes.data.selfieValidation) {
          errors = ['Your selfie was unclear', 'Your ID Image was unclear', 'You were wearing a pair of glasses'];
        }
        break;
      case VERIFICATION_METHODS.DRIVERS_LINCENSE:
        const licenseRes = await this.youverify.verifyLicense(idVerification.id_number, idVerification.selfie);

        if (licenseRes.error) throw new HttpException({ message: 'Error verifying ID' }, 400);

        fullName = licenseRes.data.firstName.toLowerCase() + ' ' + licenseRes.data.lastName.toLowerCase();

        if (
          fullName.indexOf(storeKyc.first_name.toLocaleLowerCase()) === -1 ||
          fullName.indexOf(storeKyc.last_name.toLocaleLowerCase()) === -1
        ) {
          errors = ["Your name didn't match your ID", 'Your selfie was unclear', 'Your ID Image was unclear'];
        } else if (!licenseRes.data.selfieValidation) {
          errors = ['Your selfie was unclear', 'Your ID Image was unclear', 'You were wearing a pair of glasses'];
        }
        break;
      default:
        throw new HttpException({ message: 'Unknown document type' }, 400);
        break;
    }

    if (errors.length > 0) {
      throw new HttpException(
        {
          errors,
          message: 'Your request failed for one of the following reasons',
        },
        400,
      );
    }

    const res = await uploadImage(idVerification.photo_id, this.s3, 'KYC/PHOTO_ID/' + idVerification.id_type);
    storeKyc.identity = {
      type: idVerification.id_type,
      number: idVerification.id_number,
      url: res.Location,
      filename: idVerification.filename,
      verified_at: new Date(),
    };
    storeKyc.id_verified = true;

    await storeKyc.save();

    return storeKyc.toFilteredJSON();
  }

  async dojahIdVerification(idVerification: KycVerifyIdDto) {
    let errors: string[] = [];
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, {
        _id: idVerification.storeId,
      })
      .toPromise();
    const storeKyc = await this.kycModel.findOne({ _id: store.kyc });

    if (!storeKyc) {
      throw new BadRequestException('Please reload page');
    }

    if (storeKyc.id_verified) throw new HttpException({ message: 'ID verified' }, 400);

    let r: Exclusive<Entity, Error> = await this.dojah.verifyOtherId(
      idVerification.photo_id,
      idVerification.selfie,
      storeKyc.first_name,
      storeKyc.last_name,
    );

    console.log(r);

    if (!r.entity?.first_name?.match && !r.entity?.last_name?.match) {
      errors = ["Your name didn't match your ID", 'Your selfie was unclear', 'Your ID Image was unclear'];
    } else if (!r.entity?.selfie.match) {
      errors = [
        "Your selfie doesn't match provided ID",
        'Your selfie was unclear',
        'Your ID Image was unclear',
        'You were wearing a pair of glasses',
      ];
    }

    const idAnalysis = await this.dojah.documentAnalysis(idVerification.photo_id);

    if (idAnalysis.error) {
      errors = ['We were unable to process your ID'];
    }

    if (errors.length > 0) {
      throw new HttpException(
        {
          errors,
          message: 'Your request failed for one of the following reasons',
        },
        400,
      );
    }

    const countryCode = idAnalysis.entity.document_type.document_country_code.slice(0, 2) as COUNTRY_CODE;
    const documentType = idAnalysis.entity.document_type.document_name;

    if (countryCode !== storeKyc.country) {
      throw new PreconditionFailedException(
        `Please provide a valid ${COUNTRY_CODE_NAME_MAP[storeKyc.country].demonym} ID`,
      );
    }

    if (idVerification.photo_id.startsWith('data'))
      idVerification.photo_id = idVerification.photo_id.split('data:image/jpeg;base64,')[1];
    const res = await uploadImage(idVerification.photo_id, this.s3, 'KYC/PHOTO_ID/' + idVerification.id_type);
    storeKyc.identity = {
      type: idVerification.id_type,
      number: idVerification.id_number,
      url: res.Location,
      filename: idVerification.filename,
      verified_at: new Date(),
    };

    await new this.kycIdAnalysisModel({
      name: storeKyc.first_name + ' ' + storeKyc.last_name,
      document_country: countryCode,
      document_type: documentType,
      id_photo_url: res.Location,
    }).save();
    storeKyc.id_verified = true;

    await storeKyc.save();

    return storeKyc.toFilteredJSON();
  }

  async manualIdVerification(idVerification: KycVerifyIdDto) {
    let errors: string[] = [];
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, {
        _id: idVerification.storeId,
      })
      .toPromise();
    const storeKyc = await this.kycModel.findOne({ _id: store.kyc });

    if (!storeKyc) {
      throw new BadRequestException('Please reload page');
    }

    if (storeKyc.id_verified) throw new HttpException({ message: 'ID verified' }, 400);

    storeKyc.verification_method = 'MANUAL';

    const selfie = await uploadImage(
      idVerification.selfie.startsWith('data')
        ? idVerification.selfie.split('data:image/jpeg;base64,')[1]
        : idVerification.selfie,
      this.s3,
      'KYC/SELFIE/' + idVerification.id_type,
    );

    if (idVerification.photo_id.startsWith('data'))
      idVerification.photo_id = idVerification.photo_id.split('data:image/jpeg;base64,')[1];
    const idUserPhoto = await uploadImage(idVerification.photo_id, this.s3, 'KYC/PHOTO_ID/' + idVerification.id_type);
    // const selfie = await uploadImage(idVerification.selfie, this.s3);

    storeKyc.identity = {
      type: idVerification.id_type,
      number: idVerification.id_number,
      url: idUserPhoto.Location,
      filename: idVerification.filename,
      selfie: selfie.Location,
      verified_at: new Date(),
    };
    storeKyc.id_verified = true;

    await storeKyc.save();

    return storeKyc.toFilteredJSON();
  }

  async setKycUserAddress(address: KycAddressType, storeId: string) {
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
      .toPromise();
    const storeKyc = await this.kycModel.findOne({ _id: store.kyc });

    if (!storeKyc) {
      throw new BadRequestException('Please reload page');
    }

    storeKyc.address = address;
    await storeKyc.save();

    return storeKyc.toFilteredJSON();
  }

  async setKycAsManual(filter: FilterQuery<KycDocument>) {
    const kyc = await this.kycModel.findOneAndUpdate(filter, { verification_method: 'MANUAL' });

    return kyc.toFilteredJSON();
  }

  async getKyc(filter: FilterQuery<KycDocument>): Promise<any> {
    const kyc = await this.kycModel.findOne(filter);
    return kyc ? kyc.toFilteredJSON() : null;
  }

  async getKycStatus(filter: FilterQuery<KycDocument>): Promise<any> {
    const kyc = await this.kycModel.findOne(filter);
    return kyc ? { status: kyc.status } : { status: 'NO_KYC' };
  }

  async updateKyc(filter: FilterQuery<KycDocument>, data: Kyc) {
    return await this.kycModel.findOneAndUpdate(filter, data, { new: true });
  }

  async deleteKyc(filter: FilterQuery<KycDocument>) {
    return await this.kycModel.deleteOne(filter);
  }

  async getBvn(filter: FilterQuery<BvnDocument>): Promise<any> {
    return (await this.bvnModel.findOne(filter)).toJSON();
  }

  async checkRelatedKyc(user: IRequest['user']) {
    const stores = user.stores;
    const storeIdsMap = user.stores.filter((s) => String(s.owner) === user.id).map((s) => s._id);
    const kycs = await this.kycModel.find({ store: { $in: storeIdsMap } });

    return kycs;
  }

  async removeIdInfo(storeId: string) {
    const storeKyc = await this.kycModel.findOne({ stores: storeId });

    storeKyc.identity = {
      type: '',
      number: '',
      url: '',
      filename: '',
      verified_at: null,
      selfie: '',
    };
    storeKyc.save();
    storeKyc.status = 'IN-PROGRESS';
    storeKyc.verification_method = 'INSTANT';
    storeKyc.id_verified = false;
    storeKyc.rejection_message = '';

    return storeKyc.toFilteredJSON();
  }

  async copyKycInfo(kycId: string, store: string, user: IRequest['user']) {
    let kyc = await this.kycModel.findOne({ _id: kycId });

    const userOwnsKycStore = user.stores.find((s) => String(s._id) === String(kyc.store));

    if (!userOwnsKycStore) {
      throw new UnauthorizedException('You do not have access to this KYC data');
    }

    kyc.stores = kyc.stores.includes(store) ? kyc.stores : [...kyc.stores, store];
    kyc.status = 'IN-PROGRESS';
    kyc.save();

    await this.brokerTransport
      .send<void>(BROKER_PATTERNS.STORE.UPDATE_STORE, {
        filter: { _id: store },
        update: { kyc: kycId },
      })
      .toPromise();

    return kyc.toFilteredJSON();
  }

  async createWallet(storeId: string) {
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
      .toPromise();
    const storeKyc = await this.kycModel.findOne({ _id: store.kyc });

    if (!storeKyc)
      throw new BadRequestException('Please provide all requested details to activate payments. Reload page');

    if (!storeKyc.first_name || !storeKyc.last_name)
      throw new BadRequestException('Please name information to activate payments. Reload page');

    if (!storeKyc.bvn && storeKyc.country === COUNTRY_CODE.NG)
      throw new BadRequestException('Please provide bvn to activate payments. Reload page');
    if (!storeKyc.phone_verified && storeKyc.country === COUNTRY_CODE.GH)
      throw new BadRequestException('Please provide a phone number to activate payments. Reload page');

    if (!storeKyc.dob && storeKyc.country === COUNTRY_CODE.GH)
      throw new BadRequestException('Please provide your date of birth to activate payments. Reload page');

    if (!storeKyc.identity.number || !storeKyc.identity.type || !storeKyc.identity.verified_at)
      throw new BadRequestException('Please provide a valid ID to activate payments. Reload page');

    if (!storeKyc.address.address_line1 || !storeKyc.address.city || !storeKyc.address.lga || !storeKyc.address.city)
      throw new BadRequestException('Please provide your address to activate payments. Reload page');

    if (storeKyc.verification_method === 'MANUAL') {
      storeKyc.status = 'PENDING';
      await storeKyc.save();

      // Get social links from store
      const socialLinks = [];
      if (store.socials?.instagram) socialLinks.push(`Instagram: ${store.socials.instagram}`);
      if (store.socials?.facebook) socialLinks.push(`Facebook: ${store.socials.facebook}`);
      if (store.socials?.twitter) socialLinks.push(`Twitter: ${store.socials.twitter}`);

      await this.slack.sendManualKycVerificationNotification({
        name: `${storeKyc.first_name} ${storeKyc.last_name}`,
        country: storeKyc.country,
        store_name: store.name,
        store_slug: store.slug,
        whatsapp: storeKyc.phone,
        id_type: storeKyc.identity.type,
        id_number: storeKyc.identity.number,
        social_links: socialLinks.length > 0 ? socialLinks.join('\n') : undefined,
        cta_url: 'https://app.catlog.shop/new-internals/kyc',
        cta_label: 'Approve or Reject User',
      });

      return { kyc: storeKyc, account: null, store };
    }

    try {
      return await this.brokerTransport
        .send<{ kyc: Kyc; account: Wallet['account']; store: Store }>(BROKER_PATTERNS.WALLET.CREATE_WALLET, {
          storeId: storeId,
        })
        .toPromise();
    } catch (e) {
      throw new ServiceUnavailableException('Cannot create your account number at the moment, please try again later');
    }
  }

  async getKycWithPagination(pagination: PaginatedQueryDto, filter?: FilterQuery<KycDocument>) {
    const matchFn = () => {
      const f = {};
      if (filter?.status) f['status'] = filter?.status;
      if (filter?.country) f['country'] = filter?.country;
      if (filter?.verification_method) f['verification_method'] = filter?.verification_method;
      if (filter?.search)
        f['$or'] = [{ first_name: new RegExp(filter?.search, 'ig') }, { last_name: new RegExp(filter?.search, 'ig') }];
      // if (filter.verification_method) f['ver'] = new RegExp(filter.search, 'ig');

      return f;
    };

    // const { status, verification_method } = Object.assign(
    //   { status: undefined, verification_method: undefined },
    //   filter,
    // );

    const kycCount = await this.kycModel.countDocuments(matchFn());

    const aggregations = [
      {
        $match: matchFn(),
      },
      {
        $sort: { updatedAt: pagination?.sort === 'DATE_CREATED' ? -1 : 1 },
      },
      {
        $skip: (pagination.page - 1 || 0) * (pagination.per_page || 25),
      },
      { $limit: pagination.per_page || 25 },
      {
        $addFields: {
          store_id: { $toObjectId: '$store' },
        },
      },
      {
        $lookup: {
          from: 'stores',
          localField: 'store_id',
          foreignField: '_id',
          as: 'store',
          pipeline: [
            {
              $project: { ver: 1, slug: 1, phone: 1, name: 1 },
            },
          ],
        },
      },
      {
        $facet: {
          metadata: [
            { $count: 'kycs' },
            {
              $addFields: {
                page: pagination.page || 1,
                total_kycs: kycCount,
              },
            },
          ],
          data: [
            {
              $project: {
                id: 1,
                phone: 1,
                address: { address_line1: 1, lga: 1, state: 1, city: 1 },
                last_name: 1,
                first_name: 1,
                entity: 1,
                bvn: 1,
                status: 1,
                store: { _id: 1, slug: 1, name: 1, phone: 1 },
                createdAt: 1,
                updatedAt: 1,
                bvn_verified_at: 1,
                dob: 1,
                id_verified: 1,
                country: 1,
                gender: 1,
                identity: { type: 1, number: 1, url: 1, filename: 1, verified_at: 1, selfie: 1 },
                bvn_token: 1,
                proposed_bvn: 1,
              },
            },
          ], // add projection here wish you re-shape the docs
        },
      },
    ];

    const data = (await this.kycModel.aggregate(aggregations))[0];

    data.metadata = data.metadata[0];

    data.data.forEach((s) => (s.store = s.store[0]));

    const mt = data.metadata;
    delete data.metadata;

    return { ...mt, ...data };
  }

  async rejectKyc(kycId: string, message: string) {
    const kyc = await this.kycModel.findOne({ _id: kycId });
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: kyc.store })
      .toPromise();

    const user = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, { _id: store.owner })
      .toPromise();

    kyc.rejection_message = message;
    kyc.status = 'DENIED';

    await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.UPDATE_STORE, {
        filter: { _id: store.id },
        update: { payments_enabled: false },
      })
      .toPromise();

    await this.resend.sendEmail(BROKER_PATTERNS.MAIL.KYC_REJECTED, {
      to: user.email,
      subject: "We couldn't approve payments for your store 😔",
      data: {
        name: user.name,
        message,
        app_link: process.env.CATLOG_APP,
      },
    });

    this.brevo.kycStatusUpdate(user.email, 'DENIED');
    this.customerIo.createOrUpdateUser({
      id: getDocId(user),
      email: user?.email,
      kyc_status: kyc.status,
    });

    return kyc.save();
  }

  async acceptKyc(kycId: string) {
    const kyc = await this.kycModel.findOne({ _id: kycId });
    const data = await this.brokerTransport
      .send<{ kyc: Kyc; account: Wallet['account']; store: Store }>(BROKER_PATTERNS.WALLET.CREATE_WALLET, {
        storeId: kyc.stores[kyc.stores.length - 1],
        isAdmin: true,
      })
      .toPromise();

    // kyc.status = 'APPROVED';
    // await kyc.save();

    return data;
  }

  async approveBvn(kycId: string) {
    const kyc = await this.kycModel.findOne({ _id: kycId });

    if (!kyc) {
      throw new NotFoundException('Please reload page');
    }

    if (!kyc?.proposed_bvn?.bvn) throw new PreconditionFailedException('No BVN found');

    const bvnDoc = await this.bvnModel.findOne({ hash: md5(kyc?.proposed_bvn?.bvn) });

    if (!bvnDoc) {
      throw new NotFoundException('BVN information not found');
    }

    //do a name verification
    kyc.phone = bvnDoc.phone;
    kyc.address = {
      address_line1: bvnDoc?.address_line_1 ?? '',
      lga: bvnDoc?.lga_of_residence ?? '',
      state: bvnDoc?.state_of_residence?.replace(' State', '') ?? '',
      city: '',
    };
    kyc.bvn = kyc?.proposed_bvn?.bvn;
    kyc.dob = bvnDoc.dob;
    kyc.bvn_verified_at = new Date();
    kyc.gender = bvnDoc.gender;

    kyc.save();

    return kyc.toFilteredJSON();
  }

  async updateKycStatusToBrevo() {
    const kycs = await this.kycModel.find({}).select('status store').lean();
    const userPromises: Promise<{ email: string } & { kyc_status: KycStatus }>[] = [];

    for (const kyc of kycs) {
      userPromises.push(
        this.brokerTransport
          .send<User>(BROKER_PATTERNS.USER.GET_USER, { stores: { $in: kyc.stores } })
          .toPromise()
          .then((r) => ({ email: r?.email, kyc_status: kyc.status })),
      );
    }

    const users = await Promise.all(userPromises);

    for (const user of users) {
      if (user?.email) {
        // this.brevo.kycStatusUpdate(user.email, user.kyc_status);
        this.customerIo.createOrUpdateUser({
          id: getDocId(user),
          email: user?.email,
          kyc_status: user.kyc_status,
        });
      }
    }

    return users;
  }

  async getPendingKYCs() {
    const pendingKYCs = await this.kycModel.find({ status: 'IN-PROGRESS' }).populate('stores', 'phone name').lean();

    // let returnString = '';

    return {};

    // return pendingKYCs.map((k) => {
    //   const data = {
    //     ...k,
    //     hasAddedBVN: !!k.bvn,
    //     bvn: undefined,
    //     id_verified: !!k.id_verified,
    //     identity: undefined,
    //   };
    //   const store = data.store as any;
    //   return `${data.first_name} ${data.last_name} - ${store.phone.split('-').join('')} - ${store.name} - ${
    //     data.hasAddedBVN ? 'Added BVN' : 'No BVN'
    //   } - ${data.id_verified ? 'ID Verified' : 'NO ID'}`;
    // });
  }

  async migrateToStoreArrays() {
    const kycs = await this.kycModel.find({}).lean();
    const updateReqs: DocumentQuery<KycDocument, KycDocument, {}>[] = [];

    kycs.forEach((k) => {
      updateReqs.push(
        this.kycModel.findByIdAndUpdate(
          { _id: k._id },
          {
            ...k,
            stores: [k.store],
          },
          { useFindAndModify: false },
        ),
      );
    });

    await Promise.all(updateReqs);

    return { message: 'Updated Kyc' };
  }

  async migrateVerificationMethods() {
    const kycs = await this.kycModel.find({});
    const updates: Promise<KycDocument>[] = [];

    for (const kyc of kycs) {
      if (!kyc.verification_method) {
        kyc.verification_method = 'INSTANT';
        updates.push(kyc.save());
      }
    }

    return await Promise.all(updates);
  }

  async migrateKycOriginCountries() {
    const kycs = await this.kycModel.find({});
    const updates: Promise<KycDocument>[] = [];

    for (const kyc of kycs) {
      if (!kyc.country) {
        kyc.country = COUNTRY_CODE.NG;
        updates.push(kyc.save());
      }
    }

    return await Promise.all(updates);
  }

  async extractKycImageUrls(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const imageUrlSet = new Set<string>();

        console.log('Current working directory:', process.cwd());

        // Create a read stream from the JSON file
        const stream = fs
          .createReadStream('prod.kycs.json', { encoding: 'utf8' })
          .pipe(JSONStream.parse('*')) // Parses each KYC in the array
          .pipe(
            es.through(function (kyc) {
              // Process kyc.identity.url
              if (kyc.identity && typeof kyc.identity.url === 'string') {
                if (kyc.identity.url.includes('catlog-1.s3.eu-west-2.amazonaws.com')) {
                  imageUrlSet.add(kyc.identity.url);
                }
              }

              // Process kyc.identity.selfie
              if (kyc.identity && typeof kyc.identity.selfie === 'string') {
                if (kyc.identity.selfie.includes('catlog-1.s3.eu-west-2.amazonaws.com')) {
                  imageUrlSet.add(kyc.identity.selfie);
                }
              }

              // Continue to the next KYC
              this.emit('data', kyc);
            }),
          );

        stream.on('end', () => {
          // Convert the Set to an Array
          const imageUrlArray = Array.from(imageUrlSet);

          // Write the array to a new JSON file
          fs.writeFileSync('prod.kycs.image-urls.json', JSON.stringify(imageUrlArray, null, 2));

          console.log('KYC image URLs extracted successfully.');
          resolve();
        });

        stream.on('error', (error) => {
          console.error('Error extracting KYC image URLs:', error);
          reject(error);
        });
      } catch (error) {
        console.error('Error extracting KYC image URLs:', error);
        reject(error);
      }
    });
  }

  async updateKycImageUrlsToNewS3Migrations(): Promise<void> {
    const oldBaseUrl = 'https://catlog-1.s3.eu-west-2.amazonaws.com';
    const newBaseUrl = 'https://catlog-s3.s3.eu-west-2.amazonaws.com/KYC';

    try {
      const batchSize = 2000;
      let processedCount = 0;
      let totalUpdated = 0;

      // Filter to find KYCs that contain the oldBaseUrl in any of the specified fields
      const filter = {
        $or: [{ 'identity.url': { $regex: oldBaseUrl } }, { 'identity.selfie': { $regex: oldBaseUrl } }],
      };

      let hasMoreDocuments = true;
      const totalDocuments = await this.kycModel.countDocuments(filter);
      this.logger.log(`Total kyc documents to update: ${totalDocuments}`);

      while (hasMoreDocuments) {
        const kycs = await this.kycModel.find(filter).limit(batchSize).exec();

        if (kycs.length === 0) {
          hasMoreDocuments = false;
          break;
        }

        const bulkOps = kycs
          .map((kyc) => {
            let updated = false;

            // Update kyc.identity.url
            if (kyc.identity && typeof kyc.identity.url === 'string' && kyc.identity.url.includes(oldBaseUrl)) {
              kyc.identity.url = kyc.identity.url.replace(oldBaseUrl, newBaseUrl);
              updated = true;
            }

            // Update kyc.identity.selfie
            if (kyc.identity && typeof kyc.identity.selfie === 'string' && kyc.identity.selfie.includes(oldBaseUrl)) {
              kyc.identity.selfie = kyc.identity.selfie.replace(oldBaseUrl, newBaseUrl);
              updated = true;
            }

            if (updated) {
              const updateFields: any = {};

              if (kyc.identity && kyc.identity.url) {
                updateFields['identity.url'] = kyc.identity.url;
              }

              if (kyc.identity && kyc.identity.selfie) {
                updateFields['identity.selfie'] = kyc.identity.selfie;
              }

              return {
                updateOne: {
                  filter: { _id: kyc._id },
                  update: {
                    $set: updateFields,
                  },
                },
              };
            }

            return null;
          })
          .filter((op) => op !== null);

        if (bulkOps.length > 0) {
          const result = await this.kycModel.bulkWrite(bulkOps);
          totalUpdated += result.modifiedCount || 0;
          this.logger.log(`Batch updated ${result.modifiedCount || 0} KYCs.`);
        } else {
          hasMoreDocuments = false;
        }

        processedCount += kycs.length;
        this.logger.log(`Processed ${processedCount} of ${totalDocuments} KYCs.`);
      }

      this.logger.log(`KYC image URLs updated successfully. Total updated: ${totalUpdated}.`);
    } catch (error) {
      this.logger.error('Error updating KYC image URLs:', error);
      throw error;
    }
  }
}
