import { Controller } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { FilterQuery } from 'mongoose';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { BvnDocument } from './kyc.bvn.schema';
import { Kyc, KycDocument } from './kyc.schema';
import { KYCService } from './kyc.service';
import { SkipThrottle } from '@nestjs/throttler';

@SkipThrottle()
@Controller()
export class KycBroker {
  constructor(private readonly kycService: KYCService) {}

  @MessagePattern(BROKER_PATTERNS.KYC.GET_KYC)
  getKyc(data: FilterQuery<KycDocument>) {
    return this.kycService.getKyc(data);
  }

  @MessagePattern(BROKER_PATTERNS.BVN.GET_BVN)
  getBvn(data: FilterQuery<BvnDocument>) {
    return this.kycService.getBvn(data);
  }

  @MessagePattern(BROKER_PATTERNS.KYC.UPDATE_KYC)
  updateKyc(data: { filter: FilterQuery<KycDocument>; data: Kyc }) {
    return this.kycService.updateKyc(data.filter, data.data);
  }
}
