// Function to normalize data from both providers
export const normalizeBvnData = (data: any) => {
  const normalized = {
    title: data?.title,
    first_name: data?.firstName ?? data?.first_name,
    middle_name: data?.middleName ?? data?.middle_name,
    last_name: data?.lastName ?? data?.last_name,
    email: data?.email,
    gender: data?.gender,
    dob: data?.dob ?? data?.date_of_birth,
    phone: data?.phone ?? data?.phone_number ?? data?.phoneNumber1 ?? data?.phone_number1,
    alternate_phone:
      data?.phoneNumber2 ??
      data?.phone_number2 ??
      data?.alternate_phone ??
      data?.alternatePhone ??
      data?.phone_number_2,
    avatar: data?.photoid ?? data?.image ?? data?.avatar ?? data?.photoId,
    state_of_origin: data?.stateOfOrigin ?? data?.state_of_origin,
    state_of_residence: data?.stateOfResidence ?? data?.state_of_residence,
    lga_of_origin: data?.lgaOfOrigin ?? data?.lga_of_origin,
    lga_of_residence: data?.lgaOfResidence ?? data?.lga_of_residence,
    address_line_1: data?.residential_address ?? data?.address_line_1 ?? data?.addressLine1,
    address_line_2: data?.address_line_2 ?? data?.addressLine2,
    address_line_3: data?.address_line_3 ?? data?.addressLine3,
    marital_status: data?.maritalStatus ?? data?.marital_status,
    nin: data?.nin,
    nationality: data?.nationality,
    registration_date: data?.registrationDate ?? data?.registration_date,
    enrollment_bank: data?.enrollmentBank ?? data?.enrollment_bank,
    enrollment_branch: data?.enrollmentBranch ?? data?.enrollment_branch,
    account_level: data?.accounLevel ?? data?.level_of_account,
    watchlisted: data?.watchListed ?? data?.watchlisted ?? data?.watch_listed === 'NO' ? false : true,
  };

  return normalized;
};
