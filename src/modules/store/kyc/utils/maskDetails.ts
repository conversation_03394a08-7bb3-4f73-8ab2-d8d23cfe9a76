export const maskEmail = (email: string) => {
  const length = email.length;
  const at = email.indexOf('@');

  //   return email.slice(at >= 4 ? at - 4 : at).padStart(length, '*');

  return '****' + email.slice(at >= 4 ? at - 4 : at);
};

export const maskPhoneNumber = (number: string) => {
  return '****' + number.slice(number.length - 4);
  // return number.slice(number.length - 4).padStart(number.length, '*');
};
