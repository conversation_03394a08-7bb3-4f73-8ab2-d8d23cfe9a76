import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Document } from 'mongoose';
import { BvnResponse } from '../../../repositories/mono.repository';

export enum BVN_PROVIDER {
  MONO = 'MONO',
  DOJAH = 'DOJAH',
}

export class BvnData {
  hash: string;
  last_four: string[4];
}

export type BvnDocument = Bvn & Document;

@Schema({ timestamps: true })
export class Bvn {
  public id: string;

  @ApiProperty()
  @Prop({ type: String })
  title: string;

  @ApiProperty()
  @Prop({ type: String })
  full_name: string;

  @ApiProperty()
  @Prop({ type: String })
  first_name: string;

  @ApiProperty()
  @Prop({ type: String })
  middle_name: string;

  @ApiProperty()
  @Prop({ type: String })
  last_name: string;

  @ApiProperty()
  @Prop({ type: String })
  email: string;

  @ApiProperty()
  @Prop({ type: String })
  gender: string;

  @ApiProperty()
  @Prop({ type: String })
  dob: string;

  @ApiProperty()
  @Prop({ type: String })
  phone: string;

  @ApiProperty()
  @Prop({ type: String })
  alternate_phone: string;

  @ApiProperty()
  @Prop({ type: String })
  avatar: string;

  @ApiProperty()
  @Prop({ type: String })
  country: string;

  @ApiProperty()
  @Prop({ type: String })
  state_of_origin: string;

  @ApiProperty()
  @Prop({ type: String })
  state_of_residence: string;

  @ApiProperty()
  @Prop({ type: String })
  lga_of_origin: string;

  @ApiProperty()
  @Prop({ type: String })
  lga_of_residence: string;

  @ApiProperty()
  @Prop({ type: String })
  address_line_1: string;

  @ApiProperty()
  @Prop({ type: String })
  address_line_2: string;

  @ApiProperty()
  @Prop({ type: String })
  address_line_3: string;

  @ApiProperty()
  @Prop({ type: String })
  marital_status: string;

  @ApiProperty()
  @Prop({ type: String })
  nin: string;

  @ApiProperty()
  @Prop({ type: String })
  nationality: string;

  @ApiProperty()
  @Prop({ type: String })
  registration_date: string;

  @ApiProperty()
  @Prop({ type: String })
  enrollment_bank: string;

  @ApiProperty()
  @Prop({ type: String })
  enrollment_branch: string;

  @ApiProperty()
  @Prop({ type: String })
  account_level: string;

  @ApiProperty()
  @Prop({ type: String })
  hash: string;

  @ApiProperty()
  @Prop({ type: Boolean })
  watchlisted: boolean;
}

export const BvnSchema = SchemaFactory.createForClass(Bvn);
