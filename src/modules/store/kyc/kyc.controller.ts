import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Header,
  InternalServerErrorException,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { User } from '@sentry/node';
import { JwtAuthGuard } from '../../..//jwt/jwt-auth.guard';
import {
  CopyKYCDto,
  KycAddressDto,
  KycBasicInfoDto,
  KycLookupDto,
  KycPhoneLookupDto,
  KycPhoneVerifyDto,
  KycResendVerificationToken,
  KycSubmitDto,
  KycUpdateDobDto,
  KycVerifyDto,
  KycVerifyIdDto,
} from '../../../models/dtos/kyc.dto';
import { KYCService } from './kyc.service';
import { IRequest, IRequest as Request } from '../../../interfaces/request.interface';
import { MonoRepository } from '../../../repositories/mono.repository';
import { CountryPermissons, RolePermissions } from '../../../decorators/permission.decorator';
import { SCOPES } from '../../../utils/permissions.util';
import { CountryGuard } from '../../../guards/country.guard';
import { RoleGuard } from '../../../guards/role.guard';
import { InternalApiJWTGuard } from '../../../guards/api.guard';
import { PaginatedQueryDto } from '../../../models/dtos/PaginatedDto';
import { ApiExcludeEndpoint, ApiSecurity } from '@nestjs/swagger';

@Controller('kyc')
export class KYCController {
  constructor(private readonly mono: MonoRepository, private readonly kycService: KYCService) {}

  @Get('')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @RolePermissions(SCOPES.WALLETS.CAN_START_KYC, SCOPES.SETTINGS.ENABLE_PAYMENTS)
  @UseGuards(JwtAuthGuard, CountryGuard, RoleGuard)
  async getStoreKYCInfo(@Req() req: Request) {
    const data = await this.kycService.getKyc({ stores: req.user.store.id });

    return {
      message: 'Store KYC fetched',
      data,
    };
  }

  @Get('/status')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @RolePermissions(SCOPES.WALLETS.CAN_START_KYC, SCOPES.SETTINGS.ENABLE_PAYMENTS)
  @UseGuards(JwtAuthGuard, CountryGuard, RoleGuard)
  async getStoreKYCStatus(@Req() req: Request) {
    const data = await this.kycService.getKycStatus({ stores: req.user.store.id });

    return {
      message: 'Store KYC fetched',
      data,
    };
  }

  @Get('extractKycImages')
  async extractKycImages(@Req() req: Request) {
    await this.kycService.extractKycImageUrls();
    return {
      message: 'Images extracted',
    };
  }

  @Post('basic-info')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @RolePermissions(SCOPES.WALLETS.CAN_START_KYC, SCOPES.SETTINGS.ENABLE_PAYMENTS)
  @UseGuards(JwtAuthGuard, CountryGuard, RoleGuard)
  async setBasicInfo(@Req() req: Request, @Body() body: KycBasicInfoDto) {
    const user = req.user;
    const data = await this.kycService.setBasicInfo(user, body);

    return {
      message: 'Basic info set',
      data,
    };
  }

  @Post('lookup/bvn')
  @ApiSecurity('bearer')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @RolePermissions(SCOPES.WALLETS.CAN_START_KYC, SCOPES.SETTINGS.ENABLE_PAYMENTS)
  @UseGuards(JwtAuthGuard, CountryGuard, RoleGuard)
  async lookupBvn(@Req() req: Request, @Body() body: KycLookupDto) {
    const data = await this.kycService.lookupBvn(req.user.store.id, body);

    return {
      message: "We've sent you a token",
      data: data,
    };
  }

  @Post('lookup/phone')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @RolePermissions(SCOPES.WALLETS.CAN_START_KYC, SCOPES.SETTINGS.ENABLE_PAYMENTS)
  @UseGuards(JwtAuthGuard, CountryGuard, RoleGuard)
  async lookupPhone(@Req() req: Request, @Body() body: KycPhoneLookupDto) {
    const data = await this.kycService.sendTokenToPhoneNumber(req.user, body);

    return {
      message: "We've sent you a token",
      data: data,
    };
  }

  @Post('verify/bvn')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @RolePermissions(SCOPES.WALLETS.CAN_START_KYC, SCOPES.SETTINGS.ENABLE_PAYMENTS)
  @UseGuards(JwtAuthGuard, CountryGuard, RoleGuard)
  async verifyBvn(@Req() req: Request, @Body() body: KycVerifyDto) {
    const data = await this.kycService.verifyBvn({
      ...body,
      storeId: req.user.store.id,
    });

    return {
      message: 'BVN verified',
      data,
    };
  }

  @Post('verify/phone')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @RolePermissions(SCOPES.WALLETS.CAN_START_KYC, SCOPES.SETTINGS.ENABLE_PAYMENTS)
  @UseGuards(JwtAuthGuard, CountryGuard, RoleGuard)
  async verifyPhone(@Req() req: Request, @Body() body: KycPhoneVerifyDto) {
    const data = await this.kycService.verifyPhoneNumber(req.user, body);

    return {
      message: 'Phone verified',
      data,
    };
  }

  @Put('dob')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @RolePermissions(SCOPES.WALLETS.CAN_START_KYC, SCOPES.SETTINGS.ENABLE_PAYMENTS)
  @UseGuards(JwtAuthGuard, CountryGuard, RoleGuard)
  async updateDOB(@Req() req: Request, @Body() body: KycUpdateDobDto) {
    const data = await this.kycService.updateKycDateOfBirth(req.user, body);

    return {
      message: 'Date of birth updated',
      data,
    };
  }

  @Post('verify/resend-phone-token')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @RolePermissions(SCOPES.WALLETS.CAN_START_KYC, SCOPES.SETTINGS.ENABLE_PAYMENTS)
  @UseGuards(JwtAuthGuard, CountryGuard, RoleGuard)
  async resendVerifyPhone(@Req() req: Request, @Body() body: KycPhoneLookupDto) {
    const data = await this.kycService.sendTokenToPhoneNumber(req.user, body);

    return {
      message: 'Token resent successfully',
      data: data,
    };
  }

  @Post('verify/resend-bvn-token')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @RolePermissions(SCOPES.WALLETS.CAN_START_KYC, SCOPES.SETTINGS.ENABLE_PAYMENTS)
  @UseGuards(JwtAuthGuard, CountryGuard, RoleGuard)
  async resendVerifyBvn(@Req() req: Request, @Body() body: KycResendVerificationToken) {
    const data = await this.kycService.resendVerificationCode({
      ...body,
      storeId: req.user.store.id,
    });

    return {
      message: 'Token resent successfully',
      data: data,
    };
  }

  @Post('verify/id')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @RolePermissions(SCOPES.WALLETS.CAN_START_KYC, SCOPES.SETTINGS.ENABLE_PAYMENTS)
  @UseGuards(JwtAuthGuard, CountryGuard, RoleGuard)
  async verifyId(@Req() req: Request, @Body() body: KycVerifyIdDto) {
    let data = await this.kycService.doVerification(req.user, body);

    return {
      message: 'ID verified',
      data,
    };
  }

  @Post('manual/id')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @RolePermissions(SCOPES.WALLETS.CAN_START_KYC, SCOPES.SETTINGS.ENABLE_PAYMENTS)
  @UseGuards(JwtAuthGuard, CountryGuard, RoleGuard)
  async manuallyVerifyId(@Req() req: Request, @Body() body: KycVerifyIdDto) {
    const data = await this.kycService.manualIdVerification({ ...body, storeId: req.user.store.id });

    return {
      message: 'ID verified',
      data,
    };
  }

  @Post('manual')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @RolePermissions(SCOPES.WALLETS.CAN_START_KYC, SCOPES.SETTINGS.ENABLE_PAYMENTS)
  @UseGuards(JwtAuthGuard, CountryGuard, RoleGuard)
  async makeKycManual(@Req() req: Request) {
    const data = await this.kycService.setKycAsManual({ stores: req.user.store.id });

    return {
      message: 'KYC updated as manual',
      data,
    };
  }

  @Post('address')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @RolePermissions(SCOPES.WALLETS.CAN_START_KYC, SCOPES.SETTINGS.ENABLE_PAYMENTS)
  @UseGuards(JwtAuthGuard, CountryGuard, RoleGuard)
  async setKycAddress(@Req() req: Request, @Body() body: KycAddressDto) {
    const data = await this.kycService.setKycUserAddress(body, req.user.store.id);

    return {
      message: 'Address added',
      data,
    };
  }

  @Post('submit')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @RolePermissions(SCOPES.WALLETS.CAN_START_KYC, SCOPES.SETTINGS.ENABLE_PAYMENTS)
  @UseGuards(JwtAuthGuard, CountryGuard, RoleGuard)
  async generateWallet(@Req() req: Request, @Body() body: KycSubmitDto) {
    const data = await this.kycService.createWallet(req.user.store.id);

    return {
      message: 'Kyc complete',
      data,
    };
  }

  @Get('check-related-kyc')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @RolePermissions(SCOPES.WALLETS.CAN_START_KYC, SCOPES.SETTINGS.ENABLE_PAYMENTS)
  @UseGuards(JwtAuthGuard, CountryGuard, RoleGuard)
  async checkRelatedKyc(@Req() req: Request) {
    const data = await this.kycService.checkRelatedKyc(req.user);

    return {
      data,
      message: 'Related Kyc Fetched',
    };
  }

  @Put('remove-id')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @RolePermissions(SCOPES.WALLETS.CAN_START_KYC, SCOPES.SETTINGS.ENABLE_PAYMENTS)
  @UseGuards(JwtAuthGuard, CountryGuard, RoleGuard)
  async removeId(@Req() req: Request) {
    const data = await this.kycService.removeIdInfo(req.user.store.id);

    return {
      data,
      message: 'Id Info removed',
    };
  }

  @Post('copy-info')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @RolePermissions(SCOPES.WALLETS.CAN_START_KYC, SCOPES.SETTINGS.ENABLE_PAYMENTS)
  @UseGuards(JwtAuthGuard, CountryGuard, RoleGuard)
  async copyKYCInfo(@Req() req: Request, @Body() body: CopyKYCDto) {
    const data = await this.kycService.copyKycInfo(body.kyc_id, req.user.store.id, req.user);

    return {
      message: 'Kyc info copied',
      data,
    };
  }

  @Get('/admin/store/:id')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async adminGetKyc(@Param('id') id) {
    return {
      message: '',
      data: await this.kycService.getKyc({ store: id }),
    };
  }

  @Get('/admin/')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async adminGetKycs(
    @Query('pagination') pagination: PaginatedQueryDto,
    @Req() req: IRequest,
    @Query('filter') filter?: any,
  ) {
    try {
      const data = await this.kycService.getKycWithPagination(pagination, filter);

      return {
        message: 'Items fetched successfully',
        ...data,
      };
    } catch (err) {
      if (!(err instanceof BadRequestException)) {
        throw new InternalServerErrorException();
      }
      throw err;
    }
  }

  @Put('/admin/:id')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async adminUpdateKyc(@Param('id') id: string, @Body() body: any) {
    return {
      message: '',
      data: await this.kycService.updateKyc({ _id: id }, body),
    };
  }

  @Post('/admin/:id/approve')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async adminKycAccept(@Param('id') id: string) {
    const data = await this.kycService.acceptKyc(id);

    return {
      message: 'Kyc Manually Accepted',
      data,
    };
  }

  @Post('/admin/:id/approve-bvn')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async adminApproveBvn(@Param('id') id: string) {
    const data = await this.kycService.approveBvn(id);

    return {
      message: 'BVN Manually Approved',
      data,
    };
  }

  @Post('/admin/:id/reject')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async adminKycReject(@Param('id') id: string, @Body() body: { message: string }) {
    const data = await this.kycService.rejectKyc(id, body.message);

    return {
      message: 'Kyc Manually Rejected',
      data,
    };
  }

  @Post('/admin/store/:id/submit')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async admitSubmitKyc(@Param('id') id: string) {
    return {
      message: '',
      data: await this.kycService.createWallet(id),
    };
  }

  @Delete('/admin/:id')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async adminDeleteKyc(@Param('id') id: string) {
    return {
      message: '',
      data: await this.kycService.deleteKyc({ _id: id }),
    };
  }

  @Post('/admin/update-all-mailchimp-user-kycstatus')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async updateBrevoUserKycStatuses() {
    return await this.kycService.updateKycStatusToBrevo();
  }

  @Post('/admin/migrate-store-to-stores')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async copyStores() {
    return await this.kycService.migrateToStoreArrays();
  }

  @Post('/admin/migrate-verfication-methods')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async migrateVerificationMethods() {
    return await this.kycService.migrateVerificationMethods();
  }

  @Post('/admin/migrate-conutry-based-kycs')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async migrateConutryBasedKycs() {
    return await this.kycService.migrateKycOriginCountries();
  }

  @Post('update-kyc-image-urls-to-new-s3-migration')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async updateKycImageUrls() {
    this.kycService.updateKycImageUrlsToNewS3Migrations();
    return {
      message: 'Image urls update to new s3 migration in progress. Please wait for images urls',
    };
  }

  // @Get('/admin/pending-kycs')
  // // @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  // async getPendingKYCs() {
  //   return {
  //     message: '',
  //     data: await this.kycService.getPendingKYCs(),
  //   };
  // }
}
