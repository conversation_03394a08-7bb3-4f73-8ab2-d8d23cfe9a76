import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import mongoose, { Document } from 'mongoose';
import { COUNTRY_CODE } from '../../country/country.schema';

export type KycVerificationMethod = 'MANUAL' | 'INSTANT';
export type KycStatus = 'IN-PROGRESS' | 'PENDING' | 'APPROVED' | 'DENIED';
export type KycDocument = Kyc & Document;
export type KycIDAnalysisDocument = KycIDAnalysis & Document;
export class KycIdentityType {
  type: string;
  number: string;
  url: string;
  filename: string;
  verified_at: Date;
  selfie?: string;
}

export class KycAddressType {
  address_line1: string;
  lga: string;
  city: string;
  state: string;
}

export class ProposedBvn {
  bvn: string;
  name: string;
  phone: string;
  // address_line1: string;
  // lga: string;
  // city: string;
  // state: string;
}

@Schema({ timestamps: true })
export class Kyc {
  public id: string;

  @ApiProperty()
  @Prop({ type: String })
  first_name: string;

  @ApiProperty()
  @Prop({ type: String })
  last_name: string;

  @ApiProperty()
  @Prop({ type: String })
  phone: string;

  @ApiProperty()
  @Prop({ type: KycAddressType })
  address: KycAddressType;

  @ApiProperty()
  @Prop({ enum: ['INDIVIDUAL', 'BUSINESS'] })
  entity: 'INDIVIDUAL' | 'BUSINESS';

  @ApiProperty()
  @Prop({ type: String, unique: true, required: false, sparse: true })
  bvn: string;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Store' })
  store: string;

  @ApiProperty()
  @Prop({ type: [mongoose.Schema.Types.ObjectId] })
  stores: string[];

  @ApiProperty()
  @Prop({ type: KycIdentityType })
  identity: KycIdentityType;

  @ApiProperty()
  @Prop({ type: String })
  bvn_token: string;

  @ApiProperty()
  @Prop({ type: Number })
  token_expires: number;

  @ApiProperty()
  @Prop({ type: Boolean })
  id_verified: boolean;

  @ApiProperty()
  @Prop({ type: Date })
  bvn_verified_at: Date;

  @ApiProperty()
  @Prop({ type: String })
  dob: string;

  @ApiProperty()
  @Prop({ type: String })
  gender: string;

  @ApiProperty()
  @Prop({ enum: ['IN-PROGRESS', 'PENDING', 'APPROVED', 'DENIED'] })
  status: KycStatus;

  @ApiProperty()
  @Prop({ enum: ['MANUAL', 'INSTANT'] })
  verification_method: KycVerificationMethod;

  @ApiProperty()
  @Prop({ type: String })
  rejection_message: string;

  @ApiProperty()
  @Prop({ type: ProposedBvn })
  proposed_bvn: ProposedBvn;

  @ApiProperty()
  @Prop({ type: String, enum: Object.values(COUNTRY_CODE), default: COUNTRY_CODE.NG })
  country: COUNTRY_CODE;

  @ApiProperty()
  @Prop({ type: Boolean })
  phone_verified: boolean;

  @ApiProperty()
  @Prop({ type: Date })
  approved_at: Date;

  toFilteredJSON(): any {} // Intellisense stub for mongoose method definition below
}

export class KycIDAnalysis {
  public id: string;

  @ApiProperty()
  @Prop({ type: String })
  name: string;

  @ApiProperty()
  @Prop({ type: String })
  document_type: string;

  @ApiProperty()
  @Prop({ type: String })
  document_country: COUNTRY_CODE;

  @ApiProperty()
  @Prop({ type: String })
  id_photo_url: string;
}

export const KycSchema = SchemaFactory.createForClass(Kyc);
export const KycIDAnalysisSchema = SchemaFactory.createForClass(KycIDAnalysis);
KycSchema.methods.toFilteredJSON = function () {
  const obj = this.toJSON();
  delete obj.bvn_token;
  return obj;
};
