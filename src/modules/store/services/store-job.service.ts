import { Get, Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { StoreService } from './store.service';
import { Mutex } from 'async-mutex';
import { ResendRepository } from '../../../repositories/resend.repository';
import mongoose, { Model } from 'mongoose';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { Store, StoreDocument } from '../store.schema';
import { InjectModel } from '@nestjs/mongoose';
import { InjectQueue } from '@nestjs/bull';
import { JOBS, QUEUES } from '../../../enums/queues.enum';
import { Queue } from 'bull';
import { isUsingProdDBLocally } from '../../../utils';
dayjs.extend(utc);
dayjs.extend(timezone);

const mutex = new Mutex();

@Injectable()
export class StoreJobService {
  private readonly logger = new Logger(StoreJobService.name);

  // Define the batch size for fetching stores from the database
  private readonly BATCH_SIZE = 1000;

  constructor(
    private readonly storeService: StoreService,
    protected readonly resend: ResendRepository,
    @InjectModel(Store.name) private storeModel: Model<StoreDocument>,
    @InjectQueue(QUEUES.STORE) private readonly storeQueue: Queue,
  ) {}

  /**
   * Cron job to send weekly email summaries.
   * Triggers every week on Sunday at 00:00.
   */
  @Cron(CronExpression.EVERY_WEEK)
  async weeklyEmailSummary() {
    await this.queueEmailSummaryJobs('week');
  }

  /**
   * Cron job to send monthly email summaries.
   * Triggers on the first day of every month at 00:00.
   */
  @Cron(CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT)
  async monthlyEmailSummary() {
    await this.queueEmailSummaryJobs('month');
  }

  private async queueEmailSummaryJobs(period: 'week' | 'month') {
    if (mutex.isLocked() || isUsingProdDBLocally()) return;
    await mutex.acquire();
    // Determine date ranges based on the period
    let [startOfPeriod, endOfPeriod] = [new Date(), new Date()];
    if (period === 'week') {
      [startOfPeriod, endOfPeriod] = this.getPreviousWeekRange();
    } else {
      [startOfPeriod, endOfPeriod] = this.getPreviousMonthRange();
    }

    let skip = 0;
    let hasMoreStores = true;

    while (hasMoreStores) {
      // Fetch a batch of stores from the database
      const stores = await this.storeModel
        .find({ disabled: false, reference: { $exists: false }, subscription: { $exists: true } })
        .select('_id')
        .skip(skip)
        .limit(this.BATCH_SIZE)
        .lean()
        .exec();

      if (stores.length === 0) {
        hasMoreStores = false; // No more stores to process
      } else {
        // Process the current batch of stores
        for (const store of stores) {
          await this.storeQueue.add(QUEUES.STORE, {
            type: JOBS.SEND_EMAIL_SUMMARY,
            data: {
              storeId: store._id,
              period,
              startOfPeriod,
              endOfPeriod,
            },
          });
        }

        skip += this.BATCH_SIZE; // Move to the next batch
        this.logger.log(`Processed ${skip} stores so far.`);
      }
    }

    this.logger.log(`Finished processing all stores.`);

    mutex.release();
  }

  // async getTestSummaries() {
  //   const period = 'week';
  //   // Determine date ranges based on the period
  //   let [startOfPeriod, endOfPeriod] = [new Date(), new Date()];
  //   if (period === ('week' as any)) {
  //     [startOfPeriod, endOfPeriod] = this.getPreviousWeekRange();
  //   } else {
  //     [startOfPeriod, endOfPeriod] = this.getPreviousMonthRange();
  //   }

  //   const storeId = '64c47d95d63d6960e3e4580d'; //kwadwo's store

  //   await this.storeQueue.add(QUEUES.STORE, {
  //     type: JOBS.SEND_EMAIL_SUMMARY,
  //     data: {
  //       storeId: mongoose.Types.ObjectId(storeId),
  //       period,
  //       startOfPeriod,
  //       endOfPeriod,
  //     },
  //   });

  //   this.logger.log(`Finished processing all stores.`);

  //   return {
  //     startOfPeriod,
  //     endOfPeriod,
  //   };
  // }

  /**
   * Helper method to get the start and end dates of the previous week.
   * Assumes week starts on Sunday.
   */
  private getPreviousWeekRange(): [Date, Date] {
    const endOfLastWeek = dayjs().subtract(1, 'week').endOf('week').toDate();
    const startOfLastWeek = dayjs().subtract(1, 'week').startOf('week').toDate();
    return [startOfLastWeek, endOfLastWeek];
  }

  /**
   * Helper method to get the start and end dates of the previous month.
   */
  private getPreviousMonthRange(): [Date, Date] {
    const endOfLastMonth = dayjs().subtract(1, 'month').endOf('month').toDate();
    const startOfLastMonth = dayjs().subtract(1, 'month').startOf('month').toDate();
    return [startOfLastMonth, endOfLastMonth];
  }
}
