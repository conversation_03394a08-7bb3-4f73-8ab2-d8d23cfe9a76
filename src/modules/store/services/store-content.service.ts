import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Store, StoreDocument, Testimonial, FAQ } from '../store.schema';
import { InfoBlock, InfoBlockDocument } from '../info-blocks.schema';
import {
  CreateTestimonialDto,
  StoreAboutContentDto,
  StoreAboutContentResponseDto,
  TestimonialDto,
  UpdateTestimonialDto,
  CreateFAQDto,
  UpdateFAQDto,
  StoreFAQContentDto,
  StoreFAQContentResponseDto,
  CreateInfoBlockDto,
  UpdateInfoBlockDto,
  StoreInfoBlockContentDto,
  StoreInfoBlockContentResponseDto,
} from '../../../models/dtos/StoreDtos';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';

@Injectable()
export class StoreContentService {
  constructor(
    @InjectModel(Store.name) private readonly storeModel: Model<StoreDocument>,
    @InjectModel(InfoBlock.name) private readonly infoBlockModel: Model<InfoBlockDocument>,
    protected readonly brokerTransport: BrokerTransportService,
  ) {}

  // Testimonial methods
  async getTestimonials(storeId: string): Promise<Testimonial[]> {
    const store = await this.storeModel.findById(storeId).exec();
    if (!store) {
      throw new NotFoundException('Store not found');
    }
    return store.testimonials || [];
  }

  async getPublicTestimonials(storeId: string): Promise<Testimonial[]> {
    const store = await this.storeModel.findById(storeId).exec();
    if (!store) {
      throw new NotFoundException('Store not found');
    }
    return (store.testimonials || []).filter((testimonial) => testimonial.is_visible);
  }

  async getTestimonial(storeId: string, testimonialId: string): Promise<Testimonial> {
    const store = await this.storeModel.findById(storeId).exec();
    if (!store) {
      throw new NotFoundException('Store not found');
    }

    const testimonial = store.testimonials?.find((t) => t._id.toString() === testimonialId);
    if (!testimonial) {
      throw new NotFoundException('Testimonial not found');
    }

    return testimonial;
  }

  async createTestimonial(storeId: string, createDto: CreateTestimonialDto): Promise<Testimonial> {
    const store = await this.storeModel.findById(storeId).exec();
    if (!store) {
      throw new NotFoundException('Store not found');
    }

    const newTestimonial: Partial<Testimonial> = {
      customer_name: createDto.customer_name,
      content: createDto.content,
      source: createDto.source,
      created_at: new Date(),
      is_visible: createDto.is_visible ?? true,
    };

    if (!store.testimonials) {
      store.testimonials = [];
    }

    store.testimonials.push(newTestimonial as Testimonial);
    await store.save();

    // Return the newly created testimonial with its auto-generated _id
    return store.testimonials[store.testimonials.length - 1];
  }

  async updateTestimonial(
    storeId: string,
    testimonialId: string,
    updateDto: UpdateTestimonialDto,
  ): Promise<Testimonial> {
    const store = await this.storeModel.findById(storeId).exec();
    if (!store) {
      throw new NotFoundException('Store not found');
    }

    const testimonialIndex = store.testimonials?.findIndex((t) => t._id.toString() === testimonialId);
    if (testimonialIndex === -1 || testimonialIndex === undefined) {
      throw new NotFoundException('Testimonial not found');
    }

    if (updateDto.customer_name) {
      store.testimonials[testimonialIndex].customer_name = updateDto.customer_name;
    }

    if (updateDto.content) {
      store.testimonials[testimonialIndex].content = updateDto.content;
    }

    if (updateDto.source !== undefined) {
      store.testimonials[testimonialIndex].source = updateDto.source;
    }

    if (updateDto.is_visible !== undefined) {
      store.testimonials[testimonialIndex].is_visible = updateDto.is_visible;
    }

    await store.save();

    return store.testimonials[testimonialIndex];
  }

  async deleteTestimonial(storeId: string, testimonialId: string): Promise<boolean> {
    const store = await this.storeModel.findById(storeId).exec();
    if (!store) {
      throw new NotFoundException('Store not found');
    }

    if (!store.testimonials) {
      throw new NotFoundException('Testimonial not found');
    }

    const initialLength = store.testimonials.length;
    store.testimonials = store.testimonials.filter((t) => t._id.toString() !== testimonialId);

    if (initialLength === store.testimonials.length) {
      throw new NotFoundException('Testimonial not found');
    }

    await store.save();

    return true;
  }

  // FAQ methods
  async getFAQs(storeId: string): Promise<FAQ[]> {
    const store = await this.storeModel.findById(storeId).exec();
    if (!store) {
      throw new NotFoundException('Store not found');
    }
    return store.faqs || [];
  }

  async getPublicFAQs(storeId: string): Promise<FAQ[]> {
    const store = await this.storeModel.findById(storeId).exec();
    if (!store) {
      throw new NotFoundException('Store not found');
    }
    return (store.faqs || []).filter((faq) => faq.is_visible);
  }

  async getFAQ(storeId: string, faqId: string): Promise<FAQ> {
    const store = await this.storeModel.findById(storeId).exec();
    if (!store) {
      throw new NotFoundException('Store not found');
    }

    const faq = store.faqs?.find((f) => f._id.toString() === faqId);
    if (!faq) {
      throw new NotFoundException('FAQ not found');
    }

    return faq;
  }

  async createFAQ(storeId: string, createDto: CreateFAQDto): Promise<FAQ> {
    const store = await this.storeModel.findById(storeId).exec();
    if (!store) {
      throw new NotFoundException('Store not found');
    }

    const newFAQ: Partial<FAQ> = {
      question: createDto.question,
      answer: createDto.answer,
      created_at: new Date(),
      is_visible: createDto.is_visible ?? true,
    };

    if (!store.faqs) {
      store.faqs = [];
    }

    store.faqs.push(newFAQ as FAQ);
    await store.save();

    // Return the newly created FAQ with its auto-generated _id
    return store.faqs[store.faqs.length - 1];
  }

  async updateFAQ(storeId: string, faqId: string, updateDto: UpdateFAQDto): Promise<FAQ> {
    const store = await this.storeModel.findById(storeId).exec();
    if (!store) {
      throw new NotFoundException('Store not found');
    }

    const faqIndex = store.faqs?.findIndex((f) => f._id.toString() === faqId);
    if (faqIndex === -1 || faqIndex === undefined) {
      throw new NotFoundException('FAQ not found');
    }

    if (updateDto.question) {
      store.faqs[faqIndex].question = updateDto.question;
    }

    if (updateDto.answer) {
      store.faqs[faqIndex].answer = updateDto.answer;
    }

    if (updateDto.is_visible !== undefined) {
      store.faqs[faqIndex].is_visible = updateDto.is_visible;
    }

    await store.save();

    return store.faqs[faqIndex];
  }

  async deleteFAQ(storeId: string, faqId: string): Promise<boolean> {
    const store = await this.storeModel.findById(storeId).exec();
    if (!store) {
      throw new NotFoundException('Store not found');
    }

    if (!store.faqs) {
      throw new NotFoundException('FAQ not found');
    }

    const initialLength = store.faqs.length;
    store.faqs = store.faqs.filter((f) => f._id.toString() !== faqId);

    if (initialLength === store.faqs.length) {
      throw new NotFoundException('FAQ not found');
    }

    await store.save();

    return true;
  }

  // InfoBlock methods
  async getInfoBlocks(storeId: string): Promise<InfoBlock[]> {
    return this.infoBlockModel.find({ store: storeId }).exec();
  }

  async getInfoBlocksForItem(itemId: string): Promise<InfoBlock[]> {
    return this.infoBlockModel.find({ items: itemId }).exec();
  }

  async getPublicInfoBlocks(storeId: string): Promise<InfoBlock[]> {
    return this.infoBlockModel.find({ store: storeId, is_visible: true }).exec();
  }

  async getInfoBlock(infoBlockId: string): Promise<InfoBlock> {
    const infoBlock = await this.infoBlockModel.findById(infoBlockId).exec();
    if (!infoBlock) {
      throw new NotFoundException('Info block not found');
    }
    return infoBlock;
  }

  async createInfoBlock(storeId: string, createDto: CreateInfoBlockDto): Promise<InfoBlock> {
    const newInfoBlock = new this.infoBlockModel({
      store: storeId,
      title: createDto.title,
      content_type: createDto.content_type,
      text_content: createDto.text_content,
      image_content: createDto.image_content,
      is_visible: createDto.is_visible ?? true,
      tag: createDto.tag || null,
    });

    return newInfoBlock.save();
  }

  async updateInfoBlock(infoBlockId: string, updateDto: UpdateInfoBlockDto): Promise<InfoBlock> {
    const infoBlock = await this.infoBlockModel.findById(infoBlockId).exec();
    if (!infoBlock) {
      throw new NotFoundException('Info block not found');
    }

    if (updateDto.title) {
      infoBlock.title = updateDto.title;
    }

    if (updateDto.content_type) {
      infoBlock.content_type = updateDto.content_type;
    }

    if (updateDto.text_content !== undefined) {
      infoBlock.text_content = updateDto.text_content;
    }

    if (updateDto.image_content !== undefined) {
      infoBlock.image_content = updateDto.image_content;
    }

    if (updateDto.is_visible !== undefined) {
      infoBlock.is_visible = updateDto.is_visible;
    }

    if (updateDto.tag !== undefined) {
      infoBlock.tag = updateDto.tag;
    }

    return infoBlock.save();
  }

  async deleteInfoBlock(infoBlockId: string): Promise<boolean> {
    const result = await this.infoBlockModel.deleteOne({ _id: infoBlockId }).exec();
    if (result.deletedCount === 0) {
      throw new NotFoundException('Info block not found');
    }
    return true;
  }

  async getInfoBlocksByTag(storeId: string, tagKey: string, tagValue?: any): Promise<InfoBlock[]> {
    // Create a query object
    const query: any = { store: storeId };

    // If we have both key and value, search for exact match
    if (tagValue !== undefined) {
      query[`tag.${tagKey}`] = tagValue;
    } else {
      // Otherwise just check if the key exists
      query[`tag.${tagKey}`] = { $exists: true };
    }

    return this.infoBlockModel.find(query).exec();
  }

  // Combined methods for store info blocks
  async getStoreInfoBlockContent(storeId: string): Promise<StoreInfoBlockContentResponseDto> {
    const infoBlocks = await this.getInfoBlocks(storeId);
    return { info_blocks: infoBlocks };
  }

  async updateStoreInfoBlockContent(
    storeId: string,
    data: StoreInfoBlockContentDto,
  ): Promise<StoreInfoBlockContentResponseDto> {
    if (data.info_blocks) {
      // Process each info block
      for (const infoBlockData of data.info_blocks) {
        if (infoBlockData._id) {
          // Update existing info block
          await this.updateInfoBlock(infoBlockData._id, infoBlockData);
        } else {
          // Create new info block
          await this.createInfoBlock(storeId, infoBlockData);
        }
      }
    }

    // Return updated info blocks
    const updatedInfoBlocks = await this.getInfoBlocks(storeId);
    return { info_blocks: updatedInfoBlocks };
  }

  // Combined methods for About Us and Testimonials
  async getStoreAboutContent(storeId: string): Promise<StoreAboutContentResponseDto> {
    const store = await this.storeModel.findById(storeId).exec();
    if (!store) {
      throw new NotFoundException('Store not found');
    }

    // Ensure we have a properly structured about_us object
    const aboutUs = store.about_us || { content: '', images: [] };

    return {
      about_us: {
        content: aboutUs.content || '',
        images: Array.isArray(aboutUs.images) ? aboutUs.images : [],
      },
      testimonials: store.testimonials || [],
    };
  }

  async updateStoreAboutContent(storeId: string, data: StoreAboutContentDto): Promise<StoreAboutContentResponseDto> {
    const store = await this.storeModel.findById(storeId).exec();
    if (!store) {
      throw new NotFoundException('Store not found');
    }

    try {
      // Prepare update object
      const updateData: any = {};

      // Update About Us content
      if (data.about_us) {
        updateData.about_us = {
          content: data.about_us.content || '',
          images: Array.isArray(data.about_us.images) ? data.about_us.images : [],
        };
      }

      // Process testimonials if provided
      if (data.testimonials) {
        // Process each testimonial in the array
        const testimonials = data.testimonials.map((testimonial) => {
          // If testimonial has an _id, use it as is
          if (testimonial._id) {
            return {
              ...testimonial,
              created_at: testimonial.created_at || new Date(),
              is_visible: testimonial.is_visible !== undefined ? testimonial.is_visible : true,
            };
          }

          // For new testimonials without _id, ensure other required fields are set
          return {
            ...testimonial,
            created_at: testimonial.created_at || new Date(),
            is_visible: testimonial.is_visible !== undefined ? testimonial.is_visible : true,
          };
        });

        updateData.testimonials = testimonials;
      }

      // Use direct MongoDB update to ensure all fields are updated
      await this.storeModel.updateOne({ _id: storeId }, { $set: updateData });

      // Re-fetch the store to get the updated data
      const updatedStore = await this.storeModel.findById(storeId).exec();

      // Ensure we have a properly structured about_us object
      const aboutUs = updatedStore.about_us || { content: '', images: [] };

      return {
        about_us: {
          content: aboutUs.content || '',
          images: Array.isArray(aboutUs.images) ? aboutUs.images : [],
        },
        testimonials: updatedStore.testimonials || [],
      };
    } catch (error) {
      throw error;
    }
  }

  async getStoreFAQContent(storeId: string): Promise<StoreFAQContentResponseDto> {
    const store = await this.storeModel.findById(storeId).exec();
    if (!store) {
      throw new NotFoundException('Store not found');
    }

    return {
      faqs: store.faqs || [],
    };
  }

  async updateStoreFAQContent(storeId: string, data: StoreFAQContentDto): Promise<StoreFAQContentResponseDto> {
    const store = await this.storeModel.findById(storeId).exec();
    if (!store) {
      throw new NotFoundException('Store not found');
    }

    try {
      // Prepare update object
      const updateData: any = {};

      // Process FAQs if provided
      if (data.faqs) {
        // Process each FAQ in the array
        const faqs = data.faqs.map((faq) => {
          // If FAQ has an _id, use it as is
          if (faq._id) {
            return {
              ...faq,
              created_at: faq.created_at || new Date(),
              is_visible: faq.is_visible !== undefined ? faq.is_visible : true,
            };
          }

          // For new FAQs without _id, ensure other required fields are set
          return {
            ...faq,
            created_at: faq.created_at || new Date(),
            is_visible: faq.is_visible !== undefined ? faq.is_visible : true,
          };
        });

        updateData.faqs = faqs;
      }

      // Use direct MongoDB update to ensure all fields are updated
      await this.storeModel.updateOne({ _id: storeId }, { $set: updateData });

      // Re-fetch the store to get the updated data
      const updatedStore = await this.storeModel.findById(storeId).exec();

      return {
        faqs: updatedStore.faqs || [],
      };
    } catch (error) {
      throw error;
    }
  }

  async removeItemFromInfoBlocks(itemId: string, infoBlockIds: string[]): Promise<void> {
    await this.infoBlockModel.updateMany({ _id: { $in: infoBlockIds } }, { $pull: { items: itemId } });
  }

  async addItemToInfoBlocks(itemId: string, infoBlockIds: string[]): Promise<void> {
    await this.infoBlockModel.updateMany({ _id: { $in: infoBlockIds } }, { $addToSet: { items: itemId } });
  }

  /**
   * Add an item to an info block
   */
  async addItemToInfoBlock(infoBlockId: string, itemId: string): Promise<void> {
    await this.infoBlockModel.updateOne({ _id: infoBlockId }, { $addToSet: { items: itemId } });
  }

  /**
   * Remove an item from an info block
   */
  async removeItemFromInfoBlock(infoBlockId: string, itemId: string): Promise<void> {
    await this.infoBlockModel.updateOne({ _id: infoBlockId }, { $pull: { items: itemId } });
  }

  /**
   * Update items for an info block
   */
  async updateInfoBlockItems(infoBlockId: string, itemIds: string[], storeId: string): Promise<InfoBlock> {
    // Get current items
    const infBlock = await this.infoBlockModel.findOne({ _id: infoBlockId, store: storeId }).exec();

    if (!infBlock) {
      throw new NotFoundException('Info block not found');
    }

    const currentItems = infBlock.items || [];

    // Find items to add and remove
    const itemsToAdd = itemIds.filter((id) => !currentItems.includes(id));
    const itemsToRemove = currentItems.filter((id) => !itemIds.includes(id));

    // Add info blocks to new items
    await this.brokerTransport.send(BROKER_PATTERNS.ITEM.UPDATE_INFO_BLOCKS, {
      infoBlockId,
      itemIds: itemsToAdd,
      type: 'add',
    });

    // Remove info blocks from removed items
    await this.brokerTransport.send(BROKER_PATTERNS.ITEM.UPDATE_INFO_BLOCKS, {
      infoBlockId,
      itemIds: itemsToRemove,
      type: 'remove',
    });

    infBlock.items = itemIds;
    await infBlock.save();

    return infBlock;
  }
}
