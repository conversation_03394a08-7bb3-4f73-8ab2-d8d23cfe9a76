import { Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { genChars } from '../../../utils';
import { COUNTRY_CODE } from '../../country/country.schema';
import { PaymentMethod } from '../../paymentmethod/paymentmethod.schema';
import { Subscription, SubscriptionDocument } from '../../subscription/subscription.schema';
import { DeliveryAreaService } from '../delivery-areas/delivery-areas.service';
import { Store, StoreDocument, StorePaymentMethod } from '../store.schema';
import { Invite, InviteDocument } from '../teams/invites.schema';
import { COUNTRY_CURRENCY_MAP } from '../../country/country.schema';
import { PAYMENT_METHODS } from '../../../enums/payment.enum';
import { Plan } from '../../plan/plan.schema';
import { StoreService } from './store.service';
import { StoreUpdatesService } from './store-updates.service';
import { getDocId } from '../../../utils/functions';
import { Item } from '../../item/item.schema';
import { Wallet } from '../../wallets/wallet.schema';
import { PlanOption } from '../../plan/plan-options/plan-options.schema';
import { DEFAULT_CURRENCY_MARKUP } from '../../../utils/constants';
import { ORDER_STATUSES } from '../../orders/order.schema';
import { PLAN_TYPE } from '../../../enums/plan.enum';

@Injectable()
export class StoreMigrationService {
  constructor(
    @InjectModel(Store.name) private readonly storeModel: Model<StoreDocument>,
    @InjectModel(Invite.name)
    private readonly inviteModel: Model<InviteDocument>,
    private readonly logger: Logger,
    private readonly brokerTransport: BrokerTransportService,
    private readonly jwtService: JwtService,
    private readonly deliveryArea: DeliveryAreaService,
    private readonly storeService: StoreUpdatesService,
  ) {}

  async enablePaymentsForLatestStores() {
    const limit = 250;
    const stores = await this.storeModel
      .find({ payments_enabled: false, createdAt: { $gte: new Date('2024-09-01T00:00:00+01:00') } }, { _id: 1 })
      .limit(limit)
      .lean();

    for (let store of stores) {
      const items = await this.brokerTransport
        .send<Item[]>(BROKER_PATTERNS.ITEM.ADD_DEFAULT_QUANTITIES, getDocId(store))
        .toPromise();

      await this.brokerTransport
        .send<Wallet>(BROKER_PATTERNS.WALLET.INITIATE_WALLET, {
          storeId: getDocId(store),
        })
        .toPromise();
    }

    return {
      message: `Updated for ${stores.length} stores`,
      stores,
    };
  }

  async migrateStoreCountryFormats() {
    const stores = await this.storeModel.find({});
    for (const store of stores) {
      store.country = COUNTRY_CODE.NG;
      await store.save();
    }
  }

  async migrateStorePhoneNumbers() {
    const stores = await this.storeModel.find({});

    for (const store of stores) {
      if (store.phone.startsWith('234')) {
        store.phone = '+234-' + store.phone.slice(3);
      } else if (store.phone.startsWith('0')) {
        store.phone = '+234-' + store.phone.slice(1);
      }

      await store.save();
    }
  }

  async migrateMissingSlugs() {
    const stores = await this.storeModel.find({
      $or: [{ slug: null }, { slugs: [] }],
    });

    for (const store of stores) {
      if (store.slug === null) {
        store.slug = store.slugs[0];
      } else {
        store.slugs = [store.slug];
      }

      await store.save();
    }

    return stores.map((s) => s.toJSON());
  }

  async migrateStoreNumbersToCheckoutChannels() {
    const stores = await this.storeModel.find({});

    for (const store of stores) {
      store.checkout_channels.whatsapp = store.checkout_channels.whatsapp || [];
      if (store.checkout_channels.whatsapp.length < 1) {
        store.checkout_channels.whatsapp = [
          {
            id: genChars(8) + '-' + genChars(4) + '-' + genChars(4) + '-' + genChars(4) + '-' + genChars(10),
            label: 'Main',
            phone: store.phone,
            type: 'WHATSAPP',
            primary: true,
          },
        ];

        await store.save();
      }
    }
  }

  async updateStoresHasPaidSubscriptionBatch() {
    const perPage = 2000; //Number of stores to process in each batch

    // Preload all plans
    const plans = await this.brokerTransport
      .send<Plan[]>(BROKER_PATTERNS.PLAN.GET_PLANS_LEAN, { amount: { $gt: 0 } })
      .toPromise();

    const paidPlanIds = plans.map((plan) => getDocId(plan));

    const storesToUpdate = await this.storeModel
      .find({ has_paid_subscription: { $exists: false } })
      .populate({ path: 'subscription', select: 'plan' })
      .limit(perPage)
      .lean();

    if (storesToUpdate.length === 0) {
      return {
        message: 'No more stores to update.',
        updatedCount: 0,
      };
    }

    const updates = storesToUpdate.map(async (store) => {
      let hasPaidSubscription = false;

      // Ensure subscription is treated as a Subscription object
      const subscription = store.subscription as Subscription | null;

      // Check if subscription is active and plan is a paid plan
      if (subscription && subscription.plan && paidPlanIds.includes(subscription.plan.toString())) {
        hasPaidSubscription = true;
      }

      // Update store
      const update = this.storeModel.updateOne({ _id: store._id }, { has_paid_subscription: hasPaidSubscription });

      return update;
    });

    const updatedStores = await Promise.all(updates);

    return {
      message: `Updated ${storesToUpdate.length} stores' has_paid_subscription status.`,
    };
  }

  async migrateStoresOnFreeTrialAndPaidUpfront() {
    const perPage = 5000; // Number of stores to process in each batch

    const freePlan = await this.brokerTransport
      .send<Plan>(BROKER_PATTERNS.PLAN.GET_PLAN, { type: PLAN_TYPE.STARTER })
      .toPromise();

    const storesToUpdate = await this.storeModel
      .find({
        current_plan: { $exists: true },
        subscription: { $exists: true },
        'current_plan.on_free_trial': { $exists: false },
      })
      .populate({
        path: 'subscription',
        select: 'plan last_payment_reference paid_upfront',
      })
      .limit(perPage)
      .lean();

    const updates = storesToUpdate.map(async (store) => {
      const subscription = store.subscription as Subscription | null;
      if (subscription) {
        store.current_plan = {
          ...store.current_plan,
          on_free_trial: subscription.plan.toString() !== getDocId(freePlan) && !subscription.last_payment_reference, //user is on free trial if they are not on the free plan and have not made a payment
          paid_upfront: subscription.paid_upfront,
        };

        return this.storeModel.updateOne({ _id: store._id }, { current_plan: store.current_plan });
      }
    });

    await Promise.all(updates);

    return {
      message: `Updated ${storesToUpdate.length} stores' current_plan.`,
    };
  }

  async updateStoresCurrentPlanBatch(page: number) {
    const perPage = 5000; // Number of stores to process in each batch

    // Preload all plans with their options
    const plans = await this.brokerTransport.send<Plan[]>(BROKER_PATTERNS.PLAN.GET_PLANS_LEAN, {}).toPromise();

    // Build a lookup map for plan options
    const planOptionMap = new Map<string, PlanOption>();
    plans.forEach((plan) => {
      if (plan.options && plan.options.length > 0) {
        plan.options.forEach((option) => {
          planOptionMap.set(getDocId(option).toString(), option);
        });
      }
    });

    // Fetch stores that need to be updated
    const storesToUpdate = await this.storeModel
      .find({ subscription: { $exists: true } })
      .populate({
        path: 'subscription',
        select: 'plan_option last_payment_reference paid_upfront',
      })
      .sort({ createdAt: 1 })
      .skip((page - 1) * perPage)
      .limit(perPage)
      .lean();

    if (storesToUpdate.length === 0) {
      return {
        message: 'No more stores to update.',
        updatedCount: 0,
      };
    }

    const updates = storesToUpdate.map(async (store) => {
      let currentPlan = null;

      // Ensure subscription is treated as a Subscription object
      const subscription = store.subscription as Subscription | null;

      if (subscription && subscription.plan_option) {
        const planOptionId = getDocId(subscription.plan_option).toString();
        const planOption = planOptionMap.get(planOptionId);

        if (planOption && planOption.plan_type && planOption.interval != null && planOption.interval_text != null) {
          currentPlan = {
            plan_type: planOption.plan_type,
            interval_text: planOption.interval_text,
            interval: planOption.interval,
            on_free_trial: planOption.plan_type !== PLAN_TYPE.STARTER && !subscription.last_payment_reference, //user is on free trial if they are not on the free plan and have not made a payment
            paid_upfront: subscription.paid_upfront,
          };
        }
      }

      // Update store with current_plan
      return this.storeModel.updateOne({ _id: store._id }, { current_plan: currentPlan });
    });

    await Promise.all(updates);

    return {
      message: `Updated ${storesToUpdate.length} stores' current_plan.`,
    };
  }

  async migrateCheckoutChannels() {
    const stores = await this.storeModel
      .find({ 'checkout_channels.whatsapp': { $exists: false } }, { checkout_channels: 1 })
      .lean();
    const updates: Promise<StoreDocument>[] = [];

    for (let store of stores) {
      updates.push(
        this.storeModel
          .findByIdAndUpdate(store._id, {
            checkout_channels: {
              whatsapp: store.checkout_channels as any,
            },
          })
          .exec(),
      );
    }

    await Promise.all(updates);

    return stores;
  }

  async migrateCheckoutChannelsWithPrimary() {
    const stores = await this.storeModel.find({});

    for (const store of stores) {
      for (const index in store.checkout_channels.whatsapp) {
        if (store.checkout_channels.whatsapp[index].label === 'Main')
          store.checkout_channels.whatsapp[index].primary = true;
      }

      await store.save();
    }
  }

  async migrateOwnerSubscriptionsToStore() {
    const allSubscriptions = await this.brokerTransport
      .send<SubscriptionDocument[]>(BROKER_PATTERNS.PAYMENT.GET_SUBSCRIPTIONS_WITH_OWNER, {})
      .toPromise();

    const reqs = allSubscriptions.map((sub) =>
      this.storeModel.updateMany({ owner: sub.owner as any }, { subscription: sub.id }),
    );

    await Promise.all(reqs);

    return {};
  }

  async localMigration() {
    const stores = await this.storeModel.find();

    const res = await Promise.all(
      stores.map(async (store) => {
        await store
          .updateOne({
            configuration: {
              ...store.configuration,
              view_modes: {
                grid: true,
                card: true,
                horizontal: true,
                default: 'grid',
              },
            },
          })
          .exec();
      }),
    );

    return res;
  }

  async deleteMonoOnProd(country: COUNTRY_CODE) {
    return null;

    const stores = await this.storeModel
      .find(
        { 'payment_options.3': { $exists: true }, country },
        { payment_options: 1, payments_enabled: 1, country: 1 },
      )
      .lean();

    const ux = stores.map((store) => {
      const pOCopy = JSON.parse(JSON.stringify(store.payment_options));

      pOCopy.pop();

      return this.storeModel
        .findByIdAndUpdate(store._id, {
          payment_options: pOCopy,
        })
        .exec();
    });

    await Promise.all(ux);

    return ux;
  }

  async migrateNewPaymentOptions(country: COUNTRY_CODE) {
    const methods = await this.brokerTransport
      .send<PaymentMethod[]>(BROKER_PATTERNS.PAYMENT.GET_ALL_PAYMENT_METHODS, {})
      .toPromise();
    const stores = await this.storeModel
      .find(
        { 'payment_options.0': { $exists: true }, country },
        { payment_options: 1, payments_enabled: 1, country: 1 },
      )
      .lean();
    // const updates: any[] = [];
    const updates: Promise<StoreDocument>[] = [];
    const returnedStores = [];

    function listToObject(l: StorePaymentMethod[]): { [key: string]: StorePaymentMethod } {
      const m: { [key: string]: StorePaymentMethod } = {};

      l.forEach((_) => (m[_.type] = _));

      return m;
    }

    function objectToList(o: { [key: string]: StorePaymentMethod }): StorePaymentMethod[] {
      return Object.values(o);
    }

    for (let store of stores) {
      if (!store.payments_enabled) continue;

      const mths = listToObject(
        methods.filter((f) => f.countries.includes(country)).map((o) => ({ type: o.type, enabled: true })),
      );
      const opts = listToObject(store.payment_options[store.currencies.default] || []);

      updates.push(
        this.storeModel
          .findByIdAndUpdate(store._id, {
            payment_options: {
              [store.currencies.default]: objectToList({
                ...mths,
                ...opts,
              }),
            },
          })
          .exec(),
      );

      // updates.push({
      //   payment_options: objectToList({
      //     ...mths,
      //     ...opts,
      //   }),
      // });

      returnedStores.push(store);
    }

    await Promise.all(updates);

    return returnedStores;
  }

  async migrateStoreProductConfig() {
    const stores = await this.storeModel.find({}, { configuration: 1 }).lean();
    const updates: Promise<StoreDocument>[] = [];

    for (let store of stores) {
      updates.push(
        this.storeModel
          .findByIdAndUpdate(store._id, {
            configuration: {
              ...store.configuration,
              sort_by_latest_products: false,
              show_unavailable_products: true,
            },
          })
          .exec(),
      );
    }

    await Promise.all(updates);

    return stores;
  }

  async migrateStoreCurrencies() {
    const stores = await this.storeModel.find({}, { country: 1 }).lean();
    const updates: Promise<StoreDocument>[] = [];

    for (let store of stores) {
      updates.push(
        this.storeModel
          .findByIdAndUpdate(store._id, {
            currencies: {
              default: COUNTRY_CURRENCY_MAP[store.country as COUNTRY_CODE],
              products: COUNTRY_CURRENCY_MAP[store.country as COUNTRY_CODE],
              storefront: [COUNTRY_CURRENCY_MAP[store.country as COUNTRY_CODE]],
              storefront_default: COUNTRY_CURRENCY_MAP[store.country as COUNTRY_CODE],
              rates: null,
            },
          })
          .exec(),
      );
    }

    await Promise.all(updates);

    return stores;
  }

  async migrateStoreWallets() {
    const limit = 2000;
    const stores = await this.storeModel
      .find(
        {
          wallet: { $exists: true },
          $or: [{ wallets: { $exists: false } }, { 'payment_options.0': { $exists: true } }],
        },
        { country: 1, wallet: 1, payment_options: 1 },
      )
      .limit(limit)
      .lean();
    const updates: Promise<StoreDocument>[] = [];

    for (let store of stores) {
      const currency = COUNTRY_CURRENCY_MAP[store.country as COUNTRY_CODE];
      updates.push(
        this.storeModel
          .findByIdAndUpdate(store._id, {
            wallets: [
              {
                currency,
                id: store.wallet,
              },
            ],
            payment_options: {
              [currency]: store.payment_options as any,
            },
          })
          .exec(),
      );
    }

    await Promise.all(updates);

    return stores;
  }

  async migrateStoreCurrenciesFromWallets() {
    const stores = await this.storeModel
      .find({
        'wallets.1': { $exists: true },
      })
      .lean();
    const updates: Promise<StoreDocument>[] = [];

    for (let store of stores) {
      const enabledCurrencies = store.wallets.map((w) => w.currency);
      const storefrontCurrencies = store?.currencies?.storefront;

      if (storefrontCurrencies.length < enabledCurrencies?.length) {
        const missingCurrenciesOnStorefront = enabledCurrencies.filter((c) => !storefrontCurrencies.includes(c));

        const newMarkupRates = {};
        for (let index = 0; index < missingCurrenciesOnStorefront.length; index++) {
          const currency = missingCurrenciesOnStorefront[index];
          newMarkupRates[currency] = DEFAULT_CURRENCY_MARKUP;
        }

        updates.push(
          this.storeModel
            .findByIdAndUpdate(store._id, {
              currencies: {
                ...store.currencies,
                rates: {
                  ...store.currencies.rates,
                  ...newMarkupRates,
                },
                storefront: [...storefrontCurrencies, ...missingCurrenciesOnStorefront],
              },
            })
            .exec(),
        );
      }
    }

    await Promise.all(updates);

    return stores;
  }

  async migrateStoreItemsCount() {
    const limit = 2000;
    const ids = [
      '6783165bb5c0160006c69b90',
      '63f4bf9a99ed010007f1c049',
      '63d0765827cee14ee1d77823',
      '679a579df4d4d700070927ef',
      '67902e9d1850d30007720b9d',
      '636d528bb52ff86b26525c13',
      '634a4baaae681fea203fd7ac',
      '62971c874e75a038e09f2538',
      '66c523bf6f1f1c0007b4357b',
      '6735d53649e02000070c7609',
      '62fcd895ab6e776e8a600fd9',
      '62cd2af4d9b5146f2164244a',
      '67865ac8e2ddad0007dda36f',
      '66ac227ff82b300007718bfb',
      '66fd00a07d3ace0006326557',
      '670a33e473efb400073beb89',
      '659c7ce2d907090007d1e1bc',
      '673c5240557ef800074dac9b',
      '66731ed92a284400075cc7ee',
      '63d26cda27cee1c4b8d7eafa',
      '638641356173b8bfc00c247a',
      '660f2e10007e1c00092ae1ba',
      '66f054faa52ffd0006ba1898',
      '62ce8f800c1466166ad1a1cf',
      '66d03423457b370007825e7b',
      '628e68ee62538301f5aec601',
      '678e90b51850d3000770b349',
      '668d653a93db3c00076fa794',
      '6778897f669ac900065a214d',
      '63a3945c343959cdb31bf0c8',
      '67ab19c15e80d6000757be5d',
      '65268943d95a100007e4c385',
      '630bcf8d9c261a1160ce0fc6',
      '6786ded2e2ddad0007ddbcc9',
      '66f9054e7aa5540007a5b9dd',
      '628cfb6d625383200eaebff4',
      '647dbcc219b2310007678465',
      '6724dcbbd489a000072429c6',
      '674acd18ce96820006acb1c4',
      '66034e4ea170870007fc582b',
      '61bca427c77f767504a9f112',
      '67601536d5d9be0006184057',
      '66f3b7425355d100074bad5f',
      '678900d200caf70006ed7c90',
      '63f52e7d99ed010007f1c3a4',
      '6789066d00caf70006ed8b07',
      '62b60834d2649a8e537fd1f2',
      '61ba34bec3808b7b5b6b12eb',
      '629c70bcc7dfbc05ec8d4ab6',
      '62dba066ae71db4d147f9de0',
      '67ae1783925ad2000602ea1c',
      '64626d6b927f7500070d6268',
      '62cc5cb3d9b5141c61641efb',
      '66c492e56f1f1c0007b42528',
      '6294967a4e75a0296b9f226f',
      '679a0af5f4d4d7000708ec3d',
      '6351c7f5dfaa849d39e4f93f',
      '6345e3eeb65d166304f36308',
      '65450e11e6954700071a1735',
      '672a0f71d489a00007253bc7',
      '64c14befa9a52200074b0500',
      '67bb337261bcfb00061cb5d2',
      '670ab93173efb400073bfe3d',
      '67910ccc1850d30007723dc6',
      '67bb9a82a4805e00072c3a9b',
      '66b5e6fb8beef90007bbcd78',
      '67bb939261bcfb00061cd206',
      '62cc6bd6d9b51473e2641fba',
      '678b8cc41850d30007700ca9',
      '67138c345d8a240006d13c14',
      '66ec387ca52ffd0006b99ef2',
      '645b9924ac617500077a9977',
      '653058495fbef90007e85c57',
      '637a5622b52ff85d8e52aa17',
      '649c36173727e9000744df0a',
      '634a838cae681fb4ad3fd8a5',
      '63bd255734395962851c6e3e',
    ];

    const stores = await this.storeModel
      .find({
        _id: { $in: ids },
      })
      .lean();

    // const stores = await this.storeModel
    //   .find({
    //     item_count: { $exists: false },
    //     reference: { $exists: false },
    //   })
    //   .limit(limit)
    //   .lean();

    console.log(`Stores to update: ${stores.length}`);

    const updates = stores.map((store) => async () => {
      const items_count = await this.brokerTransport
        .send<number>(BROKER_PATTERNS.ITEM.GET_TOTAL, { store: getDocId(store) })
        .toPromise();

      return this.storeModel
        .findByIdAndUpdate(getDocId(store), {
          item_count: items_count,
        })
        .exec();
    });

    await Promise.all(updates.map((fn) => fn()));
    return stores;
  }

  async migrateStorePaymentMethods() {
    return null;
    // const stores = await this.storeModel.find({ payment_options: { $exists: true } }, { payment_options: 1 }).lean();
    // const updates: Promise<StoreDocument>[] = [];

    // for (const store of stores) {
    //   updates.push(
    //     this.storeModel
    //       .findByIdAndUpdate(store._id, {
    //         payment_options: (store.payment_options || []).map((p) => {
    //           if (p.type == PAYMENT_METHODS.MONNIFY_TRANSFER) p.type = PAYMENT_METHODS.TRANSFER;

    //           return p;
    //         }),
    //       })
    //       .exec(),
    //   );
    // }

    // await Promise.all(updates);

    // return stores;
  }

  async migrateStoreCheckoutConfig() {
    const stores = await this.storeModel.find({}, { configuration: 1 }).lean();
    const updates: Promise<StoreDocument>[] = [];

    for (const store of stores) {
      updates.push(
        this.storeModel
          .findByIdAndUpdate(store._id, {
            configuration: {
              ...store.configuration,
              whatsapp_checkout_enabled: true,
              require_delivery_info: true,
            },
          })
          .exec(),
      );
    }

    await Promise.all(updates);

    return stores;
  }

  async migrateCustomCheckoutFormOptions() {
    const stores = await this.storeModel
      .find({ 'configuration.custom_checkout_form.0': { $exists: true } }, { configuration: 1 })
      .lean();
    const updates: Promise<StoreDocument>[] = [];

    for (const store of stores) {
      updates.push(
        this.storeModel
          .findByIdAndUpdate(getDocId(store), {
            configuration: {
              ...store.configuration,
              custom_checkout_form: store.configuration.custom_checkout_form.map((f: any) => {
                if (f.options.length > 0) {
                  const modifiedItem = {
                    ...f,
                    options: f.options.map((o) => (typeof o === 'string' ? { value: o, price: 0 } : o)),
                  };

                  return modifiedItem;
                }

                return f;
              }) as any,
            },
          })
          .exec(),
      );
    }

    await Promise.all(updates);

    return stores;
  }

  async migrateStoreHasTakenOrderWithPayment(page: number) {
    const batchSize = 2000;
    const stores = await this.storeModel
      .find({ 'onboarding_steps.has_taken_first_order_with_payment': { $exists: false } }, { orders: 1 })
      .skip(page * batchSize)
      .limit(batchSize)
      .lean();
    const updates: Promise<any>[] = [];

    for (const store of stores) {
      const ordersWithPayments = await this.brokerTransport
        .send<number>(BROKER_PATTERNS.ORDER.COUNT_ORDERS, {
          store: getDocId(store),
          payment_id: { $exists: true },
        })
        .toPromise();

      if (ordersWithPayments > 0) {
        store.onboarding_steps = {
          ...(store?.onboarding_steps ?? {}),
          has_taken_first_order_with_payment: true,
        };
      } else {
        store.onboarding_steps = {
          ...(store?.onboarding_steps ?? {}),
          has_taken_first_order_with_payment: false,
        };
      }

      updates.push(
        this.storeModel.findByIdAndUpdate(getDocId(store), { onboarding_steps: store.onboarding_steps }).exec(),
      );
    }

    await Promise.all(updates);

    return {
      message: `Updated ${stores.length} stores`,
      stores,
    };
  }

  async regenerateStoreMenus(stores: string[]) {
    for (const s of stores) {
      await this.storeService.generateMenu(s);
    }
    // await Promise.all(stores.map((s) => this.storeService.generateMenu(s)));
  }

  async updateStoreImagesUrlsToNewS3Migration(): Promise<void> {
    const oldBaseUrl = 'https://catlog-1.s3.eu-west-2.amazonaws.com';
    const newBaseUrl = 'https://catlog-s3.s3.eu-west-2.amazonaws.com/STORES';

    try {
      const batchSize = 2000;
      let processedCount = 0;
      let totalUpdated = 0;

      // Filter to find stores that contain the oldBaseUrl in any of the specified fields
      const filter = {
        $or: [
          { logo: { $regex: oldBaseUrl } },
          { hero_image: { $regex: oldBaseUrl } },
          { 'configuration.menu_images': { $elemMatch: { $regex: oldBaseUrl } } },
          { 'extra_info.images': { $elemMatch: { $regex: oldBaseUrl } } },
        ],
      };

      const totatDocuments = await this.storeModel.countDocuments(filter);
      this.logger.log(`Found ${totatDocuments} stores to update.`);

      let hasMoreDocuments = true;

      while (hasMoreDocuments) {
        const stores = await this.storeModel.find(filter).limit(batchSize).exec();

        if (stores.length === 0) {
          hasMoreDocuments = false;
          break;
        }

        const bulkOps = stores
          .map((store) => {
            let updated = false;

            // Update store.logo
            if (store.logo && typeof store.logo === 'string' && store.logo.includes(oldBaseUrl)) {
              store.logo = store.logo.replace(oldBaseUrl, newBaseUrl);
              updated = true;
            }

            // Update store.hero_image
            if (store.hero_image && typeof store.hero_image === 'string' && store.hero_image.includes(oldBaseUrl)) {
              store.hero_image = store.hero_image.replace(oldBaseUrl, newBaseUrl);
              updated = true;
            }

            // Update store.configuration.menu_images
            if (store.configuration && Array.isArray(store.configuration.menu_images)) {
              const newMenuImages = store.configuration.menu_images.map((imageUrl) => {
                if (typeof imageUrl === 'string' && imageUrl.includes(oldBaseUrl)) {
                  updated = true;
                  return imageUrl.replace(oldBaseUrl, newBaseUrl);
                }
                return imageUrl;
              });
              store.configuration.menu_images = newMenuImages;
            }

            // Update store.extra_info.images
            if (store.extra_info && Array.isArray(store.extra_info.images)) {
              const newExtraImages = store.extra_info.images.map((imageUrl) => {
                if (typeof imageUrl === 'string' && imageUrl.includes(oldBaseUrl)) {
                  updated = true;
                  return imageUrl.replace(oldBaseUrl, newBaseUrl);
                }
                return imageUrl;
              });
              store.extra_info.images = newExtraImages;
            }

            if (updated) {
              const updateFields: any = {};

              if (store.logo) {
                updateFields['logo'] = store.logo;
              }

              if (store.hero_image) {
                updateFields['hero_image'] = store.hero_image;
              }

              if (store.configuration && store.configuration.menu_images) {
                updateFields['configuration.menu_images'] = store.configuration.menu_images;
              }

              if (store.extra_info && store.extra_info.images) {
                updateFields['extra_info.images'] = store.extra_info.images;
              }

              return {
                updateOne: {
                  filter: { _id: store._id },
                  update: {
                    $set: updateFields,
                  },
                },
              };
            }

            return null;
          })
          .filter((op) => op !== null);

        if (bulkOps.length > 0) {
          const result = await this.storeModel.bulkWrite(bulkOps);
          totalUpdated += result.modifiedCount || 0;
          this.logger.log(`Batch updated ${result.modifiedCount || 0} stores.`);
        } else {
          hasMoreDocuments = false;
        }

        processedCount += stores.length;

        this.logger.log(`Processed ${processedCount} stores out of ${totatDocuments}.`);
      }

      this.logger.log(`Store image URLs updated successfully. Total updated: ${totalUpdated}.`);
    } catch (error) {
      this.logger.error('Error updating store image URLs:', error);
      throw error;
    }
  }
}
