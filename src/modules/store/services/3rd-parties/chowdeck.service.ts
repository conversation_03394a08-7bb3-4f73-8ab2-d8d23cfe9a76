import {
  BadRequestException,
  CACHE_MANAGER,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  PreconditionFailedException,
} from '@nestjs/common';
import BaseStoreService from '../base.service';
import { BrokerTransportService } from '../../../../broker/broker-transport.service';
import { ChowdeckRepository } from '../../../../repositories/chowdeck/index.repository';
import { InjectModel } from '@nestjs/mongoose';
import { InjectQueue } from '@nestjs/bull';
import { IStoreCategory, Store, StoreDocument } from '../../store.schema';
import { JOBS, QUEUES } from '../../../../enums/queues.enum';
import { Queue } from 'bull';
import { DocumentDefinition, Model } from 'mongoose';
import { Mutex } from 'async-mutex';
import { secondsSince } from '../../../../utils';
import { Item } from '../../../item/item.schema';
import {
  ExportItemsToChowdeckDto,
  ImportChowdeckItemsDto,
  SyncItemsWithChowdeckDto,
} from '../../../../models/dtos/StoreDtos';
import { Cache } from 'cache-manager';
import { User } from '../../../user/user.schema';
import { BROKER_PATTERNS } from '../../../../enums/broker.enum';
import { Address } from '../../../deliveries/deliveries.schema';
import { Wallet } from '../../../wallets/wallet.schema';
import { Account } from '../../../wallets/wallet.account.schema';
import {
  catlogStoreToChowdeckMerchant,
  chowdeckItemModifiersToProductOptions,
} from '../../../../repositories/chowdeck/utils';
import { ChowdeckCategory, ChowdeckItem } from '../../../../repositories/chowdeck/types';
import { ImportChowdeckItemsPayload } from '../../store.queue';
import { DeepSet, getDocId, toNaira } from '../../../../utils/functions';
import { StoreUpdatesService } from '../store-updates.service';
import { CreateItemDtoItem, UpdateItemDto } from '../../../../models/dtos/ItemDtos';
import { DEFAULT_ITEM_IMAGE } from '../../../../utils/constants';
import { Cron, CronExpression } from '@nestjs/schedule';
import dayjs from 'dayjs';
import { DELIVERY_PROVIDERS } from '../../../../enums/deliveries';
import { ITEM_SOURCE } from '../../../../enums/item.enum';
import { WalletWithAccounts } from '../../../wallets/wallet.service';
import { ResendRepository } from '../../../../repositories/resend.repository';
import { NOTIFICATION_TYPE } from '../../../user/notifications/notification.schema';
@Injectable()
export class ChowdeckStoreService extends BaseStoreService {
  private mutex: Mutex;

  constructor(
    public readonly logger: Logger,
    protected readonly brokerTransport: BrokerTransportService,
    protected readonly chowdeck: ChowdeckRepository,
    private readonly resend: ResendRepository,
    private readonly storeUpdateService: StoreUpdatesService,
    @Inject(CACHE_MANAGER) protected readonly cacheManager: Cache,
    @InjectModel(Store.name) protected readonly storeModel: Model<StoreDocument>,
    @InjectQueue(QUEUES.STORE) private storeQueue: Queue,
  ) {
    super(storeModel, brokerTransport);
    this.mutex = new Mutex();
  }

  // @Cron(CronExpression.EVERY_MINUTE)
  async syncItemsFromChowdeck() {
    if (this.mutex.isLocked()) return;
    const release = await this.mutex.acquire();
    try {
      console.log('Running Menu Item Sync from Chowdeck'.toUpperCase());
      const dbStores = await this.storeModel
        .find({
          'public_access_tokens.chowdeck.reference': { $exists: true },
          'third_party_configs.chowdeck.sync_from_chowdeck': true,
        })
        .exec();

      const syncPromises = dbStores.map((store) => {
        return this.storeQueue.add(QUEUES.STORE, {
          type: JOBS.SYNC_CHOWDECK_TO_STORE,
          data: {
            store: getDocId(store),
          },
        });
      });

      await Promise.all(syncPromises);
      console.log('Triggered Syncing Items from Chowdeck');
    } catch (error) {
      console.log(error);
      console.error('Error during Chowdeck sync:', error);
    } finally {
      release();
    }
  }

  async addChowdeckReferenceToStore(storeId: string, reference: string, private_key: string) {
    const store = await this.getAndValidateStore(storeId);

    store.access_tokens = { ...(store?.access_tokens ?? {}), chowdeck: { token: private_key } };
    store.public_access_tokens = { ...(store?.public_access_tokens ?? {}), chowdeck: { reference: reference } };
    store.third_party_configs = {
      ...(store.third_party_configs ?? {}),
      chowdeck: {
        ...(store.third_party_configs?.chowdeck ?? {}),
        sync_to_chowdeck: true,
        sync_from_chowdeck: true,
        auto_delivery: false,
      },
    };
    store.delivery_providers = [DELIVERY_PROVIDERS.SHIPBUBBLE, DELIVERY_PROVIDERS.CHOWDECK];
    store.markModified('public_access_tokens');
    store.markModified('third_party_configs');
    store.markModified('delivery_providers');
    await store.save();

    return store;
  }

  async createChowdeckMerchant(storeId: string) {
    const store = await this.getAndValidateStore(storeId);
    const storeOwner = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, { _id: store.owner })
      .toPromise();

    if (!storeOwner) {
      throw new NotFoundException('Store owner not found');
    }

    const storePickupAddress = store?.pickup_address;

    if (!storePickupAddress) {
      throw new BadRequestException('Cannot create a chowdeck merchant without a pickup address');
    }

    const storeAddress = await this.brokerTransport
      .send<Address>(BROKER_PATTERNS.DELVERIES.GET_ADDRESS, storePickupAddress)
      .toPromise();

    if (!storeAddress) {
      throw new NotFoundException('Store address not found');
    }

    if (!store?.wallet) {
      throw new BadRequestException('Cannot create a chowdeck merchant without a wallet');
    }

    const storeWallet = await this.brokerTransport
      .send<WalletWithAccounts>(BROKER_PATTERNS.WALLET.GET_WALLET, { storeId })
      .toPromise();

    if (!storeWallet || !storeWallet.accounts || storeWallet.accounts.length === 0) {
      throw new BadRequestException('Cannot create a chowdeck merchant without a wallet account');
    }

    const storeAccount = storeWallet.accounts[0];

    if (!store?.business_category?.product_types || store?.business_category?.product_types?.length === 0) {
      throw new PreconditionFailedException('Please add some tags to this store');
    }

    if (!store?.configuration?.hours || Object.values(store?.configuration?.hours).length < 7) {
      throw new PreconditionFailedException('Please add store hours for every day of the week');
    }

    const payload = catlogStoreToChowdeckMerchant(storeOwner, store, storeAccount, storeAddress);

    if (!payload) {
      throw new BadRequestException('Invalid store data');
    }

    const response = await this.chowdeck.createMerchant(payload);

    if (response.error) {
      throw new InternalServerErrorException(response.error);
    }

    store.public_access_tokens = { ...(store?.public_access_tokens ?? {}), chowdeck: { reference: payload.reference } };
    store.third_party_configs = {
      ...(store.third_party_configs ?? {}),
      chowdeck: {
        ...(store.third_party_configs?.chowdeck ?? {}),
        sync_to_chowdeck: true,
        sync_from_chowdeck: true,
        auto_delivery: false,
      },
    };
    store.delivery_providers = [DELIVERY_PROVIDERS.SHIPBUBBLE, DELIVERY_PROVIDERS.CHOWDECK];
    store.markModified('delivery_providers');
    store.markModified('public_access_tokens');
    store.markModified('third_party_configs');
    await store.save();
    return response.data;
  }

  async getChowdeckItems(storeId: string) {
    const store = await this.getAndValidateStore(storeId);
    this.validateChowdeckStore(store);

    const chowdeckReference = store?.public_access_tokens?.chowdeck?.reference;
    const chowdeckToken = store?.access_tokens?.chowdeck?.token;

    if (!chowdeckReference || !chowdeckToken) return;

    const itemsFromCache = await this.cacheManager.get(`chowdeck_items:${store.id}`);

    if (itemsFromCache) {
      return itemsFromCache;
    }

    const items = await this.chowdeck.getMenuItems(chowdeckReference, chowdeckToken);
    const categories = await this.chowdeck.getMenuCategories(chowdeckReference, chowdeckToken);

    if (!items.error && !categories.error) {
      const data = { items: items.data, categories: categories.data };
      await this.cacheManager.set(`chowdeck_items:${store.id}`, data, { ttl: 60 * 2 });
      return data;
    }

    throw new InternalServerErrorException(items.error ?? categories.error);
  }

  async importChowdeckItems(storeId: string, dto: ImportChowdeckItemsDto) {
    const store = await this.getAndValidateStore(storeId);
    this.validateChowdeckStore(store);

    // if (!store?.flags?.uses_chowbot) {
    //   throw new BadRequestException('This store does not use chowbot');
    // }

    await this.storeQueue.add(QUEUES.STORE, {
      type: JOBS.IMPORT_CHOWDECK_ITEMS,
      data: {
        store: storeId,
        chowdeck_items: dto.items,
      },
    });
    return;
  }

  async exportItemsToChowdeck(storeId: string, dto: ExportItemsToChowdeckDto) {
    const store = await this.getAndValidateStore(storeId);
    this.validateChowdeckStore(store);

    if (!store?.flags?.uses_chowbot) {
      throw new BadRequestException('This store does not use chowbot');
    }

    await this.storeQueue.add(
      QUEUES.STORE,
      {
        type: JOBS.EXPORT_ITEMS_TO_CHOWDECK,
        data: {
          store: storeId,
          items: dto.items,
        },
      },
      {
        delay: 1000 * 60,
      },
    );
    return;
  }

  async syncItems(storeId: string, dto: SyncItemsWithChowdeckDto) {
    const store = await this.getAndValidateStore(storeId);
    // this.validateChowdeckStore(store);

    if (!store?.flags?.uses_chowbot || !store?.public_access_tokens?.chowdeck) {
      return;
    }

    const sync_to_chowdeck = store?.third_party_configs?.chowdeck?.sync_to_chowdeck;
    const sync_from_chowdeck = store?.third_party_configs?.chowdeck?.sync_from_chowdeck;
    if ((dto.from_chowdeck && !sync_from_chowdeck) || (!dto.from_chowdeck && !sync_to_chowdeck)) {
      return;
    }

    await this.storeQueue.add(
      QUEUES.STORE,
      {
        type: dto.from_chowdeck ? JOBS.SYNC_CHOWDECK_TO_STORE : JOBS.SYNC_STORE_TO_CHOWDECK,
        data: {
          store: storeId,
        },
      },
      {
        delay: 1000 * 60,
      },
    );
  }

  async autoSyncItemsFromChowdeck(storeId: string) {
    console.log('STARTING SYNC');
    const store = await this.storeModel.findById(storeId).populate('owner').lean();
    const merchantReference = store.public_access_tokens?.chowdeck?.reference;
    const chowdeckToken = store?.access_tokens?.chowdeck?.token;

    if (!merchantReference || !chowdeckToken) return;
    let categories = store?.categories ?? [];

    console.log('FETCHED CATEGORIES');

    const itemsRes = await this.chowdeck.getMenuItems(merchantReference, chowdeckToken);

    console.log({ itemsRes });

    if (itemsRes.error) {
      return;
    }

    console.log('FETCHED ITEMS');
    const chowdeckItems = itemsRes?.data as ChowdeckItem[];
    const dbItems = await this.brokerTransport
      .send<(Item & { id: string })[]>(BROKER_PATTERNS.ITEM.GET_ITEMS, {
        store: store._id,
        'meta.chowdeck_id': { $exists: true },
      })
      .toPromise();

    console.log({ dbItems });
    // const categoriesToCreate = new DeepSet<ChowdeckCategory>();

    const itemsToUpdate: { payload: UpdateItemDto; filter: any }[] = [];

    for (let dbItem of dbItems) {
      console.log(`Syncing ${dbItem.name}`);
      const lastUpdate = dbItem?.meta['last_chowdeck_update'] as Date;
      const lastUpdatedGreaterThan5Mins = !lastUpdate
        ? true
        : Math.abs(dayjs(lastUpdate).diff(dayjs(new Date()), 'minutes')) > 5;

      console.log({ lastUpdatedGreaterThan5Mins });
      if (!lastUpdatedGreaterThan5Mins) continue;

      const chowdeckItem = chowdeckItems.find((chowdeckItem) => dbItem?.meta?.chowdeck_id === chowdeckItem.id);

      console.log({ chowdeckItem });

      if (!chowdeckItem) continue;

      let category: IStoreCategory = await this.resolveCategory(categories, chowdeckItem, store);
      const shouldUpdate =
        toNaira(chowdeckItem.price) !== dbItem.price ||
        (chowdeckItem.is_published && chowdeckItem.is_active !== dbItem.available) ||
        chowdeckItem.in_stock !== dbItem.is_always_available ||
        chowdeckItem?.category?.id !== category?.meta?.chowdeck?.id;

      console.log({ shouldUpdate });

      if (shouldUpdate) {
        console.log(`Updating item: ${dbItem.name}`.toLocaleUpperCase());
        //update item
        itemsToUpdate.push({
          filter: { _id: getDocId(dbItem) },
          payload: {
            // description: chowdeckItem.description,
            // images: [...dbItem.images, ...chowdeckItem.images.map((i) => i.path)],
            price: toNaira(chowdeckItem.price),
            is_always_available: chowdeckItem.in_stock,
            available: chowdeckItem.is_published && chowdeckItem.is_active,
            category: category?._id,
            meta: {
              ...dbItem.meta,
              chowdeck_id: chowdeckItem.id,
              last_chowdeck_update: new Date(chowdeckItem?.updated_at),
            },
          },
        });
      }
    }

    await this.brokerTransport.send<Item[]>(BROKER_PATTERNS.ITEM.UPDATE_ITEMS, itemsToUpdate).toPromise();

    return {};
  }

  async autoItemSyncToChowdeck(item: Item) {
    const store = await this.getAndValidateStore(item.store);

    if (
      item.last_chowdeck_update &&
      secondsSince(item.last_chowdeck_update) < 60 &&
      store?.public_access_tokens?.chowdeck
    ) {
      return;
    }

    if (!store?.third_party_configs?.chowdeck?.sync_to_chowdeck || !store?.flags?.uses_chowbot) {
      return;
    }

    await this.storeQueue.add(
      QUEUES.STORE,
      {
        type: JOBS.SYNC_SINGLE_ITEM_TO_CHOWDECK,
        data: item,
      },
      {
        delay: 1000 * 60,
      },
    );

    return;
  }

  validateChowdeckStore(store: Store) {
    if (store?.public_access_tokens?.chowdeck && store?.access_tokens?.chowdeck?.token) {
      return;
    }

    throw new PreconditionFailedException('Chowdeck merchant reference not found');
  }

  async importBulkItemsFromChowdeck(jobData: ImportChowdeckItemsPayload) {
    const store = await this.storeModel.findById(jobData.store).populate('owner').lean();
    let categories = store?.categories ?? [];
    const merchantReference = store.public_access_tokens?.chowdeck?.reference;
    const chowdeckToken = store?.access_tokens?.chowdeck.token;

    if (!merchantReference || !chowdeckToken) return;

    let cachedData = await this.cacheManager.get<{
      categories: ChowdeckCategory[];
      items: ChowdeckItem[];
    }>(`chowdeck_items:${store._id}`);

    if (!cachedData) {
      const items = await this.chowdeck.getMenuItems(merchantReference, chowdeckToken);
      const categories = await this.chowdeck.getMenuCategories(merchantReference, chowdeckToken);
      if (items?.data && categories?.data) {
        cachedData = { items: items.data, categories: categories.data };
      } else return;
    }

    const chowdeckItems = cachedData?.items?.filter((i) => jobData.chowdeck_items.includes(i.id));

    // ITEMS THAT ARE ALREADY IMPORTED
    const chowdeckItemNames = chowdeckItems.map((item) => item.name);
    const dbItems = await this.brokerTransport
      .send<(Item & { id: string })[]>(BROKER_PATTERNS.ITEM.GET_MATCHING_ITEMS_WITH_NAME, {
        storeId: store._id,
        names: chowdeckItemNames,
        otherQuery: {
          'meta.chowdeck_id': { $in: [...jobData.chowdeck_items] },
        },
      })
      .toPromise();

    const itemsToCreate: CreateItemDtoItem[] = [];
    const itemsToUpdate: { payload: UpdateItemDto; filter: any }[] = [];

    for (let chowdeckItem of chowdeckItems) {
      // const chowdeckItemRes = await this.chowdeck.getMenuItem(merchantReference, cItem.reference, chowdeckToken);
      // console.log({ chowdeckItemRes });
      // const chowdeckItem = chowdeckItemRes?.data ?? cItem;

      let category: IStoreCategory = await this.resolveCategory(categories, chowdeckItem, store);

      const dbItem = dbItems.find(
        (i) => i?.meta?.chowdeck_id === chowdeckItem.id || i.name.toLowerCase() === chowdeckItem.name.toLowerCase(),
      );

      if (dbItem) {
        //update item
        itemsToUpdate.push({
          filter: { _id: getDocId(dbItem) },
          payload: {
            description: chowdeckItem.description,
            images: [
              ...dbItem.images,
              ...chowdeckItem.images.filter((i) => !dbItem.images.includes(i.path)).map((i) => i.path),
            ],
            price: toNaira(chowdeckItem.price),
            is_always_available: chowdeckItem.in_stock,
            available: chowdeckItem.is_published && chowdeckItem.is_active,
            category: category?._id,
            meta: {
              ...dbItem.meta,
              chowdeck_id: chowdeckItem.id,
              last_chowdeck_update: new Date(chowdeckItem?.updated_at),
            },
            variants: chowdeckItemModifiersToProductOptions(chowdeckItem?.modifiers),
          },
        });
      } else {
        itemsToCreate.push({
          description: chowdeckItem.description,
          images: chowdeckItem.images.length > 0 ? chowdeckItem.images.map((i) => i.path) : [DEFAULT_ITEM_IMAGE], //Todo: Ideally we should upload the images from chowdeck to our servers
          name: chowdeckItem.name,
          price: toNaira(chowdeckItem.price),
          thumbnail: 0,
          is_always_available: chowdeckItem.in_stock,
          available: chowdeckItem.is_published && chowdeckItem.is_active,
          category: category?._id,
          meta: {
            chowdeck_id: chowdeckItem.id,
          },
          upload_source: ITEM_SOURCE.CHOWDECK,
          variants: chowdeckItemModifiersToProductOptions(chowdeckItem?.modifiers),
        });
      }
    }

    if (itemsToUpdate?.length > 0) {
      await this.brokerTransport.send<Item[]>(BROKER_PATTERNS.ITEM.UPDATE_ITEMS, itemsToUpdate).toPromise();
    }

    if (itemsToCreate?.length > 0) {
      await this.brokerTransport
        .send<Item[]>(BROKER_PATTERNS.ITEM.CREATE_ITEMS, { store: store._id, items: [...itemsToCreate] })
        .toPromise();
    }

    await this.brokerTransport
      .send(BROKER_PATTERNS.USER.SEND_PUSH_NOTIFICATION, {
        store: store._id,
        owner_only: true,
        message: {
          title: 'Products import successful ✅',
          message: `We've successfully imported ${chowdeckItems.length} products from Chowdeck`,
          path: '/dashboard',
        },
        notification_type: NOTIFICATION_TYPE.GENERIC,
      })
      .toPromise();

    const storeOwner = store?.owner;

    const emailData = {
      to: storeOwner.email,
      subject: 'Your products have been imported from Chowdeck ✅',
      data: {
        name: storeOwner.name.split(' ')[0],
        products_count: chowdeckItems.length,
        source: 'Chowdeck',
        cta_url: `${process.env.CATLOG_DASHBOARD}/products`,
      },
    };

    await this.resend.sendEmail(BROKER_PATTERNS.MAIL.PRODUCT_IMPORT_SUCCESSFUL, emailData);

    return {};
  }

  //Checks for categories that exists on Chowdeck and compares with the store categories
  //Creates missing categories on Catlog
  async resolveCategory(
    catlogCategories: IStoreCategory[],
    chowdeckItem: ChowdeckItem,
    store: DocumentDefinition<StoreDocument>,
  ) {
    let category: IStoreCategory = null;
    const categoryMatch = catlogCategories.find(
      (c) =>
        c.name.toLowerCase() === chowdeckItem.category.name.toLowerCase() ||
        c?.meta?.chowdeck?.id === chowdeckItem?.category?.id,
    );

    if (!categoryMatch) {
      category = await this.storeUpdateService.addSingleCategory(getDocId(store), {
        name: chowdeckItem.category.name,
        emoji: '',
        meta: { chowdeck: { id: chowdeckItem?.category?.id } },
      } as any);
      catlogCategories.push(category);
    } else {
      //Update the category chowdeck id if it doesn't exist or has changed
      if (categoryMatch?.meta?.chowdeck?.id !== chowdeckItem?.category?.id) {
        const categoriesUpdateData = catlogCategories.map((c) => {
          if (c._id === categoryMatch._id) {
            c.meta = { ...(c.meta ?? {}), chowdeck: { id: chowdeckItem?.category?.id } };
          }
          return c;
        });

        const categories = await this.storeUpdateService.addOrUpdateCategories(getDocId(store), categoriesUpdateData);

        catlogCategories.length = 0;
        catlogCategories.push(...categories);
      }

      category = categoryMatch;
    }

    return category;
  }
}
