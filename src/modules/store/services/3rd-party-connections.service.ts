import {
  BadRequestException,
  CACHE_MANAGER,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  PreconditionFailedException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { <PERSON><PERSON>, CronExpression } from '@nestjs/schedule';
import { Mutex } from 'async-mutex';
import dayjs from 'dayjs';
import { Model } from 'mongoose';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { InstagramRepository } from '../../../repositories/instagram.repository';
import { S3Repository } from '../../../repositories/s3.repositories';
import { Store, StoreDocument } from '../store.schema';
import BaseStoreService from './base.service';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { Cache } from 'cache-manager';
import { QUEUES } from '../../../enums/queues.enum';
import { ChowdeckRepository } from '../../../repositories/chowdeck/index.repository';
import { isUsingProdDBLocally } from '../../../utils';

@Injectable()
export class StoreThirdPartyConnectionsService extends BaseStoreService {
  private mutex: Mutex;

  constructor(
    protected readonly logger: Logger,
    protected readonly brokerTransport: BrokerTransportService,
    protected readonly s3: S3Repository,
    protected readonly instagram: InstagramRepository,
    protected readonly chowdeck: ChowdeckRepository,
    @Inject(CACHE_MANAGER) protected readonly cacheManager: Cache,
    @InjectModel(Store.name) protected readonly storeModel: Model<StoreDocument>,
  ) {
    super(storeModel, brokerTransport);
    this.mutex = new Mutex();
  }

  //INSTAGRAM
  async generateInstagramAccessToken(access_code: string, redirect_uri: string, storeId: string) {
    const response = await this.instagram.getAccessToken(access_code, redirect_uri);
    if (response.error) throw new PreconditionFailedException(response.error);

    const store = await this.storeModel.findById(storeId).select({ access_tokens: 1 });
    const access_tokens = { ...(store.access_tokens ?? {}) };

    access_tokens['instagram'] = { token: response?.data?.access_token, meta: { last_refresh: new Date() } };
    store.access_tokens = access_tokens;
    await store.save();
    return store;
  }

  async disconnectInstagram(storeId: string) {
    const store = await this.storeModel.findById(storeId).select({ access_tokens: 1 });
    const access_tokens = { ...(store.access_tokens ?? {}) };

    if (access_tokens['instagram']) {
      delete access_tokens['instagram'];
      store.access_tokens = access_tokens;
      await store.save();
    }

    return store;
  }

  async getInstagramMedia(
    storeId: string,
    pagination?: {
      next: string;
      previous: string;
      limit: number;
    },
  ) {
    const store = await this.storeModel.findById(storeId).select({ access_tokens: 1 }).lean();
    //check if store token is valid
    const res = await this.instagram.getMedia({ access_token: store.access_tokens?.instagram?.token, pagination });
    return res;
  }

  async getAllInstagramMediaFromPosts(
    storeId: string,
    pagination?: {
      next: string;
      previous: string;
      limit: number;
    },
  ) {
    const store = await this.storeModel.findById(storeId).select({ access_tokens: 1 }).lean();
    //check if store token is valid
    const res = await this.instagram.getAllMediaFromPosts({ access_token: store.access_tokens?.instagram?.token, pagination });
    return res;
  }

  async getInstagramAlbumMedia(storeId: string, mediaId: string) {
    const store = await this.storeModel.findById(storeId).select({ access_tokens: 1 }).lean();
    //check if store token is valid
    const res = await this.instagram.getAlbumMedia({
      access_token: store.access_tokens?.instagram?.token,
      media_id: mediaId,
    });

    return res;
  }

  async getMultipleInstagramAlbumMedia(storeId: string, mediaIds: string[]) {
    const store = await this.storeModel.findById(storeId).select({ access_tokens: 1 }).lean();

    const reqs = mediaIds.map((m) =>
      this.instagram.getAlbumMedia({
        access_token: store.access_tokens?.instagram?.token,
        media_id: m,
      }),
    );

    const res = await Promise.all(reqs);
    const flattenedMediaData = res
      .map((m, index) => (m?.data ? m.data?.map((res) => ({ ...res, post_id: mediaIds[index] })) : null))
      .flat();

    return flattenedMediaData;
  }

  async checkInstagramToken(storeId: string) {
    const store = await this.storeModel.findById(storeId).select({ access_tokens: 1 }).lean();
    const token = store?.access_tokens?.instagram?.token;

    if (!token) {
      throw new NotFoundException('Instagram token not found');
    }

    return;
  }

  async getInstagramUser(storeId: string) {
    const store = await this.storeModel.findById(storeId).select({ access_tokens: 1 }).lean();
    const token = store?.access_tokens?.instagram?.token;
    if (!token) {
      throw new NotFoundException('Instagram token not found');
    }

    const res = await this.instagram.getInstagramUser(token);
    return res;
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async tokenRefreshJob() {
    if (this.mutex.isLocked() || isUsingProdDBLocally()) return;

    await this.mutex.acquire();
    console.log('Running token refresh'.toUpperCase());

    const thirtyDaysAgo = dayjs().subtract(30, 'day').toDate();
    const stores = await this.storeModel
      .find({
        $and: [
          { 'access_tokens.instagram.meta.last_refresh': { $exists: true } },
          { 'access_tokens.instagram.meta.last_refresh': { $lte: thirtyDaysAgo } },
        ],
      })
      .select({ access_tokens: 1 });

    for (let i = 0; i < stores.length; i++) {
      const store = stores[i];
      const response = await this.instagram.refreshLongLivedAccessToken(store?.access_tokens?.instagram?.token);

      if (response.error) throw new PreconditionFailedException(response.error);

      const access_tokens = { ...(store.access_tokens ?? {}) };
      access_tokens['instagram'] = { token: response?.data?.access_token, meta: { last_refresh: new Date() } };
      store.access_tokens = access_tokens;

      await store.save();
    }
    this.mutex.release();
  }
}
