import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Store, StoreDocument } from '../store.schema';
import { FilterQuery, Model, mongo } from 'mongoose';
import { YearWrap, YearWrapDocument } from './year-wrap.schema';
import { Order, OrderDocument } from '../../orders/order.schema';
import { Referrals, ReferralsDocument } from '../../user/referrals/referrals.schema';
import { CatlogCredits, CatlogCreditsDocument } from '../../user/credits/credits.schema';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import dayjs from 'dayjs';
import { PaymentGettersService } from '../../payment/services/payment.getters.service';
import { getDocId } from '../../../utils/functions';
import { PAYMENT_METHODS, PAYMENT_STATUS, PAYMENT_TYPES } from '../../../enums/payment.enum';
import { Payment } from '../../payment/payment.schema';
import mongoose from 'mongoose';
import { COUNTRY_CODE } from '../../country/country.schema';

interface TopProducts {
  id: string;
  name: string;
  thumbnail: string;
  orders_count: number;
}

interface Customer {
  id: string;
  name: string;
  phone: string;
  orders_count: number;
}

export interface OrdersWrapData {
  topProduct: TopProducts;
  topProductOrders: number;
  topCustomer: Customer;
  otherTopCustomers: Customer[];
  totalCustomers: number;
  repeatCustomers: number;
  otherTopProducts: TopProducts[];
  topOrdersLocation: string;
  noOfOrdersFromTopLocation: number;
  monthWithHighestOrders: number;
  totalOrderCount: number;
  totalOrderVolume: number;
  paidOrderCount: number;
}

@Injectable()
export class YearWrapService {
  private readonly BATCH_SIZE = 2000; // Process 2000 stores per batch
  constructor(
    @InjectModel(Store.name) private readonly storeModel: Model<StoreDocument>,
    @InjectModel(YearWrap.name) private readonly yearWrapModel: Model<YearWrapDocument>,
    private readonly paymentGetterService: PaymentGettersService,
    private readonly brokerTransport: BrokerTransportService,
    private readonly logger: Logger,
  ) {}

  async computeYearWrapData(batchSize = this.BATCH_SIZE) {
    const startOfYear = dayjs('2024-01-01').toDate();
    const endOfYear = dayjs('2024-12-31').toDate();

    // Get all stores with at least one payment within the time frame
    const storesWithPayments = await this.paymentGetterService.getStoresWithPayments(startOfYear, endOfYear);

    let batchNumber = 0;
    let storesProcessed = 0;

    while (true) {
      // Fetch stores in batches that have at least one payment
      const stores = await this.storeModel
        .find({
          _id: { $in: storesWithPayments },
          is_wrap_data_created: { $ne: true }, // Skip stores that already have wrap data
        })
        .skip(batchNumber * batchSize)
        .limit(batchSize)
        .exec();

      if (stores.length === 0) {
        // If no more stores to process, exit the loop
        break;
      }

      // Process the stores in the batch
      for (const store of stores) {
        await this.processStoreData(store);

        // Mark the store as processed (wrap data created)
        store.is_wrap_data_created = true;
        await store.save();
      }

      storesProcessed += stores.length;
      batchNumber++;

      this.logger.log(`Processed ${storesProcessed} stores so far...`);

      // await new Promise(resolve => setTimeout(resolve, 1000)); // Add a delay between batches
    }

    this.logger.log('Year wrap data generation completed for all stores.');

    return;
  }

  private async processStoreData(store: Store) {
    const startOfYear = dayjs('2024-01-01').toDate();
    const endOfYear = dayjs('2024-12-31').toDate();

    const getCreateAtFilter = (from: Date, to: Date) => ({
      $gte: from,
      $lt: to,
    });

    const created_at = getCreateAtFilter(startOfYear, endOfYear);

    // Get payment volume and counts
    const total_payments_volume = await this.paymentGetterService.getTotalPayments({
      store: store._id.toString(),
      status: PAYMENT_STATUS.SUCCESS,
      $or: [
        { type: PAYMENT_TYPES.INVOICE },
        { type: PAYMENT_TYPES.WALLET, payment_method: PAYMENT_METHODS.DIRECT_TRANSFER },
      ],
      created_at,
    } as FilterQuery<Payment>);

    const total_payments_count = await this.paymentGetterService.getPaymentCount({
      store: store._id,
      status: PAYMENT_STATUS.SUCCESS,
      $or: [
        { type: PAYMENT_TYPES.INVOICE },
        { type: PAYMENT_TYPES.WALLET, payment_method: PAYMENT_METHODS.DIRECT_TRANSFER },
      ],
      created_at,
    });

    // Filter orders from 2024
    const {
      topProduct,
      topProductOrders,
      otherTopProducts,
      topCustomer,
      otherTopCustomers,
      totalCustomers,
      repeatCustomers,
      topOrdersLocation,
      noOfOrdersFromTopLocation,
      monthWithHighestOrders,
      totalOrderCount,
      paidOrderCount,
      totalOrderVolume,
    } = await this.brokerTransport
      .send<OrdersWrapData>(BROKER_PATTERNS.ORDER.GET_WRAP_DATA, {
        store: store._id,
        startDate: startOfYear,
        endDate: endOfYear,
      })
      .toPromise();

    // Calculate Store Metrics
    const storeVisits = store.total_visits;
    const referrals = await this.brokerTransport
      .send<number>(BROKER_PATTERNS.USER.REFERRAL_WRAP_DATA, {
        ownerId: store.owner,
        startDate: startOfYear,
        endDate: endOfYear,
      })
      .toPromise();
    const catalogCredits = await this.brokerTransport
      .send<number>(BROKER_PATTERNS.USER.CREDITS.CREDITS_EARNED_WRAP_DATA, {
        ownerId: store.owner,
        startDate: startOfYear,
        endDate: endOfYear,
      })
      .toPromise();

    const fulfilledDeliveries = await this.brokerTransport
      .send<number>(BROKER_PATTERNS.DELVERIES.GET_DELIVERIES_WRAP_DATA, {
        storeId: store._id,
        startDate: startOfYear,
        endDate: endOfYear,
      })
      .toPromise();

    // Apply Benchmarks
    const storeVisitScore = Math.min((storeVisits / 15000) * 15, 15); // 15% benchmark for Store Visits (Top Value = 15k)
    const orderScore = Math.min((totalOrderCount / 500) * 25, 25); // 25% benchmark for No of Orders (Top Value = 1000)
    const paidOrderScore = Math.min((paidOrderCount / 250) * 15, 15); // 15% benchmark for Paid Orders (Top Value = 500)
    const paymentVolumeScore =
      store.currencies.default === 'NGN'
        ? Math.min((total_payments_volume / 3500000000) * 40, 40) // 40% benchmark for Payment Volume (Top Value = 50M NGN)
        : Math.min((total_payments_volume / 2500000) * 40, 40); // 40% benchmark for Payment Volume (Top Value = 25K GHS)
    const referralScore = Math.min((referrals / 25) * 5, 5); // 5% benchmark for Referrals (Top Value = 100)

    const totalScore = storeVisitScore + orderScore + paidOrderScore + paymentVolumeScore + referralScore;

    // Grading
    let grade = 'D';
    if (totalScore >= 70) grade = 'A';
    else if (totalScore >= 40) grade = 'B';
    else if (totalScore >= 15) grade = 'C';

    // Metaphor
    const metaphor = this.getMetaphor(grade);

    // Save Year Wrap Data
    const yearWrapData = {
      store: store._id,
      store_currency: store.currencies.default,
      no_of_store_visits: storeVisits,
      total_order_volume: totalOrderVolume || 0,
      total_order_count: totalOrderCount,
      month_with_highest_orders: monthWithHighestOrders,
      top_product: topProduct,
      top_product_orders: topProductOrders,
      other_top_products: otherTopProducts,
      top_orders_location: topOrdersLocation || (store.country === COUNTRY_CODE.NG ? 'Nigeria' : 'Ghana'),
      no_of_orders_for_location: noOfOrdersFromTopLocation,
      no_of_customers: totalCustomers,
      no_of_repeat_customers: repeatCustomers,
      top_customer: topCustomer,
      other_top_customers: otherTopCustomers,
      no_of_fulfilled_deliveries: fulfilledDeliveries,
      no_of_referrals: referrals,
      total_payments_volume,
      total_payments_count,
      catlog_credits_earned: catalogCredits,
      grading: totalScore,
      metaphor,
      percentile: 0, // Placeholder - will need percentile computation
    };

    await this.yearWrapModel.findOneAndUpdate({ store: store._id }, yearWrapData, {
      upsert: true,
      new: true,
    });
  }

  private getMetaphor(grade: string): string {
    switch (grade) {
      case 'A':
        return 'Iroko Tree';
      case 'B':
        return 'Oak Tree';
      case 'C':
        return 'Bamboo';
      case 'D':
        return 'Lily';
      default:
        return '';
    }
  }

  async getYearWrapData(storeId: string): Promise<YearWrap> {
    const yearWrapData = await this.yearWrapModel
      .findOneAndUpdate(
        { store: storeId },
        { $inc: { private_views: 1 } }, // Increment private_views by 1
        { new: true }, // Return the updated document
      )
      .populate({
        path: 'store',
        select: 'name',
      })
      .exec();
    if (!yearWrapData) throw new BadRequestException('Year Wrap Data not found for this store');
    return yearWrapData;
  }

  async getYearWrapByStoreSlug(storeSlug: string): Promise<YearWrap | { error: string }> {
    const store = await this.storeModel.findOne({ slug: storeSlug }).exec();

    if (!store) return { error: 'Store Not Found' };

    const yearWrapData = await this.yearWrapModel
      .findOneAndUpdate({ store: getDocId(store) }, { $inc: { public_views: 1 } }, { new: true })
      .select('-top_customer -other_top_customers')
      .populate({
        path: 'store',
        select: 'name',
      })
      .exec();
    return yearWrapData;
  }
}
