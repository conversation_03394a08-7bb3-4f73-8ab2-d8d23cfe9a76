import { BadRequestException, Body, Controller, Get, Param, Post, Req, UseGuards } from '@nestjs/common';
import { YearWrapService } from './year-wrap.service';
import { JwtAuthGuard } from '../../../jwt/jwt-auth.guard';
import { IRequest } from '../../../interfaces/request.interface';
import { ApiNotFoundResponse, ApiOkResponse, ApiSecurity } from '@nestjs/swagger';
import { InternalApiJWTGuard } from '../../../guards/api.guard';
import { YearWrap } from './year-wrap.schema';

@Controller('store/year-wrap')
export class YearWrapController {
  constructor(private readonly yearWrapService: YearWrapService) {}

  @Get()
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  async getYearWrapData(@Req() req: IRequest) {
    const data = await this.yearWrapService.getYearWrapData(req.user.store.id);

    return {
      message: 'Year Wrap Data Fetched',
      data,
    };
  }

  @Get(':slug')
  @ApiOkResponse({ type: YearWrap })
  @ApiNotFoundResponse({
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
        },
      },
    },
  })
  async getYearWrapDataBySlug(@Param('slug') slug: string) {
    const data = await this.yearWrapService.getYearWrapByStoreSlug(slug);

    if ((data as any)?.error) {
      throw new BadRequestException((data as any).error);
    }

    return {
      message: 'Year Wrap Data Fetched',
      data,
    };
  }

  @Post('compute')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async computeYearWrapData(@Body() body: { batchSize: number }) {
    const data = await this.yearWrapService.computeYearWrapData(body.batchSize);

    return {
      message: 'Year Wrap Data Computed',
      data,
    };
  }
}
