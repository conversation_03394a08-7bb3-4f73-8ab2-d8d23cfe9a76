import { Test, TestingModule } from '@nestjs/testing';
import { YearWrapService } from './year-wrap.service';

describe('YearWrapService', () => {
  let service: YearWrapService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [YearWrapService],
    }).compile();

    service = module.get<YearWrapService>(YearWrapService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
