import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import mongoose, { Document } from 'mongoose';
import { Store } from '../store.schema';

export type YearWrapDocument = YearWrap & Document;

@Schema({ timestamps: true })
export class YearWrap {
  public id: string;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Store' })
  store: string | Store;

  @ApiProperty()
  @Prop({ type: String })
  store_currency: string;

  @ApiProperty()
  @Prop({ type: Number })
  no_of_store_visits: number;

  @ApiProperty()
  @Prop({ type: Number })
  total_order_volume: number;

  @ApiProperty()
  @Prop({ type: Number })
  total_order_count: number;

  @ApiProperty()
  @Prop({ type: Number })
  month_with_highest_orders: number;

  @ApiProperty()
  @Prop({
    type: Object,
    required: true,
  })
  top_product: {
    name: string;
    thumbnail: string;
  };

  @ApiProperty()
  @Prop({ type: Number })
  top_product_orders: number;

  @ApiProperty()
  @Prop({
    type: [
      {
        name: String,
        thumbnail: String,
      },
    ],
    default: [],
  })
  other_top_products: {
    name: string;
    thumbnail: string;
  }[];

  @ApiProperty()
  @Prop({ type: String })
  top_orders_location: string;

  @ApiProperty()
  @Prop({ type: Number })
  no_of_orders_for_location: number;

  @ApiProperty()
  @Prop({ type: Number })
  no_of_customers: number;

  @ApiProperty()
  @Prop({ type: Number })
  no_of_repeat_customers: number;

  @ApiProperty()
  @Prop({
    type: Object,
    required: true,
  })
  top_customer: {
    name: string;
    orders_count: number;
    phone: string;
  };

  @ApiProperty()
  @Prop({
    type: [
      {
        name: String,
        orders_count: Number,
      },
    ],
    default: [],
  })
  other_top_customers: {
    name: string;
    orders_count: number;
    phone: string;
  }[];

  @ApiProperty()
  @Prop({ type: Number })
  no_of_fulfilled_deliveries: number;

  @ApiProperty()
  @Prop({ type: Number })
  no_of_referrals: number;

  @ApiProperty()
  @Prop({ type: Number })
  total_payments_volume: number;

  @ApiProperty()
  @Prop({ type: Number })
  total_payments_count: number;

  @ApiProperty()
  @Prop({ type: Number })
  catlog_credits_earned: number;

  @ApiProperty()
  @Prop({ type: Number })
  grading: number;

  @ApiProperty()
  @Prop({ type: String })
  metaphor: string;

  @ApiProperty()
  @Prop({ type: Number })
  percentile: number;

  @ApiProperty()
  @Prop({ type: Number })
  public_views?: number;

  @ApiProperty()
  @Prop({ type: Number })
  private_views?: number;
}

export const YearWrapSchema = SchemaFactory.createForClass(YearWrap);
