import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Logger,
  NotFoundException,
  Param,
  Post,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiParam, ApiResponse, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { PlanPermissions, RolePermissions } from '../../decorators/permission.decorator';
import { PlanGuard } from '../../guards/plan.guard';
import { RoleGuard } from '../../guards/role.guard';
import { IRequest } from '../../interfaces/request.interface';
import { JwtAuthGuard } from '../../jwt/jwt-auth.guard';
import {
  CreateFAQDto,
  CreateInfoBlockDto,
  CreateTestimonialDto,
  FAQResponseDto,
  InfoBlockResponseDto,
  StoreAboutContentDto,
  StoreFAQContentDto,
  StoreInfoBlockContentDto,
  TestimonialResponseDto,
  UpdateFAQDto,
  UpdateInfoBlockDto,
  UpdateTestimonialDto,
} from '../../models/dtos/StoreDtos';
import { SCOPES } from '../../utils/permissions.util';
import { StoreContentService } from './services/store-content.service';

@Controller('store')
@ApiTags('Store Content')
export class StoreContentController {
  private readonly logger = new Logger(StoreContentController.name);

  constructor(private readonly storeContentService: StoreContentService) {}

  @Get('testimonials')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_DETAILS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @ApiOperation({ summary: 'Get all testimonials for the authenticated store' })
  @ApiResponse({ status: 200, description: 'Testimonials retrieved successfully', type: [TestimonialResponseDto] })
  async getTestimonials(@Req() req: IRequest) {
    const storeId = req.user.store.id;
    return {
      message: 'Testimonials retrieved successfully',
      data: await this.storeContentService.getTestimonials(storeId),
    };
  }

  @Get('testimonials/:id')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_DETAILS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @ApiOperation({ summary: 'Get a specific testimonial for the authenticated store' })
  @ApiResponse({ status: 200, description: 'Testimonial retrieved successfully', type: TestimonialResponseDto })
  @ApiParam({ name: 'id', description: 'ID of the testimonial' })
  async getTestimonial(@Req() req: IRequest, @Param('id') testimonialId: string) {
    const storeId = req.user.store.id;
    return {
      message: 'Testimonial retrieved successfully',
      data: await this.storeContentService.getTestimonial(storeId, testimonialId),
    };
  }

  @Post('testimonials')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_DETAILS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @ApiOperation({ summary: 'Create a testimonial for the authenticated store' })
  @ApiResponse({ status: 201, description: 'Testimonial created successfully', type: TestimonialResponseDto })
  @ApiBody({ type: CreateTestimonialDto })
  @HttpCode(HttpStatus.CREATED)
  async createTestimonial(@Req() req: IRequest, @Body() createDto: CreateTestimonialDto) {
    const storeId = req.user.store.id;

    const testimonial = await this.storeContentService.createTestimonial(storeId, createDto);
    this.logger.log(`Created testimonial for store ${storeId}`);

    return {
      message: 'Testimonial created successfully',
      data: testimonial,
    };
  }

  @Put('testimonials/:id')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_DETAILS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @ApiOperation({ summary: 'Update a testimonial for the authenticated store' })
  @ApiResponse({ status: 200, description: 'Testimonial updated successfully', type: TestimonialResponseDto })
  @ApiParam({ name: 'id', description: 'ID of the testimonial' })
  @ApiBody({ type: UpdateTestimonialDto })
  async updateTestimonial(
    @Req() req: IRequest,
    @Param('id') testimonialId: string,
    @Body() updateDto: UpdateTestimonialDto,
  ) {
    const storeId = req.user.store.id;

    const testimonial = await this.storeContentService.updateTestimonial(storeId, testimonialId, updateDto);
    this.logger.log(`Updated testimonial ${testimonialId} for store ${storeId}`);

    return {
      message: 'Testimonial updated successfully',
      data: testimonial,
    };
  }

  @Delete('testimonials/:id')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_DETAILS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @ApiOperation({ summary: 'Delete a testimonial for the authenticated store' })
  @ApiResponse({ status: 204, description: 'Testimonial deleted successfully' })
  @ApiParam({ name: 'id', description: 'ID of the testimonial' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteTestimonial(@Req() req: IRequest, @Param('id') testimonialId: string) {
    const storeId = req.user.store.id;

    await this.storeContentService.deleteTestimonial(storeId, testimonialId);
  }

  @Get('about')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_DETAILS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @ApiOperation({ summary: 'Get both about us content and testimonials for the authenticated store' })
  @ApiResponse({
    status: 200,
    description: 'Store about content retrieved successfully',
    schema: {
      properties: {
        message: { type: 'string', example: 'Store about content retrieved successfully' },
        data: {
          $ref: '#/components/schemas/StoreAboutContentResponseDto',
        },
      },
    },
  })
  async getStoreAboutContent(@Req() req: IRequest) {
    const storeId = req.user.store.id;
    return {
      message: 'Store about content retrieved successfully',
      data: await this.storeContentService.getStoreAboutContent(storeId),
    };
  }

  @Put('about')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_DETAILS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @ApiOperation({ summary: 'Update both about us content and testimonials for the authenticated store' })
  @ApiResponse({
    status: 200,
    description: 'Store about content updated successfully',
    schema: {
      properties: {
        message: { type: 'string', example: 'Store about content updated successfully' },
        data: {
          $ref: '#/components/schemas/StoreAboutContentResponseDto',
        },
      },
    },
  })
  @ApiBody({ type: StoreAboutContentDto })
  @HttpCode(HttpStatus.OK)
  async updateStoreAboutContent(@Req() req: IRequest, @Body() data: StoreAboutContentDto) {
    const storeId = req.user.store.id;

    const updatedContent = await this.storeContentService.updateStoreAboutContent(storeId, data);
    this.logger.log(`Updated store about content for store ${storeId}`);

    return {
      message: 'Store about content updated successfully',
      data: updatedContent,
    };
  }

  @Get('faqs')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_DETAILS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @ApiOperation({ summary: 'Get all FAQs for the authenticated store' })
  @ApiResponse({ status: 200, description: 'FAQs retrieved successfully', type: [FAQResponseDto] })
  async getFAQs(@Req() req: IRequest) {
    const storeId = req.user.store.id;
    return {
      message: 'FAQs retrieved successfully',
      data: await this.storeContentService.getFAQs(storeId),
    };
  }

  @Get('faqs/:id')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_DETAILS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @ApiOperation({ summary: 'Get a specific FAQ for the authenticated store' })
  @ApiResponse({ status: 200, description: 'FAQ retrieved successfully', type: FAQResponseDto })
  @ApiParam({ name: 'id', description: 'ID of the FAQ' })
  async getFAQ(@Req() req: IRequest, @Param('id') faqId: string) {
    const storeId = req.user.store.id;
    return {
      message: 'FAQ retrieved successfully',
      data: await this.storeContentService.getFAQ(storeId, faqId),
    };
  }

  @Post('faqs')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_DETAILS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @ApiOperation({ summary: 'Create a FAQ for the authenticated store' })
  @ApiResponse({ status: 201, description: 'FAQ created successfully', type: FAQResponseDto })
  @ApiBody({ type: CreateFAQDto })
  @HttpCode(HttpStatus.CREATED)
  async createFAQ(@Req() req: IRequest, @Body() createDto: CreateFAQDto) {
    const storeId = req.user.store.id;

    const faq = await this.storeContentService.createFAQ(storeId, createDto);
    this.logger.log(`Created FAQ for store ${storeId}`);

    return {
      message: 'FAQ created successfully',
      data: faq,
    };
  }

  @Put('faqs/:id')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_DETAILS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @ApiOperation({ summary: 'Update a FAQ for the authenticated store' })
  @ApiResponse({ status: 200, description: 'FAQ updated successfully', type: FAQResponseDto })
  @ApiParam({ name: 'id', description: 'ID of the FAQ' })
  @ApiBody({ type: UpdateFAQDto })
  async updateFAQ(@Req() req: IRequest, @Param('id') faqId: string, @Body() updateDto: UpdateFAQDto) {
    const storeId = req.user.store.id;

    const faq = await this.storeContentService.updateFAQ(storeId, faqId, updateDto);
    this.logger.log(`Updated FAQ ${faqId} for store ${storeId}`);

    return {
      message: 'FAQ updated successfully',
      data: faq,
    };
  }

  @Delete('faqs/:id')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_DETAILS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @ApiOperation({ summary: 'Delete a FAQ for the authenticated store' })
  @ApiResponse({ status: 204, description: 'FAQ deleted successfully' })
  @ApiParam({ name: 'id', description: 'ID of the FAQ' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteFAQ(@Req() req: IRequest, @Param('id') faqId: string) {
    const storeId = req.user.store.id;

    await this.storeContentService.deleteFAQ(storeId, faqId);
  }

  @Get('faq-content')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_DETAILS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @ApiOperation({ summary: 'Get FAQs for the authenticated store' })
  @ApiResponse({
    status: 200,
    description: 'Store FAQ content retrieved successfully',
    schema: {
      properties: {
        message: { type: 'string', example: 'Store FAQ content retrieved successfully' },
        data: {
          $ref: '#/components/schemas/StoreFAQContentResponseDto',
        },
      },
    },
  })
  async getStoreFAQContent(@Req() req: IRequest) {
    const storeId = req.user.store.id;
    return {
      message: 'Store FAQ content retrieved successfully',
      data: await this.storeContentService.getStoreFAQContent(storeId),
    };
  }

  @Put('faq-content')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_DETAILS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @ApiOperation({ summary: 'Update FAQs for the authenticated store' })
  @ApiResponse({
    status: 200,
    description: 'Store FAQ content updated successfully',
    schema: {
      properties: {
        message: { type: 'string', example: 'Store FAQ content updated successfully' },
        data: {
          $ref: '#/components/schemas/StoreFAQContentResponseDto',
        },
      },
    },
  })
  @ApiBody({ type: StoreFAQContentDto })
  @HttpCode(HttpStatus.OK)
  async updateStoreFAQContent(@Req() req: IRequest, @Body() data: StoreFAQContentDto) {
    const storeId = req.user.store.id;

    const updatedContent = await this.storeContentService.updateStoreFAQContent(storeId, data);
    this.logger.log(`Updated store FAQ content for store ${storeId}`);

    return {
      message: 'Store FAQ content updated successfully',
      data: updatedContent,
    };
  }

  @Get(':storeId/public/faqs')
  @ApiOperation({ summary: 'Get public FAQs for a store' })
  @ApiResponse({ status: 200, description: 'FAQs retrieved successfully', type: [FAQResponseDto] })
  @ApiParam({ name: 'storeId', description: 'ID of the store' })
  async getPublicFAQs(@Param('storeId') storeId: string) {
    return {
      message: 'Public FAQs retrieved successfully',
      data: await this.storeContentService.getPublicFAQs(storeId),
    };
  }

  @Get('info-blocks')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_DETAILS)
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_PRODUCT_INFO_BLOCKS)
  @UseGuards(JwtAuthGuard, RoleGuard, PlanGuard)
  @ApiOperation({ summary: 'Get all info blocks for the authenticated store' })
  @ApiResponse({ status: 200, description: 'Info blocks retrieved successfully', type: [InfoBlockResponseDto] })
  async getInfoBlocks(@Req() req: IRequest) {
    const storeId = req.user.store.id;
    return {
      message: 'Info blocks retrieved successfully',
      data: await this.storeContentService.getInfoBlocks(storeId),
    };
  }

  @Get('info-blocks/:id')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_DETAILS)
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_PRODUCT_INFO_BLOCKS)
  @UseGuards(JwtAuthGuard, RoleGuard, PlanGuard)
  @ApiOperation({ summary: 'Get a specific info block for the authenticated store' })
  @ApiResponse({ status: 200, description: 'Info block retrieved successfully', type: InfoBlockResponseDto })
  @ApiParam({ name: 'id', description: 'ID of the info block' })
  async getInfoBlock(@Req() req: IRequest, @Param('id') infoBlockId: string) {
    const storeId = req.user.store.id;
    const infoBlock = await this.storeContentService.getInfoBlock(infoBlockId);

    // Verify the info block belongs to the store
    if (infoBlock.store.toString() !== storeId) {
      throw new NotFoundException('Info block not found for this store');
    }

    return {
      message: 'Info block retrieved successfully',
      data: infoBlock,
    };
  }

  @Post('info-blocks')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_DETAILS)
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_PRODUCT_INFO_BLOCKS)
  @UseGuards(JwtAuthGuard, RoleGuard, PlanGuard)
  @ApiOperation({ summary: 'Create an info block for the authenticated store' })
  @ApiResponse({ status: 201, description: 'Info block created successfully', type: InfoBlockResponseDto })
  @ApiBody({ type: CreateInfoBlockDto })
  @HttpCode(HttpStatus.CREATED)
  async createInfoBlock(@Req() req: IRequest, @Body() createDto: CreateInfoBlockDto) {
    const storeId = req.user.store.id;

    const infoBlock = await this.storeContentService.createInfoBlock(storeId, createDto);
    this.logger.log(`Created info block for store ${storeId}`);

    return {
      message: 'Info block created successfully',
      data: infoBlock,
    };
  }

  @Put('info-blocks/:id')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_DETAILS)
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_PRODUCT_INFO_BLOCKS)
  @UseGuards(JwtAuthGuard, RoleGuard, PlanGuard)
  @ApiOperation({ summary: 'Update an info block for the authenticated store' })
  @ApiResponse({ status: 200, description: 'Info block updated successfully', type: InfoBlockResponseDto })
  @ApiParam({ name: 'id', description: 'ID of the info block' })
  @ApiBody({ type: UpdateInfoBlockDto })
  async updateInfoBlock(@Req() req: IRequest, @Param('id') infoBlockId: string, @Body() updateDto: UpdateInfoBlockDto) {
    const storeId = req.user.store.id;

    const infoBlock = await this.storeContentService.getInfoBlock(infoBlockId);
    // Verify the info block belongs to the store
    if (infoBlock.store.toString() !== storeId) {
      throw new NotFoundException('Info block not found for this store');
    }

    const updatedInfoBlock = await this.storeContentService.updateInfoBlock(infoBlockId, updateDto);
    this.logger.log(`Updated info block ${infoBlockId} for store ${storeId}`);

    return {
      message: 'Info block updated successfully',
      data: updatedInfoBlock,
    };
  }

  @Delete('info-blocks/:id')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_DETAILS)
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_PRODUCT_INFO_BLOCKS)
  @UseGuards(JwtAuthGuard, RoleGuard, PlanGuard)
  @ApiOperation({ summary: 'Delete an info block for the authenticated store' })
  @ApiParam({ name: 'id', description: 'ID of the info block' })
  async deleteInfoBlock(@Req() req: IRequest, @Param('id') infoBlockId: string) {
    const storeId = req.user.store.id;

    const infoBlock = await this.storeContentService.getInfoBlock(infoBlockId);
    // Verify the info block belongs to the store
    if (infoBlock.store.toString() !== storeId) {
      throw new NotFoundException('Info block not found for this store');
    }

    await this.storeContentService.deleteInfoBlock(infoBlockId);

    return {
      message: 'Info block deleted Successfully',
    };
  }

  @Get('info-block-content')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_DETAILS)
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_PRODUCT_INFO_BLOCKS)
  @UseGuards(JwtAuthGuard, RoleGuard, PlanGuard)
  @ApiOperation({ summary: 'Get info blocks for the authenticated store' })
  @ApiResponse({
    status: 200,
    description: 'Store info block content retrieved successfully',
    schema: {
      properties: {
        message: { type: 'string', example: 'Store info block content retrieved successfully' },
        data: {
          $ref: '#/components/schemas/StoreInfoBlockContentResponseDto',
        },
      },
    },
  })
  async getStoreInfoBlockContent(@Req() req: IRequest) {
    const storeId = req.user.store.id;
    return {
      message: 'Store info block content retrieved successfully',
      data: await this.storeContentService.getStoreInfoBlockContent(storeId),
    };
  }

  @Put('info-block-content')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_DETAILS)
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_PRODUCT_INFO_BLOCKS)
  @UseGuards(JwtAuthGuard, RoleGuard, PlanGuard)
  @ApiOperation({ summary: 'Update info blocks for the authenticated store' })
  @ApiResponse({
    status: 200,
    description: 'Store info block content updated successfully',
    schema: {
      properties: {
        message: { type: 'string', example: 'Store info block content updated successfully' },
        data: {
          $ref: '#/components/schemas/StoreInfoBlockContentResponseDto',
        },
      },
    },
  })
  @ApiBody({ type: StoreInfoBlockContentDto })
  @HttpCode(HttpStatus.OK)
  async updateStoreInfoBlockContent(@Req() req: IRequest, @Body() data: StoreInfoBlockContentDto) {
    const storeId = req.user.store.id;

    const updatedContent = await this.storeContentService.updateStoreInfoBlockContent(storeId, data);
    this.logger.log(`Updated store info block content for store ${storeId}`);

    return {
      message: 'Store info block content updated successfully',
      data: updatedContent,
    };
  }

  @Get(':storeId/public/info-blocks')
  @ApiOperation({ summary: 'Get public info blocks for a store' })
  @ApiResponse({ status: 200, description: 'Info blocks retrieved successfully', type: [InfoBlockResponseDto] })
  @ApiParam({ name: 'storeId', description: 'ID of the store' })
  async getPublicInfoBlocks(@Param('storeId') storeId: string) {
    return {
      message: 'Public info blocks retrieved successfully',
      data: await this.storeContentService.getPublicInfoBlocks(storeId),
    };
  }

  @Put('info-blocks/:id/items')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_DETAILS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @ApiOperation({ summary: 'Update items for an info block' })
  @ApiResponse({ status: 200, description: 'Info block items updated successfully', type: InfoBlockResponseDto })
  @ApiParam({ name: 'id', description: 'ID of the info block' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        item_ids: {
          type: 'array',
          items: {
            type: 'string',
          },
        },
      },
    },
  })
  async updateInfoBlockItems(
    @Req() req: IRequest,
    @Param('id') infoBlockId: string,
    @Body() data: { items: string[] },
  ) {
    const storeId = req.user.store.id;

    console.log({ data });

    const updatedInfoBlock = await this.storeContentService.updateInfoBlockItems(infoBlockId, data.items, storeId);
    this.logger.log(`Updated items for info block ${infoBlockId} for store ${storeId}`);

    return {
      message: 'Info block items updated successfully',
      data: updatedInfoBlock,
    };
  }
}
