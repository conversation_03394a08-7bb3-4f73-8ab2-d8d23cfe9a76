import { forwardR<PERSON>, Module } from '@nestjs/common';
import { SharedModule } from '../../shared.module';
import { MongooseModule } from '@nestjs/mongoose';
import setIdPlugin from '../../mongoose-plugins/set-id.mongoose-plugin';
import jsonHookPlugin from '../../mongoose-plugins/json-hook.mongoose-plugin';
import { Store, StoreSchema } from './store.schema';
import { StoreService } from './services/store.service';
import { StoreController } from './store.controller';
import { StoreBroker } from './store.broker';
import { DeliveryArea, DeliverAreaSchema } from './delivery-areas/delivery-areas.schema';
import { DeliverAreaController } from './delivery-areas/delivery-areas.controller';
import { DeliveryAreaService } from './delivery-areas/delivery-areas.service';
import { KYCController } from './kyc/kyc.controller';
import { KycBroker } from './kyc/kyc.broker';
import { KYCService } from './kyc/kyc.service';
import { Bvn, BvnSchema } from './kyc/kyc.bvn.schema';
import { Kyc, KycIDAnalysis, KycIDAnalysisSchema, KycSchema } from './kyc/kyc.schema';
import { InviteSchema, Invite } from './teams/invites.schema';
import { TeamsService } from './teams/teams.service';
import { TeamsController } from './teams/teams.controller';
import { StoreUpdatesService } from './services/store-updates.service';
import { StoreMigrationService } from './services/migrations.service';
import { VNin, VNinSchema } from './kyc/kyc.vnin.schema';
import mongoosePaginate from 'mongoose-paginate-v2';

import { Branch, branchSchema } from './branches/branches.schema';
import { BranchesController } from './branches/branches.controller';
import { BranchesService } from './branches/branches.service';
import { StoreThirdPartyConnectionsService } from './services/3rd-party-connections.service';
import { BullModule } from '@nestjs/bull';
import { QUEUES } from '../../enums/queues.enum';
import { StoreQueue } from './store.queue';
import { ChowdeckStoreService } from './services/3rd-parties/chowdeck.service';
import { YearWrapController } from './year-wrap/year-wrap.controller';
import { YearWrapService } from './year-wrap/year-wrap.service';
import { YearWrap, YearWrapSchema } from './year-wrap/year-wrap.schema';
import { ItemModule } from '../item/item.module';
import { CartModule } from '../cart/cart.module';
import { AnalyticModule } from '../analytic/analytic.module';
import { Order } from '@getbrevo/brevo';
import { OrdersModule } from '../orders/orders.module';
import { PaymentModule } from '../payment/payment.module';
import { StoreJobService } from './services/store-job.service';
import { StoreDomainSchema } from './domains/store-domain.schema';
import { StoreDomain } from './domains/store-domain.schema';
import { DomainPurchase, DomainPurchaseSchema } from './domains/domain-purchase.schema';
import { DomainController } from './domains/domain.controller';
import { DomainService } from './domains/domain.service';
import { Logger } from '@nestjs/common';
import { DomainBroker } from './domains/domain.broker';
import { DomainQueue } from './domains/domain.queue';
import { WalletModule } from '../wallets/wallet.module';
import { StoreContentController } from './store-content.controller';
import { StoreContentService } from './services/store-content.service';
import { InfoBlock, InfoBlockSchema } from './info-blocks.schema';

@Module({
  imports: [
    BullModule.registerQueue({
      name: QUEUES.STORE,
      limiter: {
        max: 2,
        duration: 1000,
      },
    }),
    BullModule.registerQueue({
      name: QUEUES.DOMAIN,
      limiter: {
        max: 2,
        duration: 1000,
      },
    }),
    MongooseModule.forFeatureAsync([
      {
        name: Store.name,
        useFactory: () => {
          StoreSchema.plugin(setIdPlugin());
          StoreSchema.plugin(jsonHookPlugin(['__v', '_id', 'security_pin']));
          StoreSchema.plugin(mongoosePaginate);
          return StoreSchema;
        },
      },
      {
        name: YearWrap.name,
        useFactory: () => {
          YearWrapSchema.plugin(setIdPlugin());
          YearWrapSchema.plugin(jsonHookPlugin(['_id', '__v']));
          return YearWrapSchema;
        },
      },
      {
        name: DeliveryArea.name,
        useFactory: () => {
          DeliverAreaSchema.plugin(setIdPlugin());
          DeliverAreaSchema.plugin(jsonHookPlugin(['_id', '__v']));
          return DeliverAreaSchema;
        },
      },
      {
        name: Bvn.name,
        useFactory: () => {
          BvnSchema.plugin(setIdPlugin());
          BvnSchema.plugin(jsonHookPlugin(['_id', '__v']));
          return BvnSchema;
        },
      },
      {
        name: Kyc.name,
        useFactory: () => {
          KycSchema.plugin(setIdPlugin());
          KycSchema.plugin(jsonHookPlugin(['_id', '__v']));
          return KycSchema;
        },
      },
      {
        name: KycIDAnalysis.name,
        useFactory: () => {
          KycIDAnalysisSchema.plugin(setIdPlugin());
          KycIDAnalysisSchema.plugin(jsonHookPlugin(['_id', '__v']));
          return KycIDAnalysisSchema;
        },
      },
      {
        name: Invite.name,
        useFactory: () => {
          InviteSchema.plugin(setIdPlugin());
          InviteSchema.plugin(jsonHookPlugin(['_id', '__v']));
          return InviteSchema;
        },
      },
      {
        name: VNin.name,
        useFactory: () => {
          VNinSchema.plugin(setIdPlugin());
          VNinSchema.plugin(jsonHookPlugin(['_id', '__v']));
          return VNinSchema;
        },
      },
      {
        name: Branch.name,
        useFactory: () => {
          branchSchema.plugin(setIdPlugin());
          branchSchema.plugin(jsonHookPlugin(['_id', '__v']));
          return branchSchema;
        },
      },
      {
        name: StoreDomain.name,
        useFactory: () => {
          StoreDomainSchema.plugin(setIdPlugin());
          StoreDomainSchema.plugin(jsonHookPlugin(['_id', '__v']));
          return StoreDomainSchema;
        },
      },
      {
        name: DomainPurchase.name,
        useFactory: () => {
          DomainPurchaseSchema.plugin(setIdPlugin());
          DomainPurchaseSchema.plugin(jsonHookPlugin(['_id', '__v']));
          return DomainPurchaseSchema;
        },
      },
      {
        name: InfoBlock.name,
        useFactory: () => {
          InfoBlockSchema.plugin(setIdPlugin());
          InfoBlockSchema.plugin(jsonHookPlugin(['_id', '__v']));
          return InfoBlockSchema;
        },
      },
    ]),
    SharedModule,
    forwardRef(() => ItemModule),
    forwardRef(() => CartModule),
    forwardRef(() => AnalyticModule),
    forwardRef(() => OrdersModule),
    forwardRef(() => PaymentModule),
    forwardRef(() => WalletModule),
  ],
  controllers: [
    StoreBroker,
    TeamsController,
    StoreController,
    DeliverAreaController,
    KYCController,
    KycBroker,
    BranchesController,
    YearWrapController,
    DomainController,
    DomainBroker,
    StoreContentController,
  ],
  providers: [
    StoreService,
    StoreUpdatesService,
    StoreMigrationService,
    DeliveryAreaService,
    KYCService,
    TeamsService,
    BranchesService,
    StoreThirdPartyConnectionsService,
    StoreQueue,
    ChowdeckStoreService,
    YearWrapService,
    StoreJobService,
    DomainService,
    DomainQueue,
    StoreContentService,
  ],
  exports: [
    StoreService,
    StoreQueue,
    StoreContentService,
    DeliveryAreaService,
    StoreUpdatesService,
    StoreMigrationService,
    TeamsService,
    BranchesService,
    StoreThirdPartyConnectionsService,
    DomainService,
  ],
})
export class StoreModule {}
