import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import mongoose, { Document } from 'mongoose';
import { INFO_BLOCK_CONTENT_TYPE } from './store.schema';

export type InfoBlockDocument = InfoBlock & Document;

@Schema({ timestamps: true })
export class InfoBlock {
  @ApiProperty()
  _id: string;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Store', required: true, index: true })
  store: string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  title: string;

  @ApiProperty({ enum: INFO_BLOCK_CONTENT_TYPE })
  @Prop({ type: String, enum: Object.values(INFO_BLOCK_CONTENT_TYPE), required: true })
  content_type: INFO_BLOCK_CONTENT_TYPE;

  @ApiProperty()
  @Prop({ type: String, required: false })
  text_content?: string;

  @ApiProperty({ type: [String] })
  @Prop({ type: [String], default: [] })
  image_content?: string[];

  @ApiProperty()
  @Prop({ type: Boolean, default: true })
  is_visible: boolean;

  @ApiProperty({ required: false })
  @Prop({ type: String, default: null, required: false })
  tag?: string;

  @ApiProperty({ type: [String] })
  @Prop({ type: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Item' }], default: [] })
  items?: string[];
}

export const InfoBlockSchema = SchemaFactory.createForClass(InfoBlock);
