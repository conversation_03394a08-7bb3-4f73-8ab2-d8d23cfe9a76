import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Store } from '../store.schema';

export type DeliveryAreaDocument = DeliveryArea & Document;

@Schema()
export class DeliveryArea {
  _id: any;

  id: any;

  @ApiProperty()
  @Prop({ type: Number, required: true })
  fee: number;

  @ApiProperty()
  @Prop({ type: String, required: true })
  name: string;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Store', required: true })
  store: Store;
}

export const DeliverAreaSchema = SchemaFactory.createForClass(DeliveryArea);
