import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model } from 'mongoose';
import { UpdateDeliveryAreaDto } from '../../../models/dtos/StoreDtos';
import { DeliveryArea, DeliveryAreaDocument } from './delivery-areas.schema';
import { Store, StoreDocument } from '../store.schema';

@Injectable()
export class DeliveryAreaService {
  constructor(
    @InjectModel(Store.name) private readonly storeModel: Model<StoreDocument>,
    @InjectModel(DeliveryArea.name)
    private readonly deliveryAreaModel: Model<DeliveryAreaDocument>,
    private readonly logger: Logger,
  ) {}

  async updateDeliveryAreas(storeId: string, deliveryAreas: UpdateDeliveryAreaDto[]) {
    const store = await this.storeModel.findOne({ _id: storeId });

    if (!store) {
      throw new BadRequestException('Store does not exist');
    }

    for (const a of store.delivery_areas) {
      await this.deliveryAreaModel.deleteOne({ _id: a });
    }

    const newAreas = [];

    for (const a of deliveryAreas) {
      const area = await new this.deliveryAreaModel({
        ...a,
        store: store._id,
      }).save();
      newAreas.push(area.toJSON());
    }

    await store.updateOne({
      delivery_areas: newAreas.map((a) => a.id),
    });

    return newAreas;
  }

  async getDeliveryAreas(storeId: string) {
    const store = await this.storeModel.findOne({ _id: storeId });

    if (!store) {
      throw new BadRequestException('Store does not exist');
    }

    const deliveryAreas = await Promise.all(
      store.delivery_areas.map(async (a) => {
        const area = await this.deliveryAreaModel.findOne({ _id: a });
        this.logger.log(area);
        return area;
      }),
    );

    return deliveryAreas;
  }

  async getDeliveryArea(filter: FilterQuery<DeliveryArea>) {
    return await this.deliveryAreaModel.findOne(filter);
  }
}
