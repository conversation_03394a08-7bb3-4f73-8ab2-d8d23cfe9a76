import { Body, Controller, Delete, Get, Param, Post, Put, UseGuards } from '@nestjs/common';
import { ApiSecurity, ApiTags } from '@nestjs/swagger';
import { UpdateDeliveryAreaDto } from '../../../models/dtos/StoreDtos';
import { JwtAuthGuard } from '../../../jwt/jwt-auth.guard';
import { DeliveryAreaService } from './delivery-areas.service';

@ApiTags('Stores')
@Controller('stores/:id/delivery-areas')
export class DeliverAreaController {
  constructor(private readonly deliveryAreaService: DeliveryAreaService) {}

  @Put('')
  @ApiSecurity('Bearer')
  @UseGuards(JwtAuthGuard)
  async updateDeliveryArea(@Param('id') storeId, @Body() updateDtos: UpdateDeliveryAreaDto[]) {
    const areas = await this.deliveryAreaService.updateDeliveryAreas(storeId, updateDtos);

    return {
      message: `Delivery area(s) ${areas.map((a) => a.name).join(', ')} updated`,
      data: areas,
    };
  }

  @Get('')
  async getDeliveryAreas(@Param('id') storeId: string) {
    const areas = await this.deliveryAreaService.getDeliveryAreas(storeId);

    return {
      message: 'Delivery areas fetched',
      data: areas,
    };
  }
}
