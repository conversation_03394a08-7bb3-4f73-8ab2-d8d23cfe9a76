import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Document } from 'mongoose';

export type BranchDocument = Branch & Document;

// @Schema({ _id: false }) // Set _id to false to prevent Mongoose from automatically creating an _id field for this sub-schema
export class BranchType {
  @ApiProperty()
  @Prop({ type: String, required: true })
  store_id: string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  label: string;

  @ApiProperty()
  @Prop({ type: Boolean, required: true })
  is_head_branch: boolean;
}

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class Branch {
  @ApiProperty()
  id: string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  name: string;

  @ApiProperty()
  @Prop({ type: [], required: true })
  branches: BranchType[];
}

export const branchSchema = SchemaFactory.createForClass(Branch);
