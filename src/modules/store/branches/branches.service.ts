import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { BranchType, CreateBranchDto } from './dto/create-branch.dto';
import { UpdateBranchDto } from './dto/update-branch.dto';
import { Branch, BranchDocument } from './branches.schema';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { Store, StoreDocument } from '../store.schema';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { STORE_PUBLIC_HIDDEN_INFO } from '../../../utils/constants';
import checkStoreIsOpen from '../../../utils/check-if-store-is-open';
import { getAppEnv, isProduction } from '../../../utils';
import { WA_BOT_PHONE_NOS } from '../../../utils/wabot-constants';

@Injectable()
export class BranchesService {
  constructor(
    protected readonly brokerTransport: BrokerTransportService,
    @InjectModel(Branch.name) private readonly branchModel: Model<BranchDocument>,
    @InjectModel(Store.name) private readonly storeModel: Model<StoreDocument>,
  ) {}

  async getBranchesPublic(storeSlug: string) {
    const primaryStore = await this.storeModel.findOne({ slug: storeSlug }, STORE_PUBLIC_HIDDEN_INFO).lean();

    if (!primaryStore) {
      throw new NotFoundException('Store not found');
    }

    const branch = await this.branchModel.findById(primaryStore.branches).lean();

    if (!branch) {
      throw new NotFoundException('Branch not found');
    }

    const storesIds = branch.branches.map((s) => s.store_id);
    const stores = await this.storeModel
      .find({ _id: { $in: storesIds } }, 'name logo slug maintenance_mode configuration')
      .lean();

    branch.branches = branch.branches.map((s) => {
      const storeData = stores.find((store) => store._id.toString() === s.store_id.toString());

      return {
        ...s,
        store: storeData,
        openOrClosed: checkStoreIsOpen({ ...storeData, id: storeData._id }),
      };
    });

    const botPhone = WA_BOT_PHONE_NOS[getAppEnv()];

    return { ...branch, primaryStore, botPhone: botPhone[0] };
  }

  async getBranch(id: string) {
    const branch = await this.branchModel.findById(id);

    if (!branch) {
      throw new NotFoundException('Branch not found');
    }

    const storesIds = branch.branches.map((s) => s.store_id);
    const stores = await this.storeModel.find({ _id: { $in: storesIds } }, 'name logo slug').lean();

    branch.branches = branch.branches.map((s) => {
      const storeData = stores.find((store) => store._id.toString() === s.store_id.toString());

      return {
        ...s,
        store: storeData,
      };
    });

    return branch;
  }

  async createOrUpdateBranch(userId: string, data: CreateBranchDto | UpdateBranchDto, branchId?: string): Promise<any> {
    // Validate that each storeId in dto.branches belongs to the user
    const validationPromises = data.branches.map((branch) =>
      this.brokerTransport
        .send<Store>(BROKER_PATTERNS.STORE.VALIDATE_STORE_OWNERSHIP, { userId, storeId: branch.store_id })
        .toPromise(),
    );

    const results = await Promise.all(validationPromises);
    if (results.some((isOwner) => !isOwner)) {
      throw new BadRequestException(`Please select only stores you own`);
    }

    let branch: BranchDocument;
    if (branchId) {
      // Find existing branch document by ID
      branch = await this.branchModel.findOne({ _id: branchId });
      if (!branch) {
        throw new NotFoundException(`Branch with ID ${branchId} not found`);
      }

      // Update existing branch
      branch.name = data.name;
      branch.branches = data.branches;
    } else {
      // Create new branch
      branch = new this.branchModel(data);
    }

    await branch.save();

    //update stores with branch id
    const updateStorePromises = branch.branches.map((store) =>
      this.brokerTransport
        .send<Store>(BROKER_PATTERNS.STORE.UPDATE_STORE, {
          filter: { _id: store.store_id },
          update: { branches: branch._id },
        })
        .toPromise(),
    );

    await Promise.all(updateStorePromises);

    return branch;
  }

  async delete(userId: string, id: string) {
    const branch = await this.branchModel.findById(id);

    if (!branch) {
      throw new NotFoundException('Branch not found');
    }

    const validationPromises = branch.branches.map((branch) =>
      this.brokerTransport
        .send<Store>(BROKER_PATTERNS.STORE.VALIDATE_STORE_OWNERSHIP, { userId, storeId: branch.store_id })
        .toPromise(),
    );

    const results = await Promise.all(validationPromises);
    if (results.some((isOwner) => !isOwner)) {
      throw new BadRequestException(`You do not have enough permissions to delete this branch`);
    }

    //remove branches for stores
    const updateStorePromises = branch.branches.map((store) =>
      this.brokerTransport
        .send<Store>(BROKER_PATTERNS.STORE.UPDATE_STORE, {
          filter: { _id: store.store_id },
          update: { branches: null },
        })
        .toPromise(),
    );

    await Promise.all(updateStorePromises);
    await this.branchModel.findByIdAndDelete(id);

    return null;
  }
}
