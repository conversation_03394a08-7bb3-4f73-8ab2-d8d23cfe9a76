import { Controller, Post, Body, UseGuards, Req, Param, Get, Delete } from '@nestjs/common';
import { BranchesService } from './branches.service';
import { CreateBranchDto } from './dto/create-branch.dto';
import { UpdateBranchDto } from './dto/update-branch.dto';
import { JwtAuthGuard } from '../../../jwt/jwt-auth.guard';
import { ApiSecurity } from '@nestjs/swagger';
import { SCOPES } from '../../../utils/permissions.util';
import { RoleGuard } from '../../../guards/role.guard';
import { PlanPermissions, RolePermissions } from '../../../decorators/permission.decorator';
import { PlanGuard } from '../../../guards/plan.guard';
import { IRequest } from '../../../interfaces/request.interface';

@Controller('stores/branches')
export class BranchesController {
  constructor(private readonly branchesService: BranchesService) {}

  @Get('/public/:slug')
  @ApiSecurity('bearer')
  async getPublic(@Param('slug') slug: string) {
    const data = await this.branchesService.getBranchesPublic(slug);
    return {
      message: 'Fetched Branches',
      data,
    };
  }

  @Get(':id')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard, RoleGuard, PlanGuard)
  async get(@Param('id') id: string) {
    const data = await this.branchesService.getBranch(id);
    return {
      message: 'Fetched Branches',
      data,
    };
  }

  @Post()
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_BRANCHES)
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_STORE_BRANCHES)
  @UseGuards(JwtAuthGuard, RoleGuard, PlanGuard)
  create(@Req() req: IRequest, @Body() createBranchDto: CreateBranchDto) {
    return this.branchesService.createOrUpdateBranch(req.user.id, createBranchDto);
  }

  @Post('/:id')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_BRANCHES)
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_STORE_BRANCHES)
  @UseGuards(JwtAuthGuard, RoleGuard, PlanGuard)
  update(@Req() req: IRequest, @Param('id') branchId: string, @Body() updateBranchDto: UpdateBranchDto) {
    return this.branchesService.createOrUpdateBranch(req.user.id, updateBranchDto, branchId);
  }

  @Delete('/:id')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_BRANCHES)
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_STORE_BRANCHES)
  @UseGuards(JwtAuthGuard, RoleGuard, PlanGuard)
  async delete(@Req() req: IRequest, @Param('id') id: string) {
    const data = await this.branchesService.delete(req.user.id, id);
    return {
      message: 'Fetched Branches',
      data,
    };
  }
}
