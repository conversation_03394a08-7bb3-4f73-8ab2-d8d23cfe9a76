import { PartialType } from '@nestjs/mapped-types';
import { BranchType, CreateBranchDto } from './create-branch.dto';
import { ApiProperty } from '@nestjs/swagger';
import { ArrayMinSize, IsArray, IsNotEmpty, IsString, ValidateNested } from 'class-validator';

export class UpdateBranchDto extends PartialType(CreateBranchDto) {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ type: [BranchType] })
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(2)
  @ValidateNested({ each: true })
  branches: BranchType[];
}
