import { ApiProperty } from '@nestjs/swagger';
import { A<PERSON>yMinSize, IsArray, IsNotEmpty, IsString, ValidateNested } from 'class-validator';

export class BranchType {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  store_id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  label: string;

  @ApiProperty()
  @IsNotEmpty()
  is_head_branch: boolean;
}

export class CreateBranchDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ type: [BranchType] })
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(2, { message: 'Please add at least two stores to your branches' })
  @ValidateNested({ each: true })
  branches: BranchType[];
}
