export interface Trend {
  total_orders: number; // Percentage difference
  total_visits: number;
  total_payments_count: number;
  new_customers_count: number;
}

export interface TopProduct {
  name: string;
  orders: number;
}

export interface TopCustomer {
  name: string;
  orders: number;
}

export interface EmailSummaryData {
  total_orders: number;
  total_payments_count: number;
  total_visits: number;
  new_customers_count: number;
  trends: Trend;
  top_products: TopProduct[];
  top_customers: TopCustomer[];
}
