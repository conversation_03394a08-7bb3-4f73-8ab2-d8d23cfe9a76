import { InjectQueue, OnQueueActive, OnQueueCompleted, OnQueueFailed, Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { BrokerTransportService } from '../../broker/broker-transport.service';
import { JOBS, QUEUES } from '../../enums/queues.enum';
import { ChowdeckRepository } from '../../repositories/chowdeck/index.repository';
import { ChowdeckItem, ChowdeckCategory } from '../../repositories/chowdeck/types';
import { InjectModel } from '@nestjs/mongoose';
import { PaginateModel } from 'mongoose';
import { Store, StoreDocument } from './store.schema';
import { CACHE_MANAGER, Inject } from '@nestjs/common';
import { Cache } from 'cache-manager';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { Item } from '../item/item.schema';
import { StoreService } from './services/store.service';
import { StoreUpdatesService } from './services/store-updates.service';
import { DeepSet } from '../../utils/functions';
import { UpdateItemDto } from '../../models/dtos/ItemDtos';
import { Errorable } from '../../utils/types';
import { ChowdeckStoreService } from './services/3rd-parties/chowdeck.service';
import { ResendRepository } from '../../repositories/resend.repository';
import { scheduled } from 'rxjs';
import dayjs from 'dayjs';
import { EmailMetricKey, EmailSummaryData } from '../../react-email/emails/utils/types';
import { NOTIFICATION_TYPE } from '../user/notifications/notification.schema';

interface ExportItemsToChowdeckPayload {
  items: string[];
  store: string;
}

export interface ImportChowdeckItemsPayload {
  store: string;
  chowdeck_items: number[];
}

interface ChowdeckUpdateData {
  name?: string;
  description?: string;
  menu_category_id?: number;
  price?: number;
  in_stock?: boolean;
  is_published?: boolean;
  images?: {
    path: string;
    rank: number;
  }[];
}

export interface RefreshStoreMenuJob {
  storeId: string;
}

export interface StoreJob<T> {
  type: JOBS;
  data: T;
}

export interface SendEmailSummaryJob {
  storeId: string;
  period: 'week' | 'month';
  startOfPeriod: string; // or Date
  endOfPeriod: string; // or Date
}

@Processor(QUEUES.STORE)
export class StoreQueue {
  constructor(
    @Inject(CACHE_MANAGER)
    private readonly cacheManager: Cache,
    @InjectModel(Store.name)
    protected readonly storeModel: PaginateModel<StoreDocument>,
    private readonly storeUpdateService: StoreUpdatesService,
    private readonly storeService: StoreService,
    protected readonly brokerTransport: BrokerTransportService,
    protected readonly chowdeck: ChowdeckRepository,
    private readonly storeChowdeckService: ChowdeckStoreService,
    private readonly resend: ResendRepository,
  ) {}

  @OnQueueActive()
  onActive(job: Job<any>) {
    console.log(`Processing job ${job.id} of type ${job.name} with data ${job.data}...`);
  }

  @OnQueueFailed()
  onFailed(job: Job, error: Error) {
    console.log(`Failed to process job ${job.id} of type ${job.name} with data, error: ${error.message}`);
  }

  @OnQueueCompleted()
  onCompleted(job: Job, result: any) {
    console.log(`Job ${job.id} of type ${job.name} successfully completed, result: ${result}`);
    job.remove().catch(console.log);
  }

  @Process(QUEUES.STORE)
  async send(job: Job<StoreJob<any>>) {
    switch (job.data.type) {
      case JOBS.EXPORT_ITEMS_TO_CHOWDECK:
        return await this.exportItemsToChowdeck(job);
      case JOBS.IMPORT_CHOWDECK_ITEMS:
        return await this.importChowdeckItemsJob(job);
      case JOBS.SYNC_STORE_TO_CHOWDECK:
        return await this.syncToChowdeckJob(job);
      case JOBS.SYNC_CHOWDECK_TO_STORE:
        return await this.syncFromChowdeckJob(job);
      case JOBS.SYNC_SINGLE_ITEM_TO_CHOWDECK:
        return await this.autoItemSyncToChowdeck(job);
      case JOBS.REFRESH_STORE_MENU:
        await this.refreshStoreMenuJob(job);
      case JOBS.SEND_EMAIL_SUMMARY:
        return await this.sendEmailSummaryJob(job);
        break;
      default:
    }
  }

  async refreshStoreMenuJob(job: Job<StoreJob<RefreshStoreMenuJob>>) {
    await this.storeUpdateService.generateMenu(job.data.data.storeId);
  }

  async exportItemsToChowdeck(job: Job<StoreJob<ExportItemsToChowdeckPayload>>) {
    const { items, store } = job.data.data;

    const dbStore = await this.storeModel.findById(store).populate('owner').lean();
    const storeCategories = dbStore?.categories ?? [];
    const merchantReference = dbStore.public_access_tokens?.chowdeck?.reference;
    const chowdeckToken = dbStore.access_tokens?.chowdeck?.token;

    const dbItems = await this.brokerTransport
      .send<(Item & { id: string })[]>(BROKER_PATTERNS.ITEM.GET_ITEMS, {
        _id: { $in: [...items] },
      })
      .toPromise();

    let products_count = 0;

    for (const item of dbItems) {
      if (item.meta.chowdeck_reference) {
        return;
      }

      const itemCategory = storeCategories.find((c) => c._id == item.category);

      let chowdeckCategory;
      if (itemCategory) {
        chowdeckCategory = await this.getOrCreateChowdeckCategory(merchantReference, itemCategory, chowdeckToken);
      }

      const { slug, name, price, quantity, available, is_always_available, description, images } = item;

      const createItemDto = {
        reference: slug,
        name,
        price,
        description,
        in_stock: is_always_available ? true : quantity > 0,
        is_active: available,
        images: images.map((i) => ({ path: i, rank: 1 })),
        category: {
          name: chowdeckCategory ? chowdeckCategory.name : undefined,
          rank: chowdeckCategory ? chowdeckCategory.rank : undefined,
          reference: chowdeckCategory ? chowdeckCategory.reference : undefined,
        },
        menu_category_id: chowdeckCategory ? chowdeckCategory.id : undefined,
      };

      const res = await this.chowdeck.createMenuItem(merchantReference, createItemDto, chowdeckToken);
      if (res.error) {
        console.log(res);
      }

      // Update item meta data with chowdeck_id and chowdeck_reference
      if (res?.data?.id && res?.data?.reference) {
        await this.brokerTransport
          .send(BROKER_PATTERNS.ITEM.UPDATE_ITEMS, [
            {
              filter: { _id: item.id, store: dbStore._id },
              payload: {
                meta: {
                  chowdeck_id: res.data.id,
                  chowdeck_reference: res.data.reference,
                },
              },
            },
          ])
          .toPromise();
      }
      products_count++;
    }

    await this.brokerTransport
      .send(BROKER_PATTERNS.USER.SEND_PUSH_NOTIFICATION, {
        store: dbStore._id,
        owner_only: true,
        message: {
          title: 'Products export successful ✅',
          message: `We've successfully exported ${products_count} products to Chowdeck`,
          path: '/dashboard',
        },
        notification_type: NOTIFICATION_TYPE.GENERIC,
      })
      .toPromise();

    const storeOwner = dbStore?.owner;

    const emailData = {
      to: storeOwner.email,
      subject: 'Your products have been exported to Chowdeck ✅',
      data: {
        name: storeOwner.name.split(' ')[0],
        products_count,
        destination: 'Chowdeck',
        cta_url: `${process.env.CATLOG_DASHBOARD}/products`,
      },
    };

    await this.resend.sendEmail(BROKER_PATTERNS.MAIL.PRODUCT_EXPORT_SUCCESSFUL, emailData);
  }

  async importChowdeckItemsJob(job: Job<StoreJob<ImportChowdeckItemsPayload>>) {
    const jobData = job?.data?.data;
    await this.storeChowdeckService.importBulkItemsFromChowdeck(jobData);
  }

  async syncToChowdeckJob(job: Job<StoreJob<{ store: string }>>) {
    const jobData = job?.data?.data;
    const store = await this.storeModel.findById(jobData.store).populate('owner').lean();
    const categories = store?.categories ?? [];
    const merchantReference = store.public_access_tokens?.chowdeck?.reference;
    const chowdeckToken = store?.access_tokens?.chowdeck?.token;

    const dbItems = await this.brokerTransport
      .send<Item[]>(BROKER_PATTERNS.ITEM.GET_ITEMS, {
        store: store._id,
        'meta.chowdeck_reference': { $exists: true },
      })
      .toPromise();

    const categoriesRes = await this.chowdeck.getMenuCategories(merchantReference, chowdeckToken);
    if (categoriesRes.error) {
      return;
    }
    const chowdeckCategories = categoriesRes?.data as ChowdeckCategory[];

    const updates: Promise<any>[] = [];

    for (const item of dbItems) {
      if (item.category) {
        const itemReference = item.meta?.chowdeck_reference;
        const itemCategory = categories.find((c) => c._id === item.category);
        let chowdeckCategory = chowdeckCategories.find((c) => c.name.toLowerCase() === itemCategory.name.toLowerCase());
        if (!chowdeckCategory) {
          const createCateogryRes = await this.chowdeck.createMenuCategory(
            merchantReference,
            itemCategory._id,
            itemCategory.name,
            chowdeckToken,
          );
          if (createCateogryRes.data) {
            chowdeckCategory = createCateogryRes.data;
          }
        }

        updates.push(
          this.chowdeck.updateMenuItem(
            merchantReference,
            itemReference,
            {
              name: item.name,
              description: item.description,
              menu_category_id: chowdeckCategory?.id,
              price: item.price,
              in_stock: item.is_always_available ? true : item.quantity > 0,
              is_published: item.available,
              images: item.images.map((i) => ({ path: i, rank: 1 })),
            },
            chowdeckToken,
          ),
        );
      }
    }
    const res = await Promise.all(updates);

    // await this.brokerTransport
    //   .send(BROKER_PATTERNS.USER.SEND_PUSH_NOTIFICATION, {
    //     store: store._id,
    //     owner_only: false,
    //     message: {
    //       title: 'Synced with chowdeck successfully',
    //       message: `Synced with chowdeck successfully`,
    //       path: '/dashboard',
    //     },
    //   })
    //   .toPromise();

    // const storeOwner = store?.owner;
    // this.resend.sendEmail(BROKER_PATTERNS.MAIL.SYNCED_CHOWDECK_ITEMS, {
    //   to: storeOwner.email,
    //   subject: 'Synced with chowdeck successfully',
    //   data: {
    //     name: storeOwner.name.split(' ')[0],
    //   },
    // });
  }

  async syncFromChowdeckJob(job: Job<StoreJob<{ store: string }>>) {
    console.log('JOB TRIGGERED');
    const jobData = job?.data?.data;
    return await this.storeChowdeckService.autoSyncItemsFromChowdeck(jobData.store);
  }

  async autoItemSyncToChowdeck(job: Job<StoreJob<Item>>) {
    const item = job.data.data;
    const store = await this.storeModel.findById(item?.store).populate('owner').lean();
    const storeCategories = store?.categories ?? [];
    const merchantReference = store?.public_access_tokens?.chowdeck?.reference;
    const chowdeckToken = store?.access_tokens?.chowdeck?.token;
    const itemReference = item?.meta?.chowdeck_reference;

    if (!merchantReference || !itemReference) {
      return;
    }

    const storeConfig = store?.third_party_configs?.chowdeck;
    if (!storeConfig) {
      return;
    }

    const updateData: Partial<ChowdeckUpdateData> = {};
    if (storeConfig.fields_to_update.name) {
      updateData.name = item.name;
    }
    if (storeConfig.fields_to_update.description) {
      updateData.description = item.description;
    }
    if (storeConfig.fields_to_update.category) {
      const category = storeCategories.find((c) => c._id == item?.category);
      if (category) {
        const chowdeckCategory = await this.getOrCreateChowdeckCategory(merchantReference, category, chowdeckToken);
        updateData.menu_category_id = chowdeckCategory?.id;
      }
    }
    if (storeConfig.fields_to_update.price) {
      updateData.price = item.price;
    }
    if (storeConfig.fields_to_update.availability) {
      updateData.in_stock = item.is_always_available ? true : item.quantity > 0;
      updateData.is_published = item.available;
    }
    if (storeConfig.fields_to_update.images) {
      updateData.images = item.images.map((i) => ({ path: i, rank: 1 }));
    }

    const filteredUpdateData: Partial<ChowdeckUpdateData> = Object.fromEntries(
      Object.entries(updateData).filter(([_, value]) => value !== undefined),
    );

    const updatedItem = await this.chowdeck.updateMenuItem(
      merchantReference,
      itemReference,
      filteredUpdateData,
      chowdeckToken,
    );

    if (!updatedItem.error) {
      const storeOwner = store?.owner;
      this.resend.sendEmail(BROKER_PATTERNS.MAIL.SYNCED_CHOWDECK_ITEMS, {
        to: storeOwner.email,
        subject: 'Chowdeck item synced successfully',
        data: {
          name: storeOwner.name.split(' ')[0],
        },
      });
    }
  }

  private async getOrCreateChowdeckCategory(merchantReference, category, chowdeckToken) {
    const chowdeckCategoriesRes = await this.chowdeck.getMenuCategories(merchantReference, chowdeckToken);
    let chowdeckCategory;

    if (!chowdeckCategoriesRes.error) {
      const chowdeckCategories = chowdeckCategoriesRes.data as ChowdeckCategory[];
      chowdeckCategory = chowdeckCategories.find((c) => c.name.toLowerCase() === category.name.toLowerCase());
      if (!chowdeckCategory) {
        const createCategoryRes = await this.chowdeck.createMenuCategory(
          merchantReference,
          category._id,
          category.name,
          chowdeckToken,
        );
        if (createCategoryRes.data) {
          chowdeckCategory = createCategoryRes.data;
        }
      }
    }
    return chowdeckCategory;
  }

  async sendEmailSummaryJob(job: Job<StoreJob<SendEmailSummaryJob>>) {
    const { storeId, period, startOfPeriod, endOfPeriod } = job.data.data;

    try {
      const store = await this.storeModel.findById(storeId).populate({ path: 'owner', select: 'email name' }).lean();

      if (!store || !store.owner?.email) {
        return `No valid store/owner email for store ${storeId}`;
      }

      const summaries = await this.storeService.getEmailSummaries(
        storeId,
        {
          from: startOfPeriod,
          to: endOfPeriod,
        },
        period,
      );

      const emailFormattedSummary: EmailSummaryData = {
        store_name: store.name,
        name: store.owner.name?.split(' ')[0] ?? store.owner.name,
        summary_type: period.charAt(0).toUpperCase() + period.slice(1),
        preview_text: `Here is a summary of your store performance for last ${period}`,
        store_performance: [
          {
            key: EmailMetricKey.TOTAL_ORDERS,
            value: summaries.total_orders,
            change: summaries.trends.total_orders,
          },
          {
            key: EmailMetricKey.PAYMENT,
            value: summaries.total_payments_count,
            change: summaries.trends.total_payments_count,
          },
          {
            key: EmailMetricKey.TOTAL_STORE_VISITS,
            value: summaries.total_visits,
            change: summaries.trends.total_visits,
          },
          {
            key: EmailMetricKey.NEW_CUSTOMERS_COUNT,
            value: summaries.new_customers_count,
            change: summaries.trends.new_customers_count,
          },
        ],
        payments_summary: Object.keys(summaries.total_payments_by_currency).map((key) => ({
          key: key as EmailMetricKey,
          value: summaries.total_payments_by_currency[key],
          change: summaries.trends.payments_trends[key],
        })),
        top_customers: summaries.top_customers.map((customer) => ({
          name: customer.name,
          orders: customer.orders_count,
        })),
        top_products: summaries.top_products.map((product) => ({ name: product.name, orders: product.orders_count })),
      };

      const emailData = {
        to: store.owner.email,
        subject: `Your store activity for ${
          period === 'week' ? 'last week' : dayjs().subtract(1, 'month').format('MMMM')
        } 📊`,
        scheduledAt: dayjs()
          .set('hour', 14) // Set the hour to 2 PM (14 in 24-hour format)
          .set('minute', 0) // Set the minutes to 0
          .set('second', 0) // Set the seconds to 0
          .format(),
        data: emailFormattedSummary,
      };

      await this.resend.sendEmail(BROKER_PATTERNS.MAIL.WEEK_MONTHLY_SUMMARIES, emailData);
      return `Sent ${period} summary email to ${store.owner.email}`;
    } catch (error) {
      console.error(`Failed to send ${period} summary for store ${storeId}: ${error}`);
      throw error;
    }
  }
}

/**
 *
 * CHOWDECK ALGOS
 *
 * IMPORT ITEMS (CHOWDECK ======> CATLOG)
 *
 * - (WHAT IF CHOWDECK FAILS TO ADD MERCHANT TO ACCESS LIST?)
 *
 * - ITEMS TO SELECT (FRONTEND)
 *    - IF STORE DOESN'T HAVE CHOWDECK MERCHANT ID HIDE IMPORT OPTION
 *    - IF FAIL? RETRY MANUALLY OR AUTOMATICALLY (3 TIMES THEN SHOW RETRY BTN OR CONTACT SUPPORT)
 *    - CACHE ITEMS
 *    - SELECT ITEMS AND SEND REQUEST
 *    - SHOW SUCCESSFULL SCREEN
 *
 * - ADD TO QUEUE WITH DATA (BACKEND)
 *    - ADD STORE DATA: CATEGORIES,
 *
 * - ADD PRODUCTS TO STORE (QUEUE)
 *    - FETCH CACHED RESPONSE FROM REDIS
 *    - (IF REDIS IS EMPTY FETCH AGAIN)
 *    - FETCH ITEMS WITH FILTER (ITEM.META.CHOWDECK_REFERENCE)
 *    - FETCH STORE CATEGORIES
 *    - FILTER CATEGORIES TO CREATE
 *    - FILTER ITEMS TO CREATE & ITEMS TO UPDATE (OVERRIDE)
 *    - CREATE & UDPATE ITEMS WITH NEW CATEGORIES
 *    - SEND PUSH NOTIFICATIONS & EMAILS
 *
 * EXPORT ITEMS (CATLOG ======> CHOWDECK)
 *
 * - SELECT ITEMS IN MODAL AND REQUEST
 * - ADD TO QUEUE WITH DATA
 * - FILTER ITEMS WITH CHOWDECK REF INTO TO_CREATE & TO_UPDATE
 * - FETCH CHOWDECK CATEGORIES
 * - FILTER OUT EXISTING AND NEW CATEGORIES FROM ITEMS
 * - CREATE NEW CATEGORIES
 * - MAP ITEMS TO CATEGORIES
 * - UDPATE AND CREATE ITEMS ON CHOWDECK
 * - (IF REQUEST FAILS, RETRY 3 TIMES. IF UNSUCCESSFUL SEND FAILED EMAIL AND NOTIFICATION)
 * - UPDATE CATLOG ITEMS WITH REFS (ADD META DATA ABOUT LAST_UPDATED TIMESTAMPS OF CDECK ITEMS & CURRENT_LAST_UPDATED )
 * - SEND PUSH NOTIFICATIONS & EMAILS
 *
 * AUTO SYNC ITEMS (CATLOG <======> CHOWDECK) XXXXX
 *
 * - CHECK IF UPDATE HAS OCCURRED IN RELATION TO CHOWDECK
 *    - FETCH ITEMS WITH CHOWDECK REF
 *    - FETCH ALL ITEMS FROM CHOWDECK
 *    - FILTER ITEMS IN CHOWDECK
 *
 *    - COMPARE CHOWDECK LAST_UPDATED AND  CD_LAST_UPDATED STORED ON CATLOG ITEM
 *      - IF THERE ARE CHANGES (CHOWDECK ITEM HAS UPDATES)
 *
 *    - COMPARE ITEM LAST_UPDATED TO CT_LAST_UPDATED
 *      - IF THERE ARE CHANGES (CATLOG ITEM HAS UPDATES)
 *
 * MANUAL SYNC ITEMS (CATLOG <=======> CHOWDECK)
 *
 * - SYNC FROM CATLOG ===> CHOWDECK
 *    - FETCH ITEMS WITH CHOWDECK REF
 *    - UPDATE ALL ITEMS WITH REF
 *
 * - SYNC FROM CHOWDECK ===> CATLOG
 *    - FETCH ITEMS FROM CHOWDECK
 *    - FETCH ITEMS WITH CHOWDECK REF
 *    - UPDATE ALL CATLOG ITEMS WITH CHOWDECK DATA
 *
 *
 *
 *
 */
