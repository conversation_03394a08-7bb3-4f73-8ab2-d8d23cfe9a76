import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type TweetedItemDocument = TweetedItem & Document;

@Schema()
export class TweetedItem {
  _id: any;

  @ApiProperty()
  @Prop({ type: String, required: true})
  item: string;

  @ApiProperty()
  @Prop({ type: String, required: true})
  tweet: string;
}

export const TweetedItemSchema = SchemaFactory.createForClass(TweetedItem);
