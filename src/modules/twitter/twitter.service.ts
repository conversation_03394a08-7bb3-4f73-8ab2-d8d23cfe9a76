import { InjectQueue } from '@nestjs/bull';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Queue } from 'bull';
import { Model } from 'mongoose';
import { BrokerTransportService } from '../../broker/broker-transport.service';
import { TwitterApiConfig } from '../../config/types/twitter.config';
import { QUEUES } from '../../enums/queues.enum';
import { TwitterApi } from 'twitter-api-v2';
import { Item } from '../item/item.schema';
import { TweetedItem, TweetedItemDocument } from './twitter.schema';

@Injectable()
export class TwitterBotService {
  protected client: TwitterApi;
  constructor(
    protected readonly brokerTransport: BrokerTransportService,
    protected readonly config: ConfigService,
    @InjectModel(TweetedItem.name)
    protected readonly tweetedItemModel: Model<TweetedItemDocument>, // @InjectQueue(QUEUES.BOT)
  ) // protected readonly botQueue: Queue<Item>
  {
    const keys = config.get<TwitterApiConfig>('twitterConfiguration');
    if (process.env.USE_TWITTER_API == 'false') return;

    this.client = new TwitterApi({
      appKey: keys.appKey,
      appSecret: keys.appSecret,

      accessToken: keys.accessToken,
      accessSecret: keys.accessSecret,
    });
  }

  randFunc() {
    const randString = 'aA1bB2cC3dD4eE5fF6gG7hH8iI9jJ0kK1lL2mM3nN4oO5pP6qQ7rR8sS9tT0uU9vV8wW7xX6yY5zZ4';
    let r = '';
    let i = 0;
    while (i < 8) {
      r += randString[Math.round(Math.random() * (randString.length - 1))];
      i++;
    }

    return r;
  }
}
