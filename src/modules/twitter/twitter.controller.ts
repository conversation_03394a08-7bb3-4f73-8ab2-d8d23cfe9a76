import { InjectQueue } from '@nestjs/bull';
import { Controller, Delete, Get, Injectable, Post, UseGuards } from '@nestjs/common';
import Bull, { Queue } from 'bull';
import { InternalApiKeyGuard } from '../../guards/internal-api-key.guard';
import { BrokerTransportService } from '../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { JOBS, QUEUES } from '../../enums/queues.enum';
import { Item } from '../item/item.schema';
import { InjectModel } from '@nestjs/mongoose';
import { TweetedItem, TweetedItemDocument } from './twitter.schema';
import { Model } from 'mongoose';
import { InternalApiJWTGuard } from '../../guards/api.guard';
import { JwtAuthGuard } from '../../jwt/jwt-auth.guard';
import { ApiExcludeEndpoint } from '@nestjs/swagger';

const ONE_MINUTE = 1000 * 60;
const TWO_MINUTES = 2 * ONE_MINUTE;

interface NewItem extends Item {
  id: string;
}

@Controller('bot/tweet')
@UseGuards(JwtAuthGuard, InternalApiJWTGuard)
export class TwitterBotController {
  constructor(
    // @InjectQueue(QUEUES.BOT)
    // private readonly botQueue: Queue<Item>,
    private readonly brokerTransport: BrokerTransportService,
    @InjectModel(TweetedItem.name)
    protected readonly tweetedItemModel: Model<TweetedItemDocument>,
  ) {}

  @Post('')
  @ApiExcludeEndpoint()
  async tweetNewProducts() {
    // const items = await this.brokerTransport
    //   .send<NewItem[]>(BROKER_PATTERNS.ITEM.GET_SORTED_ITEMS, {
    //     is_deleted: {
    //       $ne: true,
    //     },
    //   })
    //   .toPromise();
    // const tweetedItems: {
    //   id?: string;
    //   item: string;
    // }[] = await this.tweetedItemModel.find({}, { item: 1 }).exec();
    // const flattenedTweetedItems = tweetedItems.map((t) => t.item);
    // let removedItems = 0;
    // const pushedItems = [];
    // const chunkSize = 6;
    // const jobs = [];
    // for (let i = 0; i < items.length; i++) {
    //   const item = items[i];
    //   if (flattenedTweetedItems.includes(item.id)) {
    //     removedItems += 1;
    //     continue;
    //   }
    //   // const actualIndex = i - removedItems;
    //   const chunkIndex = Math.floor((i - removedItems) / chunkSize);
    //   if (item.id) item._id = item.id;
    //   jobs.push(
    //     this.botQueue.add(JOBS.TWEET_EXISTING_PRODUCT, item, {
    //       delay: chunkIndex * (4 * ONE_MINUTE) + ONE_MINUTE,
    //     }),
    //   );
    //   pushedItems.push(item);
    // }
    // Promise.all(jobs);
    // return {
    //   message: 'The following added to the queue successfully',
    //   data: {
    //     totalItems: items.length,
    //     previouslyTweeted: flattenedTweetedItems.length,
    //     itemsToTweet: pushedItems.length,
    //     queue: pushedItems,
    //   },
    // };
    // // const itemsToTweet = items.filter(i => i)
    // return pushedItems;
    // for (let i = 0; i < items.length; i += chunkSize) {
    //   const chunk = items.slice(i, i + chunkSize);
    //   for (let j = 0; j < chunk.length; j++) {
    //     const newItem = chunk[j];
    //     if ((newItem as any).id) newItem._id = (newItem as any).id;
    //     jobs.push(
    //       this.botQueue.add(JOBS.TWEET_EXISTING_PRODUCT, newItem, {
    //         delay: (i / chunkSize) * TWO_MINUTES + ONE_MINUTE,
    //       }),
    //     );
    //     pushedItems.push(newItem);
    //   }
    // }
  }

  @Delete('')
  @ApiExcludeEndpoint()
  async clearQueue() {
    // const jobs = await this.botQueue.getDelayed();
    // jobs.forEach(async (j) => {
    //   await j.discard();
    //   j.remove();
    // });
    // return jobs.map((j) => [j.timestamp, j.opts.delay]);
  }

  @Get('')
  @ApiExcludeEndpoint()
  async getJobs() {
    // const waiting = await this.botQueue.getWaiting();
    // const completed = await this.botQueue.getCompleted();
    // const delayed = await this.botQueue.getDelayed();
    // const parse = (jobs) => jobs.map((j) => j.toJSON());
    // const subData = {
    //   waiting: { length: waiting.length, jobs: parse(waiting) },
    //   processed: { length: completed.length, jobs: parse(completed) },
    //   delayed: { length: delayed.length, jobs: parse(delayed) },
    // };
    // return {
    //   data: subData,
    //   //   number: jobs.length,
    //   //   jobs: jobs.map((j) => j.toJSON()),
    // };
  }
}
