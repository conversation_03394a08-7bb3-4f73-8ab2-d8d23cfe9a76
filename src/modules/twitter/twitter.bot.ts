import axios from 'axios';
import { EUploadMimeType, TwitterApi } from 'twitter-api-v2';
import { Item } from '../item/item.schema';
import { TwitterBotService } from './twitter.service';

import {
  OnQueueActive,
  OnQueueCompleted,
  OnQueueDrained,
  OnQueueEvent,
  OnQueueFailed,
  Process,
  Processor,
} from '@nestjs/bull';
import { JOBS, QUEUES } from '../../enums/queues.enum';
import { Job } from 'bull';
import { Cron, CronExpression } from '@nestjs/schedule';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { IStoreCategory, Store } from '../store/store.schema';
import { Country } from '../country/country.schema';
import { capitalizeFirstLetter } from '../../utils';

const TWEET_TEMPLATE = (
  store: string,
  itemName: string,
  price: string,
  location: string,
  url: string,
  category: string,
) =>
  `
${capitalizeFirstLetter(itemName)} (${store})

💵 Price: ${price}

🌍 ${location}

🔗 To buy & more info: ${url}

#findwithcatlog ${category ? `#${category}` : ''}
`;

const toCurrency = (amount: string, currency: string = 'NGN', decimals?: number) => {
  return `${currency} ${amountFormat(amount, decimals)}`;
};

const amountFormat = (amount: string, toFixed = 2) => {
  return Number(amount)
    .toFixed(toFixed)
    .toString()
    .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// @Processor(QUEUES.BOT)
export class TwitterBot extends TwitterBotService {
  @OnQueueActive()
  onActive(job: Job<Item & { id: string }>) {
    console.log(`Processing job ${job.id} of type ${job.name} with data ${job.data.name}...`);
  }

  @OnQueueFailed()
  onFailed(job: Job, error: Error) {
    console.log(`Failed to process job ${job.id} of type ${job.name} with data, error: ${error.message}`);
  }

  @OnQueueCompleted()
  onCompleted(job: Job, result: any) {
    console.log(`Job ${job.id} of type ${job.name} successfully completed, result: ${result}`);
  }

  @Process(JOBS.TWEET_PRODUCT)
  async handleNewProduct(job: Job<Item & { id: string }>) {
    await this.tweetItem(job);
  }

  @Process(JOBS.TWEET_EXISTING_PRODUCT)
  async handleExistingProduct(job: Job<Item & { id: string }>) {
    await this.tweetItem(job);
  }

  @Process(JOBS.DELETE_PRODUCT_TWEET)
  async handleDeleteItemTweet(job: Job<Item & { id: string }>) {
    await this.deleteItemTweet(job);
  }

  private async deleteItemTweet(job: Job<Item & { id: string }>) {
    const item = job.data;

    const tweetedItem = await this.tweetedItemModel.findOne({ item: item.id });

    if (!tweetedItem) return;

    await this.client.v1.deleteTweet(tweetedItem.tweet);
    await tweetedItem.deleteOne();
  }

  private async tweetItem(job: Job<Item & { id: string }>) {
    // const item = job.data;
    const item = await this.brokerTransport
      .send<Item & { id: string }>(BROKER_PATTERNS.ITEM.GET_ITEM, {
        _id: job.data.id,
      })
      .toPromise();

    const mediaIds = [];

    const tweetedItem = await this.tweetedItemModel.exists({ item: item.id });
    if (tweetedItem) return;

    for (let i = 0; i < 2 && i < item.images.length; i++) {
      const image = await axios
        .get(item.images[i], {
          responseType: 'arraybuffer',
        })
        .then((response) => Buffer.from(response.data, 'binary'));

      const id = await this.client.v1.uploadMedia(image, {
        mimeType: EUploadMimeType.Jpeg,
      });
      mediaIds.push(id);
    }

    const url = await this.brokerTransport
      .send<string, { url: string; referrer: string }>(BROKER_PATTERNS.URL_SHORTENER.SHORTEN, {
        url: process.env.CATLOG_WWW + '/p/' + item.slug,
        referrer: 'twitter-bot',
      })
      .toPromise();
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: item.store })
      .toPromise();
    const country = await this.brokerTransport
      .send<Country>(BROKER_PATTERNS.COUNTRY.GET_COUNTRY, {
        code: store.country,
      })
      .toPromise();

    const category =
      store.categories
        .find((c) => c.id === item?.category)
        ?.name?.split(' ')
        .join('_') ?? '';

    const storeName = store.name.length >= 22 ? store.name.slice(0, 19) + '...' : store.name;
    const itemName = item.name.length >= 115 ? item.name.slice(0, 112) + '...' : item.name;
    const categoryName = category.length >= 18 ? category.slice(0, 15) + '...' : category;

    const res = await this.client.v1.tweet(
      TWEET_TEMPLATE(
        storeName,
        itemName,
        toCurrency(item.price.toString(), country.currency, 0),
        (store.state ? store.state + ', ' : '') + country.name,
        url,
        categoryName ?? '',
      ),
      {
        media_ids: mediaIds,
      },
    );

    await new this.tweetedItemModel({
      item: item.id,
      tweet: res.id_str,
    }).save();

    // console.log(res);
    // await job.finished();
    // job.remove();
  }
}
