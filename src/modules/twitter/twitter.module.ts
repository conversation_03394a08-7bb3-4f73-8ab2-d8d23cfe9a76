import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TwitterBotService } from './twitter.service';
import { TwitterBot } from './twitter.bot';
import { SharedModule } from '../../shared.module';
import { MongooseModule } from '@nestjs/mongoose';
import { TweetedItem, TweetedItemSchema } from './twitter.schema';
import { jsonHookMongoosePlugin, setIdMongoosePlugin } from '../../mongoose-plugins';
import { BullModule } from '@nestjs/bull';
import { QUEUES } from '../../enums/queues.enum';
import { TwitterBotController } from './twitter.controller';

@Module({
  imports: [
    MongooseModule.forFeatureAsync([
      {
        name: TweetedItem.name,
        useFactory: () => {
          TweetedItemSchema.plugin(setIdMongoosePlugin());
          TweetedItemSchema.plugin(jsonHookMongoosePlugin(['_id', '__v']));
          return TweetedItemSchema;
        },
      },
    ]),
    SharedModule,
  ],
  controllers: [TwitterBotController],
  providers: [TwitterBot],
})
export class TwitterBotModule {}
