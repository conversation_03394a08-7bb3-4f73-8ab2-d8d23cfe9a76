import { Controller } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { FilterQuery } from 'mongoose';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { CountryService } from './country.service';
import { Country } from './country.schema';
import { SkipThrottle } from '@nestjs/throttler';

@SkipThrottle()
@Controller()
export class CountryBroker {
  constructor(private readonly countryService: CountryService) {}

  @MessagePattern(BROKER_PATTERNS.COUNTRY.GET_COUNTRY)
  async getCountry(filter: FilterQuery<Country>): Promise<Country> {
    return this.countryService.getCountry(filter);
  }
  @MessagePattern(BROKER_PATTERNS.COUNTRY.GET_ALL_COUNTRIES)
  async getCountries(): Promise<Country[]> {
    return this.countryService.getCountries();
  }
}
