import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Country, StoreCountrySchema } from './country.schema';

import setIdPlugin from '../../mongoose-plugins/set-id.mongoose-plugin';
import jsonHookPlugin from '../../mongoose-plugins/json-hook.mongoose-plugin';

import { SharedModule } from '../../shared.module';
import { StoreCountryController } from './country.controller';
import { CountryService } from './country.service';
import { CountryBroker } from './country.broker';

@Module({
  imports: [
    MongooseModule.forFeatureAsync([
      {
        name: Country.name,
        useFactory: () => {
          StoreCountrySchema.plugin(setIdPlugin());
          StoreCountrySchema.plugin(jsonHookPlugin(['_id', '__v']));
          return StoreCountrySchema;
        }
      }
    ]),
    SharedModule
  ],
  providers: [CountryService],
  controllers: [StoreCountryController, CountryBroker]
})
export class CountryModule {}
