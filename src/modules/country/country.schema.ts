import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Document } from 'mongoose';

export type CountryDocument = Country & Document;

export enum COUNTRY_CODE {
  NG = 'NG',
  GH = 'GH',
  ZA = 'ZA',
  KE = 'KE',
}

export enum COUNTRY_FLAG_EMOJIS {
  NG = '🇳🇬',
  GH = '🇬🇭',
  KE = '🇰🇪',
  ZA = '🇿🇦',
}

export enum CURRENCY_FLAG_EMOJIS {
  GHC = '🇬🇭',
  NGN = '🇳🇬',
  ZAR = '🇿🇦',
  KES = '🇰🇪',
  CAD = '🇨🇦',
  USD = '🇺🇸',
  EUR = '🇪🇺',
  GBP = '🇬🇧',
}

export enum CURRENCIES {
  GHC = 'GHS',
  NGN = 'NGN',
  USD = 'USD',
  EUR = 'EUR',
  GBP = 'GBP',
  ZAR = 'ZAR',
  KES = 'KES',
  CAD = 'CAD',
}

export const CURRENCY_COUNTRY_MAP = {
  [CURRENCIES.GHC]: COUNTRY_CODE.GH,
  [CURRENCIES.NGN]: COUNTRY_CODE.NG,
  [CURRENCIES.ZAR]: COUNTRY_CODE.ZA,
  [CURRENCIES.KES]: COUNTRY_CODE.KE,
};

export const COUNTRY_CURRENCY_MAP = {
  [COUNTRY_CODE.NG]: CURRENCIES.NGN,
  [COUNTRY_CODE.GH]: CURRENCIES.GHC,
  [COUNTRY_CODE.ZA]: CURRENCIES.ZAR,
  [COUNTRY_CODE.KE]: CURRENCIES.KES,
};

export type CurrencyMap = {
  [key in CURRENCIES]: number;
};

@Schema()
export class Country {
  _id: string;

  id: string;

  @ApiProperty()
  @Prop()
  name: string;

  @ApiProperty()
  @Prop({ enum: [CURRENCIES.NGN, CURRENCIES.GHC, CURRENCIES.ZAR, CURRENCIES.KES] })
  currency: CURRENCIES;

  @ApiProperty()
  @Prop()
  dial_code: string;

  @ApiProperty()
  @Prop({ type: String, enum: [COUNTRY_CODE.GH, COUNTRY_CODE.NG, COUNTRY_CODE.ZA, COUNTRY_CODE.KE] })
  code: COUNTRY_CODE;

  @ApiProperty()
  @Prop()
  emoji: string;
}

export const StoreCountrySchema = SchemaFactory.createForClass<Country>(Country);
