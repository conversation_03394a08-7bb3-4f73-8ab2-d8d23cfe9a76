import { Body, Controller, Get, Logger, Post, UseGuards } from '@nestjs/common';
import {
  ApiBody,
  ApiExcludeEndpoint,
  ApiExtraModels,
  ApiHeader,
  ApiParam,
  ApiSecurity,
  ApiTags,
} from '@nestjs/swagger';
import { InternalApiKeyGuard } from '../../guards/internal-api-key.guard';
import { CountryService, ICountry } from './country.service';
import { Country } from './country.schema';
import { JwtAuthGuard } from '../../jwt/jwt-auth.guard';
import { InternalApiJWTGuard } from '../../guards/api.guard';

@Controller('countries')
@ApiTags('StoreCountry')
@ApiExtraModels(Country)
export class StoreCountryController {
  constructor(private readonly countryService: CountryService, private readonly logger: Logger) {}

  @Post('/')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  @ApiBody({ type: Country })
  async addCountry(@Body() country: ICountry) {
    await this.countryService.addNewCountry(country);
    return {
      message: `${country.name} added to catlog`,
      data: {},
    };
  }

  @Get('/')
  async getCountries() {
    const data = await this.countryService.getCountries();
    return {
      message: 'Countries fetched successfully',
      data,
    };
  }
}
