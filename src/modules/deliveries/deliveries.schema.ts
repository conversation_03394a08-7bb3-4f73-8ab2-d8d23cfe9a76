import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Store } from '../store/store.schema';
import { Customer } from '../orders/customers/customer.schema';
import { ApiProperty } from '@nestjs/swagger';
import mongoose, { Document } from 'mongoose';
import { DELIVERY_STATUS, DELIVERY_PROVIDERS, DELIVERY_SERVICE_TYPE } from '../../enums/deliveries';
import { IsDateString, IsNumber, IsOptional, IsString } from 'class-validator';
import { Item } from '../item/item.schema';
import { CURRENCIES } from '../country/country.schema';
import { Order } from '../orders/order.schema';

export enum DELIVERY_TYPE {
  MANUAL = 'MANUAL',
  AUTOMATED = 'AUTOMATED',
}

export class AddressMeta {
  shipbubble_address_code: number;
}

export class ShipbubbleCourier {
  courier_id: string;
  courier_name: string;
  courier_image: string;
  service_code: string;
  insurance: {
    code: string;
    fee: number;
  };
  discount: {
    percentage: number;
    symbol: string;
    discounted: number;
  };
  service_type: string;
  waybill: boolean;
  is_cod_available: boolean;
  tracking_level: number;
  ratings: number;
  votes: number;
  connected_account: boolean;
  rate_card_amount: number;
  pickup_eta: string;
  pickup_eta_time: Date;
  dropoff_station: null;
  pickup_station: null;
  delivery_eta: string;
  delivery_eta_time: Date;
  info: null;
  currency: string;
  vat: number;
  total: number;
  tracking: {
    bars: number;
    label: string;
  };
}

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class Address {
  id: string;
  _id: string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  name: string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  email: string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  phone: string;

  @ApiProperty()
  @Prop({ type: String, required: false })
  street?: string;

  @ApiProperty()
  @Prop({ type: String, required: false })
  user_provided_address?: string;

  @ApiProperty()
  @Prop({ type: String, required: false })
  street_no?: string;

  @Prop({ type: String, required: true })
  formatted_address: string;

  @Prop({ type: String, required: true })
  country: string;

  @Prop({ type: String, required: true })
  country_code: string;

  @Prop({ type: String, required: true })
  city: string;

  @Prop({ type: String, required: true })
  city_code: string;

  @Prop({ type: String, required: true })
  state: string;

  @Prop({ type: String, required: true })
  state_code: string;

  @Prop({ type: String, required: false })
  postal_code?: string;

  @Prop({ type: Number, required: true })
  latitude: number;

  @Prop({ type: Number, required: true })
  longitude: number;

  @Prop({ type: AddressMeta })
  meta: AddressMeta;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Store' })
  store: Store | string;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Customer' })
  customer?: Customer | string;

  @Prop({ type: Boolean, required: false })
  is_guest_address?: boolean;
}

export class DeliveryItem {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty()
  @IsString()
  description: string;

  @ApiProperty()
  @IsNumber()
  unit_weight: number;

  @ApiProperty()
  @IsNumber()
  unit_amount: number;

  @ApiProperty()
  @IsNumber()
  quantity: number;

  @ApiProperty()
  @IsString()
  image?: string;
}

export class DeliveryMeta {
  shipbubble_category_id?: number;
  shipbubble_request_token?: string;
  chowdeck_fee_id?: number;
  chowdeck_order_id?: string;
  fez_order_no?: string;
  shaq_tracking_number?: string;
}

export class DeliveryCourier {
  @ApiProperty()
  @IsString()
  courier_id: string;

  @ApiProperty()
  @IsString()
  courier_name: string;

  @ApiProperty()
  @IsString()
  courier_image: string;

  @ApiProperty()
  @IsString()
  service_code: string;

  @ApiProperty()
  @IsOptional()
  @IsDateString()
  delivery_date: string;

  @ApiProperty()
  @IsOptional()
  @IsDateString()
  pickup_date: string;
}

export class DeliveryPackageDimensions {
  @ApiProperty()
  @IsNumber()
  length: number;

  @ApiProperty()
  @IsNumber()
  width: number;

  @ApiProperty()
  @IsNumber()
  height: number;
}

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class Delivery {
  id: string;
  _id: string;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Address', required: false })
  sender_address?: Address | string;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Address', required: false })
  receiver_address?: Address | string;

  @Prop({
    type: String,
    enum: Object.keys(DELIVERY_PROVIDERS),
    required: false,
  })
  provider?: DELIVERY_PROVIDERS;

  @Prop({
    type: String,
    enum: Object.keys(DELIVERY_TYPE),
    default: DELIVERY_TYPE.MANUAL,
    required: false,
  })
  type?: DELIVERY_TYPE;

  @Prop({ type: DeliveryPackageDimensions, required: false })
  package_dimensions?: DeliveryPackageDimensions;

  @Prop({
    type: String,
    required: false,
    enum: Object.keys(DELIVERY_SERVICE_TYPE),
  })
  service_type?: DELIVERY_SERVICE_TYPE;

  @Prop({ type: String, required: false })
  delivery_notes?: string;

  @Prop({
    type: String,
    required: true,
    enum: Object.keys(DELIVERY_STATUS),
    default: DELIVERY_STATUS.DRAFT,
  })
  status: DELIVERY_STATUS;

  @Prop({
    type: [
      {
        id: String,
        name: String,
        quantity: Number,
        description: String,
        unit_amount: Number,
        unit_weight: Number,
        image: String,
        price: String,
      },
    ],
    required: false,
    default: [],
  })
  items?: DeliveryItem[];

  @Prop({ type: DeliveryMeta, required: false })
  meta?: DeliveryMeta;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Store', required: true })
  store: Store | string;

  @Prop({ type: Date, required: false })
  pickup_date?: Date;

  @Prop({ type: Date, required: false })
  delivery_date?: Date;

  @Prop({ type: String, required: false })
  tracking_code?: string;

  @Prop({ type: String, required: false })
  tracking_url?: string;

  @Prop({ type: Number, required: false })
  delivery_amount?: number;

  @Prop({ type: Number, required: false })
  provider_amount?: number;

  @Prop({ type: Number, required: false })
  package_weight?: number;

  @Prop({ type: DeliveryCourier, required: false })
  courier?: DeliveryCourier;

  @Prop({ type: CURRENCIES, default: CURRENCIES.NGN })
  currency?: CURRENCIES;

  @Prop({ type: [], required: false })
  rates_cache?: any[];

  @Prop({ type: String })
  payment_reference?: string;

  @Prop({ type: String })
  error?: string;

  @Prop({ type: Boolean, required: false, default: false })
  shipment_processed?: boolean;

  @Prop({ type: Boolean, required: false, default: false })
  notify_customer?: boolean;

  @ApiProperty()
  @Prop({ type: String, ref: 'Order' })
  order?: Order | string;
}

export type DeliveryDocument = Delivery & Document;
export type AddressDocument = Address & Document;
export const DeliverySchema = SchemaFactory.createForClass<Delivery>(Delivery);
export const AddressSchema = SchemaFactory.createForClass<Address>(Address);
