import {
  BadRequestException,
  ForbiddenException,
  HttpException,
  Injectable,
  Logger,
  NotFoundException,
  PreconditionFailedException,
} from '@nestjs/common';
import { ShipbubbleRepository, ValidateAddressResponse } from '../../repositories/shipbubble.repository';
import {
  CreateAddressDto,
  CreateDeliveryDraftDto,
  InitiateDeliveryDto,
  UpdateDeliveryDraftDto,
} from '../../models/dtos/delivery.dto';
import { InjectModel } from '@nestjs/mongoose';
import {
  Address,
  AddressDocument,
  DELIVERY_TYPE,
  Delivery,
  DeliveryCourier,
  DeliveryDocument,
  DeliveryItem,
  DeliveryMeta,
  ShipbubbleCourier,
} from './deliveries.schema';
import { FilterQuery, Model, PaginateModel } from 'mongoose';
import { BrokerTransportService } from '../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { Customer, CustomerDocument } from '../orders/customers/customer.schema';
import { DELIVERY_PROVIDERS, DELIVERY_SERVICE_TYPE, DELIVERY_STATUS } from '../../enums/deliveries';
import { PaginatedQueryDto } from '../../models/dtos/PaginatedDto';
import { formatPhoneNumber, mapPaginatedResponse, regularizePhoneNumber } from '../../utils';
import { cleanString, getDocId, stripUndefinedAndNull, toKobo, toNaira } from '../../utils/functions';
import { Store, StoreDocument } from '../store/store.schema';
import { DELIVERIES_MARKUP, DELIVERIES_CANCELLATION_FEE, SHIPBUBBLE_DELIVERY_CATEGORIES } from '../../utils/constants';
import { RpcException } from '@nestjs/microservices';
import COURIER_LOGOS from '../../utils/courier-logos';
import { ChowdeckRepository } from '../../repositories/chowdeck/index.repository';
import { extractErrorMessage } from '../../utils/axios-errors';
import { v4 as uuidV4 } from 'uuid';
import { Cart } from '../cart/cart.schema';
import { format } from 'path';
import { Order, ORDER_CHANNELS, OrderDocument } from '../orders/order.schema';
import { registerErrors } from '../../utils/errors.util';
import { FezCreateOrder, FezDeliveryRepository } from '../../repositories/fez-delivery.repository';
import dayjs from 'dayjs';
import { Payment } from '../payment/payment.schema';
import { PAYMENT_METHODS, PAYMENT_STATUS } from '../../enums/payment.enum';
import { CatlogCreditsTransactions } from '../user/credits/credits.schema';
import { ResendRepository } from '../../repositories/resend.repository';
import { GoogleMapsRepository } from '../../repositories/maps.google.repository';
import { ShaqExpressRepository } from '../../repositories/shaq-express.repository';
import { NOTIFICATION_TYPE } from '../user/notifications/notification.schema';

const DEFAULT_EMAIL = '<EMAIL>';
@Injectable()
export class DeliveryService {
  constructor(
    private readonly logger: Logger,
    public readonly brokerTransport: BrokerTransportService,
    public readonly shipbubble: ShipbubbleRepository,
    protected readonly googleMapsRepository: GoogleMapsRepository,
    public readonly chowdeck: ChowdeckRepository,
    public readonly fezDelivery: FezDeliveryRepository,
    public readonly shaqExpress: ShaqExpressRepository,
    @InjectModel(Delivery.name)
    public readonly deliveryModel: PaginateModel<DeliveryDocument>,
    @InjectModel(Address.name)
    public readonly addressModel: PaginateModel<AddressDocument>,
    @InjectModel(Customer.name)
    public readonly customerModel: Model<CustomerDocument>,
    private readonly resend: ResendRepository,
  ) {}

  async getPackageCategories() {
    return this.shipbubble.getPackageCategories();
  }

  async getPackageDimensions() {
    return this.shipbubble.getPackageDimensions();
  }

  async createAddress(store: string, data: CreateAddressDto, storeName?: string) {
    let { address: userAddress, email, name, phone, customer, save_as_customer } = data;

    if (customer) {
      const validatedCustomer = await this.customerModel.findById(customer);

      if (!validatedCustomer) {
        throw new BadRequestException('Invalid customer selected');
      }

      // name = validatedCustomer.name;
      // phone = validatedCustomer.phone;
      // email = validatedCustomer.email;
    }

    const nameParts = name.split(' ');
    name = nameParts?.length > 1 ? nameParts.join(' ') : `${nameParts[0]} ${storeName}`;

    let strippedData = stripUndefinedAndNull({
      user_provided_address: userAddress,
      customer,
      store,
      phone,
    });

    const address = await this.addressModel.findOne(strippedData);

    if (address) {
      return address;
    }

    let validatedAddress;

    try {
      validatedAddress = await this.shipbubble.validateAddress({
        address: cleanString(userAddress),
        email: email ?? '<EMAIL>',
        name: cleanString(name, true),
        phone,
      });
    } catch (e) {
      validatedAddress = { error: e?.message };
    }

    if (validatedAddress.data) {
      if (save_as_customer) {
        if (
          await this.customerModel.exists({
            phone: phone,
            store: store as any,
          })
        ) {
          throw new HttpException(
            { message: 'Customer with phone number exists, please turn off saving as customer' },
            400,
          );
        }

        const db_customer = await this.customerModel.create({
          name: cleanString(name, true),
          email,
          phone,
          store: store as any,
        });
        customer = db_customer.id;
      }

      return this.saveAddress(validatedAddress?.data, phone, store, userAddress, customer, save_as_customer);
    }

    throw new BadRequestException({ message: `Invalid Address: ${validatedAddress.error}` });
    // throw new HttpException({ message: `Invalid Address: ${validatedAddress.error}` }, 400);

    // throw new PreconditionFailedException('Invalid Address', validatedAddress.error);
  }

  async saveAddress(
    validatedAddress: ValidateAddressResponse,
    originalPhone: string,
    store: string,
    userAddress: string,
    customer?: string,
    save_as_customer: boolean = undefined,
  ) {
    const { address_code, ...rest } = validatedAddress;

    let addressData = {
      ...rest,
      phone: originalPhone, //use user provided phone so as to prevent overiting app phone format "code-digits"
      meta: {
        shipbubble_address_code: address_code,
      },
      store,
      user_provided_address: userAddress,
    };

    if (save_as_customer === false) {
      addressData['is_guest_address'] = true;
    }

    if (customer) {
      addressData['customer'] = customer;
    }

    const newAddress = await this.addressModel.create(addressData);
    return newAddress;
  }

  async createAddressUsingGoogleLookup(store: string, data: CreateAddressDto, storeName?: string) {
    let { address: userAddress, email, name, phone, customer, save_as_customer } = data;

    if (customer) {
      const validatedCustomer = await this.customerModel.findById(customer);

      if (!validatedCustomer) {
        throw new BadRequestException('Invalid customer selected');
      }

      // name = validatedCustomer.name;
      // phone = validatedCustomer.phone;
      // email = validatedCustomer.email;
    }

    const nameParts = name.split(' ');
    name = nameParts?.length > 1 ? nameParts.join(' ') : `${nameParts[0]} ${storeName}`;

    let strippedData = stripUndefinedAndNull({
      user_provided_address: userAddress,
      customer,
      store,
      phone,
    });

    const address = await this.addressModel.findOne(strippedData);

    if (address) {
      return address;
    }

    let validatedAddress;

    try {
      const googleMapsResponse = await this.googleMapsRepository.lookupAddressFullResult(userAddress);

      console.log('googleMapsResponse', googleMapsResponse);

      if (googleMapsResponse.data.status !== 'OK' || !googleMapsResponse.data.results.length) {
        throw new Error('Invalid address');
      }

      const firstResult = googleMapsResponse.data.results[0];
      const addressComponents = firstResult.address_components;

      validatedAddress = {
        name,
        email: email ?? '<EMAIL>',
        formatted_address: firstResult.formatted_address,
        street: this.getAddressComponent(addressComponents, 'route'),
        street_no: this.getAddressComponent(addressComponents, 'street_number'),
        city: this.getAddressComponent(addressComponents, 'locality'),
        city_code: this.getAddressComponent(addressComponents, 'locality'),
        state: this.getAddressComponent(addressComponents, 'administrative_area_level_1'),
        state_code: this.getAddressComponent(addressComponents, 'administrative_area_level_1', true),
        country: this.getAddressComponent(addressComponents, 'country'),
        country_code: this.getAddressComponent(addressComponents, 'country', true),
        postal_code: this.getAddressComponent(addressComponents, 'postal_code'),
        latitude: firstResult.geometry.location.lat,
        longitude: firstResult.geometry.location.lng,
      };
    } catch (e) {
      validatedAddress = { error: e?.message };
    }

    if (!validatedAddress.error) {
      if (save_as_customer) {
        if (
          await this.customerModel.exists({
            phone: phone,
            store: store as any,
          })
        ) {
          throw new HttpException(
            { message: 'Customer with phone number exists, please turn off saving as customer' },
            400,
          );
        }

        const db_customer = await this.customerModel.create({
          name: cleanString(name, true),
          email,
          phone,
          store: store as any,
        });
        customer = db_customer.id;
      }

      return this.saveAddressFromGoogleLookup(validatedAddress, phone, store, userAddress, customer, save_as_customer);
    }

    throw new BadRequestException({ message: `Invalid Address: ${validatedAddress.error}` });
  }

  async saveAddressFromGoogleLookup(
    validatedAddress: any,
    originalPhone: string,
    store: string,
    userAddress: string,
    customer?: string,
    save_as_customer: boolean = undefined,
  ) {
    const addressData: AddressDocument = {
      ...validatedAddress,
      phone: originalPhone,
      store,
      user_provided_address: userAddress,
    };

    if (save_as_customer === false) {
      addressData['is_guest_address'] = true;
    }

    if (customer) {
      addressData['customer'] = customer;
    }

    const newAddress = await this.addressModel.create(addressData);
    return newAddress;
  }

  private getAddressComponent(addressComponents: any[], type: string, shortName: boolean = false): string {
    const component = addressComponents.find((comp) => comp.types.includes(type));
    return component ? (shortName ? component.short_name : component.long_name) : '';
  }

  async createAutoDelivery(
    storeId: string,
    customer: string,
    { phone, delivery_address, name, cartId, note } /* : CreateAutoDeliveryDto */,
  ) {
    let deliveryDraft: DeliveryDocument;
    const cart = await this.brokerTransport
      .send<Cart>(BROKER_PATTERNS.CART.GET_CART, {
        id: cartId,
      })
      .toPromise();

    try {
      const store = await this.brokerTransport
        .send<Store>(BROKER_PATTERNS.STORE.GET_STORE_LEAN, { filter: { _id: storeId } })
        .toPromise();

      const addressDocument = await this.createAddress(
        store.id,
        {
          address: delivery_address,
          email: DEFAULT_EMAIL,
          name,
          phone,
          customer,
          save_as_customer: false,
        },
        store.name,
      );

      deliveryDraft = await this.createDeliveryDraft(store, {
        provider: DELIVERY_PROVIDERS.CHOWDECK,
        receiver_address: getDocId(addressDocument),
        sender_address: getDocId(store.pickup_address),
        items: cart.items.map((i) => ({
          description: i.object.description,
          id: getDocId(i),
          name: i.object.name,
          quantity: i.quantity,
          image: i.object.images[i.object.thumbnail],
          unit_amount: i.object.price,
          unit_weight: 1,
        })),
        pickup_date: new Date().toISOString(),
        delivery_date: new Date().toISOString(),
        package_category: 0,
        package_dimensions: { height: 1, width: 1, length: 1 },
        type: DELIVERY_TYPE.AUTOMATED,
        delivery_notes: `Note: ${note ?? 'None'}, Items: ${cart.items.reduce(
          (p, c) => p + `${c.object.name}, `,
          '',
        )}, Provided Address: ${addressDocument?.user_provided_address ?? 'None'}`,
      });

      await deliveryDraft.save();

      const rates = await this.getShippingRates(storeId, getDocId(deliveryDraft));
      const courier = rates.couriers[0];

      deliveryDraft.courier = courier;
      deliveryDraft.delivery_amount = courier.total;
      deliveryDraft.provider_amount = courier.total - DELIVERIES_MARKUP[deliveryDraft.currency];
      deliveryDraft.notify_customer = true;

      await deliveryDraft.save();

      return { rates, delivery_id: getDocId(deliveryDraft), address_id: getDocId(addressDocument) };
    } catch (e) {
      await this.brokerTransport
        .emit(BROKER_PATTERNS.WHATSAPP_BOT.HANDLE_FAILED_AUTO_DELIVERY, {
          deliveryId: getDocId(deliveryDraft),
          courier: 'Chowdeck',
          storeId: storeId,
          orderId: '',
          error: "Couldn't fetch rates",
          cartItems: cart?.items,
          customerName: name,
        })
        .toPromise();
      throw new RpcException(e);
    }
  }

  async createDeliveryDraft(store: Store, data: CreateDeliveryDraftDto) {
    const { package_category, provider } = data;

    const deliveryDraft = await this.deliveryModel.create({
      status: DELIVERY_STATUS.DRAFT,
      ...data,
      store: getDocId(store),
      provider,
      currency: store?.currencies?.default,
      notify_customer: true,
      meta: {
        shipbubble_category_id: package_category,
      },
    });

    if (data.order) {
      await this.brokerTransport
        .send(BROKER_PATTERNS.ORDER.UPDATE_ORDER, {
          filter: { _id: data?.order },
          payload: { delivery: deliveryDraft?._id },
        })
        .toPromise();
    }

    if (deliveryDraft.items && deliveryDraft.items.length > 0) {
      await this.updateItemWeights(deliveryDraft.items);
    }

    return deliveryDraft;
  }

  private async updateItemWeights(items: DeliveryItem[]) {
    return this.brokerTransport
      .send(BROKER_PATTERNS.ITEM.UPDATE_ITEM_WEIGHTS, {
        items: items.map(({ id, unit_weight, image }) => ({ id, weight: unit_weight, isCustomItem: !Boolean(image) })),
      })
      .toPromise();
  }

  async deleteDeliveryDraft(store: string, delivery_id: string) {
    const dbDelivery = await this.deliveryModel.findById(delivery_id);
    this.validateDelivery(store, dbDelivery);
    return dbDelivery.deleteOne();
  }

  async toggleNotifyCustomers(store: string, id: string, state: boolean) {
    const dbDelivery = await this.deliveryModel.findById(id).populate('receiver_address').populate('sender_address');
    this.validateDelivery(store, dbDelivery);
    dbDelivery.notify_customer = state;
    return dbDelivery.save();
  }

  async updateDeliveryDraft(store: string, data: UpdateDeliveryDraftDto, delivery_id: string) {
    const dbDelivery = await this.deliveryModel.findById(delivery_id);

    this.validateDelivery(store, dbDelivery);
    const { package_category, pickup_date, sender_address, courier } = data;

    const markup: number = DELIVERIES_MARKUP[dbDelivery.currency];
    let courierData = {};

    if (courier && courier?.courier_id) {
      const _courierData = (dbDelivery.rates_cache as ShipbubbleCourier[])?.find(
        (c) => c.courier_id === courier?.courier_id,
      );

      if (!_courierData) {
        throw new HttpException({ message: 'Invalid courier selected' }, 400);
      }

      courierData = { courier, delivery_amount: _courierData.total, provider_amount: _courierData.total - markup };
    }

    const valuesToSave = stripUndefinedAndNull({
      ...data,
      ...courierData,
      sender_address: data?.sender_address ?? undefined,
      receiver_address: data?.receiver_address ?? undefined,
      pickup_date: pickup_date ? new Date(pickup_date) : undefined,
      'meta.shipbubble_category_id': package_category ?? undefined,
    });

    const deliveryDraft = await this.deliveryModel.findOneAndUpdate({ _id: delivery_id }, valuesToSave, {
      new: true,
    });

    if (deliveryDraft.items && deliveryDraft.items.length > 0) {
      await this.updateItemWeights(deliveryDraft.items);
    }

    return deliveryDraft;
  }

  async verifyShipbubbleAddress(address: Address): Promise<Address> {
    // Skip if address already has shipbubble code
    if (address?.meta?.shipbubble_address_code) {
      return address;
    }

    try {
      const validatedAddress = await this.shipbubble.validateAddress({
        address: cleanString(address.formatted_address),
        email: address.email ?? '<EMAIL>',
        name: cleanString(address.name, true),
        phone: address.phone,
      });

      if (!validatedAddress.data) {
        throw new Error(validatedAddress.error || 'Address validation failed');
      }

      // Update address with shipbubble code
      const updatedAddress = await this.addressModel.findByIdAndUpdate(
        address._id,
        {
          meta: {
            ...address.meta,
            shipbubble_address_code: validatedAddress.data.address_code,
          },
        },
        { new: true },
      );

      return updatedAddress;
    } catch (error) {
      throw new BadRequestException(`Failed to verify address with Shipbubble: ${error.message}`);
    }
  }

  async getShippingRates(store: string, delivery_id: string) {
    const dbDelivery = await this.deliveryModel
      .findOne({ _id: delivery_id })
      .populate('receiver_address')
      .populate('sender_address')
      .populate('order');

    this.validateDelivery(store, dbDelivery);

    if (
      !dbDelivery?.meta ||
      !dbDelivery?.receiver_address ||
      !dbDelivery?.sender_address ||
      !dbDelivery?.package_dimensions ||
      !dbDelivery?.pickup_date ||
      !dbDelivery?.items ||
      dbDelivery?.items.length < 1
    ) {
      throw new HttpException({ message: 'Please provide all requested information to get rates' }, 400);
    }

    const {
      meta,
      receiver_address,
      sender_address,
      delivery_notes,
      package_dimensions,
      package_weight,
      items,
      pickup_date,
      currency,
      provider,
    } = dbDelivery;

    let rates;

    const receiverAddress = receiver_address as Address;
    const senderAddress = sender_address as Address;

    if (provider === DELIVERY_PROVIDERS.SHIPBUBBLE) {
      // Verify and update addresses if needed
      const verifiedReceiverAddress = await this.verifyShipbubbleAddress(receiverAddress);
      const verifiedSenderAddress = await this.verifyShipbubbleAddress(senderAddress);

      rates = await this.shipbubble.getShippingRates({
        receiver_address_code: verifiedReceiverAddress.meta?.shipbubble_address_code,
        sender_address_code: verifiedSenderAddress.meta?.shipbubble_address_code,
        category_id: meta.shipbubble_category_id,
        delivery_instructions: delivery_notes,
        package_dimension: package_dimensions,
        package_items: items,
        pickup_date: pickup_date.toISOString().split('T')[0],
        service_type: DELIVERY_SERVICE_TYPE.PICKUP,
      });
    }

    if (provider === DELIVERY_PROVIDERS.CHOWDECK) {
      rates = await this.chowdeck.getDeliveryFee({
        source_address: {
          latitude: senderAddress.latitude.toString(),
          longitude: senderAddress.longitude.toString(),
        },
        destination_address: {
          latitude: receiverAddress.latitude.toString(),
          longitude: receiverAddress.longitude.toString(),
        },
      });
    }

    if (provider === DELIVERY_PROVIDERS.FEZ_DELIVERY) {
      const payload = {
        state: getFezState(receiverAddress.state),
        pickUpState: getFezState(senderAddress.state),
        weight: package_weight,
      };

      rates = await this.fezDelivery.getOrderCost(payload);
    }

    if (provider === DELIVERY_PROVIDERS.SHAQ_EXPRESS) {
      const package_type = SHIPBUBBLE_DELIVERY_CATEGORIES.find((c) => c.category_id === meta.shipbubble_category_id)
        ?.category;
      const amount_paid = items.reduce((sum, item) => sum + item.unit_amount * item.quantity, 0);

      const payload = {
        package_type,
        delivery_notes: delivery_notes,
        pickup_phone_number: regularizePhoneNumber(senderAddress.phone),
        pickup_lng: senderAddress.longitude.toString(),
        pickup_lat: senderAddress.latitude.toString(),
        reference: dbDelivery._id.toString(),
        amount_paid: amount_paid.toString(),
        drop_offs: [
          {
            lng: receiverAddress.longitude.toString(),
            lat: receiverAddress.latitude.toString(),
            phone: regularizePhoneNumber(receiverAddress.phone),
          },
        ],
      };

      rates = await this.shaqExpress.checkPricing(payload);
    }

    if (rates.data) {
      const markup: number = DELIVERIES_MARKUP[currency];
      let couriers = [];

      if (provider === DELIVERY_PROVIDERS.SHIPBUBBLE && rates.data.couriers) {
        couriers = rates.data.couriers.map((c) => ({
          ...c,
          rate_card_amount: c.rate_card_amount + markup,
          total: c.total + markup,
          courier_image: COURIER_LOGOS[c.service_code] ?? c.courier_image,
        }));
      }

      if (provider === DELIVERY_PROVIDERS.CHOWDECK) {
        couriers = [
          {
            courier_id: 'chowdeck',
            courier_name: 'Chowdeck',
            service_code: 'chowdeck',
            pickup_eta: 'Within 20 minutes',
            pickup_eta_time: new Date(),
            delivery_eta: 'Same day delivery',
            delivery_eta_time: new Date(),
            currency: 'NGN',
            rate_card_amount: toNaira(rates.data.total_amount) + markup,
            total: toNaira(rates.data.total_amount) + markup,
            courier_image: COURIER_LOGOS['chowdeck'],
            delivery_date: new Date(),
          },
        ];
      }

      if (provider === DELIVERY_PROVIDERS.FEZ_DELIVERY && rates.data.Cost) {
        const now = dayjs();
        const isIntraState = receiverAddress.state === senderAddress?.state;
        // const order = dbDelivery.order as OrderDocument;
        // let payment: Payment = null;

        // if (order?.invoice) {
        //   payment = await this.brokerTransport
        //     .send<Payment>(BROKER_PATTERNS.PAYMENT.GET_PAYMENT, {
        //       'meta.invoice': order?.invoice,
        //       status: PAYMENT_STATUS.SUCCESS,
        //     })
        //     .toPromise();
        // }

        // const paidForOnCatlog = !!payment;

        couriers = rates.data.Cost.map((c) => {
          const pickupEtaTime = now.add(24, 'hour').toDate();
          const deliveryEtaTime = isIntraState ? now.add(24, 'hour').toDate() : now.add(5, 'days').toDate();
          const total = parseFloat(c.cost) + markup;

          return {
            courier_id: 'fez_delivery',
            courier_name: 'Fez Delivery',
            service_code: 'fez_delivery',
            pickup_eta: 'Within 24 hours',
            pickup_eta_time: pickupEtaTime,
            delivery_eta: isIntraState ? 'Within 24 hours' : 'Between 3 - 5 working days',
            delivery_eta_time: deliveryEtaTime,
            currency: 'NGN',
            rate_card_amount: parseFloat(c.cost) + markup,
            total,
            courier_image: COURIER_LOGOS['fez_delivery'],
            delivery_date: deliveryEtaTime,
          };
        });
      }

      if (provider === DELIVERY_PROVIDERS.SHAQ_EXPRESS && rates.data.data.prices) {
        const shaqData = rates.data.data;
        const now = dayjs();
        const markup: number = DELIVERIES_MARKUP[currency];

        console.log('SHAQ EXPRESS DATA', shaqData);

        // Example “couriers” mapping
        couriers = shaqData.prices.map((price) => {
          // Example logic for pickup/delivery times
          // You can adjust these to reflect your actual SLAs for ShaQ
          const isExpress = price.delivery_type?.toLowerCase() === 'express';
          const pickupEtaTime = now.add(20, 'minutes').toDate();
          const deliveryEtaTime = isExpress ? now.add(2, 'hours').toDate() : now.add(3, 'hours').toDate();

          const baseCost = parseFloat(String(price.charge));
          const total = baseCost + markup;

          // Combine “delivery_type” & “vehicle_type” for the courier’s display name
          const combinedName = `${price.delivery_type} - ${price.vehicle_type}`;

          return {
            courier_id: price.id.toString(),
            courier_name: `Shaq Express (${combinedName})`,
            service_code: 'shaq_express',
            pickup_eta: isExpress ? 'Within 20 minutes' : 'Within an hour',
            pickup_eta_time: pickupEtaTime,
            delivery_eta: isExpress ? 'Within a few hours' : 'Same day delivery',
            delivery_eta_time: deliveryEtaTime,
            currency: 'GHS',
            rate_card_amount: baseCost + markup,
            total,
            courier_image: COURIER_LOGOS['shaq_express'],
            delivery_date: deliveryEtaTime,
          };
        });
      }

      await this.deliveryModel.findOneAndUpdate(
        { _id: delivery_id },
        {
          'meta.shipbubble_request_token': provider === DELIVERY_PROVIDERS.SHIPBUBBLE ? rates.data.request_token : null,
          'meta.chowdeck_fee_id': provider === DELIVERY_PROVIDERS.CHOWDECK ? rates.data.id : null,
          'meta.shaq_tracking_number':
            provider === DELIVERY_PROVIDERS.SHAQ_EXPRESS ? rates.data.data.tracking_number : null,
          rates_cache: couriers,
        },
        {
          new: true,
        },
      );

      return {
        ...rates.data,
        couriers,
      };
    }

    throw new PreconditionFailedException('Invalid delivery parameters', extractErrorMessage(rates.error));
  }

  async retryDeliveryInitiation(store: string, deliveryId: string) {
    const dbDelivery = await this.deliveryModel.findById(deliveryId);
    this.validateDelivery(store, dbDelivery);

    if (!dbDelivery?.payment_reference) {
      throw new BadRequestException('Please pay for this delivery to book it');
    }

    const response = await this.initiateDelivery(deliveryId);

    if (response?.error || (response as Delivery)?.status === DELIVERY_STATUS.DRAFT) {
      throw new BadRequestException(response?.error ?? 'Something went wrong');
    }
    return response;
  }

  async initiateDelivery(deliveryId: string, paymentRef?: string) {
    const dbDelivery = await this.deliveryModel
      .findById(deliveryId)
      .populate('receiver_address')
      .populate('sender_address')
      .populate('order');

    if (!dbDelivery) {
      throw new NotFoundException(`No delivery found with id: ${deliveryId}`);
    }

    const {
      _id,
      sender_address,
      receiver_address,
      meta,
      courier,
      payment_reference,
      package_weight,
      order,
    } = dbDelivery as {
      _id: string;
      sender_address: Address;
      receiver_address: Address;
      meta: DeliveryMeta;
      courier: DeliveryCourier;
      payment_reference: string;
      package_weight: number;
      order: Order;
    };

    let res;
    let trackingUrl;
    let packageWeight;
    let deliveryDate;
    let trackingCode;
    let chowdeckId;
    let reference;
    let fezOrderNo;

    if (dbDelivery.provider === DELIVERY_PROVIDERS.SHIPBUBBLE) {
      console.log('INITIATING SHIPBUBBLE DELIVERY');
      res = await this.shipbubble.initiateDelivery({
        request_token: meta?.shipbubble_request_token,
        courier_id: courier?.courier_id,
        service_code: courier?.service_code,
      });
    }

    if (dbDelivery.provider === DELIVERY_PROVIDERS.CHOWDECK) {
      console.log('INITIATING CHOWDECK DELIVERY');
      reference = uuidV4();

      res = await this.chowdeck.initiateDelivery({
        destination_contact: {
          name: receiver_address?.name,
          phone: regularizePhoneNumber(receiver_address?.phone),
          email: receiver_address?.email,
          country_code: 'NG',
        },
        source_contact: {
          name: sender_address?.name,
          phone: regularizePhoneNumber(sender_address?.phone),
          email: sender_address?.email,
          country_code: 'NG',
        },
        fee_id: meta?.chowdeck_fee_id,
        item_type: 'food',
        user_action: 'sending',
        reference,
        customer_delivery_note: dbDelivery?.delivery_notes,
      });

      if (res.data) {
        try {
          const deliveryInfo = await this.chowdeck.getDeliveryInfo(res?.data?.id);
          res.data.tracking_url = deliveryInfo?.data?.tracking_url;
        } catch {
          console.log('GETTING DELIVERY FROM CHOWDECK DASHBOARD FAILED');
          registerErrors({ message: "COULDN'T GET DELIVERY FROM CHOWDECK DASHBOARD" });
          res.data.tracking_url = '';
        }
      }
    }

    if (dbDelivery.provider === DELIVERY_PROVIDERS.FEZ_DELIVERY) {
      console.log('INITIATING FEZ DELIVERY');
      const totalOrderCost = dbDelivery.items.reduce((sum, item) => sum + item.unit_amount, 0);
      const payload: FezCreateOrder = {
        recipientAddress: receiver_address.formatted_address,
        recipientState: getFezState(receiver_address.state),
        recipientName: receiver_address.name,
        recipientPhone: regularizePhoneNumber(receiver_address.phone),
        uniqueID: getDocId(dbDelivery),
        BatchID: uuidV4(),
        valueOfItem: (totalOrderCost / 2).toString(),
        weight: package_weight,
        pickUpState: getFezState(sender_address.state),
        pickupAddress: sender_address?.formatted_address,
        additionalDetails: `Pickup Address: ${sender_address.formatted_address}\nNotes: ${
          dbDelivery?.delivery_notes ?? ''
        }`,
      };

      res = await this.fezDelivery.createOrder(payload);

      if (res.data) {
        fezOrderNo = res.data.orderNos[payload.uniqueID];
        trackingCode = fezOrderNo;
        trackingUrl = `https://web.fezdelivery.co/track-delivery/${fezOrderNo}`;
      }
    }

    if (dbDelivery.provider === DELIVERY_PROVIDERS.SHAQ_EXPRESS) {
      console.log('INITIATING SHAQ EXPRESS DELIVERY');

      const shaQTrackingNumber = meta?.shaq_tracking_number;
      const shaQPriceId = courier.courier_id;

      if (!shaQTrackingNumber || !shaQPriceId) {
        throw new PreconditionFailedException('Missing ShaQ Express tracking_number or price_id');
      }

      res = await this.shaqExpress.bookTrip({
        tracking_number: shaQTrackingNumber,
        price_id: Number(shaQPriceId),
      });

      console.log(res);
    }

    let delivery: Delivery;

    if (res.data) {
      if (dbDelivery.provider === DELIVERY_PROVIDERS.SHIPBUBBLE) {
        trackingUrl = res.data.tracking_url;
        packageWeight = res.data.package_weight;
        deliveryDate = new Date(dbDelivery?.courier?.delivery_date);
        trackingCode = res.data.order_id;
      }

      if (dbDelivery.provider === DELIVERY_PROVIDERS.CHOWDECK) {
        trackingUrl = res?.data?.tracking_url ?? '';
        trackingCode = trackingUrl?.split('track/')[1] ?? '';
        packageWeight = 1;
        deliveryDate = new Date();
        chowdeckId = res?.data?.id;
      }

      if (dbDelivery.provider === DELIVERY_PROVIDERS.FEZ_DELIVERY) {
        packageWeight = package_weight;
        deliveryDate = new Date();
      }

      if (dbDelivery.provider === DELIVERY_PROVIDERS.SHAQ_EXPRESS) {
        const shaqData = res.data.data;
        console.log(JSON.stringify(shaqData));
        trackingUrl = shaqData?.tracking_url ?? '';
        trackingCode = shaqData?.tracking_number;
        packageWeight = package_weight;
        deliveryDate = new Date();
      }

      delivery = await this.deliveryModel
        .findOneAndUpdate(
          { _id: deliveryId },
          {
            status: DELIVERY_STATUS.PENDING,
            tracking_url: trackingUrl,
            tracking_code: trackingCode,
            package_weight: packageWeight,
            'meta.chowdeck_order_id': chowdeckId,
            'meta.chowdeck_reference': reference,
            'meta.fez_order_no': fezOrderNo,
            // delivery_amount: res.data.payment.shipping_fee,
            pickup_date: new Date(dbDelivery?.courier?.pickup_date),
            delivery_date: new Date(dbDelivery?.courier?.delivery_date),
            payment_reference: paymentRef ?? payment_reference,
            error: undefined,
          },
          {
            new: true,
          },
        )
        .populate('receiver_address')
        .populate('sender_address');

      const emailData = {
        to: sender_address.email,
        subject: `Your Delivery has been booked`,
        data: {
          name: sender_address.name,
          delivery_amount: delivery.delivery_amount,
          item_count: delivery.items.length.toString(),
          pick_up_address: `${sender_address.city_code}, ${sender_address.state}`,
          drop_off_address: `${receiver_address.city_code}, ${receiver_address.state}`,
          courier_name: delivery.courier.courier_name,
          pick_up_date: delivery.pickup_date.toDateString(),
          drop_off_date: delivery.delivery_date.toDateString(),
          tracking_link: delivery.tracking_url,
          courier_image: delivery.courier.courier_image,
        },
      };
      await this.resend.sendEmail(BROKER_PATTERNS.MAIL.DELIVERY_BOOKED, emailData);

      if (delivery.type === DELIVERY_TYPE.AUTOMATED) {
        await this.brokerTransport
          .emit(BROKER_PATTERNS.WHATSAPP_BOT.AUTO_DELIVERY_BOOKED, {
            deliveryId: getDocId(delivery),
            courier: delivery.courier.courier_name,
            storeId: getDocId(delivery.store),
            orderId: delivery.order,
            trackingUrl,
          })
          .toPromise();
      }

      return delivery;
    }

    delivery = await this.deliveryModel
      .findOneAndUpdate(
        { _id: deliveryId },
        {
          payment_reference: paymentRef ?? dbDelivery?.payment_reference,
          error: extractErrorMessage(res?.error),
          status: dbDelivery.type === DELIVERY_TYPE.AUTOMATED ? DELIVERY_STATUS.CANCELLED : dbDelivery.status,
        },
        {
          new: true,
        },
      )
      .populate('receiver_address')
      .populate('sender_address');

    if (dbDelivery.type === DELIVERY_TYPE.AUTOMATED) {
      await this.brokerTransport
        .send(BROKER_PATTERNS.WALLET.REFUND_PAYMENT_TO_WALLET, {
          storeId: getDocId(dbDelivery.store),
          paymentReference: paymentRef ?? dbDelivery?.payment_reference,
          narration: `Refund for cancelled delivery: ${dbDelivery.id}`,
        })
        .toPromise();

      await this.brokerTransport
        .emit(BROKER_PATTERNS.WHATSAPP_BOT.HANDLE_FAILED_AUTO_DELIVERY, {
          deliveryId: getDocId(delivery),
          courier: delivery.courier.courier_name,
          storeId: getDocId(delivery.store),
          orderId: delivery.order,
          error: extractErrorMessage(res?.error),
        })
        .toPromise();
    }

    return delivery;
    // throw new PreconditionFailedException('Invalid delivery parameters', res.data.error);
  }

  //   async getAddress(id: string) {
  // return await this.addressModel.findById(id);
  //   }

  async getStoreAddresses(store: string, include_customers: boolean) {
    if (include_customers) {
      return this.addressModel.find({ store, $or: [{ customer: { $exists: true } }, { is_guest_address: true }] });
    }

    return this.addressModel.find({
      store,
      $and: [{ customer: { $exists: false } }, { is_guest_address: { $exists: false } }],
    });
  }

  async getPaginatedDeliveries(store: string, pagination: PaginatedQueryDto, filter?: FilterQuery<DeliveryDocument>) {
    const compoundFilter: any = {};
    const searchFields = ['items.name', 'items.description', 'tracking_code'];

    if (filter?.search) {
      compoundFilter['$or'] = [...searchFields.map((f) => ({ [f]: { $regex: filter.search, $options: 'i' } }))];
    }

    if (filter?.status) {
      compoundFilter.status = filter.status;
    }

    const result = await this.deliveryModel.paginate(
      {
        ...compoundFilter,
        store,
      },
      {
        sort: { created_at: pagination?.sort === 'DESC' ? -1 : 1 },
        page: pagination.page || 1,
        limit: pagination.per_page || 50,
        lean: true,
      },
    );

    return {
      data: result.docs,
      ...mapPaginatedResponse(result),
    };
  }

  async getDelivery(store: string, delivery_id: string) {
    const dbDelivery = await this.deliveryModel
      .findById(delivery_id)
      .populate('receiver_address')
      .populate('sender_address');
    this.validateDelivery(store, dbDelivery);
    return dbDelivery;
  }

  async updateStatus({ filter, update }) {
    let dbDelivery = await this.deliveryModel
      .findOne(filter)
      .populate('store')
      .populate('order')
      .populate('sender_address')
      .populate('receiver_address');

    if (!dbDelivery) throw new BadRequestException('Delivery does not exist');

    if (update.status === dbDelivery.status) {
      return dbDelivery;
    }

    dbDelivery.status = update.status;
    const sender_address = dbDelivery.sender_address as AddressDocument;
    const receiver_address = dbDelivery.receiver_address as AddressDocument;
    const order = dbDelivery?.order as OrderDocument;
    const store = dbDelivery.store as Store;
    await dbDelivery.save();

    if (update.status === DELIVERY_STATUS.CANCELLED) {
      this.finalizeCancelledDelivery(dbDelivery);
    }

    this.notifyCustomerOfUpdates(dbDelivery, update.status);

    return dbDelivery;
  }

  async cancelDelivery(store: string, delivery_id: string) {
    // const dbDelivery = await this.deliveryModel.findById(delivery_id);
    const dbDelivery = await this.deliveryModel
      .findById(delivery_id)
      .populate('store')
      .populate('order')
      .populate('sender_address')
      .populate('receiver_address');

    this.validateDelivery(store, dbDelivery);

    // const isProcessed = dbDelivery.pickup_date < new Date();

    if (dbDelivery.status !== DELIVERY_STATUS.PENDING && dbDelivery.status !== DELIVERY_STATUS.CONFIRMED) {
      throw new PreconditionFailedException('Delivery cannot be canceled');
    }

    let res;

    if (dbDelivery.provider === DELIVERY_PROVIDERS.SHIPBUBBLE) {
      res = await this.shipbubble.cancelDelivery({
        order_id: dbDelivery.tracking_code,
      });
    }

    if (dbDelivery.provider === DELIVERY_PROVIDERS.CHOWDECK) {
      res = await this.chowdeck.cancelDelivery(dbDelivery.meta?.chowdeck_order_id);
    }

    if (dbDelivery.provider === DELIVERY_PROVIDERS.FEZ_DELIVERY) {
      res = await this.fezDelivery.cancelOrder(dbDelivery.meta?.fez_order_no);
    }

    if (dbDelivery.provider === DELIVERY_PROVIDERS.SHAQ_EXPRESS) {
      res = await this.shaqExpress.cancelTrip({ tracking_number: dbDelivery.meta?.shaq_tracking_number });
    }

    if (res.data) {
      dbDelivery.status = DELIVERY_STATUS.CANCELLED;
      await dbDelivery.save();

      await this.finalizeCancelledDelivery(dbDelivery, true);
      await this.notifyCustomerOfUpdates(dbDelivery, DELIVERY_STATUS.CANCELLED);
      return;
    }

    throw new PreconditionFailedException('Invalid delivery parameters', res.error);
  }

  async finalizeCancelledDelivery(delivery: Delivery, cancelledByMerchant: boolean = false) {
    const order = delivery?.order as OrderDocument;
    const store = delivery.store as Store;

    //remove delivery from order
    if (delivery?.order) {
      await this.brokerTransport
        .send(BROKER_PATTERNS.ORDER.UPDATE_ORDER, {
          filter: { _id: getDocId(order) },
          payload: { delivery: null },
        })
        .toPromise();
    }

    //refund payments
    if (delivery?.payment_reference) {
      const payment = await this.brokerTransport
        .send<Payment>(BROKER_PATTERNS.PAYMENT.GET_PAYMENT, { reference: delivery?.payment_reference })
        .toPromise();

      const cancellationFee = toKobo(cancelledByMerchant ? DELIVERIES_CANCELLATION_FEE[payment.currency] : 0);
      const isCatlogCreditPayment = payment?.payment_method === PAYMENT_METHODS.CATLOG_CREDIT;
      let catlogCreditPartPayment: Payment;
      let cancellationFeeToChargeCredits = 0;

      if (payment?.partner_payment) {
        catlogCreditPartPayment = await this.brokerTransport
          .send<Payment>(BROKER_PATTERNS.PAYMENT.GET_PAYMENT, { _id: payment?.partner_payment })
          .toPromise();
      }

      if (isCatlogCreditPayment || catlogCreditPartPayment) {
        const paymentToUse = isCatlogCreditPayment ? payment : catlogCreditPartPayment;
        cancellationFeeToChargeCredits =
          cancelledByMerchant &&
          (isCatlogCreditPayment || (!isCatlogCreditPayment && payment.amount_with_charge < cancellationFee))
            ? cancellationFee
            : 0; //charge catlog credits the cancellation fee if the payment was made from catlog credits or the amount paid by other payment method is less than the cancellation fee

        await this.brokerTransport
          .send<CatlogCreditsTransactions>(BROKER_PATTERNS.USER.CREDITS.ADD_CREDITS, {
            user_id: getDocId(payment?.owner),
            amount: paymentToUse?.amount_with_charge - cancellationFeeToChargeCredits,
            meta: {
              payment_id: getDocId(paymentToUse),
            },
            narration: `Reversal for Delivery ${delivery.id}`,
          })
          .toPromise();
      }

      if (store?.wallet && !isCatlogCreditPayment) {
        const cancellationFeeToChargeWallet =
          cancelledByMerchant && cancellationFeeToChargeCredits === 0 ? cancellationFee : 0;

        await this.brokerTransport
          .send(BROKER_PATTERNS.WALLET.REFUND_PAYMENT_TO_WALLET, {
            storeId: getDocId(store),
            paymentReference: delivery.payment_reference,
            narration: `Refund for cancelled delivery: ${delivery.id}`,
            deductAmount: cancellationFeeToChargeWallet,
          })
          .toPromise();
      }
    }
  }

  async notifyCustomerOfUpdates(delivery: Delivery, status: DELIVERY_STATUS) {
    const order = delivery?.order as OrderDocument;
    const store = delivery.store as Store;
    const sender_address = delivery.sender_address as AddressDocument;
    const receiver_address = delivery.receiver_address as AddressDocument;

    const sellerEmailTexts = getSellerEmailText(delivery.courier?.courier_name, receiver_address?.name)[
      delivery.status
    ];

    const customerEmailTexts = getCustomerEmailText(
      store.name,
      delivery.delivery_date.toDateString(),
      delivery.courier?.courier_name,
    )[delivery.status];

    if (
      delivery.notify_customer &&
      delivery.status !== DELIVERY_STATUS.IN_TRANSIT &&
      receiver_address.email !== DEFAULT_EMAIL
    ) {
      const customerEmailData = {
        to: receiver_address.email,
        subject: customerEmailTexts.subject,
        data: {
          title_text: customerEmailTexts.title_text,
          store_name: store?.name,
          body_text: customerEmailTexts.body_text,
          name: receiver_address.name,
          tracking_link: delivery.tracking_url,
          courier_name: delivery.courier?.courier_name,
        },
      };

      await this.resend.sendEmail(BROKER_PATTERNS.MAIL.CUSTOMER_DELIVERY_STATUS, customerEmailData);
    }

    if (
      order &&
      order?.channel === ORDER_CHANNELS.CHATBOT &&
      delivery.notify_customer &&
      status !== DELIVERY_STATUS.PENDING
    ) {
      await this.brokerTransport
        .emit(BROKER_PATTERNS.WHATSAPP_BOT.SEND_DELIVERY_STATUS, {
          status: status,
          phone: formatPhoneNumber(receiver_address.phone),
          items: delivery.items.reduce((p, c) => p + `${c.name}(${c.quantity}X) `, ''),
          trackingUrl: delivery.tracking_url,
          storeName: store?.name,
          storePhone: formatPhoneNumber(store.phone),
          orderId: getDocId(order),
          courier: delivery.courier?.courier_name,
        })
        .toPromise();
    }

    const sellerEmailData = {
      to: sender_address.email,
      subject: sellerEmailTexts.subject,
      data: {
        title_text: sellerEmailTexts.title_text,
        body_text: sellerEmailTexts.body_text,
        name: sender_address.name.split(' ')[0],
        tracking_link: delivery.tracking_url,
        tracking_code: delivery.tracking_code,
        courier_name: delivery.courier?.courier_name,
      },
    };

    await this.resend.sendEmail(BROKER_PATTERNS.MAIL.SELLER_DELIVERY_STATUS, sellerEmailData);

    if (order && order?.channel === ORDER_CHANNELS.CHATBOT) {
      await this.brokerTransport
        .emit(BROKER_PATTERNS.WHATSAPP_BOT.HANDLE_FAILED_AUTO_DELIVERY, {
          deliveryId: getDocId(delivery),
          courier: delivery.courier.courier_name,
          storeId: getDocId(delivery.store),
          orderId: order?.id,
          error: 'Courier cancelled/rejected delivery',
        })
        .toPromise();
    }
    await this.brokerTransport
      .emit(BROKER_PATTERNS.USER.SEND_PUSH_NOTIFICATION, {
        message: {
          title: sellerEmailTexts.subject,
          message: sellerEmailTexts?.push_message,
          path: `/deliveries/${delivery?.id}`,
        },
        owner_only: false,
        store: store._id,
        notification_type: NOTIFICATION_TYPE.DELIVERY_STATUS,
        data: {
          id: delivery?.id,
          store_id: store?._id,
          order_id: order?._id,
          courier: delivery?.courier?.courier_name,
        },
      })
      .toPromise();
  }

  async getStatistics(store: string, filter?: any) {
    const statuses = ['CANCELLED', 'ONGOING', 'CONFIRMED'];
    const deliveries = await this.deliveryModel.find({ store }, { created_at: 1, status: 1 }).lean();
    const statistics = statuses.reduce(
      (acc, key) => {
        acc[key.toLowerCase() + '_shipments'] = 0;
        return acc;
      },
      {
        total_shipments: deliveries.length,
      },
    );

    for (let i = 0; i < deliveries.length; i++) {
      const delivery = deliveries[i];
      if (
        [
          DELIVERY_STATUS.PENDING,
          DELIVERY_STATUS.IN_TRANSIT,
          DELIVERY_STATUS.CONFIRMED,
          DELIVERY_STATUS.CONFIRMED,
        ].includes(delivery.status)
      )
        statistics['ongoing_shipments']++;
      else statistics[delivery.status.toLocaleLowerCase() + '_shipments']++;
    }
    return statistics;
  }

  validateDelivery(store: string, dbDelivery: DeliveryDocument) {
    if (!dbDelivery) throw new BadRequestException('Delivery does not exist');
    if (getDocId(dbDelivery.store).toString() !== store) throw new ForbiddenException('Delivery is not owned by store');
  }

  async getAddress(addressId) {
    return await this.addressModel.findById(addressId).lean();
  }

  async deliveriesWrapCompute(storeId: string, startDate: Date, endDate: Date): Promise<number> {
    const completedDeliveriesCount = await this.deliveryModel.countDocuments({
      store: storeId, // Filter by storeId
      status: DELIVERY_STATUS.COMPLETED, // Filter by completed status
      delivery_date: {
        $gte: startDate, // Filter by start date (greater than or equal)
        $lte: endDate, // Filter by end date (less than or equal)
      },
    });

    return completedDeliveriesCount;
  }
}

const getFezState = (state) => {
  return state === 'Federal Capital Territory' ? 'FCT' : state;
};

const getCustomerEmailText = (storeName: string, eta: string, courier: string) => {
  return {
    [DELIVERY_STATUS.CONFIRMED]: {
      subject: `${storeName} booked a delivery for your order ✅`,
      title_text: `A delivery has been <br /> booked for your order`,
      body_text: `<b style="font-weight:500;">${storeName}</b> has booked a delivery with ${courier} for your order, it will be picked up soon and should arrive on <b style="font-weight:500;">${eta}</b>`,
    },
    [DELIVERY_STATUS.IN_TRANSIT]: {
      subject: `Your items from ${storeName} are on their way to you 🚚`,
      title_text: `Your items from <br /> ${storeName} are on their way to you`,
      body_text: `is now in transit and should arrive on ${eta}`,
    },
    [DELIVERY_STATUS.PICKED_UP]: {
      subject: `Your items have been picked up for delivery 📦`,
      title_text: `${courier} has <br /> picked up your order`,
      body_text: `<b style="font-weight:500;">${courier}</b> has picked up your order from <b style="font-weight:500;">${storeName}</b>, it should arrive on <b style="font-weight:500;">${eta}</b>`,
    },
    [DELIVERY_STATUS.COMPLETED]: {
      subject: `Your items from ${storeName} have been arrived 🎉`,
      title_text: `${courier} has <br /> delivered your items`,
      body_text: `<b style="font-weight:500;">${courier}</b> has delivered your order from <b style="font-weight:500;">${storeName}</b>, we hope you enjoyed our service and look forward to your next purchase.`,
    },
    [DELIVERY_STATUS.CANCELLED]: {
      subject: `Your delivery was cancelled ❌`,
      title_text: `${courier} has <br /> cancelled your delivery`,
      body_text: `We regret to inform you that, <b style="font-weight:500;">${courier}</b> has cancelled delivery of your items from <b style="font-weight:500;">${storeName}</b>. Please reach out to <b style="font-weight:500;">${storeName}</b> for next steps`,
    },
  };
};

const getSellerEmailText = (courier: string, receiver: string) => ({
  [DELIVERY_STATUS.CONFIRMED]: {
    subject: 'Your delivery has been confirmed ✅',
    title_text: 'Your delivery has <br /> been confirmed',
    notification_text: 'Your delivery has been confirmed',
    body_text: 'has been confirmed',
    push_message: `${courier} has confirmed your delivery to ${receiver}`,
  },
  [DELIVERY_STATUS.IN_TRANSIT]: {
    subject: "Your delivery is on it's way 🚚",
    title_text: 'Your delivery is <br /> now in transit',
    notification_text: 'Your delivery is now in transit',
    body_text: 'is now in transit',
    push_message: `Your delivery to ${receiver} is now in transit`,
  },
  [DELIVERY_STATUS.PICKED_UP]: {
    subject: 'Your items have been picked up for delivery 📦',
    title_text: 'Your items have <br /> been picked up',
    notification_text: 'Your items have been picked up',
    body_text: 'has been picked up',
    push_message: `${courier} has picked up your items, for delivery to ${receiver}`,
  },
  [DELIVERY_STATUS.COMPLETED]: {
    subject: 'Your items have been delivered 🎉',
    title_text: 'Your items have <br /> been delivered',
    notification_text: 'Your items have been delivered',
    body_text: 'has been completed',
    push_message: `Your delivery to ${receiver} has been successfully delivered`,
  },
  [DELIVERY_STATUS.CANCELLED]: {
    subject: 'Your delivery was cancelled ❌',
    title_text: 'Your delivery <br /> was cancelled',
    notification_text: 'Your delivery was cancelled',
    body_text: 'was cancelled',
    push_message: `${courier} has cancelled your delivery to ${receiver}`,
  },
});
