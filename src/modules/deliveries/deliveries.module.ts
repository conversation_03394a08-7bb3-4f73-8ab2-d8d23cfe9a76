import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import setIdPlugin from '../../mongoose-plugins/set-id.mongoose-plugin';
import jsonHookPlugin from '../../mongoose-plugins/json-hook.mongoose-plugin';
import mongoosePaginate from 'mongoose-paginate-v2';
import { SharedModule } from '../../shared.module';
import { DeliveryService } from './deliveries.service';
import { Address, AddressSchema, Delivery, DeliverySchema } from './deliveries.schema';
import { DeliveriesController } from './deliveries.controller';
import { DeliveriesBroker } from './deliveries.broker';
import { Customer, CustomerSchema } from '../orders/customers/customer.schema';

@Module({
  imports: [
    MongooseModule.forFeatureAsync([
      {
        name: Delivery.name,
        useFactory: () => {
          DeliverySchema.plugin(setIdPlugin());
          DeliverySchema.plugin(jsonHookPlugin(['_id', '__v']));
          DeliverySchema.plugin(mongoosePaginate);
          return DeliverySchema;
        },
      },
      {
        name: Address.name,
        useFactory: () => {
          AddressSchema.plugin(setIdPlugin());
          AddressSchema.plugin(jsonHookPlugin(['_id', '__v']));
          AddressSchema.plugin(mongoosePaginate);
          return AddressSchema;
        },
      },
      {
        name: Customer.name,
        useFactory: () => {
          CustomerSchema.plugin(setIdPlugin());
          CustomerSchema.plugin(jsonHookPlugin(['_id', '__v']));
          CustomerSchema.plugin(mongoosePaginate);
          return CustomerSchema;
        },
      },
    ]),
    SharedModule,
  ],
  providers: [DeliveryService],
  controllers: [DeliveriesController, DeliveriesBroker],
})
export class DeliveryModule {}
