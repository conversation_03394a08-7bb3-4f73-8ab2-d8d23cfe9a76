import { Controller } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { BROKER_PATTERNS } from '../../enums/broker.enum';

import { DeliveryService } from './deliveries.service';
import { AddressDocument, Delivery, DeliveryDocument } from './deliveries.schema';
import { Address } from 'aws-sdk/clients/ses';
import { CreateAddressDto } from '../../models/dtos/delivery.dto';
import { ValidateAddressResponse } from '../../repositories/shipbubble.repository';
import { SkipThrottle } from '@nestjs/throttler';

@SkipThrottle()
@Controller()
export class DeliveriesBroker {
  constructor(private readonly deliveryService: DeliveryService) {}

  @MessagePattern(BROKER_PATTERNS.DELVERIES.UPDATE_ADDRESS)
  async updateAddress({ filter, update }): Promise<AddressDocument> {
    return this.deliveryService.addressModel.updateOne(filter, update);
  }

  @MessagePattern(BROKER_PATTERNS.DELVERIES.UPDATE_DELIVERY)
  async updateDelivery({ filter, update }): Promise<AddressDocument> {
    return this.deliveryService.deliveryModel.updateOne(filter, update);
  }

  @MessagePattern(BROKER_PATTERNS.DELVERIES.UPDATE_STATUS)
  async updateStatus({ filter, update }): Promise<DeliveryDocument> {
    return this.deliveryService.updateStatus({ filter, update });
  }

  @MessagePattern(BROKER_PATTERNS.DELVERIES.GET_DELIVERY)
  async getDelivery(filter): Promise<Delivery> {
    return this.deliveryService.deliveryModel.findOne(filter).populate('receiver_address').populate('sender_address');
  }

  @MessagePattern(BROKER_PATTERNS.DELVERIES.INITIATE_DELIVERY)
  async initiateDelivery(data: { deliveryId: string; paymentRef: string }) {
    return await this.deliveryService.initiateDelivery(data.deliveryId, data.paymentRef);
  }

  @MessagePattern(BROKER_PATTERNS.DELVERIES.CREATE_ADDRESS)
  async createAddress(payload: { store: string; data: CreateAddressDto; storeName?: string }) {
    return this.deliveryService.createAddress(payload.store, payload.data, payload?.storeName);
  }

  @MessagePattern(BROKER_PATTERNS.DELVERIES.CREATE_ADDRESS_USING_GOOGLE_VALIDATION)
  async createAddressUsingGoogleValidation(payload: { store: string; data: CreateAddressDto; storeName?: string }) {
    return this.deliveryService.createAddressUsingGoogleLookup(payload.store, payload.data, payload?.storeName);
  }

  @MessagePattern(BROKER_PATTERNS.DELVERIES.CREATE_AUTO_DELIVERY)
  async createAutoDelivery(payload: { store: string; customer: string; data: any }) {
    return this.deliveryService.createAutoDelivery(payload.store, payload.customer, payload.data);
  }

  @MessagePattern(BROKER_PATTERNS.DELVERIES.GET_ADDRESS)
  async getAddress(addressId) {
    return this.deliveryService.getAddress(addressId);
  }

  @MessagePattern(BROKER_PATTERNS.DELVERIES.SAVE_ADDRESS)
  async saveAddress(payload: {
    validatedAddress: ValidateAddressResponse;
    phone: string;
    store: string;
    customer: string;
  }) {
    const { validatedAddress, phone, store, customer } = payload;
    return this.deliveryService.saveAddress(validatedAddress, phone, store, customer);
  }

  @MessagePattern(BROKER_PATTERNS.DELVERIES.GET_DELIVERIES_WRAP_DATA)
  async getDeliveriesWrapData(payload) {
    return this.deliveryService.deliveriesWrapCompute(payload.storeId, payload.startDate, payload.endDate);
  }
}
