import { Injectable } from '@nestjs/common';
import { EventEmitter } from 'events';

@Injectable()
export class EventEmitterService {
  private readonly eventEmitter = new EventEmitter();

  emit(event: string, data: any) {
    console.log('<===== EMIT SSE EVENT =====>');
    console.log({ event, data });
    this.eventEmitter.emit(event, data);
  }

  on(event: string, callback: (...args: any[]) => void) {
    this.eventEmitter.on(event, callback);
    return () => this.eventEmitter.off(event, callback);
  }
}
