import { Injectable, Logger } from '@nestjs/common';
import { Response } from 'express';
import { observable, Observable, Subject } from 'rxjs';
import { EventEmitterService } from './sse.bridge';

@Injectable()
export class SSEService {
  private readonly logger = new Logger(SSEService.name);
  private clients = new Map<string, Subject<MessageEvent>>();
  private clientResponses = new Map<string, Set<Response>>();

  constructor(private readonly eventEmitter: EventEmitterService) {
    // Listen for client messages from any module
    this.eventEmitter.on('sse:send', ({ clientId, data }) => {
      console.log('<===== RECEIVED SSE EVENT =====>');
      console.log({ clientId, data });
      this.sendToClient(clientId, data);
    });
  }

  addClient(clientId: string, res: Response): Observable<MessageEvent> {
    // Create a new Subject if one doesn't exist for this client ID
    if (!this.clients.has(clientId)) {
      this.clients.set(clientId, new Subject<MessageEvent>());
    }

    const subject = this.clients.get(clientId);

    // Store the response object in memory
    if (!this.clientResponses.has(clientId)) {
      this.clientResponses.set(clientId, new Set());
    }
    this.clientResponses.get(clientId).add(res);

    // Send initial message using both methods
    const initialMessage = { type: 'connected' };
    subject.next({ data: JSON.stringify(initialMessage) } as MessageEvent);
    res.write(`data: ${JSON.stringify(initialMessage)}\n\n`);

    // Handle client disconnect
    res.on('close', () => {
      this.logger.debug(`Client ${clientId} disconnected`);
      this.handleClientDisconnect(clientId, res);
    });

    return subject.asObservable();
  }

  private handleClientDisconnect(clientId: string, res: Response) {
    // Remove this specific response from our tracking
    const responseSet = this.clientResponses.get(clientId);
    if (responseSet) {
      responseSet.delete(res);

      // If no more connections for this client, clean up
      if (responseSet.size === 0) {
        this.clientResponses.delete(clientId);
        this.clients.delete(clientId);
      }
    }
  }

  sendToClient(clientId: string, data: any) {
    this.logger.debug(`Sending to client ${clientId}:`, data);

    // Method 1: Use the Subject (preferred NestJS way)
    console.log({ clients: this.clients });
    const subject = this.clients.get(clientId);

    console.log({ subject });
    if (subject) {
      subject.next({ data: JSON.stringify(data) } as MessageEvent);
    }

    // Method 2: Directly write to all response objects
    const responseSet = this.clientResponses.get(clientId);
    if (responseSet && responseSet.size > 0) {
      for (const res of responseSet) {
        try {
          res.write(`data: ${JSON.stringify(data)}\n\n`);
        } catch (error) {
          this.logger.error(`Error writing to response for client ${clientId}:`, error);
          responseSet.delete(res);
        }
      }
    }
  }
}
