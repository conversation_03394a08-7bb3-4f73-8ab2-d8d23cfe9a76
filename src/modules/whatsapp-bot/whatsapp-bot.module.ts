import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SharedModule } from '../../shared.module';
import { WhatsappChatbotController } from './whatsapp-bot.controller';
import { WhatsappChatbotBroker } from './whatsapp-bot.broker';
import { WhatsappMessagingService } from './services/messaging.service';
import { WABotSession, BotSessionSchema, StoreOpenedReminder, StoreOpenedReminderSchema } from './whatsapp-bot.schema';
import setIdPlugin from '../../mongoose-plugins/set-id.mongoose-plugin';
import jsonHookPlugin from '../../mongoose-plugins/json-hook.mongoose-plugin';
import mongoosePaginate from 'mongoose-paginate-v2';
import { WhatsappChatbotService } from './services/whatsapp-bot.service';
import { BullModule } from '@nestjs/bull';
import { QUEUES } from '../../enums/queues.enum';
import { WABotQueue } from './whatsapp-bot.queue';
import { TokenSessionService } from './services/token-session.service';
import { SellerProcessor } from './services/seller.processor.service';

@Module({
  imports: [
    BullModule.registerQueueAsync({
      name: QUEUES.WHATSAPP_BOT,
    }),
    MongooseModule.forFeatureAsync([
      {
        name: WABotSession.name,
        useFactory: () => {
          BotSessionSchema.plugin(setIdPlugin());
          BotSessionSchema.plugin(jsonHookPlugin(['_id', '__v']));
          BotSessionSchema.plugin(mongoosePaginate);
          return BotSessionSchema;
        },
      },
      {
        name: StoreOpenedReminder.name,
        useFactory: () => {
          StoreOpenedReminderSchema.plugin(setIdPlugin());
          StoreOpenedReminderSchema.plugin(jsonHookPlugin(['_id', '__v']));
          StoreOpenedReminderSchema.plugin(mongoosePaginate);
          return StoreOpenedReminderSchema;
        },
      },
    ]),
    SharedModule,
  ],
  providers: [WABotQueue, WhatsappChatbotService, WhatsappMessagingService, SellerProcessor, TokenSessionService],
  controllers: [WhatsappChatbotController, WhatsappChatbotBroker],
})
export class WhatsappChatbotModule {}
