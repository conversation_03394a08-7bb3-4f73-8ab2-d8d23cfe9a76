import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'mongoose';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { INTENTS, INTERACTIVE_MESSAGE_TYPES, WHATSAPP_BOT_STEPS } from '../../../enums/whatsapp.enum';
import { BotProcessorResult, GenericBotSession } from '../../../interfaces/whatsapp.interface';
import { MESSAGE_TYPES } from '../../../repositories/whatsapp.repository';
import { sluggify } from '../../../utils';
import { Store } from '../../store/store.schema';
import { MessageData, getContext, getCurrentSessionTtl, getFirstName } from '../utils/functions.utils';
import { CTA_REPLIES, MESSAGES } from '../utils/messages.utils';
import { BaseProcessor } from './base.processor.service';

export class SearchProcessor extends BaseProcessor {
  protected async processSearchResultsStep(session: GenericBotSession, data: MessageData): BotProcessorResult {
    const ctx = getContext(session);
    if (data.getMessageType() === MESSAGE_TYPES.INTERACTIVE) {
      const interactiveMessage = data.getInteractiveMessage();
      const isListReply = interactiveMessage?.list_reply !== undefined;
      const isButtonReply = interactiveMessage?.button_reply !== undefined;

      if (isButtonReply) {
        const key = interactiveMessage?.button_reply?.id;
        switch (key) {
          case sluggify(CTA_REPLIES.LOAD_MORE_STORES):
            return this.searchStores(session, session?.search_data?.query);
          case sluggify(CTA_REPLIES.MAKE_NEW_SEARCH):
            const stepMessage = await this.messaging.send(MESSAGES.SEARCH_STORE_MESSAGE, ctx);
            session.search_data = { query: undefined, skip: 0 };

            await this.saveSession(session);

            return { currentStep: WHATSAPP_BOT_STEPS.SEARCH_STORE, intent: INTENTS.SEARCH_STORE, stepMessage };
        }
      } else if (isListReply) {
        // handle selection
        const store = await this.brokerTransport
          .send<Store>(BROKER_PATTERNS.STORE.GET_STORE_LEAN, { filter: { _id: interactiveMessage.list_reply.id } })
          .toPromise();

        return this.handleStoreInitiation(store, session, data.getName(), true);
      }
    }

    await this.messaging.send(MESSAGES.UNEXPECTED_RESPONSE_MESSAGE, ctx, {
      name: session.customer?.name ?? data.getName(),
    });

    return { currentStep: WHATSAPP_BOT_STEPS.STORE_SEARCH_NO_RESULTS, intent: INTENTS.SEARCH_STORE };
  }
  protected async processNoSearchResultsStep(session: GenericBotSession, data: MessageData): BotProcessorResult {
    const ctx = getContext(session);
    if (data.getInteractiveMessageType() === INTERACTIVE_MESSAGE_TYPES.BUTTON_REPLY) {
      const key = data.getInteractiveMessage()?.button_reply?.id;
      switch (key) {
        case sluggify(CTA_REPLIES.MAKE_NEW_SEARCH):
          const stepMessage = await this.messaging.send(MESSAGES.SEARCH_STORE_MESSAGE, ctx);
          session.search_data = { query: undefined, skip: 0 };

          await this.saveSession(session);

          return { currentStep: WHATSAPP_BOT_STEPS.SEARCH_STORE, intent: INTENTS.SEARCH_STORE, stepMessage };
      }
    } else {
      await this.messaging.send(MESSAGES.UNEXPECTED_RESPONSE_MESSAGE, ctx, {
        name: session.customer?.name ?? data.getName(),
      });

      return { currentStep: WHATSAPP_BOT_STEPS.STORE_SEARCH_NO_RESULTS, intent: INTENTS.SEARCH_STORE };
    }
  }

  protected async processSearchStoreStep(session: GenericBotSession, data: MessageData): BotProcessorResult {
    const ctx = getContext(session);
    const searchQuery = data.getMessageBody();

    if (data.getMessageType() === MESSAGE_TYPES.TEXT) {
      return this.searchStores(session, searchQuery);
    }

    await this.messaging.send(MESSAGES.UNEXPECTED_RESPONSE_MESSAGE, ctx, {
      name: session.customer?.name ?? data.getName(),
    });

    return { currentStep: WHATSAPP_BOT_STEPS.SEARCH_STORE, intent: INTENTS.NONE };
  }

  protected async searchStores(session: GenericBotSession, searchQuery: string) {
    const ctx = getContext(session);
    // const skip = session.search_data.skip;
    const page = session.search_data?.query === searchQuery ? session.search_data?.page + 1 : 1;
    const filter = {
      $or: searchQuery.split(' ').map((n) => ({ name: { $regex: n, $options: 'i' } })),
      'flags.uses_chowbot': true,
    };

    const paginatedStores = await this.brokerTransport
      .send<PaginateResult<Store>>(BROKER_PATTERNS.STORE.PAGINATE_STORES_LEAN, {
        filter,
        count: 10,
        page,
        select: 'name description',
      })
      .toPromise();

    const stores = paginatedStores.docs;

    if (stores?.length > 0) {
      const stepMessage = await this.messaging.send(MESSAGES.SEARCH_STORE_RESULTS_MESSAGE, ctx, {
        query: searchQuery,
        stores: stores.map(({ name, description, _id }) => ({ name, description, id: _id })),
        range: '',
        hasMoreResults: paginatedStores.hasNextPage,
        isFirstPage: paginatedStores.page === 1,
      });
      session.search_data = { query: searchQuery, page: paginatedStores.page };

      await this.saveSession(session);

      return { currentStep: WHATSAPP_BOT_STEPS.STORE_SEARCH_RESULTS, intent: INTENTS.SEARCH_STORE, stepMessage };
    } else {
      const stepData = await this.messaging.send(MESSAGES.EMPTY_SEARCH_RESULTS_MESSAGE, ctx, {
        query: searchQuery,
      });

      return { currentStep: WHATSAPP_BOT_STEPS.STORE_SEARCH_NO_RESULTS, intent: INTENTS.SEARCH_STORE, stepData };
    }

    // else if (session.search_data.skip > 0) {
    //   const stepData = await this.messaging.send(MESSAGES.SEARCH_RESULTS_NO_EXTRA_MESSAGE, ctx, {
    //     query: searchQuery,
    //   });

    //   return { currentStep: WHATSAPP_BOT_STEPS.STORE_SEARCH_NO_RESULTS, intent: INTENTS.SEARCH_STORE, stepData };
    // }
  }
}
