import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { DELIVERY_PROVIDERS } from '../../../enums/deliveries';
import { FEE_TYPES } from '../../../enums/order.enum';
import { PAYMENT_METHODS, PAYMENT_TYPES } from '../../../enums/payment.enum';
import { JOBS, QUEUES } from '../../../enums/queues.enum';
import {
  INTENTS,
  INTERACTIVE_MESSAGE_TYPES,
  WHATSAPP_BOT_EVENTS,
  WHATSAPP_BOT_STEPS,
} from '../../../enums/whatsapp.enum';
import {
  BotContext,
  BotProcessorResult,
  DeliveryFormResponse,
  GenericBotSession,
} from '../../../interfaces/whatsapp.interface';
import { VerifyCouponDto } from '../../../models/dtos/ItemDtos';
import { MESSAGE_TYPES } from '../../../repositories/whatsapp.repository';
import { formatPhoneNumber, sluggify, toCurrency } from '../../../utils';
import { getDocId, toNaira } from '../../../utils/functions';
import { PhoneCodesLean } from '../../../utils/phone-codes';
import { Errorable } from '../../../utils/types';
import { GET_APP_TIMEOUT } from '../../../utils/wabot-constants';
import { Cart } from '../../cart/cart.schema';
import { Address, AddressDocument, Delivery, DeliveryItem } from '../../deliveries/deliveries.schema';
import { Coupon } from '../../item/discount-coupons/discounts-coupons.schema';
import { Item } from '../../item/item.schema';
import { Customer } from '../../orders/customers/customer.schema';
import { CreateOrderDto } from '../../orders/dto/create-order.dto';
import { DELIVERY_METHODS, DeliveryInfo, ORDER_CHANNELS, ORDER_STATUSES, Order } from '../../orders/order.schema';
import { computeCouponDiscount } from '../../orders/utils';
import { InitiatePublicPaymentDto } from '../../payment/payment.dto';
import {
  MessageData,
  cleanString,
  formatAWSResource,
  getActualPrice,
  getContext,
  getOrderItemsText,
  getPublicUrl,
  normalizeOrderItemsData,
  stringifyMessage,
  unnormalizePhone,
} from '../utils/functions.utils';
import { CTA_REPLIES, LOG_MESSAGES, MESSAGES, MESSAGE_TEMPLATES } from '../utils/messages.utils';
import { DeliveryData } from '../utils/types';
import { SESSION_STATUSES } from '../whatsapp-bot.schema';
import { MakeOrderProcessor } from './order1.processor.service';

export class MakeOrderProcessor2 extends MakeOrderProcessor {
  // PROCESSORS
  protected async processItemsConfirmation(session: GenericBotSession, data: MessageData): BotProcessorResult {
    const ctx = getContext(session);
    if (data.getInteractiveMessageType() === INTERACTIVE_MESSAGE_TYPES.BUTTON_REPLY) {
      const key = data.getInteractiveMessage()?.button_reply?.id;
      switch (key) {
        case sluggify(CTA_REPLIES.EDIT_SELECTIONS):
          this.captureMessageEvent(WHATSAPP_BOT_EVENTS.SELECTED_EDIT_ITEMS, session);
          return await this.sendStoreFrontUrl(ctx, session, true);
      }
    }
    if (data.getMessageType() === MESSAGE_TYPES.TEXT) {
      return await this.processAutoSelectionStep(session, data, true);
    }
  }

  protected async processDeliveryMethodStep(session: GenericBotSession, data: MessageData): BotProcessorResult {
    const ctx = getContext(session);
    if (data.getInteractiveMessageType() === INTERACTIVE_MESSAGE_TYPES.BUTTON_REPLY) {
      const key = data.getInteractiveMessage()?.button_reply?.id;
      if (session.order?.invalid_items?.length > 0) return;
      switch (key) {
        case sluggify(CTA_REPLIES.DELIVERY):
          this.captureMessageEvent(WHATSAPP_BOT_EVENTS.SELECTED_DELIVERY, session);
          return await this.handleSelectedDelivery(ctx, session);
        case sluggify(CTA_REPLIES.PICKUP):
          this.captureMessageEvent(WHATSAPP_BOT_EVENTS.SELECTED_PICKUP, session);
          return await this.sendPickupAddress(ctx, session);
      }
    }
  }

  protected async processDeliveryFormStep(session: GenericBotSession, data: MessageData): BotProcessorResult {
    const ctx = getContext(session);
    if (data.getInteractiveMessageType() === INTERACTIVE_MESSAGE_TYPES.NFM_REPLY) {
      const result = await this.handleDeliveryForm(ctx, session, data);
      if (result) return result;
    }
  }

  protected async processPickupAddressStep(session: GenericBotSession, data: MessageData): BotProcessorResult {
    const ctx = getContext(session);
    if (data.getInteractiveMessageType() === INTERACTIVE_MESSAGE_TYPES.BUTTON_REPLY) {
      const key = data.getInteractiveMessage()?.button_reply?.id;
      switch (key) {
        case sluggify(CTA_REPLIES.ADD_COUPON):
          return await this.handleAddCoupon(ctx, session, true);
        case sluggify(CTA_REPLIES.REMOVE_COUPON):
          return await this.handleRemoveCoupon(ctx, session);
        case sluggify(CTA_REPLIES.ADD_ORDER_NOTES):
          return await this.handleAddOrderNotes(ctx, session);
        case sluggify(CTA_REPLIES.CHANGE_ORDER_NOTES):
          return await this.handleAddOrderNotes(ctx, session);
        case sluggify(CTA_REPLIES.PROCEED_TO_PAYMENT):
          this.captureMessageEvent(WHATSAPP_BOT_EVENTS.SELECTED_PROCEED_TO_PAYMENT, session);
          const order = await this.createOrder(ctx, session, DELIVERY_METHODS.PICKUP);
          if (order === null) return {};
          return await this.sendPaymentMessage(ctx, session, order);
      }
    }
  }

  protected async processDeliveryInfoDetailsStep(session: GenericBotSession, data: MessageData): BotProcessorResult {
    const ctx = getContext(session);
    if (data.getInteractiveMessageType() === INTERACTIVE_MESSAGE_TYPES.BUTTON_REPLY) {
      const key = data.getInteractiveMessage()?.button_reply?.id;
      switch (key) {
        case sluggify(CTA_REPLIES.EDIT_INFO):
          this.captureMessageEvent(WHATSAPP_BOT_EVENTS.SELECTED_EDIT_DELIVERY_INFO, session);
          return await this.sendDeliveryInfoForm(ctx, session, true);
        case sluggify(CTA_REPLIES.ADD_COUPON):
          return await this.handleAddCoupon(ctx, session, true);
        case sluggify(CTA_REPLIES.REMOVE_COUPON):
          return await this.handleRemoveCoupon(ctx, session);
        case sluggify(CTA_REPLIES.PROCEED_TO_PAYMENT):
          const order = await this.createOrder(ctx, session);
          if (order === null) return {};
          return await this.sendPaymentMessage(ctx, session, order);
      }
    }
  }

  protected async processPreviousOrderSelectStep(session: GenericBotSession, data: MessageData): BotProcessorResult {
    const ctx = getContext(session);
    const listReply = data.getInteractiveMessage()?.list_reply;
    if (listReply) {
      const order = await this.brokerTransport
        .send<Order>(BROKER_PATTERNS.ORDER.GET_ORDER, { _id: listReply.id })
        .toPromise();

      const invalidItems = await this.validateOrderItems(order.items);
      const cart = await this.brokerTransport
        .send<Cart>(BROKER_PATTERNS.CART.CREATE_CART, {
          items: order.items.map(({ item_id, quantity }) => ({ item_id, quantity })), //Todo: We're ignoring variants here
          store: session.store.id,
        })
        .toPromise();

      session.order = { cart_id: cart?._id ?? cart?.id };
      session.order.invalid_items = invalidItems;

      if (order.delivery_info) {
        const { delivery_address, delivery_area, name, phone, user_provided_address } = order.delivery_info;
        session.order.delivery_info = {
          delivery_address,
          delivery_area: getDocId(delivery_area),
          name,
          phone,
          delivery_notes: order?.order_notes,
          user_provided_address,
        };
      }
      session.order.order_notes = order?.order_notes;
      await this.saveSession(session);
      const messageProps = await this.getItemsAndDeliveryMessageProps(ctx, session, cart, order.delivery_info);
      const stepMessage = await this.messaging.send(MESSAGES.ITEMS_AND_DELIVERY_MESSAGE, ctx, messageProps);

      this.captureMessageEvent(
        WHATSAPP_BOT_EVENTS.SELECTED_PREVIOUS_ORDER,
        session,
        true,
        `Order ${getDocId(order)} selected with items: ${messageProps.items}`,
      );

      if (messageProps.hasInvalidItems === false) {
        this.captureMessageEvent(WHATSAPP_BOT_EVENTS.RECIEVED_CONFIRM_SELECTION_REQUEST, session, false);
        if (messageProps.sendDeliveryMethodMessage) {
          this.captureMessageEvent(WHATSAPP_BOT_EVENTS.RECEIVED_DELIVERY_METHOD_REQUEST, session, false);
        } else if (messageProps.previousDeliveryInfoMessageProps) {
          this.captureMessageEvent(WHATSAPP_BOT_EVENTS.RECIEVED_CONFIRM_DELIVERY_INFO_REQUEST, session, false);
        } else if (messageProps.deliveryFormMessageProps) {
          this.captureMessageEvent(WHATSAPP_BOT_EVENTS.RECEIVED_DELIVERY_INFO_FORM, session, false);
        } else if (messageProps.pickUpMessageProps) {
          this.captureMessageEvent(WHATSAPP_BOT_EVENTS.RECIEVED_PICKUP_ADDRESS, session, false);
        }
      }

      return {
        currentStep: WHATSAPP_BOT_STEPS.ITEMS_CONFIRMATION,
        secondarySteps: [
          WHATSAPP_BOT_STEPS.DELIVERY_METHOD_SELECTION,
          WHATSAPP_BOT_STEPS.DELIVERY_INFO_FORM,
          WHATSAPP_BOT_STEPS.PICKUP_ADDRESS,
          WHATSAPP_BOT_STEPS.DELIVERY_INFO_CONFIRMATION,
        ],
        intent: INTENTS.REPEAT_ORDER,
        stepMessage,
      };
    }
    await this.handleUnexpectedResponse(ctx, session);
  }

  protected async processPaymentMethodStep(session: GenericBotSession, data: MessageData): BotProcessorResult {
    const ctx = getContext(session);
    if (data.getInteractiveMessageType() === INTERACTIVE_MESSAGE_TYPES.BUTTON_REPLY) {
      const key = data.getInteractiveMessage()?.button_reply?.id;
      switch (key) {
        case sluggify(CTA_REPLIES.MADE_TRANSFER):
          await this.messaging.send(MESSAGES.TRANSFER_MADE, ctx, {});
          return {};
      }
    }
    await this.handleUnexpectedResponse(getContext(session), session);
  }

  protected async processOrderCancelledStep(session: GenericBotSession, data: MessageData): BotProcessorResult {
    const ctx = getContext(session);
    if (data.getInteractiveMessageType() === INTERACTIVE_MESSAGE_TYPES.BUTTON_REPLY) {
      const key = data.getInteractiveMessage()?.button_reply?.id;
      switch (key) {
        case sluggify(CTA_REPLIES.EDIT_ORDER):
          const hasDeliveryInfo = session.store.deliveries_enabled && Boolean(session.order.delivery_info);
          if (hasDeliveryInfo) {
            const stepMessage = await this.messaging.send(MESSAGES.EDIT_ORDER, ctx, {});
            return { currentStep: WHATSAPP_BOT_STEPS.EDIT_ORDER, intent: INTENTS.PLACE_ORDER, stepMessage };
          }
          this.captureMessageEvent(WHATSAPP_BOT_EVENTS.SELECTED_EDIT_ITEMS, session);
          return await this.sendStoreFrontUrl(ctx, session, true);
        case sluggify(CTA_REPLIES.RETRY_CONFIRMATION):
          const order = await this.createOrder(ctx, session, session.order.delivery_method as any);
          return await this.sendPaymentMessage(ctx, session, order);
      }
    }

    await this.handleUnexpectedResponse(getContext(session), session);
    return;
  }

  protected async processEditOrderStep(session: GenericBotSession, data: MessageData): BotProcessorResult {
    const ctx = getContext(session);
    if (data.getInteractiveMessageType() === INTERACTIVE_MESSAGE_TYPES.BUTTON_REPLY) {
      const key = data.getInteractiveMessage()?.button_reply?.id;
      switch (key) {
        case sluggify(CTA_REPLIES.EDIT_INFO):
          this.captureMessageEvent(WHATSAPP_BOT_EVENTS.SELECTED_EDIT_DELIVERY_INFO, session);
          return await this.sendDeliveryInfoForm(ctx, session, true);
        case sluggify(CTA_REPLIES.EDIT_ITEMS):
          this.captureMessageEvent(WHATSAPP_BOT_EVENTS.SELECTED_EDIT_ITEMS, session);
          return await this.sendStoreFrontUrl(ctx, session, true);
      }
    }
    return;
  }

  protected async processRequestLocationStep(session: GenericBotSession, data: MessageData): BotProcessorResult {
    const ctx = getContext(session);
    const cachedDeliveryInfo = session.order.delivery_info;

    if (data.getMessageType() === MESSAGE_TYPES.LOCATION) {
      const location = data.getLocationDetails();

      const deliveryInfo = {
        longitude: location.longitude,
        latitude: location.latitude,
        delivery_address: location.name,
        user_provided_address: undefined,
        delivery_area: cachedDeliveryInfo.delivery_area,
        name: cachedDeliveryInfo?.name,
        phone: cachedDeliveryInfo?.phone,
        delivery_notes: cachedDeliveryInfo?.delivery_notes,
      };

      const googleMapsLocation = await this.googleMapsRepository.getAddressFromLatLong(
        location.latitude,
        location.longitude,
      );

      if (!googleMapsLocation || googleMapsLocation.error) {
        await this.messaging.send(MESSAGES.REQUEST_LOCATION, ctx, {
          isRedo: true,
        });
        return {};
      }

      deliveryInfo.delivery_address = googleMapsLocation.data;
      if (location.name !== googleMapsLocation.data) {
        deliveryInfo.user_provided_address = location.name;
      }

      session.order.delivery_info = { ...deliveryInfo };

      await this.handleAutoDelivery(session, deliveryInfo);

      const messageProps = await this.getDeliveryInfoMessageProps(ctx, session, deliveryInfo);
      const stepMessage = await this.messaging.send(MESSAGES.DELIVERY_DETAILS_MESSAGE, ctx, messageProps);

      this.captureMessageEvent(
        WHATSAPP_BOT_EVENTS.FILLED_DELIVERY_INFO_FORM,
        session,
        true,
        LOG_MESSAGES[MESSAGES.DELIVERY_DETAILS_MESSAGE](stepMessage.data),
      ); //Todo update this

      this.captureMessageEvent(WHATSAPP_BOT_EVENTS.RECIEVED_CONFIRM_DELIVERY_INFO_REQUEST, session, false);
      return {
        currentStep: WHATSAPP_BOT_STEPS.DELIVERY_INFO_CONFIRMATION,
        intent: INTENTS.PLACE_ORDER,
        stepMessage,
      };
    }
    return;
  }

  protected async processAddCouponStep(session: GenericBotSession, data: MessageData): BotProcessorResult {
    const ctx = getContext(session);

    if (data.getMessageType() === MESSAGE_TYPES.TEXT) {
      const couponCode = data.getMessageBody();
      const cart = await this.brokerTransport
        .send(BROKER_PATTERNS.CART.GET_CART, {
          id: session.order.cart_id,
          data: {
            _id: session.order.cart_id,
          },
        })
        .toPromise();

      const total = cart.items.reduce((p, item) => {
        return item.quantity * getActualPrice(item?.variant ? item?.variant : item?.object ?? item) + p;
      }, 0);

      const dto: VerifyCouponDto = {
        coupon_code: couponCode,
        currency: ctx.currency,
        store: session.store.id,
        itemsAmount: total,
        customer: session.customer.id,
      };

      try {
        const coupon = await this.brokerTransport.send<Coupon>(BROKER_PATTERNS.ITEM.VERIFY_COUPON, dto).toPromise();
        const couponAmount = computeCouponDiscount(total, coupon, (number) => number);

        session.order = { ...session.order, coupon_code: couponCode };

        await this.messaging.send(MESSAGES.COUPON_APPLIED, ctx, {
          couponCode,
          couponAmount: toCurrency(Math.abs(couponAmount), ctx.currency),
        });
        return await this.useSessionBookmark(session, ctx, { couponValidated: true });
      } catch (e) {
        const stepMessage = await this.messaging.send(MESSAGES.COUPON_VALIDATION_FAILED, ctx, { message: e?.message });
        return {
          currentStep: WHATSAPP_BOT_STEPS.ENTER_COUPON,
          intent: INTENTS.PLACE_ORDER,
          stepMessage,
        };
      }
    } else if (data.getInteractiveMessageType() === INTERACTIVE_MESSAGE_TYPES.BUTTON_REPLY) {
      const key = data.getInteractiveMessage()?.button_reply?.id;
      switch (key) {
        case sluggify(CTA_REPLIES.PROCEED_WITHOUT_COUPON):
          return await this.useSessionBookmark(session, ctx, { couponValidated: false });
      }
    }
  }

  // CLASS METHODS
  protected async sendPaymentMessage(ctx: BotContext, session: GenericBotSession, order: Order): BotProcessorResult {
    const messageProps = await this.getPaymentMessageProps(ctx, session, order);

    if (messageProps.orderConfirmationPendingMessageProps) {
      const stepMessage = await this.messaging.send(
        MESSAGES.ORDER_CONFIRMATION_PENDING_MESSAGE,
        ctx,
        messageProps.orderConfirmationPendingMessageProps,
      );
      this.captureMessageEvent(WHATSAPP_BOT_EVENTS.RECIEVED_ORDER_CONFIRMATION_REQUEST, session, false);
      await this.updateSessionStatus(session, SESSION_STATUSES.CONFIRMATION_PENDING);
      return {
        currentStep: WHATSAPP_BOT_STEPS.PAYMENT_REQUEST,
        intent: INTENTS.PLACE_ORDER,
        stepMessage,
      };
    }

    this.handlePaymentReminder(session.key);
    const stepMessage = await this.messaging.send(
      MESSAGES.PAYMENT_DETAILS_MESSAGE,
      ctx,
      messageProps.bankDetailsMessageProps,
    );
    this.captureMessageEvent(WHATSAPP_BOT_EVENTS.RECIEVED_PAYMENT_DETAILS, session, false);
    return {
      currentStep: WHATSAPP_BOT_STEPS.PAYMENT_DETAILS,
      intent: INTENTS.PLACE_ORDER,
      stepMessage,
    };
  }

  protected async sendPickupAddress(ctx: BotContext, session: GenericBotSession): BotProcessorResult {
    const stepMessage = await this.messaging.send(MESSAGES.PICKUP_ADDRESS_MESSAGE, ctx, {
      name: session.store.name,
      phone: session.store.contact_phone,
      address: session.store.pickup_address,
      state: session.store.state,
      couponValidated: !!session?.order?.coupon_code,
      hasOrderNotes: !!session?.order?.order_notes,
      collectOrderNote: session.store?.collect_order_notes ?? true,
    });

    return {
      intent: INTENTS.PLACE_ORDER,
      currentStep: WHATSAPP_BOT_STEPS.PICKUP_ADDRESS,
      stepMessage,
    };
  }

  private async handleAutoDelivery(session: GenericBotSession, deliveryInfo: DeliveryData): Errorable<any> {
    const delvInfo = session.order.delivery_info;

    if (session.store.auto_delivery) {
      try {
        const res = await this.brokerTransport
          .send<any>(BROKER_PATTERNS.DELVERIES.CREATE_AUTO_DELIVERY, {
            store: session.store.id,
            customer: session.customer.id,
            data: {
              phone: deliveryInfo.phone,
              delivery_address: deliveryInfo.delivery_address,
              name: deliveryInfo.name,
              cartId: session.order.cart_id,
              note: deliveryInfo.delivery_notes,
            },
          })
          .toPromise();

        if (res) {
          const { rates, delivery_id } = res;
          delvInfo.auto_delivery_fee = rates.couriers[0]?.total;
          delvInfo.delivery_id = delivery_id;
          await this.saveSession(session);
          // console.log('<======================', res,'', '=======================>');
        }
      } catch (e) {
        this.logger.error(e);
        return { error: e };
      }
    }
  }

  private async handleDeliveryForm(ctx: BotContext, session: GenericBotSession, data: MessageData): BotProcessorResult {
    const json = data.getInteractiveMessage()?.nfm_reply?.response_json;

    const form = JSON.parse(json) as DeliveryFormResponse;

    const { order } = session;

    if (await this.jwtService.verifyAsync(form.flow_token)) {
      // create delivery info
      const deliveryInfo = {
        delivery_address: cleanString(form.delivery_address),
        user_provided_address: undefined,
        delivery_area: form.delivery_area,
        name: form?.full_name,
        phone: form?.phone_number,
        delivery_notes: cleanString(form?.delivery_notes),
      };

      this.captureMessageEvent(
        WHATSAPP_BOT_EVENTS.FILLED_DELIVERY_INFO_FORM,
        session,
        true,
        LOG_MESSAGES[MESSAGES.DELIVERY_DETAILS_MESSAGE]({
          name: deliveryInfo.name,
          phone: deliveryInfo.phone,
          delivery_area: deliveryInfo.delivery_area,
          address: deliveryInfo.delivery_address,
          email: '',
        }),
      );

      if (session.store.require_geolocation) {
        const googleMapsLookup = await this.googleMapsRepository.lookupAddress(form.delivery_address);
        let validatedAddress: Address = null;
        if (googleMapsLookup?.data) {
          const addressData = {
            name: form.full_name,
            phone: form.phone_number,
            address: googleMapsLookup.data,
            store: session.store.id,
            customer: session.customer.id,
          };
          //wrap this in a try catch incase anything fails so it could request for location input
          try {
            validatedAddress = await this.brokerTransport
              .send<Address>(BROKER_PATTERNS.DELVERIES.CREATE_ADDRESS, {
                store: session?.store?.id,
                data: addressData,
                storeName: session.store.name,
              })
              .toPromise();
          } catch (e) {
            console.log("COULDN'T VALIDATE ADDRESS", addressData);
          }
        }

        if (!googleMapsLookup || googleMapsLookup.error || !validatedAddress) {
          session.order.delivery_info = { ...deliveryInfo }; // save info
          await this.saveSession(session);

          const stepMessage = await this.messaging.send(MESSAGES.REQUEST_LOCATION, ctx, {
            isRedo: true,
          });

          return {
            intent: INTENTS.PLACE_ORDER,
            currentStep: WHATSAPP_BOT_STEPS.REQUEST_LOCATION,
            stepMessage,
          };
        }

        deliveryInfo.user_provided_address = form.delivery_address;
        deliveryInfo.delivery_address = googleMapsLookup.data;
        (deliveryInfo as GenericBotSession['order']['delivery_info']).address_id = getDocId(validatedAddress);
      }

      session.order.delivery_info = { ...deliveryInfo }; // save info
      await this.saveSession(session);

      let dbOrder: Order;

      // update order if it exists
      if (order?.order_id) {
        dbOrder = await this.brokerTransport
          .send<Order>(BROKER_PATTERNS.ORDER.UPDATE_ORDER, {
            filter: { _id: order.order_id },
            payload: {
              delivery_info: {
                area: deliveryInfo.delivery_area === 'NONE' ? undefined : deliveryInfo.delivery_area,
                delivery_address: deliveryInfo.delivery_address,
                name: deliveryInfo?.name,
                phone: deliveryInfo?.phone,
              },
            },
          })
          .toPromise();
      } else {
        // save delivery info name as customer name if not verified
        if (!session.customer.name_verified) {
          await this.brokerTransport
            .send<Customer>(BROKER_PATTERNS.ORDER.UPDATE_CUSTOMER, {
              filter: { _id: session.customer.id, store: session.store.id },
              payload: { name_verified: true, name: deliveryInfo.name },
            })
            .toPromise();
          session.customer = { ...session.customer, name: deliveryInfo.name, name_verified: true };
        }
      }

      await this.handleAutoDelivery(session, deliveryInfo);
      const stepMessage = await this.messaging.send(
        MESSAGES.DELIVERY_DETAILS_MESSAGE,
        ctx,
        await this.getDeliveryInfoMessageProps(ctx, session, deliveryInfo),
      );

      this.captureMessageEvent(WHATSAPP_BOT_EVENTS.RECIEVED_CONFIRM_DELIVERY_INFO_REQUEST, session, false);
      return {
        currentStep: WHATSAPP_BOT_STEPS.DELIVERY_INFO_CONFIRMATION,
        intent: INTENTS.PLACE_ORDER,
        stepMessage,
      };
    }
  }

  protected async processOrderConfirmationStep(session: GenericBotSession, order: Order) {
    await this.sellerProcessor.removeOrderJobForGenericSession(session, false);

    this.handlePaymentReminder(session.key);
    this.captureMessageEvent(WHATSAPP_BOT_EVENTS.ORDER_CONFIRMED, session);
    const ctx = getContext(session);

    const bankDetailsMessageProps = await this.getBankPaymentDetails(ctx, session, order);
    const stepMessage = await this.messaging.send(MESSAGES.PAYMENT_DETAILS_MESSAGE, ctx, bankDetailsMessageProps);

    this.captureMessageEvent(WHATSAPP_BOT_EVENTS.RECIEVED_PAYMENT_DETAILS, session);
    session.step = WHATSAPP_BOT_STEPS.PAYMENT_DETAILS;

    await this.pushHistory(session, [
      {
        step: session.step,
        timestamp: new Date().toISOString(),
        step_message: stepMessage,
      },
    ]);
  }

  protected async processOrderRejectionStep(session: GenericBotSession, reason: string) {
    this.captureMessageEvent(WHATSAPP_BOT_EVENTS.ORDER_REJECTED, session, false);
    const ctx = getContext(session);

    await this.sellerProcessor.removeOrderJobForGenericSession(session, false);

    if (session.metadata?.order_timedout) {
      await this.updateSessionStatus(session, SESSION_STATUSES.UNCONFIRMED, true);
      await this.createDbSession(session, session.store.id, session.store?.storeCode);
    } else if (session.metadata?.order_rejected) {
      await this.updateSessionStatus(session, SESSION_STATUSES.REJECTED);
    }

    await this.messaging.send(MESSAGES.ORDER_CANCELLED, ctx, {
      storeName: session.store.name,
      reason,
      ...session.metadata,
    });

    // send message to store
    // await this.messaging.send(
    //   MESSAGES.ORDER_STATUS_UPDATE_MESSAGE,
    //   { botPhoneId: ctx.botPhoneId, destinationPhone: session.store.contact_phone },
    //   {
    //     previouslyUpdated: false,
    //     orderId: session.order.order_id,
    //     status: ORDER_STATUSES.CANCELLED,
    //     reason,
    //     url: getPublicUrl(`/o/${session.order.order_id}`),
    //   },
    // );

    session.step = WHATSAPP_BOT_STEPS.ORDER_CANCELLED;
    session.order.is_cancelled = true;
    await this.saveSession(session);

    this.captureMessageEvent(WHATSAPP_BOT_EVENTS.RECEIEVED_ORDER_REJECTED, session);

    // await (await this.waBotQueue.getJob(session.timeout_job))?.remove();
    // this.cacheManager.del(session.key);
  }

  //Todo: Review this
  protected async processPaymentSuccessfulStep(session: GenericBotSession, receiptId: string) {
    this.captureMessageEvent(WHATSAPP_BOT_EVENTS.MADE_PAYMENT, session);

    const ctx = getContext(session);

    await this.messaging.send(MESSAGES.ORDER_PAYMENT_SUCCESSFUL, ctx, {
      storeName: session.store.name,
      phone: session.store.contact_phone,
      receiptUrl: getPublicUrl(`/receipts/${receiptId}`),
      deliveryTimeline: session.store.average_delivery_timeline,
      customMessage:
        session.store.custom_order_success_message?.trim() === ''
          ? undefined
          : session.store.custom_order_success_message,
    });

    // SEND NOTIFICATION TO SELLER
    const order = await this.brokerTransport
      .send<Order>(BROKER_PATTERNS.ORDER.GET_ORDER_LEAN, {
        _id: session.order.order_id,
      })
      .toPromise();
    const orderItems = normalizeOrderItemsData(order.items);
    const items = getOrderItemsText(orderItems, ctx.currency, true);

    const sellerPhone = session.store.contact_phone;
    const deliveryInfo = order.delivery_info;
    const isDelivery = order.delivery_method === DELIVERY_METHODS.DELIVERY && Boolean(deliveryInfo);

    await this.messaging.send(
      MESSAGES.PAYMENT_RECEIVED,
      { botPhoneId: ctx.botPhoneId, destinationPhone: sellerPhone },
      {
        url: getPublicUrl(`/o/${session.order.order_id}`),
        isDelivery,
        items,
        orderId: session.order.order_id,
        totalAmount: toCurrency(order.total_amount, ctx.currency),
        deliveryInfo: isDelivery
          ? {
              name: deliveryInfo?.name,
              phone: deliveryInfo?.phone,
              address: deliveryInfo?.delivery_address,
              delivery_area: session.store.delivery_areas.find(
                (a) => getDocId(a) === getDocId(deliveryInfo?.delivery_area),
              )?.title,
            }
          : {
              name: order?.customer?.name,
              phone: formatPhoneNumber(order?.customer?.phone),
            },
        note: order?.order_notes,
      },
    );

    await this.waBotSessionModel.updateOne(
      { _id: session.db_session, store: session.store.id },
      {
        customer: session.customer.id,
        order: session.order.order_id,
        store: session.store.id,
        status: SESSION_STATUSES.COMPLETED,
      },
    );

    this.captureMessageEvent(WHATSAPP_BOT_EVENTS.RECIEVED_PAYMENT_CONFIRMATION, session, false);

    await this.removeSession(session);
    return {};
  }

  protected async processAddOrderNotes(session: GenericBotSession, data: MessageData): BotProcessorResult {
    const ctx = getContext(session);

    if (data.getMessageType() === MESSAGE_TYPES.TEXT) {
      const orderNotes = data.getMessageBody();
      session.order.order_notes = orderNotes;
      await this.saveSession(session);
      await this.messaging.send(MESSAGES.ORDER_NOTES_ADDED, ctx, { orderNotes });
      return this.sendPickupAddress(ctx, session);
    }
  }

  // CLASS METHODS
  protected async handleAddOrderNotes(ctx: BotContext, session: GenericBotSession) {
    const stepMessage = await this.messaging.send(MESSAGES.ORDER_NOTES, ctx, {});
    return {
      currentStep: WHATSAPP_BOT_STEPS.ADD_ORDER_NOTES,
      intent: INTENTS.PLACE_ORDER,
      stepMessage,
    };
  }

  protected async handleAddCoupon(ctx: BotContext, session: GenericBotSession, saveLastMsgData = false) {
    if (saveLastMsgData) {
      await this.bookmarkSessionStep(session);
    }
    this.captureMessageEvent(WHATSAPP_BOT_EVENTS.SELECTED_ADD_COUPON, session);
    const stepMessage = await this.messaging.send(MESSAGES.ENTER_COUPON_CODE, ctx, {});
    return {
      currentStep: WHATSAPP_BOT_STEPS.ENTER_COUPON,
      intent: INTENTS.PLACE_ORDER,
      stepMessage,
    };
  }

  protected async handleRemoveCoupon(ctx: BotContext, session: GenericBotSession) {
    this.captureMessageEvent(WHATSAPP_BOT_EVENTS.SELECTED_REMOVE_COUPON, session);
    delete session.order.coupon_code;
    await this.saveSession(session);
    await this.messaging.send(MESSAGES.COUPON_REMOVED, ctx, {});
    return await this.useSessionBookmark(session, ctx, { couponValidated: false });
  }

  protected async createOrder(ctx: BotContext, session: GenericBotSession, deliveryMethod = DELIVERY_METHODS.DELIVERY) {
    const {
      customer,
      store,
      order: { delivery_info, cart_id },
      from,
      to,
    } = session;

    const cart = await this.brokerTransport
      .send<Cart>(BROKER_PATTERNS.CART.GET_CART, { id: cart_id })
      .toPromise();

    const deliveryFee = session?.order?.delivery_info?.auto_delivery_fee
      ? {
          amount: session.order.delivery_info.auto_delivery_fee,
          type: FEE_TYPES.DELIVERY,
          label: 'Delivery Fee',
        }
      : null;

    const paymentFee = session?.order?.payment_fee
      ? {
          amount: session.order.payment_fee,
          type: FEE_TYPES.PAYMENT,
          label: 'Payment Fee',
        }
      : null;

    const fees = [deliveryFee, paymentFee].filter(Boolean);

    const data: CreateOrderDto = {
      customer: {
        phone: customer.phone_number,
        name: customer.name,
        store: store.id,
      },
      delivery_info:
        deliveryMethod === DELIVERY_METHODS.DELIVERY
          ? {
              area: delivery_info.delivery_area === 'NONE' ? undefined : delivery_info.delivery_area,
              delivery_address: delivery_info.delivery_address,
              name: delivery_info.name,
              phone: delivery_info.phone,
              user_provided_address: delivery_info.user_provided_address,
            }
          : undefined,
      delivery_method: deliveryMethod,
      store: store.id,
      channel: ORDER_CHANNELS.CHATBOT,
      items: cart.items.map((i) => ({ ...i, snapshot: i.object })),
      order_notes: session?.order?.order_notes ?? delivery_info?.delivery_notes ?? '',
      currency: ctx.currency,
      delivery: delivery_info?.delivery_id,
      validated_delivery_address: delivery_info?.address_id,
      coupon: session.order?.coupon_code,
      fees,
      meta: {
        chowbot: {
          session_id: this.removeSessionPrefix(session.key),
          db_session_id: session.db_session,
        },
      },
    };

    try {
      const newOrder = await this.brokerTransport.send<Order>(BROKER_PATTERNS.ORDER.CREATE_ORDER, data).toPromise();

      if (delivery_info?.delivery_id) {
        await this.brokerTransport
          .send(BROKER_PATTERNS.DELVERIES.UPDATE_DELIVERY, {
            filter: { _id: delivery_info?.delivery_id },
            update: { order: getDocId(newOrder) },
          })
          .toPromise();
      }

      session.order.delivery_method = deliveryMethod;
      session.order.order_id = newOrder.id;
      session.order.invoice_id = newOrder.invoice;
      session.order.is_cancelled = false;

      delete session.metadata;
      delete session.order.invalid_items;

      await this.saveSession(session);
      return newOrder;
    } catch (e) {
      if (e.outOfStockError === true) {
        const invalidItems = await this.validateOrderItems(cart.items);
        session.order.invalid_items = invalidItems;

        const updatedCartItems = cart.items.filter(
          (i) =>
            !invalidItems.find((ii) => (ii.id === i.item_id && ii.variant_id ? ii.variant_id === i.variant_id : true)),
        );
        const updatedCart = await this.brokerTransport
          .send<Cart>(BROKER_PATTERNS.CART.UPDATE_CART, {
            id: session.order.cart_id,
            data: {
              store: session?.store?.id,
              items: updatedCartItems,
            },
          })
          .toPromise();

        const messageProps = await this.getItemsAndDeliveryMessageProps(ctx, session, updatedCart);
        const stepMessage = await this.messaging.send(MESSAGES.ITEMS_AND_DELIVERY_MESSAGE, ctx, messageProps);

        session.step = WHATSAPP_BOT_STEPS.ITEMS_CONFIRMATION;
        session.secondary_steps = [
          WHATSAPP_BOT_STEPS.DELIVERY_METHOD_SELECTION,
          WHATSAPP_BOT_STEPS.DELIVERY_INFO_FORM,
          WHATSAPP_BOT_STEPS.PICKUP_ADDRESS,
        ];
        session.history.push({
          step: session.step,
          timestamp: new Date().toISOString(),
          secondary_steps: session.secondary_steps,
          step_message: stepMessage,
        });

        delete session.order.invalid_items;
        await this.saveSession(session);

        return null;
      }
      throw e;
    }
  }

  protected async handleSelectedDelivery(ctx: BotContext, session: GenericBotSession) {
    if (session.intent === INTENTS.REPEAT_ORDER) {
      const deliveryInfo = await this.getPreviousDeliveryInfo(session);

      if (deliveryInfo) {
        await this.handleAutoDelivery(session, deliveryInfo);
        /* if (result?.error) {
        await this.messaging.send(MESSAGES.REQUEST_LOCATION, ctx, {
          isRedo: true,
        });
        return {};
        // return this.sendDeliveryInfoForm(ctx, session, true);
      } */

        const stepMessage = await this.messaging.send(
          MESSAGES.DELIVERY_DETAILS_MESSAGE,
          ctx,
          await this.getDeliveryInfoMessageProps(ctx, session, deliveryInfo),
        );

        this.captureMessageEvent(WHATSAPP_BOT_EVENTS.RECIEVED_CONFIRM_DELIVERY_INFO_REQUEST, session, false);
        return {
          currentStep: WHATSAPP_BOT_STEPS.DELIVERY_INFO_CONFIRMATION,
          intent: INTENTS.REPEAT_ORDER,
          stepMessage,
        };
      }
    }

    return this.sendDeliveryInfoForm(ctx, session);
  }

  protected async sendDeliveryInfoForm(ctx: BotContext, session: GenericBotSession, isEdit = false) {
    const deliveryInfo = await this.getPreviousDeliveryInfo(session);
    const deliveryFormMessageProps = await this.getDeliveryFormProps(session, isEdit, deliveryInfo);
    const stepMessage = await this.messaging.send(MESSAGES.ENTER_DELIVERY_INFO_MESSAGE, ctx, deliveryFormMessageProps);

    this.captureMessageEvent(WHATSAPP_BOT_EVENTS.RECEIVED_DELIVERY_INFO_FORM, session, false);

    return {
      currentStep: WHATSAPP_BOT_STEPS.DELIVERY_INFO_FORM,
      intent: INTENTS.PLACE_ORDER,
      stepMessage,
    };
  }

  protected async validateOrderItems(items: any) {
    const invalidItems: {
      id: string;
      variant_id: string;
      name: string;
      status: 'deleted' | 'unavailable' | 'depleted' | 'over_stock';
      quantity?: number;
      inventory?: number;
    }[] = [];

    await Promise.all(
      items.map(async (item) => {
        const dbItem = await this.brokerTransport
          .send<Item>(BROKER_PATTERNS.ITEM.GET_ITEM, {
            _id: item.item_id,
          })
          .toPromise();

        let status;

        if (dbItem.is_deleted) {
          status = 'deleted';
        } else if (!dbItem.available) {
          status = 'unavailable';
        } else if (dbItem && !dbItem.is_always_available && dbItem.quantity === 0) {
          status = 'depleted';
        } else if (dbItem && !dbItem.is_always_available && dbItem.quantity < item.quantity) {
          status = 'over_stock';
        }

        if (status) {
          invalidItems.push({
            id: item.item_id,
            name: item.snapshot?.name ?? item.object?.name,
            status,
            variant_id: item.variant_id,
            quantity: item.quantity,
            inventory: dbItem.quantity,
          });
        }
      }),
    );
    return invalidItems;
  }

  protected async getPaymentMessageProps(ctx: BotContext, session: GenericBotSession, order: Order) {
    if (session.store.confirm_order_before_payment) {
      await this.sellerProcessor.handleOrderConfirmationRequest(session);
      return {
        orderConfirmationPendingMessageProps: {
          storeName: session.store.name,
        },
        bankDetailsMessageProps: null,
      };
    }
    return { bankDetailsMessageProps: await this.getBankPaymentDetails(ctx, session, order) };
  }

  protected async getBankPaymentDetails(ctx: BotContext, session: GenericBotSession, order: Order) {
    await this.messaging.send(MESSAGES.PAYMENT_LOADING, ctx, {});
    const { totalPrice, totalQuantity } = order.items.reduce(
      (c, item) => {
        let price = (item as any).variant?.price || item.snapshot.price;
        c.totalPrice += Number(price) * Number(item.quantity);
        c.totalQuantity += Number(item.quantity);
        return c;
      },
      { totalPrice: 0, totalQuantity: 0 },
    );

    const paymentDto: InitiatePublicPaymentDto = {
      currency: order.currency,
      invoice: order?.invoice,
      payment_method_type: PAYMENT_METHODS.TRANSFER,
      type: PAYMENT_TYPES.INVOICE,
    };

    let payment: {
      currency: string;
      id: string;
      bank: {
        accountNumber: string;
        accountName: string;
        bankName: string;
        bankCode: string;
        ussdCode: any;
        amount: number;
      };
      total_amount: number;
      fee: number;
    };

    let hasBankPayment = true;

    if (paymentDto.invoice) {
      try {
        // throw '';
        payment = await this.brokerTransport
          .send(BROKER_PATTERNS.PAYMENT.CREATE_PUBLIC_PAYMENT, {
            data: paymentDto,
            metaData: {
              chowbot: { session_id: session.key, db_session_id: session.db_session },
            },
            payment_fee: session.order?.payment_fee, //in naira
          })
          .toPromise();
      } catch (e) {
        hasBankPayment = false;
      }
    }

    // await this.waBotSessionModel.updateOne(
    //   { _id: session.db_session },
    //   {
    //     payment: payment.id,
    //     status: SESSION_STATUSES.PAYMENT_PENDING,
    //   },
    // );

    const deliveryFee =
      order.delivery_method === DELIVERY_METHODS.DELIVERY
        ? session.order?.delivery_info?.auto_delivery_fee ?? order.delivery_info?.delivery_area?.fee ?? 0
        : 0;

    return {
      totalItemPrice: toCurrency(totalPrice.toString(), ctx.currency),
      discountAmount: order.discount_amount < 0 ? toCurrency(order.discount_amount, ctx.currency) : undefined,
      couponCode: session.order?.coupon_code,
      totalQuantity,
      deliveryFee: deliveryFee > 0 ? toCurrency(deliveryFee + (session?.order?.payment_fee ?? 0), ctx.currency) : null,
      totalPrice: toCurrency(hasBankPayment ? toNaira(payment?.total_amount) : order.total_amount, ctx.currency),
      processingFee: payment ? toCurrency(toNaira(payment.fee), ctx.currency) : undefined,
      accountName: payment
        ? `Account Name: *${payment?.bank.accountName}* \nBank: *${payment?.bank.bankName}*`
        : undefined,
      accountNumber: payment?.bank?.accountNumber,
      hasBankPayment,
      url: hasBankPayment
        ? undefined
        : `${process.env.CATLOG_WWW}/pay/${session?.order?.invoice_id}?chowbotPhone=${unnormalizePhone(
            session.to,
            PhoneCodesLean,
          )}&byCustomer=true`,
    };
  }

  protected async handlePaymentReminder(sessionKey: string) {
    return await this.waBotQueue.add(
      QUEUES.WHATSAPP_BOT,
      {
        type: JOBS.WABOT_PAYMENT_REMINDER,
        data: {
          sessionKey,
        },
      },
      { delay: GET_APP_TIMEOUT('payment_reminder') },
    );
  }

  public async sendPaymentReminder(sessionKey: string) {
    try {
      const session = await this.cacheManager.get<GenericBotSession>(sessionKey);
      if (session && session?.step === WHATSAPP_BOT_STEPS.PAYMENT_DETAILS) {
        const ctx = getContext(session);
        await this.messaging.send(MESSAGES.PAYMENT_REMINDER, ctx, { storeName: session.store.name });
      }
    } catch (error) {
      console.log(error);
    }
  }
}
