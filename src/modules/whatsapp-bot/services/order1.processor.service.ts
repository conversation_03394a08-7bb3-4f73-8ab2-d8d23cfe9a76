import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import {
  INTENTS,
  INTERACTIVE_MESSAGE_TYPES,
  WHATSAPP_BOT_EVENTS,
  WHATSAPP_BOT_STEPS,
} from '../../../enums/whatsapp.enum';
import {
  BotContext,
  BotProcessorResult,
  GenericBotSession,
  GptItemSearch,
  GptItemSearchOptimized,
} from '../../../interfaces/whatsapp.interface';
import { GptResponse } from '../../../repositories/openai.respository';
import { MESSAGE_TYPES } from '../../../repositories/whatsapp.repository';
import { formatPhoneNumber, sluggify, toCurrency } from '../../../utils';
import { chowbotCustomerFee } from '../../../utils/fees';
import { getDocId } from '../../../utils/functions';
import { PROMPT_TEMPLATES } from '../../../utils/prompts';
import { NONE_ID } from '../../../utils/wabot-constants';
import { Cart } from '../../cart/cart.schema';
import { Item } from '../../item/item.schema';
import { DELIVERY_METHODS, DeliveryInfo, ORDER_CHANNELS, Order } from '../../orders/order.schema';
import {
  MessageData,
  generateStringFromVariantValues,
  getActualPrice,
  getContext,
  getInvalidItemsText,
  getOrderItemsText,
  getProductNameText,
  getTotalCartPrice,
} from '../utils/functions.utils';
import { CTA_REPLIES, MESSAGES } from '../utils/messages.utils';
import { DeliveryData } from '../utils/types';
import { SearchProcessor } from './search.processor.service';

export class MakeOrderProcessor extends SearchProcessor {
  // PROCESSORS
  protected async processWelcomeStep(session: GenericBotSession, data: MessageData): BotProcessorResult {
    const ctx = getContext(session);
    if (data.getInteractiveMessageType() === INTERACTIVE_MESSAGE_TYPES.BUTTON_REPLY) {
      const key = data.getInteractiveMessage()?.button_reply?.id;
      switch (key) {
        case sluggify(CTA_REPLIES.PLACE_NEW_ORDER):
          // this.captureMessageEvent(WHATSAPP_BOT_EVENTS.SELECTED_MAKE_NEW_ORDER, session);
          // if (session.store?.use_menu_ordering) return await this.sendStoreMenuAndUrl(ctx, session);
          // return await this.sendStoreFrontUrl(ctx, session);
          return await this.placeNewOrder(session);
        case sluggify(CTA_REPLIES.REPEAT_ORDER):
          this.captureMessageEvent(WHATSAPP_BOT_EVENTS.SELECTED_REPEAT_ORDER, session);

          const orders = await this.brokerTransport
            .send<Order[]>(BROKER_PATTERNS.ORDER.GET_ALL_LEAN, {
              filter: {
                store: session.store?.id,
                channel: ORDER_CHANNELS.CHATBOT,
                customer: session.customer?.id,
              },
              sort: {
                created_at: -1,
              },
              limit: 10,
            })
            .toPromise();

          if (orders?.length > 0) {
            const messageData = orders.slice(0, 10).map((order, index) => {
              return {
                id: order.id ?? order._id,
                // description: new Date(order.created_at).toDateString(),
                description:
                  new Date(order.created_at).toDateString() +
                  ' | ' +
                  (order?.delivery_method === DELIVERY_METHODS.PICKUP
                    ? 'Pickup'
                    : `Delivery: ${
                        order?.delivery_info?.user_provided_address ?? order?.delivery_info?.delivery_address
                      }`),
                name: getProductNameText(order.items),
              };
            });
            const stepMessage = await this.messaging.send(MESSAGES.SELECT_PREVIOUS_ORDER_MESSAGE, ctx, {
              orders: messageData,
            });
            this.captureMessageEvent(WHATSAPP_BOT_EVENTS.RECEIVED_PREVIOUS_ORDERS, session, false);
            return { currentStep: WHATSAPP_BOT_STEPS.ORDER_SELECTION, intent: INTENTS.REPEAT_ORDER, stepMessage };
          }
          const stepMessage = await this.messaging.send(MESSAGES.PREVIOUS_ORDERS_NOT_FOUND_MESSAGE, ctx, {
            name: session.customer.name,
          });
          return { currentStep: WHATSAPP_BOT_STEPS.ORDERS_NOT_FOUND, intent: INTENTS.REPEAT_ORDER, stepMessage };
      }
    }
  }

  public async placeNewOrder(session: GenericBotSession) {
    const ctx = getContext(session);

    this.captureMessageEvent(WHATSAPP_BOT_EVENTS.SELECTED_MAKE_NEW_ORDER, session);
    if (session.store?.use_menu_ordering) return await this.sendStoreMenuAndUrl(ctx, session);
    return await this.sendStoreFrontUrl(ctx, session);
  }

  protected async processItemSelectionStep(session: GenericBotSession, cart: Cart, isAutoSelection = false) {
    const ctx = getContext(session);

    if (session.order?.invalid_items && !isAutoSelection) {
      delete session.order.invalid_items;
      await this.saveSession(session);
    }

    const messageProps = await this.getItemsAndDeliveryMessageProps(ctx, session, cart);
    messageProps.isAutoSelection = isAutoSelection;

    this.captureMessageEvent(WHATSAPP_BOT_EVENTS.SELECTED_ITEMS, session, true, messageProps.items);
    const step_message = await this.messaging.send(MESSAGES.ITEMS_AND_DELIVERY_MESSAGE, ctx, messageProps);

    this.captureMessageEvent(WHATSAPP_BOT_EVENTS.RECIEVED_CONFIRM_SELECTION_REQUEST, session, false);
    if (messageProps.sendDeliveryMethodMessage) {
      this.captureMessageEvent(WHATSAPP_BOT_EVENTS.RECEIVED_DELIVERY_METHOD_REQUEST, session, false);
    } else if (messageProps.deliveryFormMessageProps) {
      this.captureMessageEvent(WHATSAPP_BOT_EVENTS.RECEIVED_DELIVERY_INFO_FORM, session, false);
    } else if (messageProps.pickUpMessageProps) {
      this.captureMessageEvent(WHATSAPP_BOT_EVENTS.RECIEVED_PICKUP_ADDRESS, session, false);
    }

    if (session.step !== WHATSAPP_BOT_STEPS.ITEMS_CONFIRMATION) {
      session.order = { cart_id: cart._id ?? cart.id, ...session.order };
      session.step = WHATSAPP_BOT_STEPS.ITEMS_CONFIRMATION;
      session.secondary_steps = [
        WHATSAPP_BOT_STEPS.DELIVERY_METHOD_SELECTION,
        WHATSAPP_BOT_STEPS.DELIVERY_INFO_FORM,
        WHATSAPP_BOT_STEPS.PICKUP_ADDRESS,
      ];

      await this.pushHistory(session, [
        {
          step: session.step,
          secondary_steps: session.secondary_steps,
          timestamp: new Date().toISOString(),
          step_message,
        },
      ]);
    }

    if (isAutoSelection) {
      delete session.order.invalid_items;
      await this.saveSession(session);
    }
  }

  protected async processAutoSelectionStep(
    session: GenericBotSession,
    data: MessageData,
    isEdit = false,
  ): BotProcessorResult {
    const ctx = getContext(session);
    if (data.getMessageType() === MESSAGE_TYPES.TEXT) {
      const text = data.getMessageBody(); // GET TEXT
      await this.messaging.send(MESSAGES.REQUEST_PROCESSING, ctx, {});

      let cart: Cart = null;

      if (session.order?.cart_id) {
        cart = await this.brokerTransport
          .send<Cart>(BROKER_PATTERNS.CART.GET_CART, { id: session.order?.cart_id })
          .toPromise();
      }

      // AFTER REMOVING OR ADDING ITEMS IN THE CURRENT CART
      console.log('<======== BEFORE PROCESSING ITEMS FROM REQUEST OPTIMIZED =======>');
      const dtoItems = await this.itemsFromRequestOptimized(session, text, isEdit);
      const invalidSearch = !Boolean(dtoItems);

      // IF CART IS EMPTY AND SEARCH IS INVALID RETURN WITHOUT PROCESSING ANYTHING
      if (invalidSearch) {
        await this.messaging.send(MESSAGES.EMPTY_ITEMS_MATCH_MESSAGE, ctx, { query: text });
        if (!cart || cart?.items?.length < 1) return {};
      } else if (dtoItems?.length === 0 && session.order?.invalid_items?.length === 0) {
        await this.messaging.send(MESSAGES.EMPTY_CART_MESSAGE, ctx, {
          url: this.getStoreUrl(session, true),
        });
      }

      // UPDATE CART IF DTO ITEMS ARE EMPTY OR NOT BUT NOT NULL
      if (dtoItems) {
        dtoItems?.forEach((item, idx) => {
          if (item.variant_id === null) {
            delete dtoItems[idx].variant_id;
          }
        });

        if (session.order?.cart_id) {
          cart = await this.brokerTransport
            .send<Cart>(BROKER_PATTERNS.CART.UPDATE_CART, {
              id: session.order.cart_id,
              data: {
                store: session?.store?.id,
                items: dtoItems,
              },
            })
            .toPromise();
        } else {
          cart = await this.brokerTransport
            .send<Cart>(BROKER_PATTERNS.CART.CREATE_CART, {
              store: session?.store?.id,
              items: dtoItems,
            })
            .toPromise();
        }
      }

      if (cart?.items?.length > 0 || session.order?.invalid_items?.length > 0) {
        await this.processItemSelectionStep(session, cart, true);
      }

      return {};
    }
    return;
  }

  // CLASS METHODS

  protected async sendStoreFrontUrl(ctx: BotContext, session: GenericBotSession, isEdit = false): BotProcessorResult {
    const url = this.getStoreUrl(session, isEdit);
    const stepMessage = await this.messaging.send(MESSAGES.PICK_ITEMS_MESSAGE, ctx, {
      isEdit,
      url,
      name: session.customer.name,
    });
    this.captureMessageEvent(WHATSAPP_BOT_EVENTS.RECEIVED_ITEM_SELECTION_LINK, session, false);
    return { currentStep: WHATSAPP_BOT_STEPS.ITEMS_SELECTION, intent: INTENTS.PLACE_ORDER, stepMessage };
  }

  protected async sendStoreMenuAndUrl(ctx: BotContext, session: GenericBotSession): BotProcessorResult {
    const url = this.getStoreUrl(session);
    const stepMessage = await this.messaging.send(MESSAGES.PICK_MENU_ITEMS_MESSAGE, ctx, {
      url,
      image: session.store?.menu_images[0],
      storeMenu: session.store?.store_menu,
    });

    this.captureMessageEvent(WHATSAPP_BOT_EVENTS.RECEIVED_ITEM_SELECTION_LINK, session, false);
    return {
      currentStep: WHATSAPP_BOT_STEPS.ITEMS_SELECTION,
      secondarySteps: [WHATSAPP_BOT_STEPS.AUTO_SELECTION],
      intent: INTENTS.PLACE_ORDER,
      stepMessage,
    };
  }

  protected async getPreviousDeliveryInfo(session: GenericBotSession): Promise<DeliveryData> {
    if (session.order.delivery_info) {
      const {
        delivery_address,
        delivery_area,
        name,
        phone,
        delivery_notes,
        user_provided_address,
      } = session.order.delivery_info;

      return {
        delivery_address,
        delivery_area,
        name,
        phone,
        delivery_notes,
        user_provided_address,
      } as any;
    }

    const order = (
      await this.brokerTransport
        .send<Order[]>(BROKER_PATTERNS.ORDER.GET_ALL_LEAN, {
          filter: {
            store: session.store?.id,
            channel: ORDER_CHANNELS.CHATBOT,
            customer: session.customer?.id,
            delivery_info: { $ne: null },
          },
          sort: {
            created_at: -1,
          },
          limit: 1,
        })
        .toPromise()
    )?.[0];

    if (order) {
      if (order.delivery_info)
        order.delivery_info.delivery_area = (order as any)?.delivery_info?.area ?? order?.delivery_info?.delivery_area;
      return { ...order?.delivery_info, delivery_notes: order?.order_notes };
    }
  }

  protected async getItemsAndDeliveryMessageProps(
    ctx: BotContext,
    session: GenericBotSession,
    cart: Cart,
    deliveryInfo?: DeliveryInfo,
    isRepeatOrder = false,
  ) {
    const invalidItems = session.order?.invalid_items;
    const invalidItemsString = getInvalidItemsText(invalidItems, false);
    const validCartItems =
      invalidItems?.length > 0
        ? cart.items.filter(
            (item) =>
              !(invalidItems ?? []).find(
                (i) =>
                  //check for item with id and variant id match
                  // If the item is stated as over_stock, check that the quantity currently in cart is greater than the item available
                  // If the quantity isn't greater than what's in the inventory, leave it in the cart
                  i.id === item.item_id &&
                  (i?.variant_id ? i.variant_id === item.variant_id : true) &&
                  (i.status === 'over_stock' ? item.quantity > i.inventory : true),
              ),
          )
        : cart?.items;
    const itemsString = getOrderItemsText(validCartItems, ctx.currency, false);
    const totalCartPrice = getTotalCartPrice(validCartItems, ctx.currency);

    let messageProps = {
      name: session.customer.name,
      items: itemsString,
      totalPrice: totalCartPrice,
      sendDeliveryMethodMessage: false,
      pickUpMessageProps: null,
      deliveryFormMessageProps: null,
      previousDeliveryInfoMessageProps: null,
      isAutoSelection: false,
      url: this.getStoreUrl(session, true),
      hasInvalidItems: invalidItems?.length > 0,
      invalidItemsString,
      noOfValidItems: validCartItems.length,
    };

    const { deliveries_enabled, pickup_enabled } = session.store;

    if (deliveries_enabled && pickup_enabled) {
      messageProps.sendDeliveryMethodMessage = true;
    } else if (pickup_enabled) {
      messageProps.pickUpMessageProps = {
        name: session.store.name,
        phone: session.store.contact_phone,
        address: session.store.pickup_address,
        state: session.store.state,
        storeName: session.store.name,
      };
    } else if (deliveries_enabled) {
      const delvInfo = deliveryInfo
        ? ({ ...deliveryInfo, delivery_notes: '' } as DeliveryData)
        : await this.getPreviousDeliveryInfo(session);

      if (isRepeatOrder && session.store.auto_delivery !== true) {
        messageProps.previousDeliveryInfoMessageProps = this.getDeliveryInfoMessageProps(ctx, session, delvInfo);
      } else messageProps.deliveryFormMessageProps = await this.getDeliveryFormProps(session, false, delvInfo);
    }

    return messageProps;
  }

  protected async getDeliveryInfoMessageProps(ctx: BotContext, session: GenericBotSession, delvInfo: DeliveryData) {
    const area = session.store.delivery_areas.find((a) => a.id == getDocId(delvInfo?.delivery_area, true));
    let deliveryFee = session.order?.delivery_info?.auto_delivery_fee
      ? session.order.delivery_info.auto_delivery_fee
      : area?.fee;

    const currentCart = await this.brokerTransport
      .send<Cart>(BROKER_PATTERNS.CART.GET_CART, {
        id: session.order.cart_id,
        data: {
          _id: session.order.cart_id,
        },
      })
      .toPromise();
    const totalItemsCost = currentCart?.items.reduce((p, item) => {
      return item.quantity * getActualPrice(item?.variant ? item?.variant : item?.object ?? item) + p;
    }, 0);

    if (session?.store?.pass_chowbot_fee_to_deliveries && deliveryFee > 0) {
      //add chowbot tx fees to delivery fee
      const totalAmount = deliveryFee + totalItemsCost;
      const chowbotFee = chowbotCustomerFee(totalAmount, session.store.currency);

      deliveryFee += chowbotFee;

      session.order.payment_fee = chowbotFee;
      await this.saveSession(session);
    }

    return {
      name: delvInfo?.name,
      phone: delvInfo?.phone,
      address: delvInfo?.user_provided_address
        ? `${delvInfo?.user_provided_address} (_${delvInfo?.delivery_address?.trim()}_)`
        : delvInfo?.delivery_address,
      email: '',
      delivery_fee:
        area?.fee >= 0 || session?.order.delivery_info?.auto_delivery_fee
          ? toCurrency(deliveryFee, session.store.currency)
          : undefined,
      delivery_area: area?.title,
      note: delvInfo?.delivery_notes,
      couponValidated: !!session?.order?.coupon_code,
      total_items_cost: toCurrency(totalItemsCost, session.store.currency),
      total_items_count: currentCart?.items?.length,
    };
  }

  protected async getDeliveryFormProps(session: GenericBotSession, isEdit = false, deliveryInfo: DeliveryData) {
    await this.saveSession(session);
    return {
      delivery_areas: session.store.delivery_areas?.map((a) => ({
        id: a.id,
        // title: `${a.name} - ${toCurrency(a.fee, session.store.currency)}`,
        title: a.title,
      })),
      delivery_area: session.store.delivery_areas?.find(
        (a) =>
          a.id === session.order?.delivery_info?.delivery_area ||
          a.id === getDocId(deliveryInfo?.delivery_area) ||
          a.id === NONE_ID,
      )?.id,
      full_name: session.order?.delivery_info?.name ?? deliveryInfo?.name ?? session.customer.name,
      phone_number: formatPhoneNumber(
        session.order?.delivery_info?.phone ?? deliveryInfo?.phone ?? session.customer.phone_number,
      ),
      delivery_address:
        session.order?.delivery_info?.user_provided_address ??
        session.order?.delivery_info?.delivery_address ??
        deliveryInfo?.user_provided_address ??
        deliveryInfo?.delivery_address,
      delivery_notes: session.order?.delivery_info?.delivery_notes,
      isEdit,
      hasPickupAndDelivery: session.store?.pickup_enabled && session.store?.deliveries_enabled,
      show_delivery_notes: session?.store?.collect_order_notes,
    };
  }

  // EXPERIMENTAL

  protected async itemsFromRequestOptimized(session: GenericBotSession, message: string, isEdit = false) {
    let results: GptItemSearch['results'] = [];

    const { cartString, reducedItems } = await this.getReducedItemsAndCart(session, isEdit);

    const minifiedItems = Object.values(reducedItems).map((r: any) => {
      const i = {} as any;
      i.n = r.name;
      i.o = r.options?.map((o) => o[Object.keys(o)[0]]);
      if (i.o?.length === 0) delete i.o;
      return i;
    });

    const computedMessage = `${cartString} MENU=> ${JSON.stringify(minifiedItems)} ORDER_REQUEST=> ${message}`;
    // const data = res?.data as GptResponse;
    // const raw = data?.choices?.[0]?.message?.content;

    try {
      const res = await this.openai.getJsonResponseFromPrompt(
        PROMPT_TEMPLATES.GET_PRODUCT_FROM_MESSAGE_OPTIMIZED,
        computedMessage,
      );
      // const res: GptItemSearchOptimized = JSON.parse(Array.from(raw.matchAll(/{(\n|.)*}/g), (m) => m[0])[0]);
      console.log('<======================', res, 'OPEN AI RESPONSE', '=======================>');
      const resData = Array.isArray(res) ? res : Array.isArray(Object.values(res)[0]) ? Object.values(res)[0] : [];
      console.log('<======================', resData, 'OPEN AI RESPONSE', '=======================>');

      if (resData?.length > 0) {
        let mappedItems: GptItemSearch['results'] = [];

        for (let i = 0; i < resData.length; i++) {
          const r = resData[i];
          const reduced = Object.values(reducedItems).find(
            (o) => o.name.trim().toLowerCase() === r.item_name.trim().toLowerCase(),
          );

          let selectedOptionId = reduced?.options?.length > 0 ? Object.keys(reduced?.options[0])[0] : null;
          if (r.selected_option_name) {
            const selectedOption = reduced.options?.find((o) => o[Object.keys(o)[0]] === r.selected_option_name);
            if (selectedOption) {
              selectedOptionId = Object.keys(selectedOption)[0];
            }
          }

          mappedItems.push({
            ...r,
            item_id: reduced?.item_id,
            selected_option_id: selectedOptionId,
          });
        }

        results.push(...mappedItems);
      } else return null;
    } catch (e) {
      console.log('<======== ERROR IN ITEMS FROM REQUEST OPTIMIZED =======>');
      console.log(e);
      return null;
    }

    // AFTER SEARCHING THE STORE ITEMS IN COMPARISON TO OPEN AI RESPONSE
    if (results?.length === 0) {
      return null;
    }

    return await this.getCartDto(session, results, reducedItems);
  }

  protected async getCartDto(
    session: GenericBotSession,
    results: GptItemSearch['results'],
    reduced: { [id: string]: { name: string; inventory: number; options: any[] } },
  ) {
    let currentCart: Cart;
    if (session.order?.cart_id)
      currentCart = await this.brokerTransport
        .send(BROKER_PATTERNS.CART.GET_CART, {
          id: session.order.cart_id,
          data: {
            _id: session.order.cart_id,
          },
        })
        .toPromise();

    const dtoItems = [
      ...(currentCart?.items ?? []).map(({ item_id, quantity, variant_id }) => ({
        item_id,
        quantity,
        variant_id,
      })),
    ];

    const invalidItems = [...(session?.order?.invalid_items ?? [])];

    const addOrRemoveCartDto = (c: GptItemSearch['results'][0], inventory: number) => {
      const intent = c.intent ?? 'ADD';
      const updateIndex = dtoItems.findIndex(
        (item) =>
          item.item_id === c.item_id &&
          (Boolean(c.selected_option_id) ? item.variant_id === c.selected_option_id : true),
      );

      switch (intent) {
        case 'ADD':
          const quantity = Number.parseInt(c.quantity.toString());
          let totalQuantity = quantity;

          if (updateIndex > -1) {
            totalQuantity = dtoItems[updateIndex].quantity + quantity;
          }

          const itemUnavailable =
            totalQuantity > inventory &&
            !invalidItems.some(
              (i) => i.id === c.item_id && (i.variant_id ? i.variant_id === c.selected_option_id : true),
            ); //Todo: Looks fishy, this would mean that if item is already in invalidItems list don't add TEST

          if (itemUnavailable) {
            const status = inventory === 0 ? 'depleted' : 'over_stock';
            invalidItems.push({
              id: c.item_id,
              name: reduced[c.item_id].name,
              variant_id: c.selected_option_id,
              status,
              quantity: totalQuantity,
              inventory,
            });
          } else {
            if (updateIndex > -1) {
              dtoItems[updateIndex].quantity += Number.parseInt(c.quantity.toString());
            } else {
              dtoItems.push({
                item_id: c.item_id,
                quantity,
                variant_id: c.selected_option_id,
              }); //only add to cart if item isn't invalid
            }
          }

          return;
        case 'REMOVE':
          if (updateIndex >= 0) {
            const { variant_id, item_id, quantity: currentItemQuantity } = dtoItems[updateIndex];
            const newQuantity = currentItemQuantity - Number.parseInt(c.quantity.toString());
            if (newQuantity > 0) {
              dtoItems[updateIndex].quantity = newQuantity;
              return;
            }
            dtoItems.splice(updateIndex, 1);
            const invalidIndex = invalidItems.findIndex(
              (i) => i.id === item_id && (i.variant_id ? i.variant_id === variant_id : true),
            );
            if (invalidIndex >= 0) {
              invalidItems.splice(invalidIndex, 1);
            }
          }
          return;
      }
    };

    for (const c of results) {
      const reducedItem = reduced[c.item_id];
      if (reducedItem) {
        const inventory = reducedItem.inventory ?? Number.MAX_SAFE_INTEGER;
        addOrRemoveCartDto(c, inventory);
      }
    }

    session.order = { ...session.order, invalid_items: invalidItems };
    await this.saveSession(session);

    return dtoItems;
  }

  protected async getReducedItemsAndCart(session: GenericBotSession, isEdit = false) {
    let cartString = '';

    const items = await this.brokerTransport
      .send<Item[]>(BROKER_PATTERNS.ITEM.GET_ITEMS_WITH_DISCOUNTS, {
        is_deleted: { $ne: true },
        available: true,
        store: session.store.id,
      })
      .toPromise();

    if (session.order?.cart_id && isEdit) {
      const currentCart = await this.brokerTransport
        .send<Cart>(BROKER_PATTERNS.CART.GET_CART, {
          id: session.order.cart_id,
          data: {
            _id: session.order.cart_id,
          },
        })
        .toPromise();

      cartString =
        'CART=>' +
        JSON.stringify(
          currentCart.items.map((i) => ({
            name: i.object.name,
            selected_option_name:
              i.variant?.options?.length > 0 ? generateStringFromVariantValues(i.variant?.options) : undefined,
          })),
        );
    }

    const reducedItems: {
      [id: string]: { name: string; item_id: string; inventory: number; options: { [id: string]: string }[] };
    } = items.reduce((acc, item) => {
      const obj = {} as any;
      obj.name = item.name;
      obj.item_id = item.id;
      obj.inventory = item.is_always_available ? null : item.quantity;
      obj.options = [];

      if (item.variants?.options?.length > 0)
        for (const option of item.variants.options) {
          const optionName = generateStringFromVariantValues(option.values);
          obj.options.push({ [option._id ?? option.id]: optionName });
        }
      acc[getDocId(item)] = obj;
      return acc;
    }, {});

    return { cartString, reducedItems };
  }
}

/*   protected async itemsFromRequest(session: GenericBotSession, message: string, isEdit = false) {
    interface SearchForm {
      query: string;
      follow_ups: string[];
      items: {
        intent?: 'ADD' | 'REMOVE';
        name: string;
        quantity: number;
        inventory: number;
        options: Item['variants']['options'];
        has_options: boolean;
        matched_options: string[];
        selected_options?: string[];
      }[];
    }

    const res = await this.openai.getJsonResponseFromPrompt(
      PROMPT_TEMPLATES.GET_PRODUCT_DETAILS_FROM_MESSAGE_SIMPLE,
      message,
    );
    const data = res?.data as GptResponse;
    const raw = data?.choices?.[0]?.message?.content;
    let details;

    try {
      details = JSON.parse(Array.from(raw.matchAll(/{(\n|.)*}/g), (m) => m[0])[0]) ?? {};
    } catch (e) {
      return null;
    }
    console.log('<======================', details, 'OPEN AI RESPONSE', '=======================>');
    const keys = Object.keys(details);

    if (keys?.length === 0) return null;

    const form: SearchForm[] = [];
    function pushToForm(
      items: {
        object: Item;
        matched_options?: string[];
      }[],
      query: string,
    ) {
      form.push({
        query,
        follow_ups: [],
        items: [
          ...items.map((item) => ({
            intent: isEdit ? details[query]?.intent ?? 'ADD' : undefined,
            name: item.object.name,
            quantity: Number.parseInt(details[query]?.quantity ?? 1),
            inventory: item.object.quantity,
            options: item.object.variants?.options,
            has_options: item.object.variants?.options?.length > 0,
            matched_options: item.matched_options ?? [],
          })),
        ],
      });
    }

    root: for (const key of keys) {
      const item = await this.brokerTransport
        .send<Item>(BROKER_PATTERNS.ITEM.GET_ITEM, {
          $or: [{ name: { $regex: key, $options: 'ig' } }],
          is_deleted: false,
          available: true,
          store: session.store.id,
        })
        .toPromise();
      if (item) {
        pushToForm([{ object: item }], key);
      }else{

      }
    }
    console.log('<======================', form,'SEARCH FORM', '=======================>');
  }




    protected async getCartDtoForSimple(session: GenericBotSession, cartForm: CartForm) {
    let currentCart: Cart;
    if (session.order?.cart_id)
      currentCart = await this.brokerTransport
        .send(BROKER_PATTERNS.CART.GET_CART, {
          id: session.order.cart_id,
          data: {
            _id: session.order.cart_id,
          },
        })
        .toPromise();

    const dtoItems = [
      ...(currentCart?.items ?? []).map(({ item_id, quantity, variant_id }) => ({
        item_id,
        quantity,
        variant_id,
      })),
    ];
    const cartValues = Object.values(cartForm);
    const invalidItems = [...(session?.order?.invalid_items ?? [])];

    const updateCartDto = (c: CartForm[''], variantId?: string) => {
      // console.log('\n\n<======================',dtoItems, variantId, c,'GOT HERE', '=======================>\n\n');
      // return;
      const intent = c.intent ?? 'ADD';
      const updateIndex = dtoItems.findIndex(
        (item) => item.item_id === c.id && (variantId !== undefined ? item.variant_id === variantId : true),
      );
      switch (intent) {
        case 'ADD':
          if (updateIndex > -1) {
            dtoItems[updateIndex].quantity += c.quantity;
            return;
          }
          const quantity = Number.parseInt(c.quantity.toString());
          dtoItems.push({
            item_id: c.id,
            quantity,
            variant_id: variantId,
          });

          if (
            quantity > c.item_quantity &&
            !invalidItems.some((i) => i.id === c.id && (i.variant_id ? i.variant_id === variantId : true))
          ) {
            invalidItems.push({ id: c.id, status: 'over_stock', name: c.name, variant_id: variantId });
          }
          return;
        case 'REMOVE':
          if (updateIndex >= 0) {
            const currentItemQuantity = dtoItems[updateIndex].quantity;
            const { variant_id, item_id } = dtoItems[updateIndex];

            const newQuantity = currentItemQuantity - c.quantity;
            if (newQuantity > 0) {
              dtoItems[updateIndex].quantity = newQuantity;
              return;
            }
            dtoItems.splice(updateIndex, 1);
            const invalidIndex = invalidItems.findIndex(
              (item) => item.id === item_id && (item.variant_id ? item.variant_id === variant_id : true),
            );
            if (invalidIndex >= 0) {
              invalidItems.splice(invalidIndex, 1);
            }
          }
          return;
      }
    };

    for (const c of cartValues) {
      if (c.selectedVariants?.length > 0) {
        // PUSH MULTIPLE CART ITEMS BASED ON VARIANTS
        for (const variant of c.selectedVariants) {
          updateCartDto(c, variant);
        }
      } else updateCartDto(c, c.hasVariants ? c.options[0].id : undefined);
    }
    session.order = { ...session.order, invalid_items: invalidItems };
    await this.saveSession(session);
    return dtoItems;
  }


    protected async itemsFromRequestSimple(session: GenericBotSession, message: string, isEdit = false) {
    const ctx = getContext(session);
    const res = await this.openai.getJsonResponseFromPrompt(
      PROMPT_TEMPLATES.GET_PRODUCT_DETAILS_FROM_MESSAGE_SIMPLE,
      message,
    );
    const data = res?.data as GptResponse;
    const raw = data?.choices?.[0]?.message?.content;
    let details;

    if (!res) return null;

    try {
      details = JSON.parse(Array.from(raw.matchAll(/{(\n|.)*}/g), (m) => m[0])[0]) ?? {};
    } catch (e) {
      return null;
    }

    console.log('<======================', details, 'OPEN AI RESPONSE', '=======================>');

    const keys = Object.keys(details); // GET ITEM SEARCH QUERIES
    const cartForm: CartForm = {};

    const addToCart = (
      item: Item,
      detailsKey: string,
      selectedVariant: string = null,
      options: Item['variants']['options'] = [],
      itemQuantity,
    ) => {
      const cartItem = cartForm[item.id];
      cartForm[item.id] = {
        id: item.id,
        quantity: Number.parseInt(details[detailsKey]?.quantity ?? 1),
        hasVariants: options?.length > 0,
        selectedVariants: selectedVariant
          ? [...(cartItem?.selectedVariants ?? []), selectedVariant]
          : cartItem?.selectedVariants,
        options: options,
        intent: isEdit ? details[detailsKey]?.intent ?? 'ADD' : undefined,
        item_quantity: itemQuantity,
        name: item.name,
      };
    };

    const findItem = async (name: string) => {
      const item = await this.brokerTransport
        .send<Item>(BROKER_PATTERNS.ITEM.GET_ITEM, {
          $or: [{ name: { $regex: name, $options: 'ig' } }, { description: { $regex: name, $options: 'ig' } }],
          is_deleted: false,
          available: true,
          store: session.store.id,
        })
        .toPromise();
      return item;
    };

    const searchOptions = (options: Item['variants']['options'], key: string) => {
      for (const option of options) {
        const optionValues = genarateStringFromVariantValues(option.values);

        const isMatch = new RegExp(key, 'ig').test(optionValues);
        if (isMatch) {
          return option.id ?? option?._id;
        }
      }
    };

    if (keys?.length === 0) {
      await this.messaging.send(MESSAGES.EMPTY_ITEMS_MATCH_MESSAGE, ctx, { url: this.getStoreUrl(session, true) });
      return null;
    }

    root: for (const key of keys) {
      // SIMPLE SEARCH
      const item = await findItem(key);
      // console.log('<======================', item, 'ITEM SEARCH', '=======================>');

      // IF SIMPLE SEARCH WORKS
      if (item) {
        addToCart(item, key, null, item?.variants?.options, item?.is_always_available ? undefined : item?.quantity);
      } else {
        // CHECK IF QUERY IS A VARIANT OF A CART ITEM
        const currentCartItems = Object.keys(cartForm);
        if (currentCartItems?.length > 0) {
          for (const id of currentCartItems) {
            const cartItem = cartForm[id];

            if (cartItem.hasVariants) {
              const options = cartItem.options;
              const optionRes = searchOptions(options, key);
              if (optionRes) {
                // FOUND VARIANT
                cartForm[id].selectedVariants = [...(cartForm[id].selectedVariants ?? []), optionRes];
                continue root;
              } else {
                // ADVANCED SEARCH FOR VARIANT BASED ON STRING PARTS
                const parts = key.split(' ');
                if (parts.length > 1)
                  for (const part of parts) {
                    const optionRes = searchOptions(options, part);
                    if (optionRes) {
                      cartForm[id].selectedVariants = [...(cartForm[id].selectedVariants ?? []), optionRes];
                      continue root;
                    }
                  }
              }
            }
          }
        }
        // ADVANCED ITEM SEARCH
        const parts = key.split(' ');
        if (parts.length > 1) {
          // FIND ALL ITEMS MATCHING STRING PARTS
          const items = await this.brokerTransport
            .send<Item[]>(BROKER_PATTERNS.ITEM.GET_ITEMS, {
              $or: [...parts.map((part) => ({ name: { $regex: part, $options: 'ig' } }))],
              is_deleted: false,
              available: true,
              store: session.store.id,
            })
            .toPromise();

          if (items.length > 0) {
            // SORY BY OPTIONS
            if (items.length > 1)
              items.sort((a, b) => (a.variants?.options?.length ?? 0) - (b.variants?.options?.length ?? 0));

            // LOOP THROUGH SORTED ITEMS
            for (const [idx, i] of items.entries()) {
              const options = i.variants?.options ?? [];

              // IF ITEM HAS OPTIONS
              if (options.length > 0) {
                for (const part of parts) {
                  // ADVANCED SEARCH FOR VARIANT BASED ON STRING PARTS
                  const optionRes = searchOptions(options, part);
                  if (optionRes) {
                    addToCart(i, key, optionRes, i?.variants?.options, i?.quantity);
                    continue root;
                  }
                }
              }

              // IF THERE'S NO MATCH WITH ITEM WITH VARIANTS PICK LAST ITEM MATCH (WE DON TRY)
              if (idx === items.length - 1) {
                addToCart(i, key, null, options, i?.quantity);
              }
            }
          }
        }
      }
    }

    return this.getCartDtoForSimple(session, cartForm);
  }




    protected async itemsFromRequest(session: GenericBotSession, message: string, isEdit = false) {
    let reduced = {};
    let results: GptItemSearch['results'] = [];
    const { cartString, reducedItems } = await this.getReducedItemsAndCart(session, isEdit);

    const computedMessage = `${cartString} MENU=> ${JSON.stringify(reducedItems)} ORDER_REQUEST=> ${message}`;
    const template = isEdit
      ? PROMPT_TEMPLATES.EDIT_PRODUCT_DETAILS_FROM_MESSAGE
      : PROMPT_TEMPLATES.GET_PRODUCT_DETAILS_FROM_MESSAGE;

    const res = await this.openai.getJsonResponseFromPrompt(template, computedMessage);
    const data = res?.data as GptResponse;
    const raw = data?.choices?.[0]?.message?.content;

    try {
      const res: GptItemSearch = JSON.parse(Array.from(raw.matchAll(/{(\n|.)*}/g), (m) => m[0])[0]);
      console.log('<======================', res, 'OPENAI RESPONSE', '=======================>');
      // return
      if (res?.results?.length > 0) {
        results.push(...res?.results);
        reduced = { ...reduced, ...reducedItems };
      } else return null;
    } catch (e) {
      return null;
    }

    if (results?.length === 0) {
      return null;
    }

    return await this.getCartDto(session, results, reduced);
  }
 */
