import { Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { FLOWS } from '../../../enums/whatsapp.enum';
import { BotContext } from '../../../interfaces/whatsapp.interface';
import {
  HEADER_TYPES,
  MESSAGE_TYPES,
  WHATSAPP_TEMPLATES,
  WhatsappBusinessApiRepository,
} from '../../../repositories/whatsapp.repository';
import { formatPhoneNumber } from '../../../utils';
import { Errorable } from '../../../utils/types';
import {
  GET_CONFIRM_ORDER_FLOW_ID,
  GET_DELIVERY_INFO_FLOW_ID,
  GET_FEEDBACK_FLOW_ID,
} from '../../../utils/wabot-constants';
import {
  cleanString,
  delay,
  formatAWSResource,
  getChowbotUrl,
  removeNullOrEmptyFromArray,
  truncateText,
} from '../utils/functions.utils';
import { CTA_REPLIES, MESSAGES, MESSAGE_TEMPLATES, OTHER_TEXTS } from '../utils/messages.utils';

@Injectable()
export class WhatsappMessagingService {
  constructor(protected readonly whataspp: WhatsappBusinessApiRepository, protected readonly logger: Logger) {}

  private async sendWelcomeMessage(ctx: BotContext, data: any) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.WELCOME_MESSAGE](data);
    const message = await this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons: removeNullOrEmptyFromArray([
        CTA_REPLIES.PLACE_NEW_ORDER,
        data.hasPreviousOrders ? CTA_REPLIES.REPEAT_ORDER : null,
        CTA_REPLIES.CONTACT_RESTAURANT,
      ]),
      header: OTHER_TEXTS.WELCOME_STORE_MESSAGE(data),
      header_type: HEADER_TYPES.IMAGE,
      header_data: {
        link: formatAWSResource(data.storeImage),
      },
      body: messageBody,
      footer: OTHER_TEXTS.HELP_FOOTER,
    });

    if (data.branchesData) {
      await delay(1750);
      const res = await this.sendSelectBranchesMessage(ctx, { storeName: data.storeName, branches: data.branchesData });
      if (res?.error) return res;
    }

    return message;
  }

  private async sendWelcomeCTAMessage(ctx: BotContext, data: any) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.WELCOME_CTA_MESSAGE](data);
    const message = await this.whataspp.sendInteractiveMessage(ctx, {
      type: 'cta_url',
      cta: {
        display_text: 'Select Items',
        url: data.url,
      },
      header: OTHER_TEXTS.WELCOME_STORE_MESSAGE(data),
      header_type: HEADER_TYPES.IMAGE,
      header_data: {
        link: formatAWSResource(data.storeImage),
      },
      body: messageBody,
      footer: OTHER_TEXTS.HELP_FOOTER,
    });

    if (data.branchesData) {
      await delay(1750);
      const res = await this.sendSelectBranchesMessage(ctx, { storeName: data.storeName, branches: data.branchesData });
      if (res?.error) return res;
    }

    return message;
  }

  private async sendPickMenuItemsMessage(ctx: BotContext, data: any) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.PICK_MENU_ITEMS_MESSAGE](data);
    if (data.storeMenu) {
      return this.whataspp.sendInteractiveMessage(ctx, {
        type: 'cta_url',
        cta: {
          display_text: 'Select Items',
          url: data.url,
        },
        header: OTHER_TEXTS.WHITESPACE(1),
        footer: OTHER_TEXTS.USE_LINK_FOOTER,
        header_type: HEADER_TYPES.DOCUMENT,
        header_data: {
          link: formatAWSResource(data.storeMenu),
          filename: 'menu.pdf',
        },
        body: messageBody,
      });
    }

    /* return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'cta_url',
      cta: {
        display_text: 'Select Items',
        url: data.url,
      },
      header: OTHER_TEXTS.WHITESPACE(1),
      // footer: OTHER_TEXTS.USE_LINK_FOOTER,
      header_type: HEADER_TYPES.IMAGE,
      header_data: {
        link: formatAWSResource(data.storeMenu ?? data.image),
      },
      body: messageBody,
    }); */
  }

  private async sendExistingCustomerMessage(ctx: BotContext, data: any) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.CHAT_INITIATION_EXISTING_CUSTOMER](data);
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons: [CTA_REPLIES.PROCEED, CTA_REPLIES.FIND_ANOTHER_STORE, CTA_REPLIES.END_PROCESS],
      body: messageBody,
    });
  }

  private async sendNewCustomerMessage(ctx: BotContext) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.NEW_CUSTOMER_NO_STORE_MESSAGE]();
    return this.whataspp.sendTextMessage(ctx, messageBody);
  }

  private async sendSearchStoreMessage(ctx: BotContext) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.SEARCH_STORE_MESSAGE]();
    return this.whataspp.sendTextMessage(ctx, messageBody);
  }

  private async sendEmptySearchResultsMessage(ctx: BotContext, data: any) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.EMPTY_SEARCH_RESULTS_MESSAGE](data);
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons: [CTA_REPLIES.MAKE_NEW_SEARCH, CTA_REPLIES.END_PROCESS],
      body: messageBody,
    });
  }

  private async sendSearchResultsNoExtraMessage(ctx: BotContext, data: any) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.SEARCH_RESULTS_NO_EXTRA_MESSAGE](data);
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons: [CTA_REPLIES.MAKE_NEW_SEARCH, CTA_REPLIES.END_PROCESS],
      body: messageBody,
    });
  }

  private async sendSearchStoreResultsMessage(
    ctx: BotContext,
    data: {
      stores: { name: string; description: string; id: string }[];
      query: string;
      range: string;
      hasMoreResults: boolean;
      isFirstPage: boolean;
    },
  ) {
    let res = await this.whataspp.sendInteractiveMessage(ctx, {
      type: 'list',
      list: {
        list_cta: CTA_REPLIES.SEARCH_STORE_RESULTS_CTA,
        sections: [
          {
            title: '',
            rows: data.stores.map(({ name, description, id }) => ({
              title: truncateText(name, 24),
              description: truncateText(description, 48),
              id,
            })),
          },
        ],
      },
      body: MESSAGE_TEMPLATES[MESSAGES.SEARCH_STORE_RESULTS_MESSAGE]({
        query: data.query,
        range: data.range,
        isFirstPage: data.isFirstPage,
      }),
    });

    if (res.data) return this.sendLoadMoreStoresMessage(ctx, { hasMoreResults: data.hasMoreResults });
    else return res;
  }

  private async sendLoadMoreStoresMessage(ctx: BotContext, data: { hasMoreResults: boolean }) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.SEARCH_STORE_RESULTS_EXTRA_MESSAGE]();
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons: removeNullOrEmptyFromArray([
        data?.hasMoreResults ? CTA_REPLIES.LOAD_MORE_STORES : null,
        CTA_REPLIES.MAKE_NEW_SEARCH,
        CTA_REPLIES.END_PROCESS,
      ]),
      body: messageBody,
    });
  }

  private async sendConfirmNameMessage(ctx: BotContext, data: any) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.CONFIRM_NAME_MESSAGE](data);
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons: [CTA_REPLIES.PROCEED, CTA_REPLIES.ENTER_NAME],
      body: messageBody,
    });
  }

  private async sendPickItemsMessage(ctx: BotContext, data: any) {
    const messageBody = data.isEdit
      ? MESSAGE_TEMPLATES[MESSAGES.EDIT_ITEMS_MESSAGE](data)
      : MESSAGE_TEMPLATES[MESSAGES.PICK_ITEMS_MESSAGE](data);

    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'cta_url',
      cta: {
        display_text: 'Select Items',
        url: data.url,
      },
      body: messageBody,
    });
  }

  private async sendEmptyItemMatchMessage(ctx: BotContext, data: any) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.EMPTY_ITEMS_MATCH_MESSAGE](data);

    return this.whataspp.sendTextMessage(ctx, messageBody);

    // return this.whataspp.sendInteractiveMessage(ctx, {
    //   type: 'cta_url',
    //   cta: {
    //     display_text: 'Select Items',
    //     url: data.url,
    //   },
    //   body: messageBody,
    //   footer: OTHER_TEXTS.USE_LINK_FOOTER,
    // });
  }

  private async sendEmptyCartMessage(ctx: BotContext, data: any) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.EMPTY_CART_MESSAGE](data);

    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'cta_url',
      cta: {
        display_text: 'Select Items',
        url: data.url,
      },
      body: messageBody,
      footer: OTHER_TEXTS.USE_LINK_FOOTER,
    });
  }

  private async sendOutofStockMessage(ctx: BotContext, data: any) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.ITEMS_OUT_OF_STOCK_MESSAGE](data);

    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'cta_url',
      cta: {
        display_text: 'Select Items',
        url: data.url,
      },
      body: messageBody,
    });
  }

  private async sendEnterNameMessage(ctx: BotContext) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.ENTER_NAME_MESSAGE]();
    return this.whataspp.sendTextMessage(ctx, messageBody);
  }

  private async sendItemsAndDeliveryMessage(
    ctx: BotContext,
    data: {
      url: string;
      name: string;
      items: string;
      totalPrice: string;

      pickUpMessageProps: any;
      deliveryFormMessageProps: any;
      previousDeliveryInfoMessageProps: any;

      sendDeliveryMethodMessage: boolean;
      isAutoSelection: boolean;
      hasInvalidItems: boolean;
      noOfValidItems: number;
    },
  ) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.ITEMS_AND_DELIVERY_MESSAGE](data);
    let result = await this.whataspp.sendInteractiveMessage(ctx, {
      type: 'cta_url',
      cta: {
        display_text: 'Edit Selections',
        url: data.url,
      },
      body: messageBody,
      footer: data.isAutoSelection ? OTHER_TEXTS.AI_DISCLAIMER_FOOTER : undefined,
    });

    if (result.error || (data.hasInvalidItems && data.noOfValidItems < 1)) return result;
    else if (data.sendDeliveryMethodMessage) return this.sendDeliveryMethodMessage(ctx);
    else if (data.previousDeliveryInfoMessageProps)
      return this.sendDeliveryInfoMessage(ctx, data.previousDeliveryInfoMessageProps);
    else if (data.pickUpMessageProps) return this.sendPickupAddressMessage(ctx, data.pickUpMessageProps);
    else if (data.deliveryFormMessageProps) return this.sendDeliveryInfoFlowMessage(ctx, data.deliveryFormMessageProps);
  }

  private async sendDeliveryMethodMessage(ctx: BotContext) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.CONFIRM_DELIVERY_METHOD_MESSAGE]();
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons: [CTA_REPLIES.PICKUP, CTA_REPLIES.DELIVERY],
      body: messageBody,
    });
  }

  private async sendPreviousDeliveryInfoMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.PREVIOUS_DELIVERY_INFO_MESSAGE](data);
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons: [CTA_REPLIES.LAST_DELIVERY_INFO, CTA_REPLIES.NEW_DELIVERY_INFO],
      body: messageBody,
    });
  }

  private async sendPickupAddressMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.PICKUP_ADDRESS_MESSAGE](data);

    if (data.couponValidated) {
      // send message
    }
    const couponButton = data.couponValidated ? CTA_REPLIES.REMOVE_COUPON : CTA_REPLIES.ADD_COUPON;
    const orderNotesButton = data?.collectOrderNote
      ? data.hasOrderNotes
        ? CTA_REPLIES.CHANGE_ORDER_NOTES
        : CTA_REPLIES.ADD_ORDER_NOTES
      : null; //don't show button for stores that don't want order notes

    await this.whataspp.sendLocationMessage(ctx, data?.address);
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons: [orderNotesButton, couponButton, CTA_REPLIES.PROCEED_TO_PAYMENT].filter(Boolean),
      body: messageBody,
    }); //await to ensure message sends before location message
  }

  private async sendDeliveryInfoFlowMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.ENTER_DELIVERY_INFO_MESSAGE](data);
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'flow',
      body: messageBody,
      flow: {
        flow_cta: `${data.isEdit ? 'Edit' : 'Add'} Delivery Info`,
        flow_id: GET_DELIVERY_INFO_FLOW_ID(),
        flow_action: 'navigate',
        flow_action_payload: {
          data,
          screen: data.isAlt ? FLOWS.ALT_DELIVERY_INFO : FLOWS.DELIVERY_INFO,
        },
      },
      // footer: data.isAlt ? OTHER_TEXTS.LOCATION_REQUIRED_FOOTER : OTHER_TEXTS.DELIVERY_FOOTER,
    });
  }

  private async sendPaymentRequestMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.ORDER_CONFIRMATION_PENDING_MESSAGE](data);
    let message;

    setTimeout(() => {
      message = this.whataspp.sendInteractiveMessage(ctx, {
        type: 'button',
        buttons: [CTA_REPLIES.CONTACT_RESTAURANT, CTA_REPLIES.CANCEL_ORDER],
        body: messageBody,
        footer: OTHER_TEXTS.TOO_LONG_FOOTER,
      });
    }, 250); //always delay sending this message
    return message;
  }

  private async sendAutoCheckInMessage(ctx: BotContext, data) {
    return await this.whataspp.sendTemplatedMessage({
      from: ctx.botPhoneId,
      to: ctx.destinationPhone,
      templateName: WHATSAPP_TEMPLATES.AUTO_CUSTOMER_CHECK_IN,
      parameters: [cleanString(data?.customerName), cleanString(data?.store), cleanString(data?.message)],
      buttons: [
        { sub_type: 'url', parameters: [data?.slug] },
        {
          sub_type: 'quick_reply',
          parameters: [],
        },
      ],
      language: 'en',
    });
  }

  private async sendStoreOpenedReminderMessage(ctx: BotContext, data) {
    return await this.whataspp.sendTemplatedMessage({
      from: ctx.botPhoneId,
      to: ctx.destinationPhone,
      templateName: WHATSAPP_TEMPLATES.STORE_OPENED_REMINDER,
      parameters: [cleanString(data?.customerName?.split(' ')[0]), cleanString(data?.storeName)],
      buttons: [{ sub_type: 'url', parameters: [data?.storeCode] }],
      language: 'en',
    });
  }

  /* private async sendStoreOpenedMessage(ctx: BotContext, data: any) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.STORE_OPENED_REMINDER](data);

    return await this.whataspp.sendInteractiveMessage(ctx, {
      type: 'cta_url',
      cta: {
        display_text: 'Order Now',
        url: getChowbotUrl(data.storeCode),
      },
      body: messageBody,
    });
  }
 */

  // SELLER MESSAGES START

  private async sendAutoDeliveryCancelledMessage(ctx: BotContext, data) {
    return await this.whataspp.sendTemplatedMessage({
      from: ctx.botPhoneId,
      to: ctx.destinationPhone,
      templateName: WHATSAPP_TEMPLATES.AUTO_DELIVERY_CANCELLATION_MESSAGE,
      parameters: [data?.orderId, cleanString(data?.items)],
      buttons: [{ sub_type: 'url', parameters: [data?.orderId] }],
      language: 'en',
    });
  }

  private async sendAutoDeliveryFailedMessage(ctx: BotContext, data) {
    return await this.whataspp.sendTemplatedMessage({
      from: ctx.botPhoneId,
      to: ctx.destinationPhone,
      templateName: WHATSAPP_TEMPLATES.AUTO_DELIVERY_FAILED,
      parameters: [
        cleanString(data?.customerName),
        data?.orderId,
        data?.courier,
        cleanString(data?.items),
        cleanString(data?.error),
      ],
      buttons: [{ sub_type: 'url', parameters: [data?.deliveryId] }],
      language: 'en',
    });
  }

  private async sendAutoDeliveryBookedMessage(ctx: BotContext, data) {
    return await this.whataspp.sendTemplatedMessage({
      from: ctx.botPhoneId,
      to: ctx.destinationPhone,
      templateName: WHATSAPP_TEMPLATES.AUTO_DELIVERY_BOOKED,
      parameters: [data?.courier, cleanString(data?.customerName), data?.orderId, data?.trackingUrl],
      buttons: [],
      language: 'en',
    });
  }

  private async sendFeedbackFormMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.FEEDBACK_FORM_MESSAGE](data);
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'flow',
      body: messageBody,
      flow: {
        flow_cta: `Give Feedback`,
        flow_id: GET_FEEDBACK_FLOW_ID(),
        flow_action: 'navigate',
        flow_action_payload: {
          data: null,
          screen: FLOWS.RATE,
        },
      },
    });
  }

  private async sendOrderConfirmationRequestMessage(ctx: BotContext, data) {
    if (data.isRedo === true) {
      const messageBody = MESSAGE_TEMPLATES[MESSAGES.SELLER_ORDER_CONFIRMATION](data);
      return this.whataspp.sendInteractiveMessage(ctx, {
        type: 'flow',
        body: messageBody,
        flow: {
          flow_cta: `Confirm or Reject`,
          flow_id: GET_CONFIRM_ORDER_FLOW_ID(!Boolean(data.body.deliveryAddress)),
          flow_action: 'navigate',
          flow_action_payload: {
            data: null,
            screen: FLOWS.CONFIRM_ORDER,
          },
          flow_token: data.buttons.orderId,
        },
      });
    }

    return await this.whataspp.sendTemplatedMessage({
      from: ctx.botPhoneId,
      to: ctx.destinationPhone,
      templateName: data.body.deliveryAddress
        ? WHATSAPP_TEMPLATES.CONFIRM_DELIVERY_ORDER_WITH_FLOW
        : WHATSAPP_TEMPLATES.CONFIRM_PICKUP_ORDER_WITH_FLOW,
      parameters: [
        data.body?.title,
        cleanString(data.body?.items),
        data.body?.totalCartPrice,
        cleanString(data.body?.deliveryAddress),
        `${process.env.CATLOG_WWW}/o/${data.buttons.orderId}`,
        data.buttons.orderId,
      ].filter((e) => !!e),
      buttons: [{ sub_type: 'flow', parameters: [''] }],
      language: 'en',
      flowActionData: {
        flow_token: data.buttons.orderId,
      },
    });
  }

  private async sendOrderStatusUpdateMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.ORDER_STATUS_UPDATE_MESSAGE](data);
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'cta_url',
      cta: {
        display_text: 'View Order',
        url: data.url,
      },
      body: messageBody,
    });
  }

  private async sendOrderExpiredMessage(ctx: BotContext, data) {
    return await this.whataspp.sendTemplatedMessage({
      from: ctx.botPhoneId,
      to: ctx.destinationPhone,
      templateName: WHATSAPP_TEMPLATES.AUTO_CANCELLATION_MESSAGE,
      parameters: [data?.orderId, cleanString(data?.reason), cleanString(data?.items)],
      buttons: [{ sub_type: 'url', parameters: [data?.orderId] }],
      language: 'en',
    });
  }

  private async sendPaymentReceivedMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.PAYMENT_RECEIVED](data);
    return await this.whataspp.sendInteractiveMessage(ctx, {
      type: 'cta_url',
      cta: {
        display_text: 'View Order',
        url: data.url,
      },
      header: OTHER_TEXTS.WELCOME_STORE_MESSAGE(data),
      header_type: HEADER_TYPES.IMAGE,
      header_data: {
        link: 'https://res.cloudinary.com/catlog/image/upload/v1717485274/chowbot/payment-received.png',
      },
      body: messageBody,
    });
  }

  // SELLER MESSAGES END

  private async sendSelectBranchesMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.SWITCH_BRANCHES]({ storeName: data?.storeName });
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'list',
      // header: CTA_REPLIES.SELECT_PREVIOUS_ORDER_HEADER,
      list: {
        list_cta: CTA_REPLIES.SELECT_BRANCH_CTA,
        sections: [
          {
            title: '',
            rows: data.branches.map(({ name, description, id }) => ({
              title: truncateText(name, 24),
              description: truncateText(description, 48),
              id,
            })),
          },
        ],
      },
      body: messageBody,
    });
  }

  private async sendDeliveryInfoMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.DELIVERY_DETAILS_MESSAGE](data);
    if (data.couponValidated) {
      // send message
    }
    const couponButton = data.couponValidated ? CTA_REPLIES.REMOVE_COUPON : CTA_REPLIES.ADD_COUPON;
    return await this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons: [CTA_REPLIES.EDIT_INFO, couponButton, CTA_REPLIES.PROCEED_TO_PAYMENT],
      body: messageBody,
    });

    /* if (result.error) return result;
    else if (data.orderConfirmationPendingMessageProps)
      return this.sendPaymentRequestMessage(ctx, data.orderConfirmationPendingMessageProps);
    else if (data.bankDetailsMessageProps) return this.sendBankDetailsMessage(ctx, data.bankDetailsMessageProps); */
  }

  private async sendConfirmPaymentMethodMessage(ctx: BotContext, isRequestType = false) {
    const messageBody = MESSAGE_TEMPLATES[
      isRequestType ? MESSAGES.PAYMENT_REQUEST_ACCEPTED_MESSAGE : MESSAGES.CONFIRM_PAYMENT_METHOD_MESSAGE
    ]();
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons: [CTA_REPLIES.BANK_TRANSFER, CTA_REPLIES.OTHER_METHODS],
      body: messageBody,
    });
  }

  private async sendBankDetailsMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.PAYMENT_DETAILS_MESSAGE](data);

    if (!data.hasBankPayment) {
      return this.whataspp.sendInteractiveMessage(ctx, {
        type: 'cta_url',
        cta: {
          display_text: 'Make Payment',
          url: data.url,
        },
        body: messageBody,
      });
    }

    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons: [CTA_REPLIES.MADE_TRANSFER, CTA_REPLIES.OTHER_METHODS],
      body: messageBody,
      footer: OTHER_TEXTS.AUTO_CONFIRMATION_FOOTER,
    });
  }

  private async sendOtherPaymentMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.OTHER_PAYMENT_METHOD_MESSAGE](data);
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'cta_url',
      cta: {
        display_text: 'Make Payment',
        url: data.url,
      },
      body: messageBody,
    });
  }

  private async sendSelectPreviousOrderMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.SELECT_PREVIOUS_ORDER_MESSAGE]();
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'list',
      // header: CTA_REPLIES.SELECT_PREVIOUS_ORDER_HEADER,
      list: {
        list_cta: CTA_REPLIES.SELECT_PREVIOUS_ORDER_CTA,
        sections: [
          {
            title: '',
            rows: data.orders.map(({ name, description, id }) => ({
              title: truncateText(name, 24),
              description: truncateText(description, 48),
              id,
            })),
          },
        ],
      },
      body: messageBody,
    });
  }

  private async sendPreviousOrdersNotFoundMessage(ctx: BotContext) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.PREVIOUS_ORDERS_NOT_FOUND_MESSAGE]();
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons: [CTA_REPLIES.PLACE_NEW_ORDER, CTA_REPLIES.END_PROCESS],
      body: messageBody,
    });
  }

  private async sendOrderCancelledWithReasonMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.ORDER_CANCELLED](data);
    const buttons = [];

    if (data.order_timedout) {
      buttons.push(CTA_REPLIES.RETRY_CONFIRMATION);
    } else {
      buttons.push(CTA_REPLIES.EDIT_ORDER);
    }

    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons: [...buttons, CTA_REPLIES.RESTART_NEW_ORDER, CTA_REPLIES.END_PROCESS],
      body: messageBody,
    });
  }

  private async sendEditOrderMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.EDIT_ORDER](data);

    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons: [CTA_REPLIES.EDIT_ITEMS, CTA_REPLIES.EDIT_INFO],
      body: messageBody,
    });
  }

  private async sendOrderFulfilledMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.ORDER_FULFILLED](data);
    return this.whataspp.sendTextMessage(ctx, messageBody);
  }

  private async sendDeliveryStatusMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.DELIVERY_STATUS](data);
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'cta_url',
      cta: {
        display_text: 'Track Delivery',
        url: data.trackingUrl,
      },
      header: OTHER_TEXTS.WELCOME_STORE_MESSAGE(data),
      header_type: HEADER_TYPES.IMAGE,
      header_data: {
        link: 'https://res.cloudinary.com/catlog/image/upload/v1717485548/chowbot/track-delivery.png',
      },
      body: messageBody,
      // footer: OTHER_TEXTS.TRACK_DELIVERY_FOOTER
    });
  }
  private async sendOrderSuccessfulMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.ORDER_PAYMENT_SUCCESSFUL](data);

    // if (data?.receiptLink) {
    //   await this.whataspp.sendMediaMessage(
    //     ctx,
    //     { link: data?.receiptLink, caption: messageBody },
    //     MESSAGE_TYPES.DOCUMENT,
    //   );
    // } else {
    //   await this.whataspp.sendTextMessage(ctx, messageBody);
    // }

    return await this.whataspp.sendInteractiveMessage(ctx, {
      type: 'cta_url',
      cta: {
        display_text: CTA_REPLIES.DOWNLOAD_RECEIPT,
        url: data.receiptUrl,
      },
      header: OTHER_TEXTS.WELCOME_STORE_MESSAGE(data),
      header_type: HEADER_TYPES.IMAGE,
      header_data: {
        link: 'https://res.cloudinary.com/catlog/image/upload/v1717485274/chowbot/payment-successful.png',
      },
      body: messageBody,
    });

    // await this.whataspp.sendMediaMessage(
    //   ctx,
    //   {
    //     link: 'https://res.cloudinary.com/catlog/image/upload/v1716345378/chowbot/payment-successful.png',
    //     caption: messageBody,
    //   },
    //   MESSAGE_TYPES.IMAGE,
    // );

    // let contactMenu: any;

    // setTimeout(async () => {
    //   contactMenu = await this.whataspp.sendContactMessage(ctx, {
    //     name: data.storeName,
    //     phones: [{ phone: data.phone, wa_id: formatPhoneNumber(data.phone) }],
    //   });
    // }, 5000);

    // return contactMenu;
    // return this.sendContactKitchenMessage(ctx, data);
  }

  private async sendPaymentReceiptMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.PAYMENT_RECEIPT](data);
    return this.whataspp.sendMediaMessage(
      ctx,
      { link: data.link, caption: messageBody, filename: 'receipt.pdf' },
      MESSAGE_TYPES.DOCUMENT,
    );
  }

  private async sendFeedbackReceivedMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.FEEDBACK_SUBMITTED_MESSAGE](data);

    return await this.whataspp.sendInteractiveMessage(ctx, {
      type: 'cta_url',
      cta: {
        display_text: 'Order Again',
        url: getChowbotUrl(data.storeCode),
      },
      body: messageBody,
    });
  }

  // generic

  private async sendGenericTextMessage(ctx: BotContext, msgType: MESSAGES, data) {
    const messageBody = MESSAGE_TEMPLATES[msgType](data);
    return this.whataspp.sendTextMessage(ctx, messageBody);
  }

  private async sendStoreContactMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.STORE_CONTACT](data);
    await this.whataspp.sendTextMessage(ctx, messageBody);
    return this.whataspp.sendContactMessage(ctx, {
      name: data.name,
      phones: [{ phone: data.phone, wa_id: formatPhoneNumber(data.phone) }],
    });
  }
  private async sendSupportContactMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.SUPPORT_CONTACT](data);
    await this.whataspp.sendTextMessage(ctx, messageBody);
    return this.whataspp.sendContactMessage(ctx, {
      name: data.name,
      phones: [{ phone: data.phone, wa_id: formatPhoneNumber(data.phone) }],
    });
  }

  private async sendPaymentReminder(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.PAYMENT_REMINDER](data);
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons: [CTA_REPLIES.OTHER_METHODS, CTA_REPLIES.CANCEL_ORDER, CTA_REPLIES.CONTACT_SUPPORT], //Todo: why is continue process used here
      body: messageBody,
    });
  }

  private async sendCouponValidationFailedMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.COUPON_VALIDATION_FAILED](data);
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons: [CTA_REPLIES.PROCEED_WITHOUT_COUPON],
      body: messageBody,
    });
  }

  private async sendHelpMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.HELP_MESSAGE](data);
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons: [CTA_REPLIES.CONTACT_SUPPORT], //Todo: why is continue process used here
      body: messageBody,
      header: '📖 Help Menu',
      footer: OTHER_TEXTS.CONTACT_SUPPORT_FOOTER,
    });
  }
  private async sendContactKitchenMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.CONTACT_STORE](data);
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons: [CTA_REPLIES.CONTACT_RESTAURANT], //Todo: why is continue process used here
      body: messageBody,
    });
  }
  private async sendStoreNotfoundMessage(ctx: BotContext, showResume = false) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.STORE_NOT_FOUND_MESSAGE]();
    const buttons = [CTA_REPLIES.SEARCH_FOR_A_STORE, CTA_REPLIES.END_PROCESS];
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons: showResume ? [CTA_REPLIES.CONTINUE_PROCESS, ...buttons] : buttons, //Todo: why is continue process used here
      body: messageBody,
    });
  }
  private async sendRequestLocationMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.REQUEST_LOCATION](data);
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'location_request_message',
      body: messageBody,
    });
  }

  private async sendStoreClosedMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.STORE_CLOSED](data);
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons: [CTA_REPLIES.FIND_ANOTHER_STORE, CTA_REPLIES.CONTACT_RESTAURANT, CTA_REPLIES.END_PROCESS],
      body: messageBody,
    });
  }

  private async sendStoreUnavailableMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.STORE_UNAVAILABLE](data);
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons: [CTA_REPLIES.FIND_ANOTHER_STORE, CTA_REPLIES.CONTACT_RESTAURANT, CTA_REPLIES.END_PROCESS],
      body: messageBody,
    });
  }

  private async sendErrorOccuredMessage(ctx: BotContext) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.ERROR_MESSAGE]();
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons: [CTA_REPLIES.RESUME_PROCESS, CTA_REPLIES.CONTACT_SUPPORT, CTA_REPLIES.END_PROCESS],
      body: messageBody,
    });
  }

  private async sendSessionCancelledMessage(ctx: BotContext, data: any) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.CANCEL_PROCESS_MESSAGE](data);

    return await this.whataspp.sendInteractiveMessage(ctx, {
      type: 'cta_url',
      cta: {
        display_text: 'Order Again',
        url: getChowbotUrl(data.storeCode),
      },
      body: messageBody,
    });
  }

  private async sendExpiredSessionMessage(ctx: BotContext, data: any) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.EXPIRED_SESSION_MESSAGE](data);

    return await this.whataspp.sendInteractiveMessage(ctx, {
      type: 'cta_url',
      cta: {
        display_text: 'Order Again',
        url: getChowbotUrl(data.storeCode),
      },
      body: messageBody,
    });
  }

  private async sendSameStoreInitiationMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.SAME_STORE_INITIATION_MESSAGE](data);
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons: [CTA_REPLIES.CONTINUE_PROCESS, CTA_REPLIES.RESTART_PROCESS, CTA_REPLIES.END_PROCESS], //Todo: should have a switch branch if the store has branches
      body: messageBody,
    });
  }

  private async sendDifferentStoreInitiationMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.DIFFERENT_STORE_INITIATION_MESSAGE](data);
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons: [CTA_REPLIES.CONTINUE_PROCESS, CTA_REPLIES.START_NEW_PROCESS, CTA_REPLIES.END_PROCESS],
      body: messageBody,
    });
  }
  private async sendStoreReminderMessage(ctx: BotContext, data) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.STORE_REMINDER_MESSAGE](data);
    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons: [CTA_REPLIES.CONTINUE_PROCESS, CTA_REPLIES.RESTART_PROCESS, CTA_REPLIES.END_PROCESS],
      body: messageBody,
    });
  }

  private async sendUnexpectedResponseMessage(ctx: BotContext, data: any) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.UNEXPECTED_RESPONSE_MESSAGE](data);
    const buttons = data.isPaymentStep
      ? [CTA_REPLIES.CANCEL_ORDER, CTA_REPLIES.CONTACT_SUPPORT]
      : [CTA_REPLIES.RESUME_PROCESS, data.showMainOptions ? CTA_REPLIES.MAIN_OPTIONS : CTA_REPLIES.END_PROCESS];

    return this.whataspp.sendInteractiveMessage(ctx, {
      type: 'button',
      buttons,
      body: messageBody,
    });
  }

  private async sendUnsupportedCountryMessage(ctx: BotContext, data: any) {
    const messageBody = MESSAGE_TEMPLATES[MESSAGES.UNSUPPORTED_COUNTRY_MESSAGE](data);
    return this.whataspp.sendTextMessage(ctx, messageBody);
  }

  private MESSAGE_TO_FUNCTION: {
    [key in MESSAGES]: (ctx: BotContext, data?: any) => Errorable<any>;
  } = {
    [MESSAGES.WELCOME_MESSAGE]: (ctx, data) => this.sendWelcomeMessage(ctx, data),
    [MESSAGES.WELCOME_CTA_MESSAGE]: (ctx, data) => this.sendWelcomeCTAMessage(ctx, data),
    [MESSAGES.PICK_MENU_ITEMS_MESSAGE]: (ctx, data) => this.sendPickMenuItemsMessage(ctx, data),
    [MESSAGES.CHAT_INITIATION_EXISTING_CUSTOMER]: (ctx, data) => this.sendExistingCustomerMessage(ctx, data),
    [MESSAGES.NEW_CUSTOMER_NO_STORE_MESSAGE]: (ctx) => this.sendNewCustomerMessage(ctx),
    [MESSAGES.SEARCH_STORE_RESULTS_EXTRA_MESSAGE]: (ctx, data) => this.sendLoadMoreStoresMessage(ctx, data),
    [MESSAGES.SEARCH_STORE_MESSAGE]: (ctx) => this.sendSearchStoreMessage(ctx),
    [MESSAGES.EMPTY_SEARCH_RESULTS_MESSAGE]: (ctx, data) => this.sendEmptySearchResultsMessage(ctx, data),
    [MESSAGES.SEARCH_RESULTS_NO_EXTRA_MESSAGE]: (ctx, data) => this.sendSearchResultsNoExtraMessage(ctx, data),
    [MESSAGES.SEARCH_STORE_RESULTS_MESSAGE]: (ctx, data) => this.sendSearchStoreResultsMessage(ctx, data),
    [MESSAGES.CONFIRM_NAME_MESSAGE]: (ctx, data) => this.sendConfirmNameMessage(ctx, data),
    [MESSAGES.PICK_ITEMS_MESSAGE]: (ctx, data) => this.sendPickItemsMessage(ctx, data),
    [MESSAGES.ENTER_NAME_MESSAGE]: (ctx) => this.sendEnterNameMessage(ctx),
    [MESSAGES.ITEMS_AND_DELIVERY_MESSAGE]: (ctx, data) => this.sendItemsAndDeliveryMessage(ctx, data),
    [MESSAGES.CONFIRM_DELIVERY_METHOD_MESSAGE]: (ctx) => this.sendDeliveryMethodMessage(ctx),
    [MESSAGES.PREVIOUS_DELIVERY_INFO_MESSAGE]: (ctx, data) => this.sendPreviousDeliveryInfoMessage(ctx, data),
    [MESSAGES.ENTER_DELIVERY_INFO_MESSAGE]: (ctx, data) => this.sendDeliveryInfoFlowMessage(ctx, data),
    [MESSAGES.ORDER_CONFIRMATION_PENDING_MESSAGE]: (ctx, data) => this.sendPaymentRequestMessage(ctx, data),
    [MESSAGES.PICKUP_ADDRESS_MESSAGE]: (ctx, data) => this.sendPickupAddressMessage(ctx, data),
    [MESSAGES.DELIVERY_DETAILS_MESSAGE]: (ctx, data) => this.sendDeliveryInfoMessage(ctx, data),
    [MESSAGES.CONFIRM_PAYMENT_METHOD_MESSAGE]: (ctx) => this.sendConfirmPaymentMethodMessage(ctx),
    [MESSAGES.PAYMENT_REQUEST_ACCEPTED_MESSAGE]: (ctx) => this.sendConfirmPaymentMethodMessage(ctx, true),
    [MESSAGES.PAYMENT_DETAILS_MESSAGE]: (ctx, data) => this.sendBankDetailsMessage(ctx, data),
    [MESSAGES.OTHER_PAYMENT_METHOD_MESSAGE]: (ctx, data) => this.sendOtherPaymentMessage(ctx, data),
    [MESSAGES.SELECT_PREVIOUS_ORDER_MESSAGE]: (ctx, data) => this.sendSelectPreviousOrderMessage(ctx, data),
    [MESSAGES.PREVIOUS_ORDERS_NOT_FOUND_MESSAGE]: (ctx) => this.sendPreviousOrdersNotFoundMessage(ctx),
    [MESSAGES.ORDER_PAYMENT_SUCCESSFUL]: (ctx, data) => this.sendOrderSuccessfulMessage(ctx, data),
    [MESSAGES.ORDER_CANCELLED]: (ctx, data) => this.sendOrderCancelledWithReasonMessage(ctx, data),
    [MESSAGES.PAYMENT_RECEIPT]: (ctx, data) => this.sendPaymentReceiptMessage(ctx, data),
    [MESSAGES.ORDER_FULFILLED]: (ctx, data) => this.sendOrderFulfilledMessage(ctx, data),
    [MESSAGES.EMPTY_ITEMS_MATCH_MESSAGE]: (ctx, data) => this.sendEmptyItemMatchMessage(ctx, data),
    [MESSAGES.EMPTY_CART_MESSAGE]: (ctx, data) => this.sendEmptyCartMessage(ctx, data),
    [MESSAGES.SWITCH_BRANCHES]: (ctx, data) => this.sendSelectBranchesMessage(ctx, data),
    [MESSAGES.EDIT_ORDER]: (ctx, data) => this.sendEditOrderMessage(ctx, data),
    [MESSAGES.DELIVERY_STATUS]: (ctx, data) => this.sendDeliveryStatusMessage(ctx, data),
    [MESSAGES.HELP_MESSAGE]: (ctx, data) => this.sendHelpMessage(ctx, data),
    [MESSAGES.PAYMENT_REMINDER]: (ctx, data) => this.sendPaymentReminder(ctx, data),
    [MESSAGES.COUPON_VALIDATION_FAILED]: (ctx, data) => this.sendCouponValidationFailedMessage(ctx, data),
    [MESSAGES.CONTACT_STORE]: (ctx, data) => this.sendContactKitchenMessage(ctx, data),
    [MESSAGES.REQUEST_LOCATION]: (ctx, data) => this.sendRequestLocationMessage(ctx, data),

    // TEXT MESSAGES
    [MESSAGES.PAYMENT_LOADING]: (ctx, data) => this.sendGenericTextMessage(ctx, MESSAGES.PAYMENT_LOADING, data),
    [MESSAGES.REQUEST_PROCESSING]: (ctx, data) => this.sendGenericTextMessage(ctx, MESSAGES.REQUEST_PROCESSING, data),
    [MESSAGES.TRANSFER_MADE]: (ctx, data) => this.sendGenericTextMessage(ctx, MESSAGES.TRANSFER_MADE, data),
    [MESSAGES.ENTER_COUPON_CODE]: (ctx, data) => this.sendGenericTextMessage(ctx, MESSAGES.ENTER_COUPON_CODE, data),
    [MESSAGES.COUPON_APPLIED]: (ctx, data) => this.sendGenericTextMessage(ctx, MESSAGES.COUPON_APPLIED, data),
    [MESSAGES.COUPON_REMOVED]: (ctx, data) => this.sendGenericTextMessage(ctx, MESSAGES.COUPON_REMOVED, data),
    [MESSAGES.ORDER_NOTES]: (ctx, data) => this.sendGenericTextMessage(ctx, MESSAGES.ORDER_NOTES, data),
    [MESSAGES.ORDER_NOTES_ADDED]: (ctx, data) => this.sendGenericTextMessage(ctx, MESSAGES.ORDER_NOTES_ADDED, data),
    [MESSAGES.FEEDBACK_SUBMITTED_MESSAGE]: (ctx, data) => this.sendFeedbackReceivedMessage(ctx, data),
    [MESSAGES.EXPIRED_TEXT]: (ctx, data) => this.sendGenericTextMessage(ctx, MESSAGES.EXPIRED_TEXT, data),

    // SELLER
    [MESSAGES.SELLER_ORDER_CONFIRMATION]: (ctx, data) => this.sendOrderConfirmationRequestMessage(ctx, data),
    [MESSAGES.AUTO_DELIVERY_CANCELLED]: (ctx, data) => this.sendAutoDeliveryCancelledMessage(ctx, data),
    [MESSAGES.FEEDBACK_FORM_MESSAGE]: (ctx, data) => this.sendFeedbackFormMessage(ctx, data),
    [MESSAGES.AUTO_DELIVERY_FAILED]: (ctx, data) => this.sendAutoDeliveryFailedMessage(ctx, data),
    [MESSAGES.AUTO_CHECK_IN_MESSAGE]: (ctx, data) => this.sendAutoCheckInMessage(ctx, data),
    [MESSAGES.AUTO_DELIVERY_BOOKED]: (ctx, data) => this.sendAutoDeliveryBookedMessage(ctx, data),
    [MESSAGES.ORDER_EXPIRED_MESSAGE]: (ctx, data) => this.sendOrderExpiredMessage(ctx, data),
    [MESSAGES.ORDER_STATUS_UPDATE_MESSAGE]: (ctx, data) => this.sendOrderStatusUpdateMessage(ctx, data),
    [MESSAGES.PAYMENT_RECEIVED]: (ctx, data) => this.sendPaymentReceivedMessage(ctx, data),
    [MESSAGES.SELF_SESSION_NOT_SUPPORTED]: (ctx, data) =>
      this.sendGenericTextMessage(ctx, MESSAGES.SELF_SESSION_NOT_SUPPORTED, data),

    // GENERIC
    [MESSAGES.STORE_REMINDER_MESSAGE]: (ctx, data) => this.sendStoreReminderMessage(ctx, data),
    [MESSAGES.STORE_OPENED_REMINDER]: (ctx, data) => this.sendStoreOpenedReminderMessage(ctx, data),
    [MESSAGES.STORE_NOT_FOUND_MESSAGE]: (ctx, data) => this.sendStoreNotfoundMessage(ctx, data),
    [MESSAGES.EXPIRED_SESSION_MESSAGE]: (ctx, data) => this.sendExpiredSessionMessage(ctx, data),
    [MESSAGES.STORE_CLOSED]: (ctx, data) => this.sendStoreClosedMessage(ctx, data),
    [MESSAGES.STORE_UNAVAILABLE]: (ctx, data) => this.sendStoreUnavailableMessage(ctx, data),
    [MESSAGES.ERROR_MESSAGE]: (ctx) => this.sendErrorOccuredMessage(ctx),
    [MESSAGES.CANCEL_PROCESS_MESSAGE]: (ctx, data) => this.sendSessionCancelledMessage(ctx, data),
    [MESSAGES.SAME_STORE_INITIATION_MESSAGE]: (ctx, data) => this.sendSameStoreInitiationMessage(ctx, data),
    [MESSAGES.DIFFERENT_STORE_INITIATION_MESSAGE]: (ctx, data) => this.sendDifferentStoreInitiationMessage(ctx, data),
    [MESSAGES.UNEXPECTED_RESPONSE_MESSAGE]: (ctx, data) => this.sendUnexpectedResponseMessage(ctx, data),
    [MESSAGES.UNSUPPORTED_COUNTRY_MESSAGE]: (ctx, data) => this.sendUnsupportedCountryMessage(ctx, data),
    [MESSAGES.STORE_CONTACT]: (ctx, data) => this.sendStoreContactMessage(ctx, data),
    [MESSAGES.SUPPORT_CONTACT]: (ctx, data) => this.sendSupportContactMessage(ctx, data),
    [MESSAGES.ITEMS_OUT_OF_STOCK_MESSAGE]: (ctx, data) => this.sendOutofStockMessage(ctx, data),
    [MESSAGES.ORDER_NAVIGATION_RESTRICTED]: (ctx, data) =>
      this.sendGenericTextMessage(ctx, MESSAGES.ORDER_NAVIGATION_RESTRICTED, data),

    // unimplemented
    [MESSAGES.EDIT_ITEMS_MESSAGE]: function (ctx, data) {
      throw new Error('not implemented');
    },
  };

  async send<T>(type: MESSAGES, ctx: BotContext, data?: T) {
    const { error } = (await this.MESSAGE_TO_FUNCTION[type]?.(ctx, data ?? {})) ?? {};
    if (error) {
      this.logger.error(error);
      throw new InternalServerErrorException(error);
    }
    return { type, data };
  }
}
