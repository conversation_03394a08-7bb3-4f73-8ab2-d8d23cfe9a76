import { InjectQueue } from '@nestjs/bull';
import { CACHE_MANAGER, Inject, Injectable, Logger } from '@nestjs/common';
import { Mutex, MutexInterface } from 'async-mutex';
import { Job, Queue } from 'bull';
import { Cache } from 'cache-manager';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { JOBS, QUEUES } from '../../../enums/queues.enum';
import {
  BotSession,
  GenericBotSession,
  GlobalTimeOut,
  OrderConfirmationSession,
  SESSION_TYPES,
} from '../../../interfaces/whatsapp.interface';
import {
  ACTIVE_CONFIRMATION_SESSIONS,
  CACHE_TTL,
  GET_APP_TIMEOUT,
  GLOBAL_TIMEOUT_CACHE_KEY,
  GLOBAL_TO_TTL,
  UTILITY_CACHE_PREFIX,
} from '../../../utils/wabot-constants';
import { ONE_HOUR, ONE_MINUTE } from '../../../utils/constants';
import { DELIVERY_METHODS, ORDER_STATUSES, Order } from '../../orders/order.schema';
import {
  MessageData,
  getCurrentSessionTtl,
  getOrderItemsText,
  getPublicUrl,
  getTotalCartPrice,
  normalizeOrderItemsData,
} from '../utils/functions.utils';
import { MESSAGES, OTHER_TEXTS } from '../utils/messages.utils';
import { SessionTimeOutJob, WABotJob } from '../whatsapp-bot.queue';
import { WhatsappMessagingService } from './messaging.service';
import { CacheService } from '../../shared/cache/cache.service';
@Injectable()
export class SellerProcessor {
  private mutexStore: { [key: string]: { mutex: Mutex; waitCount: number } } = {};
  constructor(
    @Inject(CACHE_MANAGER) protected readonly cacheManager: Cache,
    protected readonly logger: Logger,
    protected readonly brokerTransport: BrokerTransportService,
    protected readonly messaging: WhatsappMessagingService,
    @InjectQueue(QUEUES.WHATSAPP_BOT)
    protected readonly waBotQueue: Queue<WABotJob>,
    private cacheService: CacheService,
  ) {}

  async handleOrderConfirmationRequest(genericSession: GenericBotSession) {
    const orderId = genericSession?.order?.order_id;
    const storeId = genericSession?.store?.id;

    const sessionCacheKey = `${UTILITY_CACHE_PREFIX}:${orderId.toUpperCase()}`;
    const storeConfirmationCountKey = `${UTILITY_CACHE_PREFIX}:${ACTIVE_CONFIRMATION_SESSIONS}:${storeId}`;

    let confirmationSession = await this.cacheManager.get<OrderConfirmationSession>(sessionCacheKey);
    const activeConfirmations = await this.cacheManager.get<number>(storeConfirmationCountKey);

    if (!confirmationSession) {
      confirmationSession = {
        bot_phone_id: genericSession.phone_id,
        seller_phone: genericSession.store.contact_phone,
        secondary_phone: genericSession.store?.secondary_phone ?? '',
        store_name: genericSession.store.name,
        store_currency: genericSession.store.currency,
        generic_session_key: genericSession.key,

        active_confirmations_at_start: activeConfirmations ?? 0,

        type: SESSION_TYPES.ORDER_CONFIRMATION,
        date_initiated: new Date().toISOString(),
        timeout_job_id: (
          await this.createTimeoutJob(
            sessionCacheKey,
            Boolean(genericSession.store.secondary_phone),
            activeConfirmations,
          )
        ).id.toString(),

        key: sessionCacheKey,
        store_id: storeId,
        order_id: orderId,
      };
    }

    await this.processConfirmationJob(confirmationSession);
  }

  protected async processConfirmationJob(session: OrderConfirmationSession) {
    const order = await this.brokerTransport
      .send<Order>(BROKER_PATTERNS.ORDER.GET_ORDER, { _id: session.order_id })
      .toPromise();

    const orderItems = normalizeOrderItemsData(order.items);
    const items = getOrderItemsText(orderItems, session.store_currency, true);
    const totalCartPrice = getTotalCartPrice(orderItems, session.store_currency);
    const userProvidedAddress = order?.delivery_info?.user_provided_address?.trim();

    const deliveryAddress =
      order?.delivery_method === DELIVERY_METHODS.DELIVERY
        ? `*(${userProvidedAddress ? userProvidedAddress + ' - ' : ''}${
            order?.delivery_info?.delivery_area?.name ?? ''
          })* ${order?.delivery_info?.delivery_address.trim()}`
        : null;

    const messageProps = {
      body: {
        title: OTHER_TEXTS.CONFIRMATION_MESSAGE_TITLE(false),
        items: items,
        totalCartPrice,
        deliveryAddress,
      },
      buttons: { orderId: order?.id },
      isRedo: false,
    };
    await this.sendOrderConfirmationMessage(this.getCtx(session), messageProps);

    messageProps.isRedo = true; // NEXT TIME IT'LL SEND AS SERVICE MSG
    session.meta = { message_data: messageProps };
    await this.saveSession(session);

    const storeConfirmationCountKey = `${UTILITY_CACHE_PREFIX}:${ACTIVE_CONFIRMATION_SESSIONS}:${session.store_id}`;
    await this.cacheService.incrementWithLock(storeConfirmationCountKey, 1, ONE_HOUR);
  }

  protected async sendOrderConfirmationMessage(
    ctx: { botPhoneId: string; destinationPhone: string },
    messageProps,
    sendToSecondary = false,
  ) {
    if (sendToSecondary === true) {
      messageProps.isRedo = false;
      messageProps.body.title = OTHER_TEXTS.CONFIRMATION_MESSAGE_TITLE(true);
    }
    const messageData = await this.messaging.send(MESSAGES.SELLER_ORDER_CONFIRMATION, ctx, messageProps);
    return messageData;
  }

  async handleSellerMessage(body: MessageData) {
    const json = body.getInteractiveMessage()?.nfm_reply?.response_json;
    const form = JSON.parse(json) as {
      flow_token: string;
      screen_0_RadioButtonsGroup_0: '0_Confirm_Order' | '1_Reject_Order';
      screen_0_TextArea_1: string;
    };

    const sessionCacheKey = `${UTILITY_CACHE_PREFIX}:${form.flow_token.toUpperCase()}`;
    const confirmationSession = await this.cacheManager.get<OrderConfirmationSession>(sessionCacheKey);
    if (!Boolean(confirmationSession)) return false; //Todo: send message saying order was previously cancelled because time ran out

    if (form.screen_0_RadioButtonsGroup_0 === '1_Reject_Order') {
      const reason = form?.screen_0_TextArea_1?.trim();

      if (!reason) {
        const messageData = confirmationSession.meta.message_data;

        const ctx = this.getCtx(confirmationSession);
        ctx.destinationPhone = body.getWaId();
        this.sendOrderConfirmationMessage(ctx, messageData);
      }

      const genericSession = await this.cacheManager.get<GenericBotSession>(confirmationSession.generic_session_key);
      if (genericSession) {
        genericSession.metadata = { order_rejected: true };
        await this.saveSession(genericSession);
      }

      await this.updateOrderAndNotifySeller(confirmationSession, ORDER_STATUSES.CANCELLED, reason);
    } else if (form.screen_0_RadioButtonsGroup_0 === '0_Confirm_Order') {
      await this.updateOrderAndNotifySeller(confirmationSession, ORDER_STATUSES.PROCESSING);
    }
    await this.cleanUpConfirmationSession(confirmationSession, true);
  }

  async handleTimeOut(job: Job<WABotJob<SessionTimeOutJob>>) {
    const { sessionKey } = job.data.data;
    const confirmationSession = await this.cacheManager.get<OrderConfirmationSession>(sessionKey);

    //Todo: If no confirmation session send message saying - order has most likely been updated somewhere else

    if (confirmationSession) {
      // const storeId = confirmationSession.store_id;
      // const timeoutCacheKey = `${GLOBAL_TIMEOUT_CACHE_KEY}:${storeId.toUpperCase()}`;

      // const isExtended = await this.runWithLock(confirmationSession.store_id, async () => {
      //   let globalTimeOutDelay = await this.cacheManager.get<GlobalTimeOut>(timeoutCacheKey);
      //   console.log({ globalTimeOutDelay });
      //   if (globalTimeOutDelay?.value > 0 && confirmationSession.order_is_escalated !== true) {
      //     await this.extendTimeoutJob(job, confirmationSession, globalTimeOutDelay?.value);
      //     return true;
      //   }
      //   return false;
      // });

      // console.log({ isExtended });

      // if (isExtended) return {};

      const genericSession = await this.cacheManager.get<GenericBotSession>(confirmationSession.generic_session_key);
      if (genericSession) {
        if (confirmationSession.secondary_phone && confirmationSession.order_is_escalated !== true) {
          await this.extendTimeoutJob(job, confirmationSession);
          await this.escalateOrderConfirmation(confirmationSession);
          return;
        }

        genericSession.metadata = { order_timedout: true };
        await this.saveSession(genericSession);

        await this.updateOrderAndNotifySeller(
          confirmationSession,
          ORDER_STATUSES.CANCELLED,
          OTHER_TEXTS.CONFIRMATION_EXPIRED({ storeName: genericSession?.store?.name }),
          true,
        );
        await this.cleanUpConfirmationSession(confirmationSession);
      }
    }
  }

  // UTILITIES

  async updateOrderAndNotifySeller(
    session: OrderConfirmationSession,
    status: ORDER_STATUSES,
    reason = undefined,
    isTimeout = false,
  ) {
    const ctx = this.getCtx(session);
    let messageData = {};
    let previouslyUpdated = false;
    let responseMessageKey: MESSAGES;

    const order = await this.brokerTransport
      .send<Order>(BROKER_PATTERNS.ORDER.GET_ORDER, { _id: session.order_id })
      .toPromise();

    if (order.status === ORDER_STATUSES.PENDING) {
      this.brokerTransport
        .send<Order>(BROKER_PATTERNS.ORDER.UPDATE_ORDER_STATUS, {
          store_id: session.store_id,
          id: session.order_id,
          status,
          reason,
          url: process.env.CATLOG_WWW,
          notifyCustomerViaBot: true,
          notifySellerViaBot: false,
        })
        .toPromise();
    }

    if (status === ORDER_STATUSES.CANCELLED && isTimeout) {
      responseMessageKey = MESSAGES.ORDER_EXPIRED_MESSAGE;
      messageData = {
        orderId: session.order_id,
        url: getPublicUrl(`/o/${session.order_id}`),
        items: session.meta?.message_data?.body?.items,
        reason,
      };
    } else {
      responseMessageKey = MESSAGES.ORDER_STATUS_UPDATE_MESSAGE;
      messageData = {
        previouslyUpdated,
        orderId: session.order_id,
        status: status,
        reason: order?.cancellation_reason ?? reason,
        url: getPublicUrl(`/o/${session.order_id}`),
      };
    }

    this.notifySeller(session, responseMessageKey, messageData);

    // console.log({ ctx, session, status, reason, isTimeout });
    // if (status === ORDER_STATUSES.CANCELLED && !isTimeout) {
    //   await this.messaging.send(MESSAGES.ORDER_REJECTED_MESSAGE, ctx, { url: getPublicUrl(`/o/${session.order_id}`) });
    // }

    // if (status === ORDER_STATUSES.CANCELLED && isTimeout) {
    //   console.log('SENDING ORDER CANCELLATION MESSAGE....');
    //   let ctx = this.getCtx(session);
    //   await this.messaging.send(MESSAGES.ORDER_EXPIRED_MESSAGE, ctx, {
    //     orderId: session.order_id,
    //     url: getPublicUrl(`/o/${session.order_id}`),
    //     items: session.meta?.message_data?.body?.items,
    //   });

    //   if (session.secondary_phone && session.order_is_escalated) {
    //     ctx = { ...ctx, destinationPhone: session.secondary_phone };
    //     await this.messaging.send(MESSAGES.ORDER_EXPIRED_MESSAGE, ctx, {
    //       orderId: session.order_id,
    //       url: getPublicUrl(`/o/${session.order_id}`),
    //       items: session.meta?.message_data?.body?.items,
    //     });
    //   }
    // }
  }

  //notifies both main & secondary numbers
  public async notifySeller(session: OrderConfirmationSession, message: MESSAGES, data: any) {
    let ctx = this.getCtx(session);
    await this.messaging.send(message, ctx, data);

    if (session.secondary_phone && session.order_is_escalated) {
      ctx = { ...ctx, destinationPhone: session.secondary_phone };
      await this.messaging.send(message, ctx, data);
    }
  }

  protected async cleanUpConfirmationSession(session: OrderConfirmationSession, shouldCalcTimeoutGrace = false) {
    // try {
    //   (await this.waBotQueue.getJob(session.timeout_job_id))?.remove();
    // } catch (err) {
    //   console.log(err);
    // }

    // if (shouldCalcTimeoutGrace) {
    //   const createdAt = session.date_initiated;
    //   const global_timeout_delay = Math.ceil(new Date().getTime() - new Date(createdAt).getTime());
    //   const timeoutCacheKey = `${GLOBAL_TIMEOUT_CACHE_KEY}:${session.store_id.toUpperCase()}`;

    //   await this.runWithLock(session.store_id, async () => {
    //     let globalTimeOut = await this.cacheManager.get<GlobalTimeOut>(timeoutCacheKey);

    //     if (!Boolean(globalTimeOut)) {
    //       globalTimeOut = { value: global_timeout_delay };
    //     } else globalTimeOut.value = global_timeout_delay;

    //     await this.cacheManager.set<GlobalTimeOut>(timeoutCacheKey, globalTimeOut, {
    //       ttl: Math.floor(global_timeout_delay / 1000),
    //     });
    //   });
    // }

    const storeConfirmationCountKey = `${UTILITY_CACHE_PREFIX}:${ACTIVE_CONFIRMATION_SESSIONS}:${session.store_id}`;

    await this.cacheService.incrementWithLock(storeConfirmationCountKey, -1, ONE_HOUR);
    await this.endSession(session.key);
  }

  protected async createTimeoutJob(sessionKey: string, willRetry = false, activeConfirmations = 0) {
    const totalDelay = GET_APP_TIMEOUT('seller') + ONE_MINUTE * activeConfirmations;
    const delay = willRetry ? totalDelay / 2 : totalDelay;
    return await this.waBotQueue.add(
      QUEUES.WHATSAPP_BOT,
      {
        type: JOBS.ORDER_CONFIRMATION_TIMEOUT,
        data: {
          sessionKey,
        },
      },
      { delay: Math.ceil(delay) },
    );
  }

  protected async endSession(sessionKey: string) {
    await this.cacheManager.del(sessionKey);
  }

  protected async extendTimeoutJob(
    job: Job<WABotJob<SessionTimeOutJob>>,
    session: OrderConfirmationSession,
    global_timeout_delay: number = undefined, // IN MILLISECONDS
  ) {
    const data = job.data;
    const activeConfirmationAtStart = session.active_confirmations_at_start;
    const totalDelay = GET_APP_TIMEOUT('seller') + ONE_MINUTE * activeConfirmationAtStart;
    const delay = Boolean(session.secondary_phone) ? totalDelay / 2 : totalDelay;

    const timeOutJob = await this.waBotQueue.add(
      QUEUES.WHATSAPP_BOT,
      { ...data },
      {
        delay: Math.ceil(delay),
      },
    );

    session.timeout_job_id = timeOutJob.id.toString();
    await this.saveSession(session);

    // const timeoutCacheKey = `${GLOBAL_TIMEOUT_CACHE_KEY}:${session.store_id.toUpperCase()}`;
    // const globalTimeOut = await this.cacheManager.get<GlobalTimeOut>(timeoutCacheKey);
    // if (globalTimeOut)
    //   await this.cacheManager.set(timeoutCacheKey, globalTimeOut, { ttl: global_timeout_delay / 1000 - 20 });
  }

  protected async acquireSessionRelease(lockKey, bypass = false) {
    if (bypass !== false) return null;

    let { mutex, waitCount } = this.mutexStore[lockKey] ?? {};
    this.logger.log(`ACQUIRING LOCK FOR KEY:: ${lockKey} WITH WAIT COUNT::${waitCount ?? 0}`);

    if (!mutex) {
      const newMutex = new Mutex();
      this.mutexStore[lockKey] = { mutex: newMutex, waitCount: 0 };
      return this.mutexStore[lockKey].mutex.acquire();
    }
    this.mutexStore[lockKey].waitCount = waitCount + 1;
    return this.mutexStore[lockKey].mutex.acquire();
  }

  protected async releaseSessionLock(lockKey: string, release: MutexInterface.Releaser) {
    if (release) {
      release();
      let { waitCount } = this.mutexStore[lockKey] ?? {};
      this.logger.log(`RELEASING LOCK FOR KEY:: ${lockKey} WITH WAIT COUNT::${waitCount}`);

      if (waitCount > 0) {
        this.mutexStore[lockKey].waitCount = waitCount - 1;
        return;
      }
      delete this.mutexStore[lockKey];
    }
  }

  async escalateOrderConfirmation(confirmationSession: OrderConfirmationSession) {
    confirmationSession.order_is_escalated = true;
    await this.saveSession(confirmationSession);

    const ctx = this.getCtx(confirmationSession);
    ctx.destinationPhone = confirmationSession.secondary_phone;
    await this.sendOrderConfirmationMessage(ctx, confirmationSession.meta.message_data, true);
  }

  async removeOrderJobForGenericSession(genericSession: GenericBotSession, shouldCancelOrder: boolean = false) {
    if (genericSession?.order?.order_id && shouldCancelOrder) {
      const order = await this.brokerTransport
        .send<Order>(BROKER_PATTERNS.ORDER.GET_ORDER, {
          _id: genericSession?.order?.order_id,
        })
        .toPromise();

      //only cancel order if it's not currently cancelled
      if (order && order.status !== ORDER_STATUSES.CANCELLED) {
        await this.brokerTransport
          .send<Order>(BROKER_PATTERNS.ORDER.UPDATE_ORDER_STATUS, {
            store_id: genericSession.store.id,
            id: genericSession?.order?.order_id,
            status: ORDER_STATUSES.CANCELLED,
            reason: 'Cancelled due to customer inactivity or request',
            url: process.env.CATLOG_WWW,
            notifyCustomerViaBot: false,
          })
          .toPromise();
      }
    }

    if (
      genericSession &&
      genericSession?.store &&
      genericSession?.order?.order_id &&
      genericSession?.store?.confirm_order_before_payment &&
      genericSession?.metadata?.order_timedout !== true
    ) {
      const cacheKey = `${UTILITY_CACHE_PREFIX}:${genericSession.order.order_id.toUpperCase()}`;
      const confirmationSession = await this.cacheManager.get<OrderConfirmationSession>(cacheKey);

      if (confirmationSession) {
        await this.cleanUpConfirmationSession(confirmationSession, true);
      }
    }

    return {};
  }

  protected async runWithLock<T>(lockKey: string, fn: () => Promise<T>): Promise<T> {
    const release = await this.acquireSessionRelease(lockKey);
    try {
      return await fn();
    } finally {
      this.releaseSessionLock(lockKey, release);
    }
  }

  protected getCtx(session: OrderConfirmationSession) {
    return { botPhoneId: session.bot_phone_id, destinationPhone: session.seller_phone };
  }

  protected async saveSession(session: BotSession) {
    await this.cacheManager.set(session.key, { ...session }, { ttl: getCurrentSessionTtl(session, CACHE_TTL) });
  }
}
