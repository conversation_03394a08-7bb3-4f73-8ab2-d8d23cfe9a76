import { Injectable } from '@nestjs/common';
import { MakeOrderProcessor2 } from './order2.processor.service';
import dayjs from 'dayjs';
import { paginateOption } from '../../../interfaces/whatsapp.interface';
import { FilterQuery, Types, isValidObjectId } from 'mongoose';
import { SubscriptionTokens } from '../../subscription/utils/balance-check-and-debit';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { getDocId } from '../../../utils/functions';
import { mapPaginateQuery, mapPaginatedResponse } from '../../../utils';
import { SESSION_STATUSES, WABotSession } from '../whatsapp-bot.schema';
import { PaginatedQueryDto } from '../../../models/dtos/PaginatedDto';

@Injectable()
export class TokenSessionService extends MakeOrderProcessor2 {
  async getTokenUsageByDate(filterCriteria: any, options: paginateOption) {
    filterCriteria.is_billable = true;
    const firstPaidSession = await this.waBotSessionModel.findOne(filterCriteria).sort({ created_at: 1 });

    const currentDate = dayjs();
    let totalDays = 0;

    if (!firstPaidSession) {
      return {
        history: [],
        total: totalDays,
        per_page: options.limit,
        sort: options.sort.created_at === 1 ? 'ASC' : 'DESC',
        page: options.page,
        total_pages: Math.ceil(totalDays / options.limit),
        next_page: options.page < Math.ceil(totalDays / options.limit) ? options.page + 1 : null,
        prev_page: options.page > 1 ? options.page - 1 : null,
      };
    }

    const firstSessionDate = dayjs(firstPaidSession.created_at);
    totalDays = currentDate.startOf('day').diff(firstSessionDate.startOf('day'), 'day') + 1; //total days equals the number od days from current date to the date of the first session

    // Calculate the start and end dates based on the options
    let startDate: dayjs.Dayjs, endDate: dayjs.Dayjs;

    //assuming we only want to fetch in descending order
    startDate =
      options.page === 1
        ? currentDate.startOf('day')
        : currentDate.startOf('day').subtract((options.page - 1) * options.limit, 'day'); //start date starts at the current date for page 1, but adjusts by page size for other pages
    endDate = startDate.subtract(options.limit - 1, 'day').startOf('day');
    endDate = endDate.isAfter(firstSessionDate) ? endDate : firstSessionDate.startOf('day');

    const dataAggregation = this.waBotSessionModel.aggregate([
      {
        $match: {
          ...filterCriteria,
          store: Types.ObjectId(filterCriteria.store),
          created_at: {
            $gte: endDate.toDate(),
            $lte: startDate.toDate(),
          },
        },
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$created_at' },
          },
          tokens_used: { $sum: 1 },
        },
      },
      {
        $sort: {
          _id: options.sort.created_at,
        },
      },
      {
        $project: {
          _id: 0,
          date: '$_id',
          tokens_used: 1,
        },
      },
    ]);

    const results = await dataAggregation.exec();
    const dateMap = new Map(results.map((item) => [item.date, item.tokens_used]));

    // Generate all dates in the range
    let dateRange = [];
    for (let d = dayjs(startDate); d.isAfter(endDate) || d.isSame(endDate); d = d.subtract(1, 'day')) {
      const formattedDate = d.format('YYYY-MM-DD');
      dateRange.push({
        date: formattedDate,
        tokens_used: dateMap.get(formattedDate) || 0, // Use existing count or default to 0
      });
    }

    return {
      data: dateRange,
      total: totalDays,
      per_page: options.limit,
      sort: options.sort.created_at === 1 ? 'ASC' : 'DESC',
      page: options.page,
      total_pages: Math.ceil(totalDays / options.limit),
      next_page: options.page < Math.ceil(totalDays / options.limit) ? options.page + 1 : null,
      prev_page: options.page > 1 ? options.page - 1 : null,
    };
  }

  async getTokenAverageDailyUsage(storeId: string, days = 30): Promise<number> {
    const today = new Date();

    // Find the earliest date a billable session was created for this store
    const earliestSession = await this.waBotSessionModel
      .findOne({ store: storeId, is_billable: true })
      .sort({ created_at: 1 })
      .exec();

    if (!earliestSession) {
      // No billable sessions found, return 0
      return 0;
    }

    const earliestSessionDate = earliestSession.created_at;

    // Calculate the number of days since the earliest billable session
    const diffTime = Math.abs(today.getTime() - earliestSessionDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    // Use the actual number of days if less than 30, otherwise use 30
    const totalDays = Math.min(diffDays, days);

    const daysAgo = new Date(today);
    daysAgo.setDate(today.getDate() - totalDays);

    // Count the billable sessions within the last `totalDays`
    const sessionsCount = await this.waBotSessionModel
      .countDocuments({
        store: storeId,
        is_billable: true,
        created_at: { $gte: daysAgo, $lte: today },
      })
      .exec();

    // Calculate and return the average daily usage
    const averageDailyUsage = sessionsCount > 0 ? sessionsCount / totalDays : 0;

    return averageDailyUsage;
  }

  async checkTokenBalance(subscriptionId): Promise<boolean> {
    const tokens = await this.brokerTransport
      .send<SubscriptionTokens>(BROKER_PATTERNS.PAYMENT.TOKENS.GET_TOKEN_BALANCE, {
        filter: { _id: getDocId(subscriptionId) },
        addPlan: false,
      })
      .toPromise();

    return tokens?.error ? false : tokens.data.canDebit;
  }

  // CONTROLLER METHODS (CALLED BY CONTROLLERS)
  async getPaginatedBotSessions(
    { ...filterQuery }: FilterQuery<WABotSession> & { store: any },
    paginationQuery: PaginatedQueryDto,
  ) {
    if (filterQuery.from) {
      filterQuery.created_at = {
        $gte: new Date(filterQuery.from as any),
        $lte: new Date(filterQuery.to as any),
      };
      delete filterQuery.from;
      delete filterQuery.to;
    }

    if (filterQuery.search) {
      filterQuery.customer = isValidObjectId(filterQuery.search) ? filterQuery.search : undefined;
      delete filterQuery.search;
    }

    if (paginationQuery.sort === 'ASC') {
      paginationQuery.sort = { created_at: 1 };
    }

    if (paginationQuery.sort === 'DESC') {
      paginationQuery.sort = { created_at: -1 };
    }

    const result = await this.waBotSessionModel.paginate(filterQuery, {
      ...mapPaginateQuery(paginationQuery),
      populate: ['customer'],
    });

    result.docs = (await Promise.all(
      result.docs.map(async (doc) => {
        return doc.toJSON();
      }),
    )) as any;

    return {
      data: result.docs,
      ...mapPaginatedResponse(result),
    };
  }

  async getBotSessionStats(storeId: string, filter?: any) {
    const created_at = filter
      ? {
          $gte: new Date(filter.from),
          $lt: new Date(filter.to),
        }
      : undefined;

    const filterQuery = created_at
      ? {
          store: storeId,
          created_at,
        }
      : { store: storeId };

    const sessions = await this.waBotSessionModel.find({ ...filterQuery }).lean();
    const stats = {
      total_sessions: sessions?.length ?? 0,
      total_completed: 0,
      total_uncompleted: 0,
      total_ongoing: 0,
      sessions: [],
    };

    for (const session of sessions) {
      stats.sessions.push({ time: session.created_at, value: session._id });
      if (session?.status === SESSION_STATUSES.COMPLETED) {
        stats.total_completed = stats.total_completed + 1;
      } else if (
        [SESSION_STATUSES.ONGOING, SESSION_STATUSES.CONFIRMATION_PENDING, SESSION_STATUSES.PAYMENT_PENDING].includes(
          session?.status,
        )
      ) {
        stats.total_ongoing = stats.total_ongoing + 1;
      } else {
        stats.total_uncompleted = stats.total_uncompleted + 1;
      }
    }
    return stats;
  }
}
