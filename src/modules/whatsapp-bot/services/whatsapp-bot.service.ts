import { BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import dayjs, { Dayjs } from 'dayjs';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { DELIVERY_STATUS } from '../../../enums/deliveries';
import { PLAN_TYPE } from '../../../enums/plan.enum';
import { JOBS, QUEUES } from '../../../enums/queues.enum';
import {
  INTENTS,
  INTERACTIVE_MESSAGE_TYPES,
  LOCKED_STEPS,
  WHATSAPP_BOT_APP_EVENTS,
  WHATSAPP_BOT_EVENTS,
  WHATSAPP_BOT_STEPS,
} from '../../../enums/whatsapp.enum';
import {
  BotContext,
  BotProcessorResult,
  BotStateMachine,
  CheckInSession,
  GenericBotSession,
  OrderConfirmationSession,
  SESSION_TYPES,
  WhatsappMessageDto,
} from '../../../interfaces/whatsapp.interface';
import { CompleteItemsSelectionDto } from '../../../models/dtos/whatsapp.dto';
import { formatPhoneNumber, getAppEnv, isProduction, isUsingProdDBLocally, sluggify } from '../../../utils';
import checkStoreIsOpen from '../../../utils/check-if-store-is-open';
import { getDocId, paramsFromObject, to } from '../../../utils/functions';
import { PhoneCodesLean } from '../../../utils/phone-codes';
import {
  CACHE_TTL,
  CATLOG_SUPPORT_PHONE,
  CHECK_IN_CACHE_PREFIX,
  GENERIC_CACHE_PREFIX,
  GET_APP_TIMEOUT,
  UTILITY_CACHE_PREFIX,
  WA_BOT_PHONE_IDS,
  WA_BOT_PHONE_NOS,
} from '../../../utils/wabot-constants';
import { Cart } from '../../cart/cart.schema';
import { Customer } from '../../orders/customers/customer.schema';
import { ORDER_STATUSES, Order } from '../../orders/order.schema';
import { Plan } from '../../plan/plan.schema';
import { Store } from '../../store/store.schema';
import { Subscription } from '../../subscription/subscription.schema';
import {
  MessageData,
  getChowbotUrl,
  getContext,
  getCurrentSessionTtl,
  getOrderItemsText,
  getPublicUrl,
  getStoreCode,
  normalizeOrderItemsData,
  unnormalizePhone,
  wrapWaMessage,
} from '../utils/functions.utils';
import { CTA_REPLIES, MESSAGES, USER_COMMANDS } from '../utils/messages.utils';
import { TokenSessionService } from './token-session.service';
import { SESSION_STATUSES } from '../whatsapp-bot.schema';
import { Mutex } from 'async-mutex';

const mutex = new Mutex();
export class WhatsappChatbotService extends TokenSessionService {
  // PUBLIC METHODS (CALLED BY OTHER EXTERNAL SERVICES)
  async handleMessageStatus(body: WhatsappMessageDto) {
    try {
      const msgData = wrapWaMessage(body);
      const userPhone = msgData.getStatusRecipient();
      const botPhone = msgData.getBotPhone();

      const genericCacheKey = `${GENERIC_CACHE_PREFIX}:${userPhone}-${botPhone}`;
      const genericSession = await this.cacheManager.get<GenericBotSession>(genericCacheKey);

      if (genericSession && genericSession.store?.id) {
        if (!genericSession.conversation_id) {
          genericSession.conversation_id = msgData.getConversationId();
          await this.saveSession(genericSession);
          await this.waBotSessionModel.findOneAndUpdate(
            { _id: genericSession.db_session },
            { conversation_id: msgData.getConversationId() },
          );
        } else if (genericSession.conversation_id && genericSession.conversation_id !== msgData.getConversationId()) {
          // USER HAS CROSSED OVER TO NEW CONVERSATION
          await this.waBotSessionModel.findOneAndUpdate(
            { _id: genericSession.db_session },
            { conversation_id: msgData.getConversationId() },
          );
        }
      }
    } catch (err) {
      this.logger.error(err);
    }
  }

  async handleMessage(body: WhatsappMessageDto, isInteractive?: boolean) {
    const msgData = wrapWaMessage(body);
    const userPhone = msgData.getWaId();
    const botPhone = msgData.getBotPhone();

    const genericCacheKey = `${GENERIC_CACHE_PREFIX}:${userPhone}-${botPhone}`;
    const checkInCacheKey = `${CHECK_IN_CACHE_PREFIX}:${userPhone}-${botPhone}`;
    // const checkInCacheKey = `${CHECK_IN_CACHE_PREFIX}:${userPhone}-${WA_BOT_PHONE_NOS[getAppEnv()][0]}`;

    let genericSession = await this.cacheManager.get<GenericBotSession>(genericCacheKey);

    if (
      !Boolean(genericSession?.store?.id) &&
      msgData.getInteractiveMessageType() === INTERACTIVE_MESSAGE_TYPES.NFM_REPLY
    ) {
      if ((await this.sellerProcessor.handleSellerMessage(msgData)) !== false) return;
    }

    if (!genericSession) {
      if (isInteractive) {
        const checkInSession = await this.cacheManager.get<CheckInSession>(checkInCacheKey);
        if (checkInSession) {
          await this.processCheckInStep(checkInSession, msgData);
        } else {
          await this.messaging.send(MESSAGES.EXPIRED_TEXT, getContext(genericSession), {});
        }
        return;
      }
      const job = await this.createTimeoutJob(genericCacheKey);
      const newSession: GenericBotSession = this.createNewGenericSession(genericCacheKey, msgData, job.id.toString());
      await this.cacheManager.set(genericCacheKey, { ...newSession }, { ttl: CACHE_TTL });
      genericSession = newSession;
    }

    // TEST MESSAGES
    {
      // const mockData = { store: 'Tasty Kitchen', message: 'Hey where have you been na?', slug: 'tasty-kitchen' };
      // this.messaging.send(MESSAGES.AUTO_CHECK_IN_MESSAGE, getContext(genericSession), mockData);
      // return;
    }

    // LOG MESSAGE
    this.captureAppEvent(
      WHATSAPP_BOT_APP_EVENTS.RECEIEVED_CUSTOMER_MESSAGE,
      genericSession,
      JSON.stringify(msgData),
      userPhone,
    );

    try {
      const {
        currentStep,
        secondarySteps,
        intent,
        popHistoryAndResendPrevious,
        endSession,
        stepMessage,
        meta,
        clear,
        restart,
        repeat,
        startFromItems,
      } = (await this.processCurrentStep(genericSession, msgData)) ?? {};

      const ctx = getContext(genericSession);
      genericSession.last_incoming_message_time = new Date();

      if (endSession === true) {
        await this.endSession(ctx, genericSession, false);
      } else if (repeat === true) {
        //resends the previous message
        const history = genericSession.history;
        const { data, type } = history[history.length - 1]?.step_message;

        await this.messaging.send(type, ctx, data);
        await this.cacheManager.set<GenericBotSession>(
          genericCacheKey,
          {
            ...genericSession,
          },
          { ttl: getCurrentSessionTtl(genericSession, CACHE_TTL) },
        );
      } else if (restart === true) {
        if (genericSession.store) {
          let index = genericSession.history.findIndex((h) => h.step === WHATSAPP_BOT_STEPS.WELCOME_MESSAGE);
          if (index === -1) {
            index = genericSession.history.findIndex((h) => h.step === WHATSAPP_BOT_STEPS.ITEMS_SELECTION);
          }

          if (index !== -1) {
            delete genericSession.order;
            delete genericSession.payment;
            delete genericSession.search_data;
            delete genericSession.secondary_steps;
            delete genericSession.metadata;

            genericSession.history = [...genericSession.history.slice(0, index + 1)];
            await this.sellerProcessor.removeOrderJobForGenericSession(genericSession, true);

            if (startFromItems) {
              const data = await this.placeNewOrder(genericSession);

              genericSession.step = data.currentStep;
              genericSession.history = [
                ...genericSession.history,
                {
                  step: data.currentStep,
                  secondary_steps: data?.secondarySteps ?? [],
                  timestamp: new Date().toISOString(),
                  step_message: data.stepMessage,
                },
              ];
              genericSession.secondary_steps = data?.secondarySteps ?? [];
              genericSession.intent = data.intent;
              genericSession.metadata = { ...genericSession.metadata, ...(data?.meta ?? {}) };
            } else {
              const {
                step_message: { type, data },
                step,
              } = genericSession.history[index];

              genericSession.step = step;
              await this.messaging.send(type, ctx, data);
            }

            await this.cacheManager.set<GenericBotSession>(genericCacheKey, genericSession, { ttl: CACHE_TTL });
            return;
          }
        }
      } else if (popHistoryAndResendPrevious === true) {
        let lastIndex = genericSession.history.length - 1;
        if (lastIndex > 1) {
          const newHistory = [...genericSession.history.slice(0, lastIndex)];

          lastIndex = newHistory.length - 1;
          const { data, type } = newHistory[lastIndex].step_message;
          await this.messaging.send(type, ctx, data);

          genericSession.history = newHistory;
          genericSession.step = newHistory[lastIndex].step;
          genericSession.secondary_steps = newHistory[lastIndex].secondary_steps;
          await this.cacheManager.set<GenericBotSession>(
            genericCacheKey,
            {
              ...genericSession,
            },
            { ttl: getCurrentSessionTtl(genericSession, CACHE_TTL) },
          );
        }
      } else if (currentStep) {
        let sessionCopy = { ...genericSession };
        if (clear === true) {
          delete sessionCopy.order;
          delete sessionCopy.payment;
          delete sessionCopy.search_data;
          delete sessionCopy.secondary_steps;
          delete sessionCopy.metadata;

          try {
            await (await this.waBotQueue.getJob(sessionCopy.timeout_job))?.remove();
          } catch (err) {
            //do nothing
          }
          const newJob = await this.createTimeoutJob(genericCacheKey);
          sessionCopy = {
            ...sessionCopy,
            ...this.createNewGenericSession(genericCacheKey, msgData, newJob.id.toString()),
          };
        }

        sessionCopy.step = currentStep;
        sessionCopy.secondary_steps = secondarySteps;
        sessionCopy.intent = intent;
        sessionCopy.history = [
          ...sessionCopy.history,
          {
            step: currentStep,
            secondary_steps: secondarySteps,
            timestamp: new Date().toISOString(),
            step_message: stepMessage,
          },
        ];
        sessionCopy.metadata = { ...sessionCopy.metadata, ...(meta ?? {}) };

        await this.cacheManager.set<GenericBotSession>(
          genericCacheKey,
          { ...sessionCopy },
          { ttl: getCurrentSessionTtl(sessionCopy, CACHE_TTL) },
        );
      } else if (!currentStep) {
        //Todo: this might cause issues
        await this.cacheManager.set<GenericBotSession>(
          genericCacheKey,
          { ...genericSession },
          { ttl: getCurrentSessionTtl(genericSession, CACHE_TTL) },
        );
      }
    } catch (e) {
      this.messaging.send(MESSAGES.ERROR_MESSAGE, getContext(genericSession));
      this.logger.error(e.message);
      console.error(e.message);
    }
  }

  async processOrderPaymentSuccessful(sessionId: string, paymentReference: string, receiptId: string) {
    const session = await this.validateAndReturnSessionCache(this.removeSessionPrefix(sessionId)); //remove session key for validation

    if (!session) {
      this.logger.error(`BOT PAYMEMT SUCCESSFUL BUT NO SESSION: ${sessionId}, Payment Reference: ${paymentReference}`);
      return;
    }

    await this.processPaymentSuccessfulStep(session, receiptId);
  }

  async handleOrderStatusUpdate(payload: {
    status: ORDER_STATUSES;
    session_id: string;
    reason?: string;
    storeId: string;
    notifyCustomer?: boolean;
    notifySeller?: boolean;
    orderId: string;
  }) {
    const { session_id, reason, status, storeId, notifyCustomer, notifySeller, orderId } = payload;
    const order = await this.brokerTransport
      .send<Order>(BROKER_PATTERNS.ORDER.GET_ORDER, {
        _id: orderId,
      })
      .toPromise();

    if (notifySeller) {
      let messageData: any;
      const store = await this.brokerTransport
        .send<Store>(BROKER_PATTERNS.STORE.GET_STORE_LEAN, { filter: { _id: storeId } })
        .toPromise();
      const sellerContext = { botPhoneId: WA_BOT_PHONE_IDS[getAppEnv()], destinationPhone: store?.phone };
      switch (status) {
        case ORDER_STATUSES.CANCELLED:
          const normalizedItems = normalizeOrderItemsData(order.items);
          const itemsString = getOrderItemsText(normalizedItems, order?.currency, true);
          messageData = {
            orderId: orderId,
            url: getPublicUrl(`/o/${orderId}`),
            items: itemsString,
            reason,
          };
          await this.messaging.send(MESSAGES.ORDER_EXPIRED_MESSAGE, sellerContext, messageData);
          break;
        default:
          const confirmationSession: OrderConfirmationSession = {
            type: SESSION_TYPES.ORDER_CONFIRMATION,
            key: `${UTILITY_CACHE_PREFIX}:${orderId}`,
            date_initiated: new Date().toISOString(),
            bot_phone_id: WA_BOT_PHONE_IDS[getAppEnv()][0],
            seller_phone: formatPhoneNumber(store?.phone),
            store_name: store?.name,
            store_currency: order?.currency,
            timeout_job_id: '',
            generic_session_key: session_id,
            store_id: storeId,
            order_id: orderId,
            secondary_phone: null,
            active_confirmations_at_start: 0,
          };

          const messageKey = MESSAGES.ORDER_STATUS_UPDATE_MESSAGE;
          messageData = {
            previouslyUpdated: false,
            orderId,
            status: status,
            reason: order?.cancellation_reason ?? reason,
            url: getPublicUrl(`/o/${orderId}`),
          };

          await this.sellerProcessor.notifySeller(confirmationSession, messageKey, messageData);
          break;
      }
    }

    if (notifyCustomer) {
      const session = await this.cacheManager.get<GenericBotSession>(this.addSessionPrefix(session_id));
      if (!session || (storeId && session.store.id !== storeId)) {
        console.log('Invalid or expired session: Error Quietly');
        return {}; //error out quietly
      }

      switch (status) {
        case ORDER_STATUSES.CANCELLED:
          await this.processOrderRejectionStep(session, reason);
          break;
        case ORDER_STATUSES.PROCESSING:
          await this.processOrderConfirmationStep(session, order);
          break;
        case ORDER_STATUSES.FULFILLED:
          await this.messaging.send(MESSAGES.ORDER_FULFILLED, getContext(session), { storeName: session.store.name });
          break;
      }
    }
  }

  async handleFailedAutoDelivery(data: any) {
    try {
      let order, items;

      const store = await this.brokerTransport
        .send<Store>(BROKER_PATTERNS.STORE.GET_STORE_LEAN, { filter: { _id: data.storeId } })
        .toPromise();

      if (data?.orderId) {
        order = await this.brokerTransport
          .send<Order>(BROKER_PATTERNS.ORDER.GET_ORDER, { _id: data.orderId })
          .toPromise();

        const orderItems = normalizeOrderItemsData(order.items);
        items = getOrderItemsText(orderItems, store.currencies?.default, true);
      } else if (data?.cartItems) {
        items = getOrderItemsText(data?.cartItems, store.currencies?.default, true);
      } else {
        console.log(`CART ITEMS OR ORDER ITEMS IS NEEDED TO SEND NOTIFICATION: DELIVERY ${data?.deliveryId}`);
        return;
      }

      const ctx: BotContext = {
        botPhoneId: WA_BOT_PHONE_IDS[getAppEnv()][0],
        destinationPhone: formatPhoneNumber(store.phone),
      };

      await this.messaging.send(MESSAGES.AUTO_DELIVERY_FAILED, ctx, {
        items,
        deliveryId: data.deliveryId,
        customerName: order ? order?.delivery_info?.name : data?.customerName,
        courier: data?.courier,
        orderId: order ? data?.orderId : '-',
        error: data.error ?? 'Unknown error',
      });
    } catch (e) {
      console.error(e);
      console.log("COULDN'T SEND DELIVERY FAILED NOTIFICATION");
    }
  }

  async handleAutoDeliveryBooked(data: any) {
    try {
      const store = await this.brokerTransport
        .send<Store>(BROKER_PATTERNS.STORE.GET_STORE_LEAN, { filter: { _id: data.storeId } })
        .toPromise();

      const order = await this.brokerTransport
        .send<Order>(BROKER_PATTERNS.ORDER.GET_ORDER, { _id: data.orderId })
        .toPromise();

      // const orderItems = normalizeOrderItemsData(order.items);
      // items = getOrderItemsText(orderItems, store.currencies?.default, true);

      const ctx: BotContext = {
        botPhoneId: WA_BOT_PHONE_IDS[getAppEnv()][0],
        destinationPhone: formatPhoneNumber(store.phone),
      };

      await this.messaging.send(MESSAGES.AUTO_DELIVERY_BOOKED, ctx, {
        orderId: data.orderId,
        customerName: order?.delivery_info?.name ?? 'Customer',
        courier: data.courier,
        trackingUrl: data.trackingUrl,
      });
    } catch (e) {
      console.error(e);
      console.log("COULDN'T SEND DELIVERY BOOKED NOTIFICATION");
    }
  }

  async completeItemsSelection(data: CompleteItemsSelectionDto) {
    const session = await this.validateAndReturnSessionCache(data.session_id);
    const cart = await this.brokerTransport
      .send<Cart>(BROKER_PATTERNS.CART.GET_CART, { id: data.cart_id })
      .toPromise();

    // if (cart && session.step === WHATSAPP_BOT_STEPS.ITEMS_SELECTION) { Todo: coming back to this
    if (cart && session) {
      await this.processItemSelectionStep(session, cart);
    }
  }

  async getSession(sessionId: string, store: string) {
    const session = await this.validateAndReturnSessionCache(sessionId, store);
    return {
      name: session.customer?.name,
      phone: session.to,
      cart_id: session.order?.cart_id,
    };
  }

  async getSessionEvents(sessionId: string, store: string) {
    const session = await this.waBotSessionModel.findOne({ _id: sessionId }).populate('customer');
    if (!session || session.store.toString() !== store) throw new BadRequestException('Invalid request');

    const events = await this.posthog.getChowbotEvents({
      distinct_id: formatPhoneNumber((session.customer as Customer).phone),
      filters: [
        { key: 'session_id', value: session._id, operator: 'exact' },
        { key: 'is_production', value: isProduction() ? 'true' : 'false', operator: 'exact' },
        { key: 'is_session_event', value: 'true', operator: 'exact' },
      ],
    });

    if (events.error) throw new InternalServerErrorException(events.error);

    return events.data;
  }

  async handleDeliveryStatusUpdate(data: {
    status: DELIVERY_STATUS;
    phone: string;
    items: string;
    trackingUrl: string;
    storeName: string;
    storePhone: string;
    courier: string;
    orderId: string;
  }) {
    const ctx: BotContext = { botPhoneId: WA_BOT_PHONE_IDS[getAppEnv()][0], destinationPhone: data.phone };

    await this.messaging.send(MESSAGES.DELIVERY_STATUS, ctx, {
      status: data.status,
      items: data.items,
      trackingUrl: data.trackingUrl,
      storeName: data.storeName,
      courier: data.courier,
    });
  }

  // PROCESSOR METHODS
  private async processCurrentStep(session: GenericBotSession, data?: MessageData): BotProcessorResult {
    // session = await this.extendSession(session);
    const globalProcessorResult = await this.processGlobalMessages(session, data);
    if (globalProcessorResult) return globalProcessorResult;

    const primaryStepResult = await this.STATE_MACHINE[session.step]?.(session, data);
    if (primaryStepResult) return primaryStepResult;

    if (session.secondary_steps?.length > 0) {
      for (let i = 0; i < session.secondary_steps?.length; i++) {
        const step = session.secondary_steps[i];
        const secondaryStepResult = await this.STATE_MACHINE[step]?.(session, data);
        if (secondaryStepResult) return secondaryStepResult;
      }
      await this.handleUnexpectedResponse(getContext(session), session);
    }
  }

  private async processGlobalMessages(session: GenericBotSession, data?: MessageData) {
    const ctx = getContext(session);
    const replyBtnId = data.getInteractiveMessage()?.button_reply?.id;

    // GLOBAL BUTTONS
    if (replyBtnId) {
      switch (replyBtnId) {
        case sluggify(CTA_REPLIES.END_PROCESS):
          return { endSession: true };
        case sluggify(CTA_REPLIES.CONTINUE_PROCESS):
          return { popHistoryAndResendPrevious: true };
        case sluggify(CTA_REPLIES.RESUME_PROCESS):
          return { repeat: true };
        case sluggify(CTA_REPLIES.MAIN_OPTIONS):
          return { restart: true };
        case sluggify(CTA_REPLIES.RESTART_PROCESS):
          return { restart: true };
        case sluggify(CTA_REPLIES.RESTART_NEW_ORDER):
          return { restart: true, startFromItems: true };
        case sluggify(CTA_REPLIES.CANCEL_ORDER):
          await this.sellerProcessor.removeOrderJobForGenericSession(session, true);
          const stepMessage = await this.messaging.send(MESSAGES.ORDER_CANCELLED, ctx, {
            storeName: session.store.name,
            reason: null,
          });
          return { currentStep: WHATSAPP_BOT_STEPS.ORDER_CANCELLED, stepMessage, intent: INTENTS.PLACE_ORDER };
        case sluggify(CTA_REPLIES.CONTACT_SUPPORT):
          await this.messaging.send(MESSAGES.SUPPORT_CONTACT, ctx, {
            name: 'Chowbot Support',
            phone: CATLOG_SUPPORT_PHONE,
          });
          return {};
        case sluggify(CTA_REPLIES.CONTACT_RESTAURANT):
          this.captureMessageEvent(WHATSAPP_BOT_EVENTS.SELECTED_CONTACT_SUPPORT, session);
          await this.messaging.send(MESSAGES.STORE_CONTACT, ctx, {
            name: session.store?.name,
            phone: session.store?.contact_phone,
          });
          this.captureMessageEvent(WHATSAPP_BOT_EVENTS.RECIEVED_SUPPORT_CONTACT, session, false);
          return {};
        case sluggify(CTA_REPLIES.SEARCH_FOR_A_STORE):
          return {
            currentStep: WHATSAPP_BOT_STEPS.SEARCH_STORE,
            intent: INTENTS.SEARCH_STORE,
            stepMessage: await this.messaging.send(MESSAGES.SEARCH_STORE_MESSAGE, ctx),
          };
        case sluggify(CTA_REPLIES.FIND_ANOTHER_STORE):
          return {
            currentStep: WHATSAPP_BOT_STEPS.SEARCH_STORE,
            intent: INTENTS.SEARCH_STORE,
            stepMessage: await this.messaging.send(MESSAGES.SEARCH_STORE_MESSAGE, ctx),
          };
        case sluggify(CTA_REPLIES.OTHER_METHODS):
          this.captureMessageEvent(WHATSAPP_BOT_EVENTS.SELECTED_OTHER_PAYMENT_METHODS, session);
          await this.messaging.send(MESSAGES.OTHER_PAYMENT_METHOD_MESSAGE, ctx, {
            name: session.customer.name,
            url: `${process.env.CATLOG_WWW}/pay/${session?.order?.invoice_id}?chowbotPhone=${unnormalizePhone(
              session.to,
              PhoneCodesLean,
            )}&byCustomer=true`,
          });
          return {};
      }
    }

    // PROCESS USER COMMANDS
    switch (data?.getMessageBody()?.replace('/', '').toUpperCase()) {
      case USER_COMMANDS.HELP:
        await this.messaging.send(MESSAGES.HELP_MESSAGE, ctx, {});
        return {};
      case USER_COMMANDS.GO_BACK:
        const isLockedStep = process.env.NODE_ENV === 'production' && LOCKED_STEPS.includes(session.step);
        if (isLockedStep) {
          switch (session.step) {
            case WHATSAPP_BOT_STEPS.WELCOME_MESSAGE:
              return {};
            default:
              await this.messaging.send(MESSAGES.ORDER_NAVIGATION_RESTRICTED, ctx, {});
              return {};
          }
        } else {
          if (session.db_session) this.captureMessageEvent(WHATSAPP_BOT_EVENTS.WENT_BACK, session);
          return { popHistoryAndResendPrevious: true };
        }
      case USER_COMMANDS.SWITCH: {
        const data = await this.getBranchesMsgData(session);
        if (data) {
          const branchesData = {
            storeName: session.store.name,
            branches: data,
          };
          const stepMessage = await this.messaging.send(MESSAGES.SWITCH_BRANCHES, ctx, branchesData);
          return { currentStep: WHATSAPP_BOT_STEPS.SELECT_BRANCH, intent: INTENTS.NONE, stepMessage };
        }
        return {};
      }
      case USER_COMMANDS.MENU:
        if (session?.store?.use_menu_ordering) {
          return await this.sendStoreMenuAndUrl(ctx, session);
        }
      case USER_COMMANDS.SEARCH:
        return {
          currentStep: WHATSAPP_BOT_STEPS.SEARCH_STORE,
          intent: INTENTS.SEARCH_STORE,
          stepMessage: await this.messaging.send(MESSAGES.SEARCH_STORE_MESSAGE, ctx),
        };
      case USER_COMMANDS.END:
        return { endSession: true };
      case USER_COMMANDS.MAIN:
        if (session.store?.storeCode) {
          const store = await this.getStoreFromCode(session.store.storeCode);
          await this.cacheStoreInfo(session, store);
          return { restart: true };
        }
      case USER_COMMANDS.RESTART:
        return { restart: true };
      case USER_COMMANDS.RESEND:
        return { repeat: true };
      default:
    }

    const storeCode = getStoreCode(data);
    if (storeCode && session?.store) {
      if (storeCode === session.store.storeCode) {
        const stepMessage = await this.messaging.send(MESSAGES.SAME_STORE_INITIATION_MESSAGE, ctx, {
          storeName: session.store.name,
        });

        return {
          currentStep: WHATSAPP_BOT_STEPS.SAME_STORE_INITIATION,
          intent: INTENTS.NONE,
          stepMessage,
          meta: { newStoreCode: storeCode },
        };
      }

      const store = await this.getStoreFromCode(storeCode);

      if (store) {
        const storeUnavailableResult = await this.checkStoreAvailability(session, store, ctx);
        if (storeUnavailableResult) return storeUnavailableResult;
        const stepMessage = await this.messaging.send(MESSAGES.DIFFERENT_STORE_INITIATION_MESSAGE, ctx, {
          currentStoreName: session.store.name,
          newStoreName: store.name,
        });

        return {
          currentStep: WHATSAPP_BOT_STEPS.DIFFERENT_STORE_INITIATION,
          intent: INTENTS.NONE,
          meta: { newStoreCode: storeCode },
          stepMessage,
        };
      }
      await this.messaging.send(MESSAGES.STORE_NOT_FOUND_MESSAGE, ctx, true);
      return {};
    } else if (storeCode && !session.store) {
      return await this.handleStoreCodeMessage(storeCode, session, data.getName(), true);
    }
  }

  // UTILITY METHODS

  private async extendSession(session: GenericBotSession) {
    const timeElapsed = new Date().getTime() - new Date(session.date_initiated).getTime();
    const timeOutJob = await this.waBotQueue.getJob(session.timeout_job);
    const timeOutJobDelay = timeOutJob?.opts?.delay;
    const extensionInterval = 1000 * 60 * 5; // FIVE MINS

    if (timeOutJobDelay - timeElapsed <= extensionInterval) {
      await timeOutJob.remove();
      const newJob = await this.createTimeoutJob(session.key);
      session.timeout_job = newJob.id.toString();
      session.date_initiated = new Date().toISOString();
      await this.saveSession(session);
    }
    return session;
  }

  private createNewGenericSession(cacheKey: string, data: MessageData, timeoutJobId: string): GenericBotSession {
    return {
      last_incoming_message_time: new Date(),
      timeout_job: timeoutJobId,
      type: SESSION_TYPES.GENERIC,
      key: cacheKey,
      date_initiated: new Date().toISOString(),
      history: [{ step: WHATSAPP_BOT_STEPS.START, timestamp: new Date().toISOString() }],
      intent: INTENTS.NONE,
      step: WHATSAPP_BOT_STEPS.START,
      from: data.getWaId(),
      to: data.getBotPhone(),
      phone_id: data.getBotPhoneId(),
      search_data: {
        page: 0,
      },
      metadata: {},
    };
  }

  private async createTimeoutJob(cacheKey: string) {
    return await this.waBotQueue.add(
      QUEUES.WHATSAPP_BOT,
      {
        type: JOBS.WABOT_SESSION_TIMEOUT,
        data: {
          sessionKey: cacheKey,
        },
      },
      { delay: GET_APP_TIMEOUT('generic') },
    );
  }

  public async updatePaymentId(dbSessionId: string, paymentId: string) {
    return await this.waBotSessionModel.findByIdAndUpdate(dbSessionId, { payment: paymentId });
  }

  private async validateAndReturnSessionCache(key: string, store?: string) {
    const session = await this.cacheManager.get<GenericBotSession>(this.addSessionPrefix(key));
    if (!session || (store && session.store.id !== store)) throw new BadRequestException('Invalid or expired session');
    return session;
  }

  private STATE_MACHINE: BotStateMachine = {
    [WHATSAPP_BOT_STEPS.START]: (s, d) => this.processStartStep(s, d),
    [WHATSAPP_BOT_STEPS.SAME_STORE_INITIATION]: (s, d) => this.processStoreInitiationStep(s, d),
    [WHATSAPP_BOT_STEPS.DIFFERENT_STORE_INITIATION]: (s, d) => this.processStoreInitiationStep(s, d),
    [WHATSAPP_BOT_STEPS.WELCOME_MESSAGE]: (s, d) => this.processWelcomeStep(s, d),
    [WHATSAPP_BOT_STEPS.STORE_SEARCH_RESULTS]: (s, d) => this.processSearchResultsStep(s, d),
    [WHATSAPP_BOT_STEPS.SEARCH_STORE]: (s, d) => this.processSearchStoreStep(s, d),
    [WHATSAPP_BOT_STEPS.STORE_SEARCH_NO_RESULTS]: (s, d) => this.processNoSearchResultsStep(s, d),
    [WHATSAPP_BOT_STEPS.CHAT_INITIATION_EXISTING_CUSTOMER]: (s, d) => this.processExistingCustomerStep(s, d),
    [WHATSAPP_BOT_STEPS.ITEMS_CONFIRMATION]: (s, d) => this.processItemsConfirmation(s, d),
    [WHATSAPP_BOT_STEPS.DELIVERY_METHOD_SELECTION]: (s, d) => this.processDeliveryMethodStep(s, d),
    [WHATSAPP_BOT_STEPS.DELIVERY_INFO_FORM]: (s, d) => this.processDeliveryFormStep(s, d),
    [WHATSAPP_BOT_STEPS.DELIVERY_INFO_CONFIRMATION]: (s, d) => this.processDeliveryInfoDetailsStep(s, d),
    [WHATSAPP_BOT_STEPS.PAYMENT_DETAILS]: (s, d) => this.processPaymentMethodStep(s, d),
    [WHATSAPP_BOT_STEPS.ORDER_SELECTION]: (s, d) => this.processPreviousOrderSelectStep(s, d),
    [WHATSAPP_BOT_STEPS.PICKUP_ADDRESS]: (s, d) => this.processPickupAddressStep(s, d),
    [WHATSAPP_BOT_STEPS.AUTO_SELECTION]: (s, d) => this.processAutoSelectionStep(s, d),
    [WHATSAPP_BOT_STEPS.SELECT_BRANCH]: (s, d) => this.processSelectBranchStep(s, d),
    [WHATSAPP_BOT_STEPS.ORDER_CANCELLED]: (s, d) => this.processOrderCancelledStep(s, d),
    [WHATSAPP_BOT_STEPS.EDIT_ORDER]: (s, d) => this.processEditOrderStep(s, d),
    [WHATSAPP_BOT_STEPS.REQUEST_LOCATION]: (s, d) => this.processRequestLocationStep(s, d),
    [WHATSAPP_BOT_STEPS.ENTER_COUPON]: (s, d) => this.processAddCouponStep(s, d),
    [WHATSAPP_BOT_STEPS.ADD_ORDER_NOTES]: (s, d) => this.processAddOrderNotes(s, d),
  };

  async initiateChatBySlug(slug: string) {
    const normalizedSlug = slug.toLowerCase();
    const store = await this.getStoreBySlug(normalizedSlug);

    if (!store || !store.flags?.uses_chowbot) {
      return getChowbotUrl('404');
    }

    const {
      deliveries_enabled,
      payments_enabled,
      configuration: { hours, customer_pickup_enabled, bot_initiation_message },
      pickup_address,
      extra_info,
    } = store;

    const deliverySetup =
      (customer_pickup_enabled ? Boolean(pickup_address) : deliveries_enabled) &&
      Boolean(extra_info?.delivery_timeline);
    const deliveryAreas = await this.brokerTransport
      .send<number>(BROKER_PATTERNS.STORE.COUNT_DELIVERY_AREAS, store?._id)
      .toPromise();
    const hoursSetup = hours !== undefined && hours !== null && Object.values(hours)?.length === 7;
    const storeSetup = deliverySetup && hoursSetup && payments_enabled && deliveryAreas > 0;

    const storeIsOpen = checkStoreIsOpen(store);

    switch (true) {
      case ((store.subscription as Subscription).plan as Plan).type !== PLAN_TYPE.KITCHEN:
        return getChowbotUrl(`business-inactive?${paramsFromObject({ name: store.name, phone: store.phone })}`);
      case !storeSetup:
        return getChowbotUrl(
          `${normalizedSlug}/not-setup?${paramsFromObject({ name: store.name, phone: store.phone })}`,
        ); //redirect to setup page
      case !(await this.checkTokenBalance(store.subscription)):
        return getChowbotUrl(`business-inactive?${paramsFromObject({ name: store.name, phone: store.phone })}`);
      case !!store.branches:
        return getChowbotUrl(`${normalizedSlug}/branches`);
      case !storeIsOpen.open: //check for maintainance mode & opening hours
        return getChowbotUrl(
          `${normalizedSlug}/closed?${paramsFromObject({
            name: store.name,
            phone: store.phone,
            title: storeIsOpen.title,
            message: storeIsOpen.message,
          })}`,
        );
      default: {
        const canUseCustomMessage = Boolean(bot_initiation_message) && bot_initiation_message.trim() !== '';
        const whatsappText = `Hi #${normalizedSlug} ${
          canUseCustomMessage ? bot_initiation_message : "i'll like to place an order"
        }`;
        return this.getWhatsappRedirectUrl(whatsappText);
      }
    }
  }

  private async getStoreBySlug(slug: string): Promise<Store> {
    const filter = {
      $or: [{ disabled_slugs: { $in: [slug] } }, { slugs: { $in: [slug] } }, { slug }],
    };

    return await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE_LEAN, { filter, select: {} })
      .toPromise();
  }

  private getWhatsappRedirectUrl(whatsappText: string): string {
    const botPhone = WA_BOT_PHONE_NOS[getAppEnv()];
    return `https://api.whatsapp.com/send/?phone=${botPhone[0]}&text=${encodeURIComponent(whatsappText)}`;
  }

  @Cron(CronExpression.EVERY_DAY_AT_6AM)
  async sendStoreOpenedReminders() {
    if (mutex.isLocked() || isUsingProdDBLocally()) return;
    await mutex.acquire();
    const reminders = await this.storeOpenedReminderModel
      .find({
        openingTime: {
          $gte: new Date(Date.now()),
          $lte: dayjs().endOf('day').toDate(),
        },
      })
      .populate('customer')
      .populate('store');

    const customerSet = new Set<string>();

    for (const reminder of reminders) {
      const customer = to<Customer>(reminder.customer);
      const store = to<Store>(reminder.store);

      if (customerSet.has(getDocId(customer))) {
        continue;
      }
      customerSet.add(getDocId(customer));
      await this.waBotQueue.add(
        QUEUES.WHATSAPP_BOT,
        {
          type: JOBS.SEND_STORE_OPENED_REMINDER,
          data: {
            customerName: customer?.name,
            customerPhone: customer?.phone,
            storeName: store?.name,
            storeCode: store?.slug,
          },
        },
        { delay: reminder.openingTime.getTime() - Date.now() },
      );
      await reminder.deleteOne();
    }

    await mutex.release();
  }

  @Cron(CronExpression.EVERY_DAY_AT_3PM)
  async sendAutoOrderReminders() {
    if (mutex.isLocked() || isUsingProdDBLocally()) return;
    await mutex.acquire();

    const botPhone = WA_BOT_PHONE_NOS[getAppEnv()][0];
    const botPhoneId = WA_BOT_PHONE_IDS[getAppEnv()][0];

    const stores = await this.brokerTransport
      .send<Store[]>(BROKER_PATTERNS.STORE.GET_STORES_LEAN, {
        filter: { 'flags.uses_chowbot': true, 'configuration.auto_customer_check_in.enabled': true },
        select: { slug: 1, name: 1, configuration: 1 },
      })
      .toPromise();

    const sentRemindersTo = [];

    for (const store of stores) {
      const { days = 2, message } = store?.configuration?.auto_customer_check_in;
      const storeIsOpen = checkStoreIsOpen(store);

      // Calculate the start and end times
      const endTime = dayjs().subtract(days, 'day').toDate();
      const startTime = dayjs()
        .subtract(days + 1, 'day')
        .toDate();

      const sessions = await this.waBotSessionModel
        .find({
          status: SESSION_STATUSES.COMPLETED,
          store: getDocId(store),
          $or: [
            { auto_checkin_later: true },
            {
              created_at: {
                $lte: endTime,
                $gte: startTime,
              },
            },
          ],
          $and: [{ order: { $exists: true } }, { order: { $ne: null } }],
        })
        .sort({ created_at: -1 })
        .populate('customer');

      if (sessions?.length > 0) {
        // await Promise.all(
        for (let index = 0; index < sessions.length; index++) {
          const session = sessions[index];

          const { customer } = session;
          const phone = formatPhoneNumber(to<Customer>(customer)?.phone);
          const genericCacheKey = `${GENERIC_CACHE_PREFIX}:${phone}-${botPhone}`;
          const checkInCacheKey = `${CHECK_IN_CACHE_PREFIX}:${phone}-${botPhone}`;
          const genericSession = await this.cacheManager.get<GenericBotSession>(genericCacheKey);
          const checkInSession = await this.cacheManager.get<CheckInSession>(checkInCacheKey);

          const customerHasOrderedPastDate = await this.waBotSessionModel.exists({
            status: SESSION_STATUSES.COMPLETED,
            store: getDocId(store),
            customer: getDocId(session.customer),
            created_at: { $gt: session.created_at },
          });

          if (customerHasOrderedPastDate) {
            await this.waBotSessionModel.findByIdAndUpdate(getDocId(session), { auto_checkin_later: false });
            continue;
          }

          //check if customer is currently having a session
          if (genericSession || checkInSession || sentRemindersTo.includes(phone)) {
            continue;
          }

          if (!storeIsOpen.open) {
            await this.waBotSessionModel.findByIdAndUpdate(getDocId(session), { auto_checkin_later: true });
            continue;
          }

          // create check_in session
          const newSession: CheckInSession = {
            date_initiated: new Date().toISOString(),
            from: WA_BOT_PHONE_NOS[getAppEnv()][0],
            to: phone,
            key: checkInCacheKey,
            type: SESSION_TYPES.CHECK_IN,
            phone_id: botPhoneId,
            step: WHATSAPP_BOT_STEPS.CHECK_IN_INITIATION,
            order: session.order as string,
            customer: getDocId(session.customer),
            store: { id: getDocId(store), name: store.name, code: store.slug },
          };

          await this.cacheManager.set(checkInCacheKey, { ...newSession }, { ttl: CACHE_TTL });

          const ctx: BotContext = {
            botPhoneId: WA_BOT_PHONE_IDS[getAppEnv()][0],
            destinationPhone: phone,
          };

          const messageData = {
            customerName: (session.customer as Customer).name.split(' ')[0],
            store: store.name,
            slug: store.slug,
            message:
              message ??
              "🌟 Just a friendly reminder that we're here whenever you're ready to treat yourself again. Feel free to browse our latest offerings or simply reorder your favorites. ",
          };

          await this.messaging.send(MESSAGES.AUTO_CHECK_IN_MESSAGE, ctx, messageData);
          await this.waBotSessionModel.findByIdAndUpdate(getDocId(session), { auto_checkin_later: false });
          sentRemindersTo.push(phone);
        }
        // );
      }
    }

    await mutex.release();
  }
}
