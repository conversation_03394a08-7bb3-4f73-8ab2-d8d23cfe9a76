import { InjectQueue } from '@nestjs/bull';
import { CACHE_MANAGER, Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectModel } from '@nestjs/mongoose';
import { Queue } from 'bull';
import { Cache } from 'cache-manager';
import { Model, PaginateModel } from 'mongoose';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { PLAN_TYPE } from '../../../enums/plan.enum';
import { JOBS, QUEUES } from '../../../enums/queues.enum';
import {
  INTENTS,
  INTERACTIVE_MESSAGE_TYPES,
  WHATSAPP_BOT_APP_EVENTS,
  WHATSAPP_BOT_EVENTS,
  WHATSAPP_BOT_STEPS,
} from '../../../enums/whatsapp.enum';
import {
  BotContext,
  BotProcessorResult,
  BotSession,
  CheckInSession,
  GenericBotSession,
  OrderConfirmationSession,
} from '../../../interfaces/whatsapp.interface';
import { OpenaiRepository } from '../../../repositories/openai.respository';
import { PosthogRepository } from '../../../repositories/posthog.repository';
import { formatPhoneNumber, getAppEnv, isProduction, sluggify } from '../../../utils';
import { to, getDocId } from '../../../utils/functions';
import { PhoneCodesLean } from '../../../utils/phone-codes';
import { Customer } from '../../orders/customers/customer.schema';
import { ORDER_CHANNELS, ORDER_STATUSES, Order } from '../../orders/order.schema';
import { Branch } from '../../store/branches/branches.schema';
import { Store } from '../../store/store.schema';
import { SubscriptionTokens } from '../../subscription/utils/balance-check-and-debit';
import {
  MessageData,
  unnormalizePhone,
  getContext,
  getCurrentSessionTtl,
  getPublicUrl,
  getStoreCode,
} from '../utils/functions.utils';
import { CTA_REPLIES, MESSAGES } from '../utils/messages.utils';
import { WABotJob } from '../whatsapp-bot.queue';
import {
  BotSessionDocument,
  SESSION_STATUSES,
  StoreOpenedReminder,
  StoreOpenedReminderDocument,
  WABotSession,
} from '../whatsapp-bot.schema';
import { WhatsappMessagingService } from './messaging.service';
import { Address } from '../../deliveries/deliveries.schema';
import { S3Repository } from '../../../repositories/s3.repositories';
import checkStoreIsOpen, { convertStringToTime, getNextOpenDate } from '../../../utils/check-if-store-is-open';
import {
  CACHE_TTL,
  DEFAULT_RESTAURANT_HERO_IMAGE,
  GENERIC_CACHE_PREFIX,
  NONE_ID,
} from '../../../utils/wabot-constants';
import { SubscriptionDocument } from '../../subscription/subscription.schema';
import { WebServicesRepository } from '../../../repositories/webservices.repository';
import { DeliveryArea } from '../../store/delivery-areas/delivery-areas.schema';
import { GoogleMapsRepository } from '../../../repositories/maps.google.repository';
import { ShipbubbleRepository } from '../../../repositories/shipbubble.repository';
import { SellerProcessor } from './seller.processor.service';
import { MESSAGE_TYPES } from '../../../repositories/whatsapp.repository';
import { Feedback, OrderFeedback } from '../../orders/order-feedback/order-feedback.schema';
import { CreateOrderFeedbackDto } from '../../orders/dto/create-order.dto';

let HAS_INIT = false;
@Injectable()
export class BaseProcessor {
  protected isFirstInstance = false;
  constructor(
    @Inject(CACHE_MANAGER) protected readonly cacheManager: Cache,
    protected readonly logger: Logger,
    protected readonly brokerTransport: BrokerTransportService,
    protected readonly jwtService: JwtService,
    protected readonly messaging: WhatsappMessagingService,
    @InjectModel(WABotSession.name)
    protected readonly waBotSessionModel: PaginateModel<BotSessionDocument>,
    @InjectModel(StoreOpenedReminder.name)
    protected readonly storeOpenedReminderModel: Model<StoreOpenedReminderDocument>,
    protected readonly posthog: PosthogRepository,
    protected readonly openai: OpenaiRepository,
    protected readonly webservices: WebServicesRepository,
    @InjectQueue(QUEUES.WHATSAPP_BOT)
    protected readonly waBotQueue: Queue<WABotJob>,
    @Inject(forwardRef(() => SellerProcessor))
    protected readonly sellerProcessor: SellerProcessor,
    protected readonly s3: S3Repository,
    protected readonly googleMapsRepository: GoogleMapsRepository,
    protected readonly shipbubble: ShipbubbleRepository,
  ) {}

  async onModuleInit() {
    if (!HAS_INIT) {
      this.isFirstInstance = true;
      HAS_INIT = true;
    }
  }

  // Processors
  protected async processStartStep(session: GenericBotSession, data: MessageData): BotProcessorResult {
    const storeCode = getStoreCode(data);
    const ctx = getContext(session);

    if (storeCode) {
      return this.handleStoreCodeMessage(storeCode, session, data.getName());
    }

    const previousSesssion = await this.waBotSessionModel
      .findOne({
        from: session.from,
        to: session.to,
        store: { $exists: true },
      })
      .sort({ created_at: -1 })
      .populate('store');

    if (previousSesssion) {
      const store = previousSesssion.store as Store;
      session.metadata = { ...(session.metadata ?? {}), previous_session_store: getDocId(store) };
      await this.saveSession(session);

      const stepMessage = await this.messaging.send(MESSAGES.CHAT_INITIATION_EXISTING_CUSTOMER, ctx, {
        storeName: store.name,
      });

      return { currentStep: WHATSAPP_BOT_STEPS.CHAT_INITIATION_EXISTING_CUSTOMER, intent: INTENTS.NONE, stepMessage };
    }
    const stepMessage = await this.messaging.send(MESSAGES.NEW_CUSTOMER_NO_STORE_MESSAGE, ctx);
    return { currentStep: WHATSAPP_BOT_STEPS.SEARCH_STORE, intent: INTENTS.NONE, stepMessage };
  }

  protected async processExistingCustomerStep(session: GenericBotSession, data: MessageData): BotProcessorResult {
    const ctx = getContext(session);
    if (data.getInteractiveMessageType() === INTERACTIVE_MESSAGE_TYPES.BUTTON_REPLY) {
      const key = data.getInteractiveMessage()?.button_reply?.id;

      switch (key) {
        case sluggify(CTA_REPLIES.PROCEED): {
          const store = await this.brokerTransport
            .send<Store>(BROKER_PATTERNS.STORE.GET_STORE_LEAN, {
              filter: { _id: session.metadata.previous_session_store },
            })
            .toPromise();

          return this.handleStoreInitiation(store, session, undefined, true);
        }
      }
    }
    await this.handleUnexpectedResponse(ctx, session);
  }

  protected async processCheckInStep(session: CheckInSession, data: MessageData) {
    const ctx = { botPhoneId: session.phone_id, destinationPhone: session.to };

    switch (session.step) {
      case WHATSAPP_BOT_STEPS.CHECK_IN_INITIATION: {
        if (data.getMessageType() === MESSAGE_TYPES.BUTTON) {
          const key = data.getMessages()[0].button?.payload;
          switch (sluggify(key)) {
            case sluggify(CTA_REPLIES.GIVE_FEEDBACK):
              {
                await this.messaging.send(MESSAGES.FEEDBACK_FORM_MESSAGE, ctx, {});
                session.step = WHATSAPP_BOT_STEPS.FEEDBACK;
                await this.saveSession(session);
              }
              break;
          }
        }
        break;
      }
      case WHATSAPP_BOT_STEPS.FEEDBACK: {
        if (data.getInteractiveMessageType() === INTERACTIVE_MESSAGE_TYPES.NFM_REPLY) {
          const json = data.getInteractiveMessage()?.nfm_reply?.response_json;
          const form: {
            purchase_rating?: string;
            process_rating?: string;
            delivery_rating?: string;
            comment_text?: string;
          } = JSON.parse(json);

          const ratings = [5, 4, 3, 2, 1];

          const feedback: CreateOrderFeedbackDto = {
            order: session.order,
            store: session.store.id,
            customer: session.customer,
            feedback: {
              product_rating: form?.purchase_rating ? ratings[Number(form.purchase_rating)] : null,
              process_rating: form?.process_rating ? ratings[Number(form.process_rating)] : null,
              delivery_rating: form?.delivery_rating ? ratings[Number(form.delivery_rating)] : null,
              general_feedback: form?.comment_text,
            },
          };

          const orderFeedback = await this.brokerTransport
            .send<OrderFeedback>(BROKER_PATTERNS.ORDER.RECORD_FEEDBACK, feedback)
            .toPromise();
          // store feedback
          await this.messaging.send(MESSAGES.FEEDBACK_SUBMITTED_MESSAGE, ctx, { storeCode: session.store.code });
          await this.cacheManager.del(session.key);
        }
      }
    }
  }

  protected async processSelectBranchStep(session: GenericBotSession, data: MessageData): BotProcessorResult {
    const ctx = getContext(session);
    const listReply = data.getInteractiveMessage()?.list_reply;
    if (listReply) {
      const store = await this.brokerTransport
        .send<Store>(BROKER_PATTERNS.STORE.GET_STORE_LEAN, { filter: { _id: listReply.id } })
        .toPromise();
      if (store.slug === session.store.storeCode) {
        const stepMessage = await this.messaging.send(MESSAGES.SAME_STORE_INITIATION_MESSAGE, ctx, {
          storeName: session.store.name,
        });

        return {
          currentStep: WHATSAPP_BOT_STEPS.SAME_STORE_INITIATION,
          intent: INTENTS.NONE,
          stepMessage,
          meta: { newStoreCode: session.store.storeCode },
        };
      } else {
        return await this.handleStoreInitiation(store, session, data.getName(), true);
      }
    }
  }

  protected async processStoreInitiationStep(session: GenericBotSession, data: MessageData): BotProcessorResult {
    const ctx = getContext(session);
    if (data.getInteractiveMessageType() === INTERACTIVE_MESSAGE_TYPES.BUTTON_REPLY) {
      const key = data.getInteractiveMessage()?.button_reply?.id;
      switch (key) {
        case sluggify(CTA_REPLIES.START_NEW_PROCESS):
          return this.handleStoreCodeMessage(session.metadata.newStoreCode, session, data.getName(), true);
      }
    }
    await this.handleUnexpectedResponse(ctx, session);
  }
  // Class Methods
  protected async handleStoreInitiation(
    store: Store,
    session: GenericBotSession,
    name?: string,
    cacheStoreAndCustomer = false,
  ) {
    const ctx = getContext(session);
    const customer = await this.getOrCreateCustomer(session, name, getDocId(store));

    if (await this.checkIfSenderIsStore(session, store)) {
      await this.removeSession(session, true);
      return {};
    }

    session.customer = {
      id: getDocId(customer),
      name: customer?.name || '',
      name_verified: customer?.name_verified || false,
      phone_number: customer?.phone || '',
    };

    const storeUnavailableResult = await this.checkStoreAvailability(session, store, ctx);
    if (storeUnavailableResult) return storeUnavailableResult;

    const { canInitiate, shouldBillSession } = await this.trackTokensAndGetFlags(getDocId(customer), store);
    if (!canInitiate) {
      await this.createDbSession(
        session,
        getDocId(store),
        store?.slug,
        false,
        SESSION_STATUSES.LOW_TOKEN_BALANCE,
        new Date(),
      );
      await this.messaging.send(MESSAGES.STORE_UNAVAILABLE, ctx, { storeName: store?.name });
      return {};
    }

    if (cacheStoreAndCustomer) {
      await this.cacheStoreInfo(session, store);
      await this.cacheCustomerInfo(session, customer);
    }

    const previousOrders = await this.countPreviousOrders(session, getDocId(store));
    const hasPreviousOrders = previousOrders > 0;

    const stepMessage = await this.sendWelcomeMessage(session, ctx, store, hasPreviousOrders, shouldBillSession);

    if (hasPreviousOrders || session.store.use_menu_ordering)
      return {
        secondarySteps: [WHATSAPP_BOT_STEPS.SELECT_BRANCH],
        currentStep: WHATSAPP_BOT_STEPS.WELCOME_MESSAGE,
        intent: INTENTS.NONE,
        stepMessage,
        clear: cacheStoreAndCustomer,
      };
    return {
      secondarySteps: [WHATSAPP_BOT_STEPS.SELECT_BRANCH],
      currentStep: WHATSAPP_BOT_STEPS.ITEMS_SELECTION,
      intent: INTENTS.NONE,
      stepMessage,
      clear: cacheStoreAndCustomer,
    };
  }

  protected async checkStoreAvailability(
    session: GenericBotSession,
    store: Store,
    ctx: BotContext,
  ): BotProcessorResult {
    if ((store?.subscription as any).plan?.type !== PLAN_TYPE.KITCHEN) {
      await this.createDbSession(session, store._id, store?.slug, false, SESSION_STATUSES.STORE_OFFLINE, new Date());
      await this.messaging.send(MESSAGES.STORE_UNAVAILABLE, ctx, { storeName: store?.name });
      return {};
    }

    const storeIsOpen = checkStoreIsOpen(store);

    if (!storeIsOpen.open) {
      await this.createDbSession(session, store._id, store?.slug, false, SESSION_STATUSES.STORE_CLOSED, new Date());
      await this.messaging.send(MESSAGES.STORE_CLOSED, ctx, {
        isInMaintenance: storeIsOpen.isOnMaintenance,
        storeName: store.name,
        openingTime: `${
          storeIsOpen.nextDayOpen === 'today'
            ? `*${storeIsOpen?.openTomorrowTime}* today`
            : `*${storeIsOpen?.openTomorrowTime}* on ${storeIsOpen.nextDayOpen}`
        }`,
      });

      //for reminders
      let nextOpenDate = getNextOpenDate(storeIsOpen.nextDayOpen, storeIsOpen.openTomorrowTime);
      nextOpenDate = nextOpenDate.add(1, 'hour');

      if (storeIsOpen.nextDayOpen === 'today') {
        const jobId = `${getDocId(store)}-${session?.customer?.id}`;
        if (await this.waBotQueue.getJob(jobId)) return {};

        await this.waBotQueue.add(
          QUEUES.WHATSAPP_BOT,
          {
            type: JOBS.SEND_STORE_OPENED_REMINDER,
            data: {
              customerName: session?.customer?.name,
              customerPhone: formatPhoneNumber(session?.customer?.phone_number),
              storeName: store?.name,
              storeCode: store?.slug,
            },
          },
          { jobId: jobId, delay: nextOpenDate.toDate().getTime() - Date.now() },
        );
      } else {
        const existingReminder = await this.storeOpenedReminderModel.findOne({
          customer: session?.customer?.id,
          store: getDocId(store),
        });

        if (existingReminder) return {};

        await this.storeOpenedReminderModel.create({
          closedReason: storeIsOpen?.isOnMaintenance ? 'maintenance' : 'closed',
          customer: session?.customer?.id,
          store: getDocId(store),
          openingTime: nextOpenDate.toDate(),
        });
      }

      return {};
    }
  }

  protected async checkIfSenderIsStore(session: GenericBotSession, store: Store) {
    if (
      (session.from === formatPhoneNumber(store.phone) ||
        session.from === formatPhoneNumber(store.secondary_phone ?? '')) &&
      getAppEnv() === 'production'
    ) {
      await this.messaging.send(MESSAGES.SELF_SESSION_NOT_SUPPORTED, getContext(session));
      return true;
    }
    return false;
  }

  protected async cacheStoreInfo(session: GenericBotSession, store: Store) {
    const {
      name,
      _id,
      phone,
      slug,
      configuration: {
        customer_pickup_enabled,
        confirm_order_before_payment = true,
        require_delivery_info,
        menu_images,
        send_menu_on_initiation = false,
        require_geolocation = false,
        custom_order_success_message,
        auto_confirm_chowbot_orders = false,
        pass_chowbot_fee_to_deliveries = false,
        collect_order_notes = true,
      },
      extra_info = {},
      hero_image,
      state,
      currencies = {} as any,
      pickup_address,
      store_menu,
      branches,
      subscription,
      third_party_configs,
      secondary_phone,
    } = store;

    let pickupAddress: Address = null;
    if (pickup_address) {
      pickupAddress = await this.brokerTransport
        .send<Address>(BROKER_PATTERNS.DELVERIES.GET_ADDRESS, pickup_address)
        .toPromise();
    }

    const deliveryAreas = await this.getStoreDeliveryAreas(getDocId(store));

    session.store = {
      name,
      id: _id,
      contact_phone: formatPhoneNumber(phone),
      storeCode: slug,
      deliveries_enabled: require_delivery_info,
      pickup_enabled: customer_pickup_enabled,
      hero_image,
      state,
      pickup_address: pickupAddress
        ? {
            name: pickupAddress.street,
            address: pickupAddress.formatted_address,
            latitude: pickupAddress.latitude,
            longitude: pickupAddress.longitude,
          }
        : null,
      average_delivery_timeline: extra_info?.delivery_timeline ?? 'no time',
      confirm_order_before_payment,
      currency: currencies.default,
      menu_images,
      use_menu_ordering: send_menu_on_initiation,
      require_geolocation,
      store_menu,
      branches_id: getDocId(branches),
      subscription_id: getDocId(subscription),
      custom_order_success_message,
      delivery_areas: deliveryAreas.map((a) => ({
        id: getDocId(a, true),
        fee: a.fee,
        title: a.name,
      })),
      auto_delivery: third_party_configs?.chowdeck?.auto_delivery ?? false,
      secondary_phone: secondary_phone ? formatPhoneNumber(secondary_phone) : null,
      auto_confirm_chowbot_orders,
      collect_order_notes,
      pass_chowbot_fee_to_deliveries,
    };

    await this.saveSession(session);
  }

  protected async getStoreDeliveryAreas(storeId: string) {
    const deliveryAreas = await this.brokerTransport
      .send<DeliveryArea[]>(BROKER_PATTERNS.STORE.GET_STORE_DELIVERY_AREAS, {
        store: storeId,
      })
      .toPromise();

    if (deliveryAreas.length === 0) {
      deliveryAreas.push({
        _id: NONE_ID,
        id: NONE_ID,
        fee: 0,
        name: 'None',
        store: storeId as any,
      });
    }
    return deliveryAreas;
  }

  protected async cacheCustomerInfo(session: GenericBotSession, customer: Customer) {
    session.customer = {
      id: customer.id,
      name: customer.name,
      name_verified: customer.name_verified,
      phone_number: customer.phone,
    };
    await this.saveSession(session);
  }

  protected async getOrCreateCustomer(session: GenericBotSession, name: string, store: string) {
    let customer = await this.brokerTransport
      .send<Customer>(BROKER_PATTERNS.ORDER.GET_CUSTOMER, {
        phone: unnormalizePhone(session.from, PhoneCodesLean),
        store,
      })
      .toPromise();

    if (!customer) {
      customer = await this.brokerTransport
        .send<Customer>(BROKER_PATTERNS.ORDER.CREATE_CUSTOMER, {
          name: name,
          phone: unnormalizePhone(session.from, PhoneCodesLean),
          formatted_phone: session.from,
          store,
        })
        .toPromise();
    }
    return customer;
  }

  async endSession(ctx: BotContext, session: GenericBotSession, fromTimeoutJob = false) {
    const sessionCache = JSON.parse(JSON.stringify(session)) as GenericBotSession;
    await this.removeSession(session);
    await this.updateEndedSession(session, fromTimeoutJob);

    if (sessionCache?.order?.order_id) {
      await this.sellerProcessor.removeOrderJobForGenericSession(session, true);
    }

    if (sessionCache.db_session) this.captureMessageEvent(WHATSAPP_BOT_EVENTS.ENDED_CHAT, session);
    if (fromTimeoutJob) {
      await this.messaging.send(MESSAGES.EXPIRED_SESSION_MESSAGE, ctx, {
        storeCode: sessionCache.store?.storeCode ?? '',
        storeName: sessionCache.store?.name ?? '',
        hasOrder: !Boolean(sessionCache?.order?.is_cancelled) && Boolean(sessionCache?.order?.order_id),
      });
    } else {
      let storeCode = sessionCache.store?.storeCode;
      if (!storeCode) {
        const lastSession = await this.waBotSessionModel
          .findOne({ from: sessionCache.from, to: sessionCache.to })
          .sort({ created_at: -1 });
        storeCode = lastSession?.storeCode;
      }

      await this.messaging.send(MESSAGES.CANCEL_PROCESS_MESSAGE, ctx, {
        name: sessionCache?.customer?.name ?? '',
        storeName: sessionCache?.store?.name ?? '',
        storeCode,
      });
    }
  }

  async removeSession(session: GenericBotSession, removeTimeoutJob?: boolean) {
    if (removeTimeoutJob) {
      (await this.waBotQueue.getJob(session.timeout_job))?.remove();
    }
    await this.cacheManager.del(session.key);
  }

  protected async handleUnexpectedResponse(ctx: BotContext, session: GenericBotSession): BotProcessorResult {
    this.messaging.send(MESSAGES.UNEXPECTED_RESPONSE_MESSAGE, ctx, {
      showMainOptions: Boolean(session.store),
      storeName: session.store?.name ?? '',
      isPaymentStep: Boolean(session?.order?.order_id) && !session?.order?.is_cancelled,
    });
    return {};
  }

  protected async handleStoreCodeMessage(storeCode: string, session: GenericBotSession, name: string, clear?: boolean) {
    const ctx = getContext(session);
    const store = await this.getStoreFromCode(storeCode);

    if (store) {
      return await this.handleStoreInitiation(store, session, name, true);
    }
    await this.messaging.send(MESSAGES.STORE_NOT_FOUND_MESSAGE, ctx, Boolean(session.store));
    return {};
  }

  protected async getStoreFromCode(storeCode: string) {
    // const storeSelectParam = 'name phone slug deliveries_enabled configuration hero_image';
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE_LEAN, { filter: { slug: storeCode } })
      .toPromise();
    return store;
  }

  protected async trackTokensAndGetFlags(customer: string, store: Store) {
    // 24H AGO < ---------(billed session)-------- > NOW
    const billedSession = await this.waBotSessionModel.findOne({
      store: getDocId(store),
      created_at: { $gte: new Date(new Date().getTime() - 24 * 60 * 60 * 1000) },
      is_billable: true,
      customer,
    });

    let shouldBillSession = false;
    let canInitiate = true;

    if (!Boolean(billedSession)) {
      const debitTokens = await this.brokerTransport
        .send<{ message: string; result?: SubscriptionDocument; error?: boolean }>(
          BROKER_PATTERNS.PAYMENT.TOKENS.DEBIT_TOKEN,
          getDocId(store.subscription),
        )
        .toPromise();

      if (debitTokens.error || !debitTokens.result) {
        canInitiate = false;
      }

      shouldBillSession = true;
    }
    // BILL STORE
    return { shouldBillSession, canInitiate };
  }

  protected async sendWelcomeMessage(
    session: GenericBotSession,
    ctx: BotContext,
    store: Store,
    hasPreviousOrders = false,
    isBillable = false,
  ) {
    const branchesData = await this.getBranchesMsgData(session);
    const dbSession = await this.createDbSession(session, store?._id, store?.slug, isBillable);
    session.db_session = dbSession._id;

    this.captureMessageEvent(WHATSAPP_BOT_EVENTS.INITIATED_CONVERSATION, { ...session });

    if (hasPreviousOrders || session.store.use_menu_ordering) {
      const stepMessage = await this.messaging.send(MESSAGES.WELCOME_MESSAGE, ctx, {
        storeName: store?.name,
        storeImage: store?.hero_image ? store?.hero_image : DEFAULT_RESTAURANT_HERO_IMAGE,
        hasPreviousOrders,
        deliveryTimeline: session.store.average_delivery_timeline,
        branchesData,
      });

      this.captureMessageEvent(WHATSAPP_BOT_EVENTS.RECIEVED_WELCOME_MESSAGE, { ...session }, false);
      return stepMessage;
    }

    const url = this.getStoreUrl(session);

    const stepMessage = await this.messaging.send(MESSAGES.WELCOME_CTA_MESSAGE, ctx, {
      storeName: store?.name,
      storeImage: store?.hero_image ? store?.hero_image : DEFAULT_RESTAURANT_HERO_IMAGE,
      url,
      deliveryTimeline: session.store.average_delivery_timeline,
      branchesData,
    });

    this.captureMessageEvent(WHATSAPP_BOT_EVENTS.RECEIVED_ITEM_SELECTION_LINK, { ...session }, false);
    return stepMessage;
  }

  protected async getBranchesMsgData(session: GenericBotSession) {
    if (session.store?.branches_id) {
      const branch = await this.brokerTransport
        .send<Branch>(BROKER_PATTERNS.STORE.GET_BRANCHES, { id: session.store.branches_id })
        .toPromise();

      if (branch)
        return branch.branches.map((b) => {
          const headBranch = b.is_head_branch ? 'Main Branch' : '';
          return {
            name: `${branch.name} (${b.label})`,
            description: b.store_id === session.store.id ? `Current Branch` : headBranch,
            id: b.store_id,
          };
        });
    }
    return null;
  }

  protected getStoreUrl(session: GenericBotSession, openCart = false) {
    return getPublicUrl(
      `/${session.store.storeCode}/mini?bot_session=${session.from}-${session.to}&open_cart=${
        openCart ? 'true' : 'false'
      }`,
    );
  }

  protected async countPreviousOrders(session: GenericBotSession, storeId: string) {
    return await this.brokerTransport
      .send<number>(BROKER_PATTERNS.ORDER.COUNT_ORDERS, {
        store: storeId,
        channel: ORDER_CHANNELS.CHATBOT,
        customer: session.customer.id,
      })
      .toPromise();
  }

  protected async pushHistory(session: GenericBotSession, history: GenericBotSession['history']) {
    session.history = [...session.history, ...history];
    await this.saveSession(session);
  }

  protected async saveSession(session: BotSession) {
    await this.cacheManager.set<BotSession>(
      session.key,
      { ...session },
      {
        ttl: getCurrentSessionTtl(session, CACHE_TTL),
      },
    );
    return session;
  }

  protected async createDbSession(
    session: GenericBotSession,
    store: string,
    storeCode: string,
    isBillable = false,
    status?: SESSION_STATUSES,
    ended_at?: Date,
  ) {
    const dbSession = await this.waBotSessionModel.create({
      store,
      to: session.to,
      from: session.from,
      customer: session.customer?.id,
      status: status ?? SESSION_STATUSES.ONGOING,
      is_billable: isBillable,
      ended_at: ended_at,
      storeCode,
    });
    session.db_session = dbSession._id;
    await this.saveSession(session);
    return dbSession;
  }

  protected async updateEndedSession(session: GenericBotSession, isUncompleted = false) {
    if (isUncompleted) {
      await this.waBotSessionModel.updateOne(
        { _id: session.db_session },
        { status: SESSION_STATUSES.DROPPED_OFF, ended_at: new Date() },
      );
      return;
    }
    await this.updateSessionStatus(session, SESSION_STATUSES.DROPPED_OFF, true);
  }

  protected async updateSessionStatus(session: GenericBotSession, status: SESSION_STATUSES, hasEnded = false) {
    await this.waBotSessionModel.updateOne(
      { _id: session.db_session },
      { status, ended_at: hasEnded ? new Date() : undefined },
    );
  }

  protected async captureMessageEvent(
    event: WHATSAPP_BOT_EVENTS,
    session: GenericBotSession,
    isUserMessage = true,
    message?: string,
  ) {
    await this.posthog.captureChowbotEvent({
      distinct_id: formatPhoneNumber(session?.customer?.phone_number ?? ''),
      event,
      properties: {
        is_production: isProduction(),
        is_session_event: true,
        session_id: session?.db_session,
        botMeta: {
          id: session?.phone_id,
          phone: session.to,
        },
        customer: {
          id: session?.customer?.id,
          name: session?.customer?.name,
          phone: session?.customer?.phone_number,
        },
        isUserMessage,
        store: {
          id: session?.store?.id,
          name: session?.store?.name,
        },
        message,
      },
      timestamp: new Date(),
    });
  }

  protected async captureAppEvent(
    event: WHATSAPP_BOT_APP_EVENTS,
    session: GenericBotSession,
    message: string,
    senderPhone: string,
  ) {
    await this.posthog.captureChowbotEvent({
      distinct_id: senderPhone,
      event,
      properties: {
        is_production: isProduction(),
        is_session_event: false,
        session_id: session.db_session,
        botMeta: {
          id: session.phone_id,
          phone: session.to,
        },
        customer: session.customer
          ? {
              id: session.customer.id,
              name: session.customer.name,
              phone: session.customer.phone_number,
            }
          : undefined,
        isUserMessage: false,
        store: session.store
          ? {
              id: session.store?.id,
              name: session.store?.name,
            }
          : undefined,
        message,
      },
      timestamp: new Date(),
    });
  }

  protected addSessionPrefix(postfix: string) {
    return `${GENERIC_CACHE_PREFIX}:${postfix}`;
  }

  protected removeSessionPrefix(sessionId: string) {
    return sessionId.replace(`${GENERIC_CACHE_PREFIX}:`, '');
  }

  protected async bookmarkSessionStep(session: GenericBotSession) {
    session.metadata = {
      ...session.metadata,
      step_bookmark: { ...session.history[session.history.length - 1], intent: session.intent },
    };
    await this.saveSession(session);
  }

  protected async useSessionBookmark(session: GenericBotSession, ctx: BotContext, extraMessageData = {}) {
    const bookmark = { ...session.metadata?.step_bookmark };
    if (bookmark) {
      const stepMessage = await this.messaging.send(bookmark.step_message.type, ctx, {
        ...bookmark.step_message.data,
        ...extraMessageData,
      });
      return {
        currentStep: bookmark.step,
        intent: bookmark.intent,
        stepMessage,
      };
    }
  }
}

/*  protected async processStoreNotFoundStep(session: BotSession, data: MessageData): BotProcessorResult {
    const ctx = getContext(session);
    if (data.getInteractiveMessageType() === INTERACTIVE_MESSAGE_TYPES.BUTTON_REPLY) {
      const key = data.getInteractiveMessage()?.button_reply?.id;
      let stepMessage;
      switch (key) {
        case sluggify(CTA_REPLIES.ORDER_PREVIOUS_STORE):
          return;
      }
    } else {
      
    }
  } */
