import { UnauthorizedException } from '@nestjs/common';
import { INTENTS, WHATSAPP_BOT_STEPS } from '../../../enums/whatsapp.enum';
import { BotContext, BotSession, GenericBotSession, WhatsappMessageDto } from '../../../interfaces/whatsapp.interface';
import { capitalizeFirstLetter } from '../../../utils';
import { dateFromString } from '../../../utils/time';
import { Cart, CartItem } from '../../cart/cart.schema';
import { CURRENCIES } from '../../country/country.schema';
import { Item } from '../../item/item.schema';
import { Order, OrderItem } from '../../orders/order.schema';
import { Store } from '../../store/store.schema';
import { OTHER_TEXTS } from './messages.utils';
export class MessageData {
  constructor(private message: WhatsappMessageDto) {}
  getEntries() {
    return this.message?.entry;
  }
  getChanges() {
    return this.message?.entry?.[0]?.changes;
  }
  getValue() {
    return this.message?.entry?.[0]?.changes?.[0]?.value;
  }
  getContacts() {
    return this.message?.entry?.[0]?.changes?.[0]?.value?.contacts;
  }
  getMessages() {
    return this.message?.entry?.[0]?.changes?.[0]?.value?.messages;
  }
  getMetadata() {
    return this.message?.entry?.[0]?.changes?.[0]?.value?.metadata;
  }
  getBotPhoneId() {
    return this.message?.entry?.[0]?.changes?.[0]?.value?.metadata?.phone_number_id;
  }
  getBotPhone() {
    return this.message?.entry?.[0]?.changes?.[0]?.value?.metadata?.display_phone_number;
  }
  getStatuses() {
    return this.message?.entry?.[0]?.changes?.[0]?.value?.statuses;
  }
  getProfile() {
    return this.message?.entry?.[0]?.changes?.[0]?.value?.contacts?.[0]?.profile;
  }
  getName() {
    return this.message?.entry?.[0]?.changes?.[0]?.value?.contacts?.[0]?.profile?.name;
  }
  getWaId() {
    return this.message?.entry?.[0]?.changes?.[0]?.value?.contacts?.[0]?.wa_id;
  }
  getMessageType() {
    return this.message?.entry?.[0]?.changes?.[0]?.value?.messages?.[0]?.type;
  }
  getMessageTimestamp() {
    return this.message?.entry?.[0]?.changes?.[0]?.value?.messages?.[0]?.timestamp;
  }
  getMessageText() {
    return this.message?.entry?.[0]?.changes?.[0]?.value?.messages?.[0]?.text;
  }
  getMessageBody() {
    return this.message?.entry?.[0]?.changes?.[0]?.value?.messages?.[0]?.text?.body;
  }
  getInteractiveMessage() {
    return this.message?.entry?.[0]?.changes?.[0]?.value?.messages?.[0]?.interactive;
  }
  getInteractiveMessageType() {
    return this.message?.entry?.[0]?.changes?.[0]?.value?.messages?.[0]?.interactive?.type;
  }
  getConversationId() {
    return this.message?.entry?.[0]?.changes?.[0]?.value?.statuses?.[0]?.conversation?.id;
  }
  getStatusType() {
    return this.message?.entry?.[0]?.changes?.[0]?.value?.statuses?.[0]?.status;
  }
  getStatusRecipient() {
    return this.message?.entry?.[0]?.changes?.[0]?.value?.statuses?.[0]?.recipient_id;
  }
  getLocationDetails() {
    return this.message?.entry?.[0]?.changes?.[0]?.value?.messages?.[0]?.location;
  }
}

export const conditionallyAddVariable = (v: string) => `${v ? ` ${v}` : ''}`;
export const conditionallyAppendWhiteSpace = (condtion: boolean) =>
  `${condtion ? `\n${OTHER_TEXTS.WHITESPACE(1)}` : ''}`;
export const removeNullOrEmptyFromArray = (a: any[]) => a.filter((v) => v !== null && v !== undefined && v !== '');
export const removeNullUndefinedFromObject = <T extends Record<string, any>>(obj: T): Partial<T> =>
  Object.fromEntries(Object.entries(obj).filter(([_, v]) => v !== null && v !== undefined)) as Partial<T>;

export const wrapWaMessage = (message: WhatsappMessageDto) => {
  return new MessageData(message);
};
export const getCurrentSessionTtl = (session: BotSession, cacheTtl: number) => {
  return cacheTtl - Math.floor((Date.now() - dateFromString(session.date_initiated).getTime()) / 1000);
};

export const getContext = (session: GenericBotSession): BotContext => {
  return { botPhoneId: session.phone_id, destinationPhone: session.from, currency: CURRENCIES.NGN };
};

export const checkIfSessionIdle = (session: GenericBotSession, maxIdleTime: number) => {
  const elapsedTime = Math.floor(
    (Date.now() - dateFromString(session.history[session.history.length - 1].timestamp).getTime()) / 1000,
  );
  return elapsedTime >= maxIdleTime;
};

export const truncateText = (input: string, max: number) => {
  if (input.length > max) {
    return input.substring(0, max - 3) + '...';
  }
  return input;
};

export const isSupportedPhone = (phone: string, codes: string[]) => {
  return codes.some((c) => phone.startsWith(c));
};

export const unnormalizePhone = (phone: string, codes: string[]) => {
  const code = codes.find((c) => phone.startsWith(c));
  if (code) {
    return `+${code}-${phone.substring(code.length)}`;
  }
  return null;
};

export const delay = async (time: number): Promise<void> => {
  return new Promise((resolve) => {
    const id = setTimeout(() => {
      resolve();
      clearTimeout(id);
    }, time);
  });
};

export const getStoreCode = (data: MessageData) => {
  const messageValue = data.getMessageBody();
  const storeSlug = messageValue
    ?.split('#')?.[1]
    ?.trim()
    .split(' ')[0]
    .replace(/[^a-zA-Z0-9-_]/g, '');

  return storeSlug ?? null;
};

export const getFirstName = (fullName: string) => {
  return capitalizeFirstLetter(fullName.split(' ')[0]);
};

interface CartOrderCommonProps {
  item_id?: string;
  object: Item;
  quantity: number;
  variant: any;
}

export const normalizeOrderItemsData = (items: OrderItem[]): CartOrderCommonProps[] => {
  return items.map((item) => ({ object: item?.snapshot, variant: item?.variant, quantity: item?.quantity }));
};

export const getTotalCartPrice = (items?: CartOrderCommonProps[], currency = 'NGN') => {
  const total = items.reduce((p, item) => {
    return item.quantity * getActualPrice(item?.variant ? item?.variant : item?.object ?? item) + p;
  }, 0);
  return toCurrency(total, currency);
};

export const getInvalidItemsText = (
  outOfStockItems: GenericBotSession['order']['invalid_items'] = [],
  condensed = false,
) => {
  const str = outOfStockItems
    .map((item) => {
      switch (item.status) {
        case 'deleted':
          return `🥡 ${item.name} - _Unavailable_\n`;
        case 'unavailable':
          return `🥡 ${item.name} - _Unavailable_\n`;
        case 'depleted':
          return `🥡 ${item.name} - _Out of stock_\n`;
        case 'over_stock':
          return `🥡 ${item.name} - _Only ${item.inventory} in stock_\n`;
        default:
          return '';
      }
    })
    .join('');

  // const str = items
  //   .map((item) => {
  //     const invalidItem = outOfStockItems.find((i) => i.id === (item?.variant?.id ?? item.item_id));
  //     return invalidItem !== undefined
  //       ? `🥡 ${getFullProductName(item)} (${item.quantity}x) -  *⚠️ Item is ${invalidItem?.status
  //           .split('_')
  //           .join(' ')}*\n`
  //       : item.object?.is_deleted || !item.object?.available
  //       ? ''
  //       : `🥡 ${getFullProductName(item)} (${condensed ? `Qty: ${item.quantity} ` : `${item.quantity}x`}) ${
  //           !condensed ? `- ${getItemFullPrice(item, currency).replace('.00', '')}` : ''
  //         }\n`;
  //   })
  //   .join('');

  if (condensed) return str.replace(/(\r\n|\n|\r)/gm, '');
  return str;
};

export const getOrderItemsText = (
  items?: CartOrderCommonProps[],
  currency = 'NGN',
  condensed = false,
  // outOfStockItems: GenericBotSession['order']['invalid_items'] = [],
) => {
  // const str = items
  //   .map((item) => {
  //     const invalidItem = outOfStockItems.find((i) => i.id === (item?.variant?.id ?? item.item_id));
  //     return invalidItem !== undefined
  //       ? `🥡 ${getFullProductName(item)} (${item.quantity}x) -  *⚠️ Item is ${invalidItem?.status
  //           .split('_')
  //           .join(' ')}*\n`
  //       : item.object?.is_deleted || !item.object?.available
  //       ? ''
  //       : `🥡 ${getFullProductName(item)} (${condensed ? `Qty: ${item.quantity} ` : `${item.quantity}x`}) ${
  //           !condensed ? `- ${getItemFullPrice(item, currency).replace('.00', '')}` : ''
  //         }\n`;
  //   })
  //   .join('');
  const str = items
    .map((item) => {
      return item.object?.is_deleted || !item.object?.available
        ? ''
        : `🥡 ${getFullProductName(item)} (${condensed ? `Qty: ${item.quantity} ` : `${item.quantity}x`}) ${
            !condensed ? `- ${getItemFullPrice(item, currency).replace('.00', '')}` : ''
          }\n`;
    })
    .join('');

  if (condensed) return str.replace(/(\r\n|\n|\r)/gm, '');
  return str;
};

export const getPublicUrl = (path: string) => process.env.CATLOG_WWW + path;
export const getChowbotUrl = (path: string) => process.env.CHOWBOT_WWW + `/${path}`;

const getItemFullPrice = (item: { object: Item; variant: any; quantity }, currency: string) => {
  return toCurrency(item.quantity * getActualPrice(item?.variant ? item?.variant : item?.object), currency);
};

export const getActualPrice = (item: Item) => {
  return item?.discount_price ? item?.discount_price : item.price;
};

const toCurrency = (amount, currency?: string, decimals?: number) => {
  return `${currency || 'NGN'} ${amountFormat(amount, decimals)}`;
};

const amountFormat = (amount, toFixed = 2) => {
  return Number(amount)
    .toFixed(toFixed)
    .toString()
    .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

const getFullProductName = (c: { object: Item; variant: any }) => {
  let name = '';

  if (c?.variant) {
    if (c?.object?.variants.type === 'custom') {
      name = `${capitalizeFirstLetter(c.object.name)} (${generateStringFromVariantValues(c?.variant?.values)})`;
    } else {
      const hasExtraOption = c.object?.variants?.options[0]?.values !== undefined;
      name = `${capitalizeFirstLetter(c.object.name)} (Option ${
        c.object.variants.options.findIndex((o) => o.id === c.variant.id) + 1
      }${hasExtraOption ? ` - ${Object.keys(c?.variant?.values)[0]} ${Object.values(c?.variant?.values)[0]}` : ''})`;
    }
  } else {
    name = capitalizeFirstLetter(c.object.name);
  }
  return name.replace(/&/g, 'and');
};

export const generateStringFromVariantValues = (values: { [key: string]: string }) => {
  let string = '';

  if (!values) {
    return '-';
  }

  Object.values(values).forEach((val, index) => {
    string += index === 0 ? val : ` - ${val}`;
  });

  return string;
};

export const getProductNameText = (items: OrderItem[]) => {
  const s = items?.length > 2 ? 's' : '';
  const andOtherItems = items?.length >= 2 ? `& ${items?.length - 1} other item${s}` : '';
  return `${items[0]?.snapshot?.name} ${andOtherItems}`;
};

export const stringifyMessage = (value: string | object, key?: string) => {
  if (typeof value === 'string' && key) {
    const d = {};
    d[key] = value;
    return JSON.stringify(d);
  }

  return JSON.stringify(value);
};

export const formatAWSResource = (uri: string) => {
  const bucketName = uri.split('.')?.[0].replace('https://', '');
  const formattedUri = uri.replace(`${bucketName}.`, '').split('.com').join(`.com/${bucketName}`);
  return formattedUri;
};

export function cleanString(input) {
  if (!input) return null;
  // Regular expression to match newlines, tabs, backspaces, and other special characters
  const specialCharsRegex = /[\n\r\t\b\f\v]/g;

  // Replace matched special characters with an empty string
  return input.replace(specialCharsRegex, '').replace(/(\r\n|\n|\r)/gm, '');
}

export function validateSitemapToken(token: string) {
  const expectedToken = process.env.SITEMAP_ACCESS_TOKEN;
  if (!token || token !== expectedToken) {
    throw new UnauthorizedException('Invalid or missing token');
  }
}
