import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Redirect,
  Req,
  UseGuards,
} from '@nestjs/common';
import { WhatsappChatbotService } from './services/whatsapp-bot.service';
import { CompleteItemsSelectionDto } from '../../models/dtos/whatsapp.dto';
import { ApiSecurity, ApiQuery, ApiOkResponse } from '@nestjs/swagger';
import { IRequest } from '../../interfaces/request.interface';
import { JwtAuthGuard } from '../../jwt/jwt-auth.guard';
import { Order } from '@getbrevo/brevo';
import { ORDER_STATUSES } from '../orders/order.schema';
import { PaginatedQueryDto } from '../wallets/dtos/search.dto';

@Controller('whatsapp-bot')
export class WhatsappChatbotController {
  constructor(private readonly whatsappChatbotService: WhatsappChatbotService, private readonly logger: Logger) {}

  @Get('/statistics')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  @ApiQuery({
    name: 'filter',
    schema: {
      type: 'object',
      properties: {
        from: {
          type: 'string',
          format: 'date',
        },
        to: {
          type: 'string',
          format: 'date',
        },
      },
    },
  })
  async getStats(@Req() req: IRequest, @Query() query: any) {
    const storeId = req.user.store.id;
    const filter = query.filter;

    const data = await this.whatsappChatbotService.getBotSessionStats(storeId, filter);
    return {
      message: 'Chatbot stats fetched successfully',
      data,
    };
  }

  @Get('/sessions/:id')
  async getSession(@Param('id') sessionId: string, @Query('store') store: string) {
    const session = await this.whatsappChatbotService.getSession(sessionId, store);
    return {
      message: 'Successfully retrieved bot session',
      data: session,
    };
  }

  @Post('complete-selection')
  async complteItemsSelection(@Body() data: CompleteItemsSelectionDto) {
    const session = await this.whatsappChatbotService.completeItemsSelection(data);
    return {
      message: 'Successfully processed items selection',
      data: session,
    };
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiSecurity('bearer')
  @ApiOkResponse({ type: [Order] })
  @ApiQuery({
    name: 'filter',
    schema: {
      type: 'object',
      properties: {
        from: {
          type: 'string',
          format: 'date',
        },
        to: {
          type: 'string',
          format: 'date',
        },
        search: {
          type: 'string',
        },
      },
    },
  })
  async getPaginatedOrders(@Req() req: IRequest, @Query() query: PaginatedQueryDto) {
    const filter = (query as any).filter || {};
    filter.store = req.user.store.id;

    const data = await this.whatsappChatbotService.getPaginatedBotSessions(filter, query);
    return {
      message: 'Chatbot sessions paginated successfully',
      data,
    };
  }

  @Get('initiate/:slug')
  @Redirect()
  async initiateChatBySlug(@Param('slug') slug: string) {
    const url = await this.whatsappChatbotService.initiateChatBySlug(slug);

    return {
      url: url,
      statusCode: 301,
    };
  }

  @Get('/:id/events')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  async getSessionEvents(@Req() req: IRequest, @Param('id') sessionId: string) {
    const data = await this.whatsappChatbotService.getSessionEvents(sessionId, req.user.store.id);
    return {
      message: 'Successfully retrieved session events',
      data,
    };
  }

  /* 
  @Get('/test-msg')
  async testMsg(@Req() req: IRequest) {
    await this.whatsappChatbotService.testMsg();
    return {
      message: "tested"
    }
  } */
}
