import { Controller } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { WhatsappChatbotService } from './services/whatsapp-bot.service';
import { ORDER_STATUSES } from '../orders/order.schema';
import { paginateOption } from '../../interfaces/whatsapp.interface';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { QUEUES, JOBS } from '../../enums/queues.enum';
import { WABotJob } from './whatsapp-bot.queue';
import { TokenSessionService } from './services/token-session.service';
import { DELIVERY_STATUS } from '../../enums/deliveries';
import { SkipThrottle } from '@nestjs/throttler';

@SkipThrottle()
@Controller()
export class WhatsappChatbotBroker {
  constructor(
    private readonly whatsappChatbotService: WhatsappChatbotService,
    private readonly tokenSessionService: TokenSessionService,
    @InjectQueue(QUEUES.WHATSAPP_BOT)
    protected readonly waBotQueue: Queue<WABotJob>,
  ) {}

  @MessagePattern(BROKER_PATTERNS.WHATSAPP_BOT.HANDLE_TEXT_MESSAGE)
  async handleTextMessage(data: any) {
    return this.whatsappChatbotService.handleMessage(data);
  }

  @MessagePattern(BROKER_PATTERNS.WHATSAPP_BOT.HANDLE_INTERACTIVE_MESSAGE)
  async handleInteractiveMessage(data: any) {
    return this.whatsappChatbotService.handleMessage(data, true);
  }

  @MessagePattern(BROKER_PATTERNS.WHATSAPP_BOT.HANDLE_SUCESSFUL_PAYMENT)
  async handleSuccessfulOrderPayment(data: { sessionId: string; paymentReference: string; receiptId: string }) {
    return this.whatsappChatbotService.processOrderPaymentSuccessful(
      data.sessionId,
      data.paymentReference,
      data.receiptId,
    );
  }

  @MessagePattern(BROKER_PATTERNS.WHATSAPP_BOT.HANDLE_ORDER_STATUS_UPDATE)
  async handleOrderStatusUpdate(data) {
    return this.whatsappChatbotService.handleOrderStatusUpdate(data);
  }

  @MessagePattern(BROKER_PATTERNS.WHATSAPP_BOT.HANDLE_FAILED_AUTO_DELIVERY)
  async handleFailedAutoDelivery(data) {
    return this.whatsappChatbotService.handleFailedAutoDelivery(data);
  }

  @MessagePattern(BROKER_PATTERNS.WHATSAPP_BOT.AUTO_DELIVERY_BOOKED)
  async autoDeliveryBooked(data) {
    return this.whatsappChatbotService.handleAutoDeliveryBooked(data);
  }

  @MessagePattern(BROKER_PATTERNS.WHATSAPP_BOT.HANDLE_MESSAGE_STATUS)
  async handleMessageStatus(data) {
    return this.whatsappChatbotService.handleMessageStatus(data);
  }

  @MessagePattern(BROKER_PATTERNS.WHATSAPP_BOT.GET_TOKEN_USAGE)
  async getTokenUsageByDate(data: { filterCriteria: any; options: paginateOption }) {
    return this.tokenSessionService.getTokenUsageByDate(data.filterCriteria, data.options);
  }

  @MessagePattern(BROKER_PATTERNS.WHATSAPP_BOT.UPDATE_PAYMENT_ID)
  async updatePaymentId(data: { dbSessionId: string; paymentId: string }) {
    return this.whatsappChatbotService.updatePaymentId(data.dbSessionId, data.paymentId);
  }

  @MessagePattern(BROKER_PATTERNS.WHATSAPP_BOT.GET_TOKEN_AVERAGE_DAILY_USAGE)
  async getTokenAverageDailyUsage(data: { storeId: string; days: number }) {
    return this.tokenSessionService.getTokenAverageDailyUsage(data.storeId, data.days);
  }

  @MessagePattern(BROKER_PATTERNS.WHATSAPP_BOT.SEND_DELIVERY_STATUS)
  async sendDeliveryStatus(data: {
    status: DELIVERY_STATUS;
    phone: string;
    items: any;
    trackingUrl: string;
    storeName: string;
    storePhone: string;
    courier: string;
    orderId: string;
  }) {
    return this.whatsappChatbotService.handleDeliveryStatusUpdate(data);
  }
}
