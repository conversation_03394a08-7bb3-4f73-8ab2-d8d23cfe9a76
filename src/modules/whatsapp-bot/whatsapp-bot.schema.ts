import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Store } from '../store/store.schema';
import mongoose, { Document } from 'mongoose';
import { Customer } from '../orders/customers/customer.schema';
import { Cart } from '../cart/cart.schema';
import { Order } from '../orders/order.schema';
import { Payment } from '../payment/payment.schema';

export enum SESSION_STATUSES {
  ONGOING = 'ONGOING',
  PAYMENT_PENDING = 'PAYMENT_PENDING',
  CONFIRMATION_PENDING = 'CONFIRMATION_PENDING',
  REJECTED = 'ORDER_REJECTED',
  UNCONFIRMED = 'UNCONFIRMED', // TIMEOUT
  DROPPED_OFF = 'DROPPED_OFF', // ENDED SESSION
  COMPLETED = 'COMPLETED',
  STORE_OFFLINE = 'STORE_OFFLINE',
  STORE_CLOSED = 'STORE_CLOSED',
  UNAVAILABLE = 'STORE_UNAVAILABLE',
  LOW_TOKEN_BALANCE = 'LOW_TOKEN_BALANCE',
}

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class WABotSession {
  _id: string;

  @ApiProperty()
  @Prop({
    type: Date,
    index: true,
  })
  created_at?: Date;

  @ApiProperty()
  id: string;

  @ApiProperty()
  @Prop({ type: String, required: false })
  conversation_id?: string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  from: string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  to: string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  storeCode: string;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Store', required: false })
  store?: Store | string;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Customer', required: false })
  customer?: Customer | string;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Payment', required: false })
  payment?: Payment | string;

  @ApiProperty()
  @Prop({ type: String, ref: 'Order', required: false })
  order?: Order | string;

  @ApiProperty()
  @Prop({ type: Date, required: false })
  ended_at?: Date;

  @ApiProperty()
  @Prop({ type: Boolean, required: false })
  is_billable?: boolean;

  @ApiProperty()
  @Prop({ type: SESSION_STATUSES, enum: Object.values(SESSION_STATUSES), required: true })
  status?: SESSION_STATUSES;

  @ApiProperty()
  @Prop({ type: Boolean, required: false })
  auto_checkin_later?: boolean; //to delay auto-checkins that fall into store closing hours
}

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class StoreOpenedReminder {
  _id: string;

  @ApiProperty()
  @Prop({
    type: Date,
    index: true,
  })
  created_at?: Date;

  @ApiProperty()
  id: string;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Store', required: false })
  store?: Store | string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  closedReason: 'maintenance' | 'closed';

  @ApiProperty()
  @Prop({ type: Date, required: false })
  openingTime: Date;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Customer', required: false })
  customer?: Customer | string;
}

export type BotSessionDocument = WABotSession & Document;
export const BotSessionSchema = SchemaFactory.createForClass(WABotSession);

export type StoreOpenedReminderDocument = StoreOpenedReminder & Document;
export const StoreOpenedReminderSchema = SchemaFactory.createForClass(StoreOpenedReminder);
