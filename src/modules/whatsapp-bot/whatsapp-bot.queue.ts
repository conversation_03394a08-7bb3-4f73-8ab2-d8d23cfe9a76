import {
  InjectQueue,
  OnQueueActive,
  OnQueueCompleted,
  OnQueueFailed,
  OnQueueRemoved,
  Process,
  Processor,
} from '@nestjs/bull';
import { CACHE_MANAGER, Inject } from '@nestjs/common';
import { Job, Queue } from 'bull';
import { Cache } from 'cache-manager';
import { BrokerTransportService } from '../../broker/broker-transport.service';
import { JOBS, QUEUES } from '../../enums/queues.enum';
import { GenericBotSession, OrderConfirmationSession, SESSION_TYPES } from '../../interfaces/whatsapp.interface';
import { WhatsappChatbotService } from './services/whatsapp-bot.service';
import { getContext, getCurrentSessionTtl } from './utils/functions.utils';
import { SellerProcessor } from './services/seller.processor.service';
import { CACHE_TTL, GET_APP_TIMEOUT, UTILITY_CACHE_PREFIX, WA_BOT_PHONE_IDS } from '../../utils/wabot-constants';
import { WhatsappMessagingService } from './services/messaging.service';
import { MESSAGES } from './utils/messages.utils';
import { formatPhoneNumber, getAppEnv } from '../../utils';
import { ONE_MINUTE } from '../../utils/constants';

export interface WABotJob<T = any> {
  type: JOBS;
  data: T;
}

export interface SessionTimeOutJob {
  sessionKey: string;
  orderId?: string;
}

export interface RefreshStoreMenuJob {
  storeId: string;
}
export interface StoreReminderJob {
  customerPhone: string;
  storeName: string;
  storeCode: string;
  customerName?: string;
}

@Processor(QUEUES.WHATSAPP_BOT)
export class WABotQueue {
  constructor(
    @InjectQueue(QUEUES.WHATSAPP_BOT)
    protected readonly waBotQueue: Queue<WABotJob>,
    @Inject(CACHE_MANAGER) protected readonly cacheManager: Cache,
    protected readonly brokerTransport: BrokerTransportService,
    private readonly whatsappChatbotService: WhatsappChatbotService,
    private readonly sellerProcessorService: SellerProcessor,
    protected readonly messaging: WhatsappMessagingService,
  ) {}

  @OnQueueActive()
  onActive(job: Job<any>) {
    console.log(`Processing job ${job.id} of type ${job.name} with data ${job.data.data.user_name}...`);
  }

  @OnQueueFailed()
  onFailed(job: Job, error: Error) {
    console.log(
      `Failed to process job ${job.id} of type ${job.name} with data ${job.data.type} , error: ${error.message}`,
    );
  }

  @OnQueueRemoved()
  onRemove(job: Job, error: Error) {
    console.log(`Removed job ${job.id} of type ${job.name} with data ${job.data.data}`);
  }

  @OnQueueCompleted()
  onCompleted(job: Job, result: any) {
    console.log(`Job ${job.id} of type ${job.name} successfully completed, result: ${result}`);
    job.remove().catch(console.log);
  }

  @Process(QUEUES.WHATSAPP_BOT)
  async handle(job: Job<WABotJob>) {
    switch (job.data.type) {
      case JOBS.WABOT_SESSION_TIMEOUT:
        await this.endSession(job);
        break;
      case JOBS.ORDER_CONFIRMATION_TIMEOUT:
        await this.endOrderConfirmationJob(job);
        break;
      case JOBS.WABOT_PAYMENT_REMINDER:
        await this.handlePaymentReminder(job);
        break;
      case JOBS.SEND_STORE_OPENED_REMINDER:
        await this.handleSendStoreOpenedReminder(job);
        break;
      default:
      // DO NOTHING
    }
  }

  async endSession(job: Job<WABotJob<SessionTimeOutJob>>) {
    try {
      const sessionKey = job.data.data.sessionKey;
      const session = await this.cacheManager.get<GenericBotSession>(sessionKey);
      const extensionTime = GET_APP_TIMEOUT('generic_extension'); //mainly 5mins in prod

      console.log({ extensionTime });

      if (!session) return;

      const timeDifferenceFromLastIncomingMessage =
        new Date().getTime() - new Date(session.last_incoming_message_time).getTime();

      const confirmationSessionCacheKey = `${UTILITY_CACHE_PREFIX}:${session?.order?.order_id?.toUpperCase()}`;
      const confirmationSession = await this.cacheManager.get<OrderConfirmationSession>(confirmationSessionCacheKey);

      // Send reminders
      if (session.metadata?.reminder_count < 2 || session.metadata?.reminder_count === undefined) {
        if (timeDifferenceFromLastIncomingMessage > extensionTime) {
          await this.messaging.send(MESSAGES.STORE_REMINDER_MESSAGE, getContext(session), {
            count: session.metadata?.reminder_count ?? 0,
          });
        }

        const newJob = await job.queue.add(QUEUES.WHATSAPP_BOT, job.data, { delay: GET_APP_TIMEOUT('generic') / 6 });
        session.timeout_job = newJob.id.toString();
        session.metadata = {
          ...session.metadata,
          reminder_count: session.metadata?.reminder_count ? session.metadata?.reminder_count + 1 : 1,
        };
        await this.cacheManager.set(sessionKey, session, { ttl: getCurrentSessionTtl(session, CACHE_TTL) });
        return;
      }

      //Extend time if it user recently sent a response
      if (timeDifferenceFromLastIncomingMessage < extensionTime || confirmationSession) {
        const newDelay = extensionTime;
        const newJob = await job.queue.add(QUEUES.WHATSAPP_BOT, job.data, { delay: newDelay });

        session.timeout_job = newJob.id.toString();
        await this.cacheManager.set(sessionKey, session, { ttl: getCurrentSessionTtl(session, CACHE_TTL) });
      } else if (session.type === SESSION_TYPES.GENERIC) {
        const ctx = getContext(session);
        await this.whatsappChatbotService.endSession(ctx, session, true);
      }
    } catch (error) {
      console.log('<========== SOMETHING WENT WRONG ENDING SESSION FROM JOB =========>');
      console.log(error);
    }
  }

  async endOrderConfirmationJob(job: Job<WABotJob<SessionTimeOutJob>>) {
    try {
      await this.sellerProcessorService.handleTimeOut(job);
      return {};
    } catch (error) {
      console.log('<========== SOMETHING WENT WRONG HANDLING TIMEOUT FOR ORDER =========>');
      console.log(error);
    }
  }

  async handlePaymentReminder(job: Job<WABotJob<SessionTimeOutJob>>) {
    try {
      await this.whatsappChatbotService.sendPaymentReminder(job.data.data.sessionKey);
      return {};
    } catch (error) {
      console.log('<========== SOMETHING WENT WRONG SENDING PAYMENT REMINDER =========>');
      console.log(error);
    }
  }

  async handleSendStoreOpenedReminder(job: Job<WABotJob<StoreReminderJob>>) {
    const botPhoneId = WA_BOT_PHONE_IDS[getAppEnv()][0];
    const { customerPhone, storeCode, storeName, customerName } = job?.data?.data;
    try {
      await this.messaging.send(
        MESSAGES.STORE_OPENED_REMINDER,
        { botPhoneId, destinationPhone: formatPhoneNumber(customerPhone) },
        {
          customerName: customerName ?? 'There',
          storeName,
          storeCode,
        },
      );
      return {};
    } catch (error) {
      console.log('<========== SOMETHING WENT WRONG SENDING STORE OPENED REMINDER =========>');
      console.log(error);
    }
  }
}
