import { Controller } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { FilterQuery, Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { ReceiptsService } from './receipts.service';
import { Receipt, ReceiptDocument } from './receipts.schema';
import { Invoice } from '../invoice/invoice.schema';
import { PAYMENT_METHODS } from '../../enums/payment.enum';
import { SkipThrottle } from '@nestjs/throttler';

@SkipThrottle()
@Controller()
export class ReceiptsBroker {
  constructor(
    private readonly receiptService: ReceiptsService,
    @InjectModel(Receipt.name)
    private readonly receiptModel: Model<ReceiptDocument>,
  ) {}

  @MessagePattern(BROKER_PATTERNS.RECEIPT.GENERATE_FROM_ORDER)
  async generateReceiptFromOrder({ order_id, store_id }: { order_id: string; store_id: string }) {
    return await this.receiptService.createReceiptFromOrder(order_id, store_id);
  }

  @MessagePattern(BROKER_PATTERNS.RECEIPT.GENERATE_FROM_INVOICE)
  async generateReceiptFromInvoice({ invoice, payment_method }: { invoice: Invoice; payment_method: PAYMENT_METHODS }) {
    return await this.receiptService.createReceiptFromInvoice(invoice, payment_method);
  }

  @MessagePattern(BROKER_PATTERNS.RECEIPT.GET_RECEIPT_PDF_LINK)
  async getReceiptPdfLink(receiptId) {
    return await this.receiptService.getReceiptPdfUrl(receiptId);
  }
}
