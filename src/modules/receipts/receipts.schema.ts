import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { PAYMENT_METHODS } from '../../enums/payment.enum';
import { Store } from '../store/store.schema';
import { FEE_TYPES } from '../../enums/order.enum';
import { CURRENCIES } from '../country/country.schema';

export type ReceiptDocument = Receipt & Document;

@Schema({ timestamps: true })
export class Receipt {
  _id: any;

  @ApiProperty()
  @Prop({ type: String, required: true })
  receipt_id: string;

  @ApiProperty()
  @Prop({ type: Date, required: true })
  paid_at: Date;

  @ApiProperty()
  @Prop({
    type: String,
    enum: [
      PAYMENT_METHODS.ZILLA,
      PAYMENT_METHODS.PAYSTACK,
      PAYMENT_METHODS.TRANSFER,
      PAYMENT_METHODS.MONO_DIRECT_PAY,
      PAYMENT_METHODS.THEPEER,
      PAYMENT_METHODS.UNKNOWN,
      PAYMENT_METHODS.STARTBUTTON,
      PAYMENT_METHODS.MOMO,
      PAYMENT_METHODS.STRIPE,
      PAYMENT_METHODS.LEATHERBACK,
    ],
  })
  payment_method?: PAYMENT_METHODS;

  @ApiProperty()
  @Prop({ type: String })
  invoice?: string;

  @ApiProperty()
  @Prop({ type: String })
  order?: string;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Store' })
  store: string | Store;

  @ApiProperty()
  @Prop({
    enum: [
      CURRENCIES.GHC,
      CURRENCIES.NGN,
      CURRENCIES.USD,
      CURRENCIES.EUR,
      CURRENCIES.GBP,
      CURRENCIES.ZAR,
      CURRENCIES.KES,
    ],
    default: CURRENCIES.NGN,
  })
  currency: CURRENCIES = CURRENCIES.NGN;
}

export class Sender {
  @ApiProperty()
  @Prop({ type: String })
  image: string;

  @ApiProperty()
  @Prop({ type: String })
  name: string;

  @ApiProperty()
  @Prop({ type: String })
  address: string;

  @ApiProperty()
  @Prop({ type: String })
  phone: string;
}

export class Receiver {
  @ApiProperty()
  @Prop({ type: String })
  name: string;

  @ApiProperty()
  @Prop({ type: String })
  phone: string;

  @ApiProperty()
  @Prop({ type: String })
  email: string;
}

export class ReceiptItem {
  @ApiProperty()
  @Prop({ type: String })
  name: string;

  @ApiProperty()
  @Prop({ type: Number })
  price: number;

  @ApiProperty()
  @Prop({ type: Number })
  quantity: number;
}

export class ReceiptFee {
  @ApiProperty()
  @Prop({ type: Number })
  amount: number;

  @ApiProperty()
  @Prop({
    enum: [
      FEE_TYPES.DELIVERY,
      FEE_TYPES.DISCOUNT,
      FEE_TYPES.VAT,
      FEE_TYPES.COUPON,
      FEE_TYPES.OTHERS,
      FEE_TYPES.PAYMENT,
    ],
  })
  type: FEE_TYPES;
}

export class FormattedReceipt {
  @ApiProperty()
  @Prop({ type: String })
  receipt_id: string;

  @ApiProperty()
  @Prop({ type: Date })
  paid_at: Date;

  @ApiProperty()
  @Prop({ type: String })
  payment_method?: String;

  @ApiProperty()
  @Prop({ type: Sender })
  store: Sender;

  @ApiProperty()
  @Prop({ type: Receiver })
  customer: Receiver;

  @ApiProperty()
  @Prop({ type: [ReceiptItem] })
  items: ReceiptItem[];

  @ApiProperty()
  @Prop({ type: [ReceiptItem] })
  fees: ReceiptFee[];

  @ApiProperty()
  @Prop({ type: Number })
  total_amount: number;

  @ApiProperty()
  @Prop({ type: String })
  currency: CURRENCIES = CURRENCIES.NGN;
}

export const ReceiptSchema = SchemaFactory.createForClass(Receipt);
