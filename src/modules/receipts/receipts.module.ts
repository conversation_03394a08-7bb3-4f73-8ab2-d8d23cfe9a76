import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { jsonHookMongoosePlugin, setIdMongoosePlugin } from '../../mongoose-plugins';
import { SharedModule } from '../../shared.module';
import mongoosePaginate from 'mongoose-paginate-v2';
import { Receipt, ReceiptSchema } from './receipts.schema';
import { ReceiptsController } from './receipts.controller';
import { ReceiptsService } from './receipts.service';
import { ReceiptsBroker } from './receipt.broker';

@Module({
  imports: [
    MongooseModule.forFeatureAsync([
      {
        name: Receipt.name,
        useFactory: () => {
          ReceiptSchema.plugin(setIdMongoosePlugin());
          ReceiptSchema.plugin(jsonHookMongoosePlugin(['_id', '__v']));
          ReceiptSchema.plugin(mongoosePaginate);
          return ReceiptSchema;
        },
      },
    ]),
    SharedModule,
  ],
  controllers: [ReceiptsController, ReceiptsBroker],
  providers: [ReceiptsService],
})
export class ReceiptModule {}
