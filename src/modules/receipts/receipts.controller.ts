import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  Redirect,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { ApiOkResponse, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { Readable } from 'stream';
import { InternalApiKeyGuard } from '../../guards/internal-api-key.guard';
import { PlanGuard } from '../../guards/plan.guard';
import { IRequest, IResponse } from '../../interfaces/request.interface';
import { JwtAuthGuard } from '../../jwt/jwt-auth.guard';
import { SCOPES } from '../../utils/permissions.util';
import { PlanPermissions } from '../../decorators/permission.decorator';
import { GenerateFromOrderDto } from './receipts.dto';
import { ReceiptsService } from './receipts.service';
import { PaginatedQueryDto } from '../../models/dtos/PaginatedDto';
import { Receipt } from './receipts.schema';

@ApiTags('Receipts')
@Controller('receipts')
export class ReceiptsController {
  constructor(private readonly receiptsService: ReceiptsService) {}

  @Post('/generate-from-order')
  @PlanPermissions(SCOPES.RECEIPTS_PERMISSIONS.CAN_GENERATE_RECEIPTS)
  @UseGuards(JwtAuthGuard, PlanGuard)
  async generateFromOrder(@Body() data: GenerateFromOrderDto, @Req() req: IRequest) {
    // const link = await this.urlShortenerService.generateLink(url);
    const receipt = await this.receiptsService.generateReceiptFromOrder(data.order_id, req.user.store.id);

    return {
      message: 'Receipt Generated',
      data: receipt,
    };
  }

  @Get('')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  @ApiOkResponse({ type: [Receipt] })
  async getReceipts(@Req() req: IRequest, @Query() query: PaginatedQueryDto) {
    const filter = query.filter || {};
    filter.store = req.user.store.id;

    const data = await this.receiptsService.getReceipts(filter, query);
    return {
      message: 'Receipts fetched successfully',
      data,
    };
  }

  @Get('/:id')
  async getReceipt(@Param('id') receipt_id: string) {
    const receipt = await this.receiptsService.getReceipt(receipt_id);

    return {
      message: 'Receipt Fetched',
      data: receipt,
    };
  }

  @Get('/pdf/:id')
  async getPdf(@Param('id') id: string, @Res() res: IResponse) {
    const buffer = await this.receiptsService.generateReceiptPdf(id);
    const readable = new Readable();
    readable._read = () => {};
    readable.push(buffer);
    readable.push(null);
    readable.pipe(res);
    return {};
  }

  @Get('/pdf/:id/url')
  async getPdfUrl(@Param('id') id: string) {
    const url = await this.receiptsService.getReceiptPdfUrl(id);

    return {
      message: 'Receipt url fetched',
      data: url,
    };
  }
}
