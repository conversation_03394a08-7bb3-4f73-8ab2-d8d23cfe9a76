import { BadRequestException, ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, PaginateModel } from 'mongoose';
import { BrokerTransportService } from '../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { PAYMENT_METHODS } from '../../enums/payment.enum';
import { S3Repository } from '../../repositories/s3.repositories';
import { genChars, getUserFriendlyPaymentMethod, mapPaginatedResponse, mapPaginateQuery } from '../../utils';
import { Country } from '../country/country.schema';
import { Invoice, INVOICE_STATUSES } from '../invoice/invoice.schema';
import { Customer } from '../orders/customers/customer.schema';
import { Order, ORDER_STATUSES } from '../orders/order.schema';
import { Store } from '../store/store.schema';
import { FormattedReceipt, Receipt, ReceiptDocument } from './receipts.schema';
import { generatePDFFromWebpage } from '../../utils/generate-pdfs';
import { PaginatedQueryDto } from '../../models/dtos/PaginatedDto';

@Injectable()
export class ReceiptsService {
  constructor(
    @InjectModel(Receipt.name)
    private readonly receiptModel: PaginateModel<ReceiptDocument>,
    protected readonly brokerTransport: BrokerTransportService,
    protected readonly s3: S3Repository,
    protected readonly configService: ConfigService,
  ) {}

  async generateReceiptFromOrder(order_id: string, store_id: string) {
    const data = await this.createReceiptFromOrder(order_id, store_id);

    if ((data as any)?.error) {
      throw new BadRequestException((data as any)?.error);
    }

    return data;
  }

  async getReceipt(receipt_id: string) {
    const receipt = await this.receiptModel.findOne({ receipt_id });
    let order: Order, invoice: Invoice, formatted_receipt: FormattedReceipt;

    if (!receipt) {
      throw new NotFoundException('Receipt not found');
    }

    if ((!receipt.invoice && !receipt.order) || (receipt.invoice && receipt.order)) {
      throw new BadRequestException('Invalid Receipt');
    }

    if (receipt.order) {
      order = await this.brokerTransport
        .send<Order>(BROKER_PATTERNS.ORDER.GET_ORDER, { _id: receipt.order })
        .toPromise();

      if (!order) {
        throw new BadRequestException('Order linked to receipt not found');
      }

      const store = order.store as Store;
      const customer = order.customer as Customer;

      formatted_receipt = {
        receipt_id: receipt.receipt_id,
        paid_at: receipt.paid_at,
        payment_method: getUserFriendlyPaymentMethod(receipt.payment_method),
        store: {
          name: store.name,
          image: store.logo,
          address: store.address,
          phone: store.phone,
        },
        customer: {
          name: customer.name,
          email: customer.email,
          phone: customer.phone,
        },
        fees: order.fees,
        total_amount: order.total_amount,
        items: order.items.map((i) => ({
          quantity: i.quantity,
          name: i.snapshot.name,
          price: i.variant ? i.variant.price : i.snapshot.price,
        })),
        currency: receipt.currency,
      };
    }

    if (receipt.invoice) {
      invoice = await this.brokerTransport
        .send<Invoice>(BROKER_PATTERNS.INVOICE.GET_INVOICE, { invoice_id: receipt.invoice })
        .toPromise();

      if (!invoice) {
        throw new BadRequestException('Invoice linked to receipt not found');
      }

      formatted_receipt = {
        receipt_id: receipt.receipt_id,
        paid_at: receipt.paid_at,
        payment_method: getUserFriendlyPaymentMethod(receipt.payment_method),
        store: invoice.sender,
        customer: invoice.receiver,
        fees: invoice.fees,
        total_amount: invoice.total_amount,
        items: invoice.items,
        currency: receipt.currency,
      };
    }

    return formatted_receipt;
  }

  async getReceipts({ ...filterQuery }: FilterQuery<Receipt>, paginationQuery: PaginatedQueryDto) {
    if (paginationQuery.sort === 'ASC') {
      paginationQuery.sort = { paid_at: 1 };
    }

    if (paginationQuery.sort === 'DESC') {
      paginationQuery.sort = { paid_at: -1 };
    }

    const result = await this.receiptModel.paginate(filterQuery, {
      ...mapPaginateQuery(paginationQuery),
    });

    return {
      data: result.docs,
      ...mapPaginatedResponse(result),
    };
  }

  async createReceiptFromOrder(order_id: string, store_id: string) {
    let receipt: ReceiptDocument;

    const order = await this.brokerTransport
      .send<Order>(BROKER_PATTERNS.ORDER.GET_ORDER_LEAN, { _id: order_id })
      .toPromise();
    const store = order.store as Store;

    if (!order) {
      return {
        error: 'Order not found',
      };
    }

    if (store?._id !== store_id) {
      return {
        error: 'You do not have access to this order',
      };
    }

    if (order.status === ORDER_STATUSES.PENDING) {
      return {
        error: 'Please confirm order to generate receipt',
      };
    }

    if ([ORDER_STATUSES.CANCELLED, ORDER_STATUSES.ABANDONED].includes(order.status)) {
      return {
        error: 'Cannot generate receipt for a cancelled order. If order is abandoned, please move to processing',
      };
    }

    receipt = await this.receiptModel.findOne({ order: order_id });

    if (receipt) {
      return {
        error: 'A receipt already exists for this order',
      };
    }

    const country = await this.brokerTransport
      .send<Country>(BROKER_PATTERNS.COUNTRY.GET_COUNTRY, { code: store?.country })
      .toPromise();

    receipt = await this.receiptModel.create({
      receipt_id: await this.generateReceiptID(),
      paid_at: new Date(),
      order: order_id,
      payment_method: PAYMENT_METHODS.UNKNOWN,
      store: store.id,
      currency: order.currency,
    });

    await this.brokerTransport
      .send(BROKER_PATTERNS.ORDER.ADD_ORDER_RECEIPT, {
        filter: { _id: order_id },
        payload: receipt.receipt_id,
      })
      .toPromise();

    return receipt;
  }

  async createReceiptFromInvoice(invoice: Invoice, payment_method: PAYMENT_METHODS) {
    let receipt: ReceiptDocument;

    if (!invoice) {
      return {
        error: 'Invalid invoice',
      };
    }

    if (invoice.status === INVOICE_STATUSES.PENDING) {
      return {
        error: 'Please mark invoice as paid to generate receipt',
      };
    }

    if (invoice.status === INVOICE_STATUSES.EXPIRED) {
      return {
        error: 'Cannot generate receipt for an expired invoice',
      };
    }

    receipt = await this.receiptModel.findOne({ invoice: invoice.invoice_id });

    if (receipt) {
      return {
        error: 'A receipt already exists for this invoice',
      };
    }

    receipt = await this.receiptModel.create({
      receipt_id: await this.generateReceiptID(),
      paid_at: new Date(),
      invoice: invoice.invoice_id,
      payment_method: payment_method,
      store: invoice?.store,
      currency: invoice?.currency,
    });

    return receipt;
  }

  async generateReceiptPdf(receiptId: string) {
    const url = process.env.CATLOG_WWW + '/receipts/pdf/' + receiptId;
    const data = await generatePDFFromWebpage(url, 400);

    if (data.pdf) {
      return data.pdf;
    } else {
      throw new BadRequestException("Couldn't generate invoice PDF");
    }

    // try {
    //   const url = process.env.CATLOG_WWW + '/receipts/pdf/' + receiptId;
    //   const puppeteer = await import('puppeteer');
    //   const browser = await puppeteer.launch({
    //     executablePath: process.env.CHROME_BIN || null,
    //     args: ['--no-sandbox'],
    //   });
    //   const page = await browser.newPage();

    //   await page.goto(url, { waitUntil: 'networkidle0' });
    //   await page.emulateMediaType('screen');

    //   const bodyHandle = await page.$('body');
    //   const boundingBox = await bodyHandle.boundingBox();
    //   const height = boundingBox.height;

    //   const pdf = await page.pdf({
    //     printBackground: true,
    //     width: '400px',
    //     height,
    //   });

    //   await browser.close();
    //   return pdf;
    // } catch (e) {
    //   console.log(e);
    //   throw new BadRequestException('Invalid invoice url');
    // }
  }

  async getReceiptPdfUrl(receiptId: string) {
    const pdf = await this.generateReceiptPdf(receiptId);
    const receiptS3 = this.s3.withPath('/receipts/');

    const upload = await receiptS3.uploadBuffer(
      pdf,
      receiptId + '.pdf',
      'application/pdf',
      new Date(Date.now() + 1000 * 60 * 60 * 24),
    );

    return upload.Location;
  }

  async generateReceiptID() {
    let id = 'CLGR-' + genChars(8, false, true);
    while (await this.receiptModel.exists({ receipt_id: id })) {
      id = 'CLGR-' + genChars(8, false, true);
    }

    return id;
  }
}
