import { ClientSession, Model } from 'mongoose';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { PAYMENT_METHODS, PAYMENT_STATUS, PAYMENT_TYPES, SUBSCRIPTION_STATUS } from '../../../enums/payment.enum';
import { PLAN_TYPE } from '../../../enums/plan.enum';
import { CREDITS } from '../../../utils/constants';
import { formatDate, getDateAt } from '../../../utils/time';
import { CURRENCIES, CURRENCY_COUNTRY_MAP } from '../../country/country.schema';
import { Plan } from '../../plan/plan.schema';
import { Subscription } from '../../subscription/subscription.schema';
import { CatlogCredits, CatlogCreditsTransactions } from '../../user/credits/credits.schema';
import { User } from '../../user/user.schema';
import { Card, CardDocument } from '../card.schema';
import { Payment, PaymentDocument } from '../payment.schema';
import { PaymentService } from './index.service';
import { InjectModel, InjectConnection } from '@nestjs/mongoose';
import mongoose from 'mongoose';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { Logger } from '@nestjs/common';
import { Address, Delivery } from '../../deliveries/deliveries.schema';
import { PaymentsResolverService } from './payments.resolver.service';
import { CustomerIoRepository } from '../../../repositories/customer-io.repository';
export class PaymentDeliveriesSevice extends PaymentsResolverService {
  constructor(
    protected readonly paymentService: PaymentService,
    @InjectModel(Payment.name)
    public readonly paymentModel: Model<PaymentDocument>,
    @InjectModel(Card.name)
    protected readonly cardModel: Model<CardDocument>,
    public readonly brokerTransport: BrokerTransportService,
    public readonly logger: Logger,
    public readonly customerIo: CustomerIoRepository,
    @InjectConnection()
    protected readonly connection: mongoose.Connection,
  ) {
    super(paymentModel, cardModel, brokerTransport, logger, customerIo);
  }

  async paystackWebhook(data: any, payment: PaymentDocument) {
    const { authorization } = data;

    if (payment.status === PAYMENT_STATUS.SUCCESS) {
      this.logger.log(`this payment is already successful, payment: ${payment}, data: ${data}`);
      return;
    }

    this.handleSuccessfulDeliveryPayments(payment, { amount: data.amount, fees: data.fees });
  }

  async handleSuccessfulDeliveryPayments(
    payment: PaymentDocument,
    gatewayPaymentInfo: { amount: number; fees: number },
  ) {
    const user = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, { _id: payment.owner })
      .toPromise();

    const delivery = await this.brokerTransport
      .send<Delivery>(BROKER_PATTERNS.DELVERIES.GET_DELIVERY, { _id: payment.meta?.delivery })
      .toPromise();

    if (!delivery) {
      return;
    }

    try {
      const session = await this.connection.startSession();
      session.startTransaction();

      const resolvedPayment = await this.resolveAndUpdatePayment(payment, gatewayPaymentInfo, session);

      await session.commitTransaction(async () => {
        if (resolvedPayment?.partner_payment) {
          await this.updateCreditPayment(
            resolvedPayment,
            `Payment for delivery to ${(delivery?.receiver_address as Address).name}`,
          );
        }

        await this.brokerTransport
          .send(BROKER_PATTERNS.DELVERIES.INITIATE_DELIVERY, {
            deliveryId: payment.meta?.delivery,
            paymentRef: resolvedPayment?.reference,
          })
          .toPromise();

        // await this.sendNotifications(payment);
        await this.sendSSEEvent(payment);
      });
    } catch (error) {
      this.logger.log(`error occurred in processing payment ${error}`);
    }
  }
}
