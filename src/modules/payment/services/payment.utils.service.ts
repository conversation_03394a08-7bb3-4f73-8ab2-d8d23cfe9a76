import { PaymentData, PaymentDocument } from '../payment.schema';
import { PaymentService } from './index.service';
import { BadRequestException } from '@nestjs/common';
import {
  MININUM_PAYABLE,
  MOBILE_MONEY_NETWORK,
  PAYMENT_PROVIDERS,
  PAYMENT_STATUS,
  PAYMENT_TYPES,
} from '../../../enums/payment.enum';
import { PAYMENT_METHODS } from '../../../enums/payment.enum';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { PaymentMethod } from '../../paymentmethod/paymentmethod.schema';
import { InitiatePaymentDto } from '../payment.dto';
import { Store } from '../../store/store.schema';
import { PaymentCouponDocument } from '../coupons/payment-coupons.schema';
import { COUNTRY_CODE, COUNTRY_CURRENCY_MAP, CURRENCIES } from '../../country/country.schema';
import { TRANSACTION_CHANNELS } from '../../wallets/wallet.schema';
import { Plan } from '../../plan/plan.schema';
import { PlanOption } from '../../plan/plan-options/plan-options.schema';
import { getDocId, toNaira } from '../../../utils/functions';
import { Subscription } from '../../subscription/subscription.schema';
import { calculateSubscriptionAmountLeft, toKobo } from '../../../utils/functions';
import { computePaymentCouponDiscount } from '../coupons/utils';
import { genChars } from '../../../utils';
import { CatlogCredits } from '../../user/credits/credits.schema';
import {
  SUBSCRIPTION_CARD_DISCOUNT,
  SUBSCRIPTION_DD_DISCOUNT,
  applyDiscount,
  paystackFeeCalculator,
  startbuttonFeeCalculator,
  walletPaymentFeeCalculator,
  zeepayFeeCalculator,
} from '../../../utils/fees';
import { CURRENT_EXCHANGE_RATES, UPFRONT_SUBSCRIPTION_AMOUNTS } from '../../../utils/constants';

export class PaymentUtilsService {
  constructor(protected readonly paymentService: PaymentService) {}

  /**
   * Validates payment methods for the given payment request
   */
  public async validatePaymentMethods(body: InitiatePaymentDto, store: Store): Promise<PaymentMethod[]> {
    const { type, payment_methods, currency } = body;

    let availablePaymentMethods = await this.paymentService.brokerTransport
      .send<PaymentMethod[]>(BROKER_PATTERNS.PAYMENT.GET_ALL_PAYMENT_METHODS, {
        payment_types: { $in: [type] },
        currencies: { $in: currency },
        enabled: true,
      })
      .toPromise();

    if (type === PAYMENT_TYPES.SUBSCRIPTION) {
      availablePaymentMethods = [
        ...availablePaymentMethods,
        ...[{ type: PAYMENT_METHODS.CARD } as any, { type: PAYMENT_METHODS.DIRECT_DEBIT }],
      ];
    }

    const hasInvalidPaymentMethods = payment_methods.some((m) => !availablePaymentMethods.find((a) => a.type === m));

    if (hasInvalidPaymentMethods) {
      throw new BadRequestException('Invalid payment method, please reload page');
    }

    const useCatlogCredits = payment_methods.includes(PAYMENT_METHODS.CATLOG_CREDIT);

    // Make sure payment method contains just one method if catlog credits is missing
    if (!useCatlogCredits && payment_methods.length > 1) {
      throw new BadRequestException('Please select just one payment method');
    }

    return availablePaymentMethods;
  }

  /**
   * Handles coupon validation and application
   */
  public async handleCoupon(body: InitiatePaymentDto, store: Store): Promise<PaymentCouponDocument> {
    const { coupon, currency, type } = body;

    if (!coupon) {
      return null;
    }

    const couponDoc = await this.paymentService.paymentCouponModel.findOneAndUpdate(
      {
        coupon_code: coupon.trim().toUpperCase(),
        quantity: { $gt: 0 },
        used_by: { $nin: [store?.id] },
        end_date: { $gt: new Date() },
        active: true,
        currency,
      },
      {
        $inc: { quantity: -1 },
        $push: { used_by: store?.id },
      },
    );

    if (!couponDoc) throw new BadRequestException('This coupon is invalid or has been used');

    if (type !== couponDoc.payment_type) {
      throw new BadRequestException('You cannot use this coupon for this payment');
    }

    if (currency !== couponDoc.currency) {
      throw new BadRequestException('Coupon is invalid for this currency');
    }

    return couponDoc;
  }

  /**
   * Calculates the amount to be paid for a subscription
   */
  public async calculateSubscriptionAmount(
    plan: Plan,
    planOption: PlanOption,
    store: Store,
    coupon: PaymentCouponDocument,
    currency: CURRENCIES,
    upfront: boolean = false,
  ) {
    let subscription;
    let amountLeft = 0;

    if (!upfront) {
      subscription = store?.subscription as Subscription;
      const currentSubscriptionPlanOption = await this.paymentService.brokerTransport
        .send<PlanOption>(BROKER_PATTERNS.PLAN.GET_PLAN_OPTION, { id: subscription?.plan_option })
        .toPromise();

      const lastPayment = subscription?.last_payment_reference
        ? await this.paymentService.paymentModel.findOne({
            reference: subscription?.last_payment_reference,
          })
        : null;
      amountLeft = calculateSubscriptionAmountLeft(subscription, currentSubscriptionPlanOption, lastPayment?.amount);
    }

    const amountToPay = upfront ? toNaira(UPFRONT_SUBSCRIPTION_AMOUNTS[plan.type][currency]) : planOption.amount;
    const discountAmount = Boolean(coupon) ? computePaymentCouponDiscount(amountToPay, coupon) : 0;
    const amountWithDiscount = Math.max(0, amountToPay + discountAmount - amountLeft);

    const totalPayable = toKobo(amountWithDiscount);
    const balanceLeft = totalPayable;

    const meta = {
      plan: getDocId(plan),
      plan_option: getDocId(planOption),
      subscription: subscription ? getDocId(store?.subscription as Subscription) : null,
      store: getDocId(store),
      is_upfront: upfront,
      coupon: coupon
        ? {
            id: getDocId(coupon),
            coupon_code: coupon?.coupon_code,
            discount: discountAmount,
            original_amount: this.convertToKobo(planOption.amount),
          }
        : undefined,
    };

    const narration = `Payment for ${plan.name} plan subscription`;
    const entityCurrency = COUNTRY_CURRENCY_MAP[planOption.country as COUNTRY_CODE];
    const walletTxChannel = TRANSACTION_CHANNELS.SUBSCRIPTION_PAYMENT;
    const walletTxSource = { purpose: 'Subscription payment', name: 'Catlog' };

    return {
      totalPayable,
      balanceLeft,
      meta,
      narration,
      entityCurrency,
      walletTxChannel,
      walletTxSource,
    };
  }

  /**
   * Processes payment based on selected payment methods
   */
  public async processPaymentMethods(
    payment_methods: PAYMENT_METHODS[],
    store: Store,
    totalPayable: number,
    balanceLeft: number,
    meta: any,
    narration: string,
    entityCurrency: CURRENCIES,
    walletTxChannel: TRANSACTION_CHANNELS,
    walletTxSource: any,
    currency: CURRENCIES,
    type: PAYMENT_TYPES,
  ): Promise<PaymentData> {
    const useCatlogCredits = payment_methods.includes(PAYMENT_METHODS.CATLOG_CREDIT);
    const paystackMethods = [PAYMENT_METHODS.CARD, PAYMENT_METHODS.PAYSTACK, PAYMENT_METHODS.DIRECT_DEBIT];
    const usePaystackPayments = payment_methods.some((method) => paystackMethods.includes(method));
    const useWalletPayment = payment_methods.includes(PAYMENT_METHODS.WALLET);
    const useStartbutton = payment_methods.includes(PAYMENT_METHODS.STARTBUTTON);
    const useMoMoPayment = payment_methods.includes(PAYMENT_METHODS.MOMO);

    let paymentData: PaymentData;
    let payments: PaymentDocument[] = [];

    // Process Catlog credits payment
    if (useCatlogCredits) {
      const creditPaymentResult = await this.processCatlogCreditsPayment(
        store,
        totalPayable,
        balanceLeft,
        meta,
        currency,
        type,
        payment_methods,
        narration,
      );

      if (creditPaymentResult) {
        payments.push(creditPaymentResult.payment);
        paymentData = creditPaymentResult.paymentData;
        balanceLeft = creditPaymentResult.balanceLeft;
      }
    }

    // Process wallet payment if no payment data yet
    if (!paymentData && useWalletPayment) {
      const walletPaymentResult = await this.processWalletPayment(
        store,
        balanceLeft,
        meta,
        narration,
        walletTxChannel,
        walletTxSource,
        currency,
        type,
        meta?.automatedDelivery,
      );

      if (walletPaymentResult) {
        payments.push(walletPaymentResult.payment);
        paymentData = walletPaymentResult.paymentData;
      }
    }

    // Process Paystack payment if no payment data yet
    if (!paymentData && usePaystackPayments) {
      const paystackPaymentResult = await this.processPaystackPayment(
        store,
        balanceLeft,
        meta,
        narration,
        currency,
        type,
        payment_methods,
      );

      if (paystackPaymentResult) {
        payments.push(paystackPaymentResult.payment);
        paymentData = paystackPaymentResult.paymentData;
      }
    }

    // Process Startbutton payment if no payment data yet
    if (!paymentData && useStartbutton) {
      const startbuttonPaymentResult = await this.processStartbuttonPayment(
        store,
        balanceLeft,
        meta,
        narration,
        currency,
        type,
      );

      if (startbuttonPaymentResult) {
        payments.push(startbuttonPaymentResult.payment);
        paymentData = startbuttonPaymentResult.paymentData;
      }
    }

    // Process MoMo payment if no payment data yet
    if (!paymentData && useMoMoPayment) {
      const momoPaymentResult = await this.processMoMoPayment(store, balanceLeft, meta, narration, currency, type);

      if (momoPaymentResult) {
        payments.push(momoPaymentResult.payment);
        paymentData = momoPaymentResult.paymentData;
      }
    }

    // Validate payments
    if (payments.length < 1) {
      throw new BadRequestException("Couldn't initiate payment, please try again");
    }

    // Process credit payments
    let creditsPayment = payments.find((p) => p.payment_method === PAYMENT_METHODS.CATLOG_CREDIT);
    let gatewayPayment = payments.find((p) => p.payment_method !== PAYMENT_METHODS.CATLOG_CREDIT);

    // throw error if credits payment is not the only payment method and the amount paid by credits is not equal to the total payable
    if (creditsPayment && !gatewayPayment && (!paymentData || totalPayable > paymentData?.amount)) {
      throw new BadRequestException("Couldn't initiate payment, please try again");
    }

    // Save credit payment
    if (creditsPayment) {
      const credit = await this.paymentService.brokerTransport
        .send(BROKER_PATTERNS.USER.CREDITS.DEBIT_CREDITS, {
          user_id: creditsPayment.owner,
          amount: creditsPayment.amount_with_charge,
        })
        .toPromise();

      if (!credit || !credit.debitted) throw new BadRequestException('Insufficient credits balance');

      creditsPayment.rate_to_ngn = CURRENT_EXCHANGE_RATES[currency];
      creditsPayment = await creditsPayment.save();

      if (
        totalPayable === creditsPayment.amount_with_charge &&
        !gatewayPayment &&
        paymentData?.status === PAYMENT_STATUS.SUCCESS
      ) {
        await this.paymentService.brokerTransport
          .send(BROKER_PATTERNS.USER.CREDITS.RECORD_DEBIT, {
            user_id: creditsPayment.owner,
            amount: creditsPayment.amount_with_charge,
            meta: {
              payment_id: getDocId(creditsPayment),
            },
            narration: paymentData?.narration,
          })
          .toPromise();
      }
    }

    // Save gateway payment
    if (gatewayPayment) {
      gatewayPayment.partner_payment = creditsPayment ? creditsPayment.id : undefined;
      gatewayPayment.rate_to_ngn = CURRENT_EXCHANGE_RATES[currency];
      gatewayPayment = await gatewayPayment.save();
    }

    return paymentData;
  }

  /**
   * Processes Catlog credits payment
   */
  public async processCatlogCreditsPayment(
    store: Store,
    totalPayable: number,
    balanceLeft: number,
    meta: any,
    currency: CURRENCIES,
    type: PAYMENT_TYPES,
    payment_methods: PAYMENT_METHODS[],
    narration: string,
  ) {
    const credits = await this.paymentService.brokerTransport
      .send<CatlogCredits>(BROKER_PATTERNS.USER.CREDITS.GET_CREDITS, { user: store?.owner })
      .toPromise();

    if (!credits) {
      throw new BadRequestException('No credit wallet found');
    }

    if (credits.currency !== currency) {
      throw new BadRequestException('Invalid currency, please reload page');
    }

    if (credits.balance > 0) {
      const hasSufficientCredits = credits.balance >= totalPayable;
      const amountToDebit = hasSufficientCredits ? totalPayable : credits.balance;
      balanceLeft = totalPayable - amountToDebit;

      if (balanceLeft > 0 && payment_methods.length === 1) {
        throw new BadRequestException('Insufficient credits balance, please select another method');
      }

      const creditPayment = new this.paymentService.paymentModel({
        meta: meta,
        owner: store?.owner,
        vendorReference: '',
        amount_with_charge: amountToDebit,
        amount: amountToDebit,
        reference: await this.genRef(),
        status: balanceLeft === 0 ? PAYMENT_STATUS.SUCCESS : PAYMENT_STATUS.PENDING,
        payment_method: PAYMENT_METHODS.CATLOG_CREDIT,
        type,
        gateway_amount_settled: 0,
        gateway_charge: 0,
        currency,
      });

      let paymentData = null;
      if (balanceLeft === 0) {
        paymentData = {
          amount: amountToDebit,
          status: PAYMENT_STATUS.SUCCESS,
          narration: narration,
          currency,
          reference: creditPayment.reference,
          method: PAYMENT_METHODS.CATLOG_CREDIT,
        };
      }

      return {
        payment: creditPayment,
        paymentData,
        balanceLeft,
      };
    }

    return null;
  }

  /**
   * Processes wallet payment
   */
  public async processWalletPayment(
    store: Store,
    balanceLeft: number,
    meta: any,
    narration: string,
    walletTxChannel: TRANSACTION_CHANNELS,
    walletTxSource: any,
    currency: CURRENCIES,
    type: PAYMENT_TYPES,
    automatedDelivery: boolean,
  ) {
    if (!store?.wallet) {
      throw new BadRequestException("You don't have a payment wallet");
    }

    const walletFee = automatedDelivery ? 0 : this.convertToKobo(walletPaymentFeeCalculator(toNaira(balanceLeft)));
    const totalPayable = balanceLeft + walletFee;

    const walletId = store.wallets.find((w) => w.currency === currency)?.id;

    if (!walletId) {
      throw new BadRequestException('Wallet not found');
    }

    const walletPayment = new this.paymentService.paymentModel({
      meta: meta,
      owner: store?.owner,
      vendorReference: '',
      amount_with_charge: totalPayable,
      amount: balanceLeft,
      reference: await this.genRef(),
      status: PAYMENT_STATUS.PENDING,
      payment_method: PAYMENT_METHODS.WALLET,
      type,
      gateway_amount_settled: 0,
      gateway_charge: 0,
      currency,
    });

    const walletData = await this.paymentService.brokerTransport
      .send(BROKER_PATTERNS.WALLET.DEBIT_WALLET, {
        walletId: store?.wallet,
        amount: balanceLeft,
        channel: walletTxChannel,
        narration,
        meta: { payment_id: walletPayment.id },
        source: walletTxSource,
        fee: totalPayable - balanceLeft,
      })
      .toPromise();

    if (!walletData?.success) {
      throw new BadRequestException(
        walletData?.message ?? "Couldn't debit wallet, please select a different payment method",
      );
    }

    const paymentData = {
      amount: totalPayable,
      status: PAYMENT_STATUS.SUCCESS,
      narration,
      reference: walletPayment.reference,
      currency,
      method: PAYMENT_METHODS.WALLET,
    };

    return {
      payment: walletPayment,
      paymentData,
    };
  }

  /**
   * Processes Paystack payment
   */
  public async processPaystackPayment(
    store: Store,
    balanceLeft: number,
    meta: any,
    narration: string,
    currency: CURRENCIES,
    type: PAYMENT_TYPES,
    payment_methods: PAYMENT_METHODS[],
  ) {
    const cardOnlyPayments = payment_methods.includes(PAYMENT_METHODS.CARD);
    let eligibleForCardPayments = false;

    const ddOnlyPayments = payment_methods.includes(PAYMENT_METHODS.DIRECT_DEBIT);
    let eligibleForDDPayments = false;

    if (cardOnlyPayments) {
      const cards = await this.paymentService.cardModel.find({
        user: store.owner,
      });

      if (cards.length === 0) {
        eligibleForCardPayments = true;
        balanceLeft = applyDiscount(balanceLeft, SUBSCRIPTION_CARD_DISCOUNT);
      }
    } else if (ddOnlyPayments) {
      const ddTokens = await this.paymentService.ddTokenModel.find({
        user: store.owner,
      });
      eligibleForDDPayments = true;

      if (ddTokens.length === 0) {
        balanceLeft = applyDiscount(balanceLeft, SUBSCRIPTION_DD_DISCOUNT);
      }
    }

    balanceLeft = Math.max(balanceLeft, MININUM_PAYABLE[currency]); //user cannot pay less than 250
    const totalPayable = balanceLeft + this.convertToKobo(paystackFeeCalculator(toNaira(balanceLeft), currency));

    const paystackPayment = new this.paymentService.paymentModel({
      meta: meta,
      owner: store?.owner,
      vendorReference: '',
      amount_with_charge: totalPayable,
      amount: balanceLeft,
      reference: await this.genRef(),
      status: PAYMENT_STATUS.PENDING,
      payment_method: PAYMENT_METHODS.PAYSTACK,
      type,
      gateway_amount_settled: 0,
      gateway_charge: 0,
      currency,
    });

    const paymentData = {
      reference: paystackPayment.reference,
      publicKey: this.paymentService.paystackRepository.getPublicKey(currency),
      channels: eligibleForCardPayments
        ? ['card']
        : eligibleForDDPayments
        ? ['bank']
        : ['card', 'bank', 'ussd', 'qr', 'eft', 'mobile_money', 'bank_transfer', 'apple_pay'],
      currency,
      metadata: eligibleForDDPayments
        ? {
            custom_filters: {
              recurring: true,
            },
          }
        : {},
      amount: paystackPayment.amount_with_charge,
      status: PAYMENT_STATUS.PENDING,
      narration,
      method: PAYMENT_METHODS.PAYSTACK,
    };

    return {
      payment: paystackPayment,
      paymentData,
    };
  }

  /**
   * Processes Startbutton payment
   */
  public async processStartbuttonPayment(
    store: Store,
    balanceLeft: number,
    meta: any,
    narration: string,
    currency: CURRENCIES,
    type: PAYMENT_TYPES,
  ) {
    balanceLeft = Math.max(balanceLeft, MININUM_PAYABLE[currency]); //user cannot pay less than 250
    const totalPayable = balanceLeft + this.convertToKobo(startbuttonFeeCalculator(toNaira(balanceLeft), currency));

    const sbRef = await this.genRef();

    const sbPayment = await this.paymentService.startbutton.initiatePayment({
      amount: totalPayable,
      reference: sbRef,
      currency: currency,
    });

    if (!sbPayment?.error && sbPayment && sbPayment?.data && sbPayment?.data?.data) {
      const startbuttonPayment = new this.paymentService.paymentModel({
        meta: meta,
        owner: store?.owner,
        vendorReference: '',
        amount_with_charge: totalPayable,
        amount: balanceLeft,
        reference: sbRef,
        status: PAYMENT_STATUS.PENDING,
        payment_method: PAYMENT_METHODS.STARTBUTTON,
        type,
        gateway_amount_settled: 0,
        gateway_charge: 0,
        currency,
      });

      const paymentData = {
        reference: startbuttonPayment.reference,
        publicKey: this.paymentService.startbuttonConfig.publicKey,
        currency,
        amount: startbuttonPayment.amount_with_charge,
        status: PAYMENT_STATUS.PENDING,
        narration,
        method: PAYMENT_METHODS.STARTBUTTON,
        paymentLink: sbPayment.data.data,
      };

      return {
        payment: startbuttonPayment,
        paymentData,
      };
    }

    return null;
  }

  /**
   * Processes MoMo payment
   */
  public async processMoMoPayment(
    store: Store,
    balanceLeft: number,
    meta: any,
    narration: string,
    currency: CURRENCIES,
    type: PAYMENT_TYPES,
  ) {
    const totalPayable =
      balanceLeft +
      this.convertToKobo(zeepayFeeCalculator(toNaira(balanceLeft), this.paymentService.zeepayConfig.version));

    const zeepayPayment = new this.paymentService.paymentModel({
      meta: meta,
      owner: store?.owner,
      vendorReference: '',
      amount_with_charge: totalPayable,
      amount: balanceLeft,
      reference: await this.genRef(),
      status: PAYMENT_STATUS.PENDING,
      payment_method: PAYMENT_METHODS.MOMO,
      provider: PAYMENT_PROVIDERS.ZEEPAY,
      type,
      gateway_amount_settled: 0,
      gateway_charge: 0,
      currency,
    });

    const allNetworks = Object.values(MOBILE_MONEY_NETWORK);
    const paymentData = {
      reference: zeepayPayment.reference,
      currency,
      amount: totalPayable,
      status: PAYMENT_STATUS.PENDING,
      narration,
      method: PAYMENT_METHODS.MOMO,
      networks: allNetworks,
    };

    return {
      payment: zeepayPayment,
      paymentData,
    };
  }

  /**
   * Handles successful payment processing
   */
  public async handleSuccessfulPayment(paymentData: PaymentData, type: PAYMENT_TYPES) {
    let payment = await this.paymentService.paymentModel.findOne({ reference: paymentData.reference });

    if (type === PAYMENT_TYPES.SUBSCRIPTION) {
      await this.paymentService.paymentSubscriptionService.handleSuccessfulSubscriptionPayments(payment, {
        amount: payment.amount_with_charge,
        fees: payment.amount_with_charge - payment.amount,
      });
    }

    if (type === PAYMENT_TYPES.DELIVERY) {
      await this.paymentService.paymentDeliveriesService.handleSuccessfulDeliveryPayments(payment, {
        amount: payment.amount_with_charge,
        fees: payment.amount_with_charge - payment.amount,
      });
    }

    if (type === PAYMENT_TYPES.TOKEN_PURCHASE) {
      await this.paymentService.paymentTokenService.handleSuccessfulTokenPayments(payment, {
        amount: payment.amount_with_charge,
        fees: payment.amount_with_charge - payment.amount,
      });
    }

    if (type === PAYMENT_TYPES.DOMAIN_PURCHASE) {
      await this.paymentService.paymentDomainPurchaseService.handleSuccessfulDomainPurchasePayments(payment, {
        amount: payment.amount_with_charge,
        fees: payment.amount_with_charge - payment.amount,
      });
    }
  }

  async genRef() {
    let newRef = 'CLGPAY-' + genChars(8, false, true);
    while (await this.paymentService.paymentModel.exists({ reference: newRef })) {
      newRef = 'CLGPAY-' + genChars(8, false, true);
    }

    return newRef;
  }

  convertToKobo(amount) {
    // return Math.ceil(amount) * 100;
    return Math.ceil(amount * 100);
  }
}
