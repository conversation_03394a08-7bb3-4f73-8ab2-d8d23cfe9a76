import { Get, UseGuards } from '@nestjs/common';
import { PaymentJob } from '../payment-job.schema';
import { PaymentService } from './index.service';
import { CronJob } from 'cron';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { JwtAuthGuard } from '../../../jwt/jwt-auth.guard';
import { InternalApiJWTGuard } from '../../../guards/api.guard';
import { Mutex } from 'async-mutex';
import { PAYMENT_METHODS, PAYMENT_STATUS } from '../../../enums/payment.enum';

const mutex = new Mutex();

export class PaymentsJob extends PaymentService {
  // @Get('')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @Cron(CronExpression.EVERY_HOUR)
  async creditsCheckJob() {
    if (mutex.isLocked()) return;
    await mutex.acquire();
    this.logger.log('Running payments job'.toUpperCase());
    const expiredCreditOrCouponPayments = await this.paymentModel.find({
      created_at: {
        $lte: new Date(Date.now() - 59 * 60 * 1000), // 1 hour ago
      },
      $or: [
        {
          payment_method: PAYMENT_METHODS.CATLOG_CREDIT,
        },
        {
          'meta.coupon': {
            $exists: true,
          },
        },
      ],
      status: PAYMENT_STATUS.PENDING,
    });

    expiredCreditOrCouponPayments.forEach((p) => {
      if (p.payment_method === PAYMENT_METHODS.CATLOG_CREDIT) {
        this.reverseCreditPayment(p);
      }

      if (p?.meta?.coupon) {
        this.rollBackPaymentCoupon(p);
      }
    });

    mutex.release();
  }
}
