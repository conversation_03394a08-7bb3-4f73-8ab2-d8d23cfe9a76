import mongoose from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { User } from '../user/user.schema';
import { PAYMENT_METHODS } from '../../enums/payment.enum';

export type DDTokenDocument = DDToken & mongoose.Document;

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class DDToken {
  _id: string;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  user: string | User;

  @Prop({ type: String, required: true })
  authorization: string;

  @Prop({ type: String, required: true })
  bank: string;

  @Prop({ type: String, required: true })
  last4Digits: string;

  @Prop({ type: String, required: true })
  account_name: string;

  @Prop({ type: String })
  signature?: string;

  @Prop({
    type: String,
    enum: [PAYMENT_METHODS.PAYSTACK],
    required: true,
  })
  processor: string;

  @Prop({ type: Boolean })
  default?: boolean;
}

export const DDTokenSchema = SchemaFactory.createForClass(DDToken);
