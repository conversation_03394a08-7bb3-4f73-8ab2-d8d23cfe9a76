import { Controller } from '@nestjs/common';
import { PaymentService } from './services/index.service';
import { MessagePattern } from '@nestjs/microservices';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import mongoose, { FilterQuery } from 'mongoose';
import { Subscription } from '../subscription/subscription.schema';
import { Payment } from './payment.schema';
import { PaymentSubscriptionService } from './services/payment.subscription.service';
import { PaymentGettersService } from './services/payment.getters.service';
import { PaymentWebhooksService } from './services/payment.webhooks.service';
import { CURRENCIES } from '../country/country.schema';
import { InitiatePublicPaymentDto } from './payment.dto';
import { UpdateQuery } from 'mongoose';
import { SkipThrottle } from '@nestjs/throttler';
import { StripeSuccessfulChargeData } from '../../repositories/stripe.repository';

@SkipThrottle()
@Controller()
export class PaymentBroker {
  constructor(
    private readonly paymentService: PaymentService, // private readonly subscriptionService: PaymentSubscriptionService,
    private readonly paymentGettersService: PaymentGettersService,
    private readonly paymentWebhooksService: PaymentWebhooksService,
  ) {}

  @MessagePattern(BROKER_PATTERNS.PAYMENT.GET_PAYMENT)
  async getPayment(filter: FilterQuery<Payment>) {
    return this.paymentGettersService.getPayment(filter);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.GET_TOTAL_PAYMENTS)
  async getAllPayments(filter: FilterQuery<Payment>) {
    return this.paymentGettersService.getTotalPayments(filter);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.CREATE_PUBLIC_PAYMENT)
  async createPublicPayment(body: { data: InitiatePublicPaymentDto; payment_fee?: number; metaData?: any }) {
    return this.paymentService.createPublicPayment(body.data, body?.metaData ?? {}, body?.payment_fee);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.CREATE_PAYMENT_FOR_DOMAIN)
  async createPaymentForDomain(data: { body: any; storeId: string }) {
    return this.paymentService.createPayment(data.body, data.storeId);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.GET_FIRST_PAYMENT)
  async getFirstPayment(filter: FilterQuery<Payment>) {
    return this.paymentGettersService.getFirstPayment(filter);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.WEBHOOKS.RESOLVE_PAYSTACK_PAYMENTS)
  async resolvePaystackPayments(data: any) {
    return this.paymentWebhooksService.resolvePaystackPayments(data);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.WEBHOOKS.PAYSTACK_DD_AUTH_CREATED)
  async savePaystackDDAuth(data: PaystackDDAuthorization) {
    return this.paymentWebhooksService.savePaystackDDAuth(data);
  }

  // @MessagePattern(BROKER_PATTERNS.PAYMENT.CREATE_TEST_PAYMENT)
  // async createTestPayment(data: { store: string; currency: CURRENCIES }) {
  //   return await this.paymentService.createTestPayment(data.store, data.currency);
  // }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.UPDATE_PAYMENT)
  async updatePayment(data: { filter: FilterQuery<Payment>; update: UpdateQuery<Payment> }) {
    return await this.paymentService.updatePayment(data.filter, data.update);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.COUNT_SUBSCRIPTION_PAYMENTS)
  async countSubscriptionPayments(userId: string) {
    return await this.paymentGettersService.countSubscriptionPayments(userId);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.TEST_PAYMENT_PAID)
  async testPaymentPaid(data: { paymentId: string; gateway_fee: number; gateway_amount_settled: number }) {
    return this.paymentService.testPaymentPaid(data.paymentId, data.gateway_fee, data.gateway_amount_settled);
  }

  // @MessagePattern(BROKER_PATTERNS.PAYMENT.WEBHOOKS.PAYSTACK_SUBSCRIPTION)
  // async paystackSubscription(data: any) {
  //   return this.paymentWebhooksService.paystackWebhook(data);
  // }

  // @MessagePattern(BROKER_PATTERNS.PAYMENT.WEBHOOKS.PAYSTACK_STORE_PAYMENT)
  // async paystackStorePayment(data: any) {
  //   return this.paymentService.publicPaystackPayment(data);
  // }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.WEBHOOKS.MONNIFY_TRANSFER_RECEIVED)
  async monnifyTransferWebhook(data: any) {
    return this.paymentWebhooksService.publicMonnifyBankTransfer(data);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.WEBHOOKS.BLOCHQ_TRANSFER_RECEIVED)
  async blocTransferReceived(data: any) {
    return this.paymentWebhooksService.publicBlocPayment(data);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.WEBHOOKS.FLW_TRANSFER_RECIEVED)
  async flwTransferReceiced(data: any) {
    return this.paymentWebhooksService.publicFlwPayment(data);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.WEBHOOKS.KORAPAY_TRANSFER_RECEIVED)
  async korapayTransferReceiced(data: any) {
    return this.paymentWebhooksService.publicKorapayPayment(data);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.WEBHOOKS.ZILLA_PAYMENT_CONFIRMED)
  async zillaPaymentConfirmed(data: any) {
    return this.paymentWebhooksService.publicZillaPayment(data);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.WEBHOOKS.RESOLVE_ZEEPAY_PAYMENT)
  async resolveZeepayPayment(data: any) {
    return this.paymentWebhooksService.resolveZeepayPayments(data);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.WEBHOOKS.MONO_PAYMENT_CONFIRMED)
  async monoPaymentConfirmed(data: any) {
    return this.paymentWebhooksService.publicMonoPayment(data);
  }

  @MessagePattern(BROKER_PATTERNS.ANALYTICS.TOTAL_PAID_STORES)
  async totalPaidStores() {
    return await this.paymentGettersService.storesOnPaidPlan();
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.MANUALLY_RECORD_PAYMENT)
  async createSquadPayment(data: any) {
    return await this.paymentService.manuallyRecordPayment(data);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.WEBHOOKS.THEPEER_PAYMENT_RECEIVED)
  async thePeerPaymentReceived(data: any) {
    return await this.paymentWebhooksService.publicThepeerPayment(data);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.WEBHOOKS.PAYAZA_PAYMENT_RECEIVED)
  async payazaPaymentReceived(data: any) {
    return await this.paymentWebhooksService.resolvePayazaPayments(data);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.WEBHOOKS.STARTBUTTON_PAYMENT_RECEIVED)
  async startbuttonPaymentReceived(data: any) {
    return await this.paymentWebhooksService.startbuttonPaymentReceived(data);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.WEBHOOKS.STRIPE_PAYMENT_RECEIVED)
  async stripePaymentReceived(data: StripeSuccessfulChargeData) {
    return this.paymentWebhooksService.publicStripePayment(data);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.WEBHOOKS.LEATHERBACK_PAYMENT_RECEIVED)
  async leatherbackPaymentReceived(data: LeatherbackWebhookData) {
    return this.paymentWebhooksService.publicLeatherbackPayment(data);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.WEBHOOKS.SUDO_TRANSFER_RECEIVED)
  async sudoTransferWebhook(body: any) {
    return this.paymentWebhooksService.publicSudoTransfer(body);
  }
}

export interface LeatherbackWebhookData {
  Environment: string;
  Reference: string;
  AmountPaid: string;
  Amount: string;
  Currency: string;
  AppFee: string;
  Narration: string;
  PaymentStatus: string;
  ChannelType: string;
  RequestSource: string;
  Metadata: Record<string, any>; // Or you can define a stricter type for Metadata if needed
}

interface Customer {
  first_name: string;
  last_name: string;
  code: string;
  email: string;
  phone: string;
  metadata: null;
  risk_action: string;
}

export interface PaystackDDAuthorization {
  authorization_code: string;
  active: boolean;
  last4: string;
  channel: string;
  card_type: string;
  bank: string;
  exp_month: number;
  exp_year: number;
  country_code: string;
  brand: string;
  reusable: boolean;
  signature: string;
  account_name: string;
  reference: string;
  customer: Customer;
}
