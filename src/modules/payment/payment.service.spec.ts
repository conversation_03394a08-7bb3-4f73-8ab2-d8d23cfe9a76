import { Test, TestingModule } from '@nestjs/testing';
import { PaymentService } from './services/index.service';
import { PaymentModule } from './payment.module';
import { AppModule } from '../../app.module';

describe('PaymentService', () => {
  let service: PaymentService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule, PaymentModule],
    }).compile();

    service = module.get<PaymentService>(PaymentService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
