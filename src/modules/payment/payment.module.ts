import { Module } from '@nestjs/common';
import { PaymentController } from './payment.controller';
import { PaymentService } from './services/index.service';
import { PaymentBroker } from './payment.broker';
import { MongooseModule } from '@nestjs/mongoose';
import { Payment, PaymentSchema } from './payment.schema';
import { setIdMongoosePlugin } from '../../mongoose-plugins';
import jsonHookPlugin from '../../mongoose-plugins/json-hook.mongoose-plugin';
import { SharedModule } from '../../shared.module';
import { Subscription, SubscriptionSchema } from '../subscription/subscription.schema';
import { Card, CardSchema } from './card.schema';
import { User, UserSchema } from '../user/user.schema';
import { PaymentJob, PaymentJobSchema } from './payment-job.schema';
import { PaymentSubscriptionJob } from './services/payment-subscription-job.service';
import { PaymentSubscriptionService } from './services/payment.subscription.service';
import { ScheduleModule } from '@nestjs/schedule';
import { PaymentQueue } from './payment.queue';
import { BullModule } from '@nestjs/bull';
import { QUEUES } from '../../enums/queues.enum';
import { PaymentsJob } from './services/payment-job.service';
import { PaymentGettersService } from './services/payment.getters.service';
import { PaymentMigrationsService } from './services/payment.migrations.service';
import { PaymentWebhooksService } from './services/payment.webhooks.service';
import { PaymentCouponService } from './coupons/payment-coupons.service';
import { PaymentCoupon, PaymentCouponSchema } from './coupons/payment-coupons.schema';
import { PaymentCouponsController } from './coupons/payment-coupons.controller';
import mongoosePaginate from 'mongoose-paginate-v2';
import { MobileMoneyMethod, MobileMoneyMethodSchema } from './mobile-money-method.schema';
import { DDToken, DDTokenSchema } from './dd-tokens.schema';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    BullModule.registerQueueAsync({
      name: QUEUES.PAYMENT,
    }),
    MongooseModule.forFeatureAsync([
      {
        name: Payment.name,
        useFactory: () => {
          PaymentSchema.plugin(setIdMongoosePlugin());
          PaymentSchema.plugin(jsonHookPlugin(['_id', '__v']));
          // PaymentSchema.plugin(mongoosePaginate);
          return PaymentSchema;
        },
      },
      {
        name: PaymentCoupon.name,
        useFactory: () => {
          PaymentCouponSchema.plugin(setIdMongoosePlugin());
          PaymentCouponSchema.plugin(jsonHookPlugin(['_id', '__v']));
          return PaymentCouponSchema;
        },
      },
      {
        name: Subscription.name,
        useFactory: () => {
          SubscriptionSchema.plugin(setIdMongoosePlugin());
          SubscriptionSchema.plugin(jsonHookPlugin(['_id', '__v']));
          return SubscriptionSchema;
        },
      },
      {
        name: Card.name,
        useFactory: () => {
          CardSchema.plugin(setIdMongoosePlugin());
          CardSchema.plugin(jsonHookPlugin(['_id', '__v']));
          return CardSchema;
        },
      },
      {
        name: DDToken.name,
        useFactory: () => {
          DDTokenSchema.plugin(setIdMongoosePlugin());
          DDTokenSchema.plugin(jsonHookPlugin(['_id', '__v']));
          return DDTokenSchema;
        },
      },
      {
        name: MobileMoneyMethod.name,
        useFactory: () => {
          MobileMoneyMethodSchema.plugin(setIdMongoosePlugin());
          MobileMoneyMethodSchema.plugin(jsonHookPlugin(['_id', '__v']));
          return MobileMoneyMethodSchema;
        },
      },
      {
        name: PaymentJob.name,
        useFactory: () => PaymentJobSchema,
      },
      {
        name: User.name,
        useFactory: () => {
          return UserSchema;
        },
      },
    ]),
    SharedModule,
  ],
  controllers: [PaymentController, PaymentCouponsController, PaymentBroker, PaymentSubscriptionJob],
  providers: [
    PaymentService,
    PaymentCouponService,
    PaymentQueue,
    PaymentsJob,
    PaymentGettersService,
    PaymentMigrationsService,
    PaymentWebhooksService,
    PaymentSubscriptionJob,
  ],
  exports: [PaymentService, PaymentCouponService, PaymentGettersService],
})
export class PaymentModule {}
