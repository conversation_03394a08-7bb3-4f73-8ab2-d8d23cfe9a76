import { Seed } from '../../../base-seed/seed';
import { ISeed } from '../../../interfaces/seed/seed.interface';
import { Connection } from 'mongoose';
import { subscriptionMock } from '../../../tools/testdata';

export class SubscriptionSeed extends Seed implements ISeed {
  constructor(db: Connection) {
    super(db);
    this.collection = db.collection('subscriptions');
  }

  async runDevSeed(): Promise<any> {
    if (!(await this.collection.findOne({ _id: subscriptionMock._id }))) {
      return this.collection.insertOne(subscriptionMock);
    }
  }

  runProdSeed(): Promise<any> {
    return Promise.resolve(undefined);
  }
}
