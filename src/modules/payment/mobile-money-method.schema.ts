import mongoose from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { User } from '../user/user.schema';
import { MOBILE_MONEY_NETWORK } from '../../enums/payment.enum';

export type MobileMoneyMethodDocument = MobileMoneyMethod & mongoose.Document;

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class MobileMoneyMethod {
  _id: string;

  @Prop({ type: String, required: true })
  msisdn: string;

  @Prop({
    type: String,
    enum: [
      MOBILE_MONEY_NETWORK.MTN,
      MOBILE_MONEY_NETWORK.AIRTELTIGO,
      MOBILE_MONEY_NETWORK.VODAFONE,
      MOBILE_MONEY_NETWORK.ZEEPAY,
    ],
    required: true,
  })
  mno: string;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  owner: string | User;
}

export const MobileMoneyMethodSchema = SchemaFactory.createForClass(MobileMoneyMethod);
