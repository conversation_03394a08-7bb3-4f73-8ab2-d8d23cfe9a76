import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';

export type PaymentJobDocument = PaymentJob & Document;

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class PaymentJob {
  @Prop({ type: String })
  message: string;

  @Prop({ type: mongoose.Schema.Types.Mixed })
  data: any;
}

export const PaymentJobSchema = SchemaFactory.createForClass(PaymentJob);
