import mongoose from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { User } from '../user/user.schema';
import { PAYMENT_METHODS } from '../../enums/payment.enum';

export type CardDocument = Card & mongoose.Document;

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class Card {
  _id: string;

  @Prop({ type: String, required: true })
  first: string;

  @Prop({ type: String, required: true })
  last: string;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  user: string | User;

  @Prop({ type: String, required: true })
  authorization: string;

  @Prop({
    type: String,
    enum: [
      PAYMENT_METHODS.PAYSTACK,
      PAYMENT_METHODS.TRANSFER,
      PAYMENT_METHODS.TRF_MOMO,
      PAYMENT_METHODS.ZILLA,
      PAYMENT_METHODS.STARTBUTTON,
    ],
    required: true,
  })
  processor: string;

  @Prop({ type: String, required: true })
  type: string;

  @Prop({ type: Boolean })
  default?: boolean;
}

export const CardSchema = SchemaFactory.createForClass(Card);
