import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document, Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { PAYMENT_METHODS, PAYMENT_PROVIDERS, PAYMENT_STATUS, PAYMENT_TYPES } from '../../enums/payment.enum';
import { IsBoolean, IsNumber, IsObject, IsString, ValidateNested } from 'class-validator';
import { CURRENCIES } from '../country/country.schema';
import { PlanOption } from '../plan/plan-options/plan-options.schema';

export type PaymentDocument = Payment & Document;

export type PaymentData = {
  reference: string;
  publicKey?: string;
  channels?: string[];
  currency: CURRENCIES;
  amount: number;
  status: PAYMENT_STATUS;
  narration?: string;
  paymentLink?: string;
  method: PAYMENT_METHODS;
  networks?: string[];
  metadata?: any;
};

export class CouponMetaData {
  @ApiProperty()
  coupon_code: string;

  @ApiProperty()
  id: string;

  @ApiProperty()
  discount: number;

  original_amount?: number;
}

export class PaymentMeta {
  @ApiProperty()
  @IsString()
  plan?: string;

  @ApiProperty()
  @IsString()
  plan_option?: string;

  @ApiProperty()
  @IsString()
  invoice?: string;

  @ApiProperty()
  @IsString()
  subscription?: string;

  @ApiProperty()
  @IsString()
  delivery?: string;

  @ApiProperty()
  @IsString()
  transfer?: string;

  @ApiProperty()
  @IsString()
  transaction_id?: string;

  @ApiProperty()
  @IsBoolean()
  is_test_payment?: boolean;

  @ApiProperty()
  monnify_account?: any;

  @ApiProperty()
  @Prop({ type: CouponMetaData })
  coupon?: CouponMetaData;

  @ApiProperty()
  @IsString()
  store?: string;

  @ApiProperty()
  @Prop({ type: { session_id: String, db_session_id: Types.ObjectId } })
  chowbot?: {
    session_id: string;
    db_session_id: string;
  };

  // @ApiProperty()
  // @IsString()
  // sessionId?: string;

  @ApiProperty()
  @IsNumber()
  token_amount?: number;

  @ApiProperty()
  @IsObject()
  zeepay?: { [key: string]: any };

  @ApiProperty()
  @IsObject()
  sudo?: { [key: string]: any };

  @ApiProperty()
  @IsString()
  stripe_charge_id?: string;

  @ApiProperty()
  @IsBoolean()
  is_upfront?: boolean;

  @ApiProperty()
  @IsString()
  domain_purchase?: string;

  @ApiProperty()
  @IsString()
  domain?: string;
}

export class PaymentCustomer {
  @ApiProperty()
  @Prop({ type: String })
  id: string;

  @ApiProperty()
  @Prop({ type: String })
  name: string;

  @ApiProperty()
  @Prop({ type: String })
  phone: string;

  @ApiProperty()
  @Prop({ type: String })
  email: string;
}

/**
 * 
_id: 630637b0b6bb922e482be210
payment_method_type: "PAYSTACK"
success: null
bank: ""
channel: ""
status: "PENDING"
plan: 62fc3833cd744e333639c121
owner: 62900ec28be729614c39b55d
amount: 4650
reference: "TNXC-MTYQ-DXG1-1RSI"
created_at: 2022-08-24T14:37:36.954+00:00
updated_at: 2022-08-24T14:37:36.954+00:00
__v: 0

 */

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class Payment {
  @ApiProperty()
  id: string;

  @ApiProperty()
  @Prop({
    type: Date,
    index: true,
  })
  created_at?: Date;

  @ApiProperty()
  @Prop({ type: String })
  reference: string;

  @ApiProperty()
  @Prop({ type: String })
  vendorReference: string;

  @ApiProperty()
  @Prop({ type: String })
  owner: string;

  @ApiProperty()
  @Prop({ type: String })
  store: string;

  @ApiProperty()
  @Prop({ type: Number })
  amount: number;

  @ApiProperty()
  @Prop({ type: Number })
  amount_with_charge: number;

  @ApiProperty()
  @Prop({ type: Number })
  gateway_charge: number;

  @ApiProperty()
  @Prop({ type: Number })
  gateway_amount_settled: number;

  @ApiProperty()
  @Prop({ type: Number, default: 0 })
  merchant_fees?: number;

  @Prop({ type: String })
  payment_method_type?: string;

  @Prop({ type: String })
  plan?: string;

  @ApiProperty()
  @Prop({ type: Types.ObjectId, ref: 'PlanOption', required: false })
  plan_option?: PlanOption | string;

  @Prop({ type: String })
  subscription?: string;

  @ApiProperty()
  @Prop({
    type: String,
    enum: Object.values(PAYMENT_METHODS),
  })
  payment_method: PAYMENT_METHODS;

  @ApiProperty({
    type: String,
    enum: Object.values(PAYMENT_STATUS),
  })
  @Prop({ type: String, enum: Object.values(PAYMENT_STATUS) })
  status: PAYMENT_STATUS;

  @ApiProperty({
    type: String,
    enum: Object.values(PAYMENT_TYPES),
  })
  @Prop({
    type: String,
    enum: Object.values(PAYMENT_TYPES),
  })
  type: PAYMENT_TYPES;

  @ApiProperty()
  @Prop({ type: PaymentMeta })
  meta: PaymentMeta;

  @ApiProperty({
    type: String,
    enum: [CURRENCIES.NGN, CURRENCIES.GHC, CURRENCIES.USD],
  })
  @Prop({
    type: String,
    enum: [
      CURRENCIES.NGN,
      CURRENCIES.GHC,
      CURRENCIES.USD,
      CURRENCIES.KES,
      CURRENCIES.ZAR,
      CURRENCIES.GBP,
      CURRENCIES.CAD,
    ],
  })
  currency?: CURRENCIES;

  @ApiProperty()
  @Prop({ type: String, enum: Object.values(PAYMENT_PROVIDERS) })
  provider?: PAYMENT_PROVIDERS;

  @ApiProperty()
  @Prop({ type: String })
  partner_payment?: string;

  @ApiProperty()
  @Prop({ type: String })
  error?: string;

  @ApiProperty()
  @Prop({ type: PaymentCustomer })
  customer?: PaymentCustomer;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  formerly_succeeded?: boolean;

  @ApiProperty()
  @Prop({ type: Number, default: 1 })
  rate_to_ngn?: number;
}

export const PaymentSchema = SchemaFactory.createForClass(Payment);

PaymentSchema.methods.toJSON = function () {
  const p = this.toObject();

  delete p.gateway_charge;
  delete p.gateway_amount_settled;

  return p;
};
