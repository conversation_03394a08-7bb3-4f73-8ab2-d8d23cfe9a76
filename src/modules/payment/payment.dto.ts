import {
  A<PERSON>yUnique,
  IsBoolean,
  IsDateString,
  isEnum,
  IsEnum,
  IsIn,
  IsNotEmpty,
  <PERSON>N<PERSON>ber,
  IsNumberString,
  IsOptional,
  IsString,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional, OmitType } from '@nestjs/swagger';
import { MOBILE_MONEY_NETWORK, PAYMENT_METHODS, PAYMENT_STATUS, PAYMENT_TYPES } from '../../enums/payment.enum';
import { CURRENCIES } from '../country/country.schema';
import { PaginatedQueryDto } from '../../models/dtos/PaginatedDto';
import { Invoice } from '../invoice/invoice.schema';

export class CreatePaymentDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  plan: string;

  @ApiProperty({
    enum: [
      PAYMENT_METHODS.PAYSTACK,
      PAYMENT_METHODS.TRANSFER,
      PAYMENT_METHODS.TRF_MOMO,
      PAYMENT_METHODS.ZILLA,
      PAYMENT_METHODS.CATLOG_CREDIT,
      PAYMENT_METHODS.STARTBUTTON,
      PAYMENT_METHODS.MOMO,
    ],
  })
  @IsEnum(
    [
      PAYMENT_METHODS.PAYSTACK,
      PAYMENT_METHODS.TRANSFER,
      PAYMENT_METHODS.TRF_MOMO,
      PAYMENT_METHODS.ZILLA,
      PAYMENT_METHODS.CATLOG_CREDIT,
      PAYMENT_METHODS.STARTBUTTON,
      PAYMENT_METHODS.MOMO,
    ],
    { each: true },
  )
  @IsNotEmpty()
  payment_method_type: [PAYMENT_METHODS, PAYMENT_METHODS];

  @ApiProperty({ type: Boolean })
  @IsBoolean()
  @IsOptional()
  card_only?: boolean;

  owner: string;
}

export class CreateTestPaymentDto {
  @ApiProperty()
  @IsEnum([
    PAYMENT_METHODS.DIRECT_TRANSFER,
    PAYMENT_METHODS.PAYSTACK,
    PAYMENT_METHODS.STARTBUTTON,
    PAYMENT_METHODS.MOMO,
  ])
  @IsNotEmpty()
  payment_method: PAYMENT_METHODS;

  @ApiPropertyOptional({ enum: CURRENCIES })
  @IsEnum(CURRENCIES)
  @IsNotEmpty()
  currency: CURRENCIES;
}

export class GetInAppPaymentMethodsDto {
  @ApiProperty({ required: true, enum: PAYMENT_TYPES })
  @IsEnum(PAYMENT_TYPES)
  @IsNotEmpty()
  type: PAYMENT_TYPES;

  @ApiProperty({ required: true, enum: CURRENCIES })
  @IsEnum(CURRENCIES)
  @IsNotEmpty()
  currency: CURRENCIES;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  plan?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  plan_option?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  delivery?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  domain_purchase?: string;

  @ApiPropertyOptional({ description: 'Amount of Token to be purchased' })
  @IsNumberString()
  @IsOptional()
  tokens?: number;

  @ApiPropertyOptional({ type: String })
  @IsString()
  @IsOptional()
  upfront?: string;
}

export class GetPaymentMethodsDto {
  @ApiProperty({ required: true })
  @IsEnum(PAYMENT_TYPES)
  @IsNotEmpty()
  type: PAYMENT_TYPES;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  invoice_id: string;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  country: string;
}
export class InitiatePaymentDto {
  @ApiProperty({ enum: PAYMENT_TYPES })
  @IsEnum(PAYMENT_TYPES)
  @IsNotEmpty()
  type: PAYMENT_TYPES;

  @ApiProperty()
  @IsEnum(
    [
      PAYMENT_METHODS.CARD,
      PAYMENT_METHODS.PAYSTACK,
      PAYMENT_METHODS.WALLET,
      PAYMENT_METHODS.CATLOG_CREDIT,
      PAYMENT_METHODS.STARTBUTTON,
      PAYMENT_METHODS.MOMO,
      PAYMENT_METHODS.DIRECT_DEBIT,
    ],
    {
      each: true,
    },
  )
  @IsNotEmpty()
  payment_methods: [PAYMENT_METHODS, PAYMENT_METHODS?];

  @ApiPropertyOptional()
  plan?: string;

  @ApiPropertyOptional()
  plan_option?: string;

  @ApiPropertyOptional()
  subscription?: string;

  @ApiPropertyOptional()
  upfront?: boolean;

  @ApiPropertyOptional()
  delivery?: string;

  @ApiPropertyOptional()
  domain_purchase?: string;

  @ApiPropertyOptional({ type: Boolean })
  @IsBoolean()
  @IsOptional()
  card_only?: boolean;

  @ApiPropertyOptional({ enum: CURRENCIES })
  @IsEnum(CURRENCIES)
  @IsOptional()
  currency?: CURRENCIES;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  coupon?: string;

  @ApiPropertyOptional({ description: 'Amount of tokens to be purchased' })
  @IsNumber()
  @IsOptional()
  tokens?: number;
}

export class InitiatePublicPaymentDto {
  @ApiProperty()
  @IsEnum(PAYMENT_TYPES)
  @IsNotEmpty()
  type: PAYMENT_TYPES;

  @ApiProperty()
  @IsEnum([
    PAYMENT_METHODS.TRANSFER,
    PAYMENT_METHODS.PAYSTACK,
    PAYMENT_METHODS.ZILLA,
    PAYMENT_METHODS.MONO_DIRECT_PAY,
    PAYMENT_METHODS.THEPEER,
    PAYMENT_METHODS.STARTBUTTON,
    PAYMENT_METHODS.MOMO,
    PAYMENT_METHODS.STRIPE,
    PAYMENT_METHODS.LEATHERBACK,
  ])
  @IsNotEmpty()
  payment_method_type: PAYMENT_METHODS;

  @ApiProperty()
  invoice: string;

  @ApiProperty()
  @IsEnum([
    CURRENCIES.NGN,
    CURRENCIES.GHC,
    CURRENCIES.ZAR,
    CURRENCIES.KES,
    CURRENCIES.USD,
    CURRENCIES.GBP,
    CURRENCIES.CAD,
  ])
  @IsNotEmpty()
  currency: CURRENCIES;
}

export class GetPaymentAnalyticsDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsDateString()
  from?: Date;

  @ApiPropertyOptional()
  @IsOptional()
  @IsDateString()
  to?: Date;

  @ApiPropertyOptional({ type: PAYMENT_STATUS })
  @IsOptional()
  @IsEnum(PAYMENT_STATUS)
  status: PAYMENT_STATUS;
}

export class PaymentHistoryQueryDto extends PaginatedQueryDto {
  @ApiPropertyOptional({ enum: PAYMENT_STATUS })
  @IsOptional()
  @IsEnum(PAYMENT_STATUS)
  status?: PAYMENT_STATUS;

  @ApiPropertyOptional({ type: Date })
  @IsOptional()
  @IsDateString()
  from?: Date;

  @ApiPropertyOptional({ type: Date })
  @IsOptional()
  @IsDateString()
  to?: Date;

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  @IsString()
  search?: string;
}

export class InitiateZeepayWalletDebitDto {
  // @ApiProperty({ required: true })
  // @IsString()
  // @IsNotEmpty()
  // invoice_id: string;

  @ApiProperty({ required: true })
  @IsEnum(MOBILE_MONEY_NETWORK)
  @IsNotEmpty()
  network: MOBILE_MONEY_NETWORK;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  payment_reference: string;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  msisdn: string;
}

export interface DailyVolume {
  volume: number;
  transaction_count: number;
}
