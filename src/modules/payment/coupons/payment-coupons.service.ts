import mongoose, { FilterQ<PERSON>y, PaginateModel } from 'mongoose';
import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { PaymentCoupon, PaymentCouponDocument } from './payment-coupons.schema';
import { CreatePaymentDto } from '../payment.dto';
import { CreatePaymentCouponDto, VerifyPaymentCouponDTO } from './payment-coupons.dto';
import { PAYMENT_TYPES } from '../../../enums/payment.enum';
import { Delivery } from '../../deliveries/deliveries.schema';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { CURRENCIES } from '../../country/country.schema';

@Injectable()
export class PaymentCouponService {
  constructor(
    private readonly brokerTransport: BrokerTransportService,
    @InjectModel(PaymentCoupon.name)
    private readonly couponModel: PaginateModel<PaymentCouponDocument>,
    private readonly logger: Logger,
  ) {
    this.logger.setContext('item.service.ts');
  }

  async createCoupon(data: CreatePaymentCouponDto) {
    const current = await this.couponModel.findOne({
      coupon_code: data.coupon_code.toUpperCase(),
      currency: data.currency,
    });

    if (current) {
      throw new BadRequestException('Coupon with code already exists');
    }

    if (data.type === 'fixed' && data.discount_amount) {
      data.percentage = null;
      data.discount_cap = null;

      const coupon = await this.couponModel.create({
        ...data,
      });

      return coupon;
    } else if (data.type === 'percentage' && data.percentage) {
      data.discount_amount = null;
      const coupon = await this.couponModel.create({
        ...data,
      });

      return coupon;
    }

    throw new BadRequestException('cannot create coupon without type data');
  }

  async getCoupons() {}

  async getCouponByCode(coupon_code: string, currency: CURRENCIES) {
    if (!currency) throw new BadRequestException('Please provide the currency for this coupon');
    const coupon = await this.couponModel.findOne({ coupon_code: coupon_code.toUpperCase(), currency });
    if (!coupon) throw new BadRequestException('could not find coupon');
    return coupon;
  }

  async verifyCoupon(data: VerifyPaymentCouponDTO, store: string) {
    if (!data.coupon_code || (!data.plan && !data.delivery)) {
      throw new BadRequestException('Please provide valid payment details');
    }
    const coupon = await this.couponModel.findOne({
      coupon_code: data?.coupon_code.toUpperCase(),
      quantity: { $gt: 0 },
      end_date: { $gt: new Date() },
      active: true,
      currency: data.currency,
    });

    if (!coupon) throw new BadRequestException('This coupon is invalid or has been used');

    if (coupon.used_by.includes(store)) {
      throw new BadRequestException(
        'You have previously used this coupon. Contact support if you think this is a mistake',
      );
    }

    if (data?.payment_type !== coupon.payment_type) {
      throw new BadRequestException('You cannot use this coupon for this payment');
    }

    if (data.currency !== coupon.currency) {
      throw new BadRequestException('Coupon is invalid for this currency');
    }

    switch (coupon.payment_type) {
      case PAYMENT_TYPES.SUBSCRIPTION:
        if (coupon?.config?.plans?.length > 0 && !coupon?.config?.plans.includes(data?.plan)) {
          throw new BadRequestException('Coupon cannot be used for this subscription plan');
        }
        break;
      case PAYMENT_TYPES.DELIVERY:
        const delivery = await this.brokerTransport
          .send<Delivery>(BROKER_PATTERNS.DELVERIES.GET_DELIVERY, { _id: data?.delivery })
          .toPromise();

        if (coupon?.config?.provider && delivery.provider !== coupon?.config?.provider) {
          throw new BadRequestException('Coupon is Invalid');
        }

        if (coupon?.config?.couriers?.length > 0 && !coupon?.config?.couriers.includes(delivery.courier?.courier_id)) {
          throw new BadRequestException('Coupon cannot be used for this courier');
        }

        break;

      default:
        break;
    }

    return coupon;
  }
}
