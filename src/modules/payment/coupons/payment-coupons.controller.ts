import { BadRequestException, Body, Controller, Get, Param, Post, Query, Req, UseGuards } from '@nestjs/common';
import { ApiExcludeEndpoint, ApiSecurity } from '@nestjs/swagger';
import { InternalApiJWTGuard } from '../../../guards/api.guard';
import { JwtAuthGuard } from '../../../jwt/jwt-auth.guard';
import { CreatePaymentCouponDto, VerifyPaymentCouponDTO } from './payment-coupons.dto';
import { PaymentCouponService } from './payment-coupons.service';
import { IRequest } from '../../../interfaces/request.interface';
import { CURRENCIES } from '../../country/country.schema';

@Controller('payment-coupons')
export class PaymentCouponsController {
  constructor(private readonly paymentCouponService: PaymentCouponService) {}

  @Post('')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async createCoupon(@Body() createPaymentCouponDto: CreatePaymentCouponDto) {
    const data = await this.paymentCouponService.createCoupon(createPaymentCouponDto);
    return {
      message: 'Succesfully created payment coupon',
      data,
    };
  }
  @Get(':code')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  async getCouponByCode(@Param('code') code: string, @Query('currency') currency: CURRENCIES) {
    const data = await this.paymentCouponService.getCouponByCode(code, currency);
    return {
      message: 'Coupon was successfully retrieved',
      data,
    };
  }

  @Post('verify')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  async verifyCoupon(@Body() body: VerifyPaymentCouponDTO, @Req() req: IRequest) {
    const data = await this.paymentCouponService.verifyCoupon(body, req?.user?.store?.id);
    return {
      message: 'Coupon was successfully retrieved',
      data,
    };
  }
}
