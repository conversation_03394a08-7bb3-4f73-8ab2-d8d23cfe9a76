import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Document } from 'mongoose';
import { PAYMENT_TYPES } from '../../../enums/payment.enum';
import { IsEnum } from 'class-validator';
import { CURRENCIES } from '../../country/country.schema';

export type PaymentCouponDocument = PaymentCoupon & Document;

@Schema({ timestamps: true })
export class PaymentCoupon {
  _id: string;

  @ApiProperty({ enum: ['percentage', 'fixed'] })
  @Prop({ type: String, required: true, enum: ['percentage', 'fixed'] })
  type: 'percentage' | 'fixed';

  @ApiProperty()
  @Prop({ type: String, required: true })
  coupon_code: string;

  @ApiProperty()
  @Prop({ type: Date, required: true })
  end_date: string | Date;

  @ApiProperty()
  @Prop({ type: Boolean, required: false, default: true })
  active?: boolean;

  @ApiProperty()
  @Prop({ type: Number, required: false })
  discount_cap?: number;

  @ApiProperty()
  @Prop({ type: Number, required: false })
  discount_amount?: number;

  @ApiProperty()
  @Prop({ type: Number, required: true })
  quantity: number;

  @ApiProperty()
  @Prop({ type: Number, required: false })
  percentage?: number;

  @ApiProperty()
  @Prop({ type: String, required: true, enum: [...Object.values(PAYMENT_TYPES)] })
  payment_type: PAYMENT_TYPES;

  @ApiProperty()
  @Prop({
    type: {
      plans: [String],
      couriers: [String],
      provider: String,
    },
    required: true,
    default: {
      plans: [],
      couriers: [],
      provider: null,
    },
  })
  config?: {
    plans?: string[];
    couriers?: string[];
    provider?: string;
  };

  @ApiProperty()
  @Prop({ type: String, required: true, enum: [...Object.values(CURRENCIES)] })
  currency?: CURRENCIES;

  @ApiProperty()
  @Prop({ type: [String], required: false, default: [] })
  used_by?: string[];
}

export const PaymentCouponSchema = SchemaFactory.createForClass(PaymentCoupon);
