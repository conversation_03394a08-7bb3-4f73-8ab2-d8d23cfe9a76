import { ApiProperty, PartialType } from '@nestjs/swagger';
import {
  IsBoolean,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';
import { PAYMENT_TYPES } from '../../../enums/payment.enum';
import { PaymentCoupon } from './payment-coupons.schema';
import { CURRENCIES } from '../../country/country.schema';
import { Transform } from 'class-transformer';

export class CreatePaymentCouponDto {
  @ApiProperty({ type: String, enum: ['percentage', 'fixed'] })
  @IsString()
  @IsNotEmpty()
  type: 'percentage' | 'fixed';

  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  coupon_code: string;

  @ApiProperty({ type: Number })
  @IsNotEmpty()
  @IsNumber()
  @IsOptional()
  discount_cap?: number;

  @ApiProperty({ type: Number })
  @IsNotEmpty()
  @IsNumber()
  @IsOptional()
  discount_amount?: number;

  @ApiProperty({ type: Number })
  @IsNotEmpty()
  @IsNumber()
  @IsOptional()
  percentage?: number;

  @ApiProperty({ type: Number })
  @IsNotEmpty()
  @IsNumber()
  quantity: number;

  @ApiProperty()
  @Transform((value) => new Date(value).toISOString())
  // @IsString()
  @IsDateString()
  @IsNotEmpty()
  end_date: PaymentCoupon['end_date'];

  @ApiProperty({ type: String })
  @IsEnum([...Object.values(PAYMENT_TYPES)])
  payment_type: PAYMENT_TYPES;

  @ApiProperty({ type: String })
  @IsEnum([...Object.values(CURRENCIES)])
  currency: CURRENCIES;

  @ApiProperty()
  @IsOptional()
  config?: {
    plans?: string[];
    couriers?: string[];
    provider?: string;
  };
}

export class VerifyPaymentCouponDTO {
  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  coupon_code: string;

  @ApiProperty({ type: String })
  @IsEnum([...Object.values(PAYMENT_TYPES)])
  payment_type: PAYMENT_TYPES;

  @ApiProperty({ type: String })
  @IsEnum([...Object.values(CURRENCIES)])
  currency: CURRENCIES;

  @ApiProperty({ type: String })
  @IsString()
  @IsOptional()
  plan: string;

  @ApiProperty({ type: String })
  @IsString()
  @IsOptional()
  delivery: string;
}
