import { PaymentCoupon } from "./payment-coupons.schema";

export function computePaymentCouponDiscount(totalAmount: number, coupon: PaymentCoupon) {
  let discountAmount;
  if (coupon.type == 'percentage') {
    discountAmount = (totalAmount * coupon.percentage) / 100;
    const discountExceeded = coupon.discount_cap ? discountAmount > coupon.discount_cap : false;
    discountAmount = discountExceeded ? coupon.discount_cap : discountAmount;
  } else if (coupon.type == 'fixed') {
    discountAmount = coupon.discount_amount;
  }

  return discountAmount > totalAmount ? -totalAmount : -discountAmount;
}
