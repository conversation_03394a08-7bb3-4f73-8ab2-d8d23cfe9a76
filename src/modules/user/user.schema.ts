import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Store } from '../store/store.schema';
import { Subscription } from '../subscription/subscription.schema';
import { COUNTRY_CODE } from '../country/country.schema';
import { INTERNAL_ROLES } from '../../utils/permissions.util';

export type UserDocument = User & Document;

export enum USER_TYPE {
  REGULAR = 'REGULAR',
  THIRD_PARTY = 'THIRD_PARTY',
}

export enum NOTIFICATION_TYPE {
  WEB_PUSH = 'web-push',
  FIREBASE = 'firebase',
}
// First enum with key and value the same
export enum ONBOARDING_STEPS_WITH_REWARDS {
  WATCH_SETUP_VIDEO = 'WATCH_SETUP_VIDEO',
  UPLOAD_10_PRODUCTS = 'UPLOAD_10_PRODUCTS',
  ENABLE_PUSH_NOTIFICATION = 'ENABLE_PUSH_NOTIFICATION',
  COMPLETE_KYC = 'COMPLETE_KYC',
  FIRST_ORDER_WITH_PAYMENT = 'FIRST_ORDER_WITH_PAYMENT',
}

export enum ONBOARDING_STEPS_REWARD_KEYS {
  WATCH_SETUP_VIDEO = 'setup_video_credits_earned',
  UPLOAD_10_PRODUCTS = 'product_upload_credits_earned',
  ENABLE_PUSH_NOTIFICATION = 'push_notification_credits_earned',
  COMPLETE_KYC = 'kyc_credits_earned',
  FIRST_ORDER_WITH_PAYMENT = 'first_order_credits_earned',
}

export class SourceAd {
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  gclid?: string;
  fbp?: string; //facebook pixel id
  fbc?: string; //facebook click id
  ip_address?: string;
  user_agent?: string;
}

export class UserMeta {
  @Prop({ type: Boolean, required: false })
  added_to_customer_io?: boolean;

  @Prop({ type: String, required: false })
  bigin_record_id?: string;

  @Prop({ type: Boolean, required: false, default: false })
  has_watched_onboarding_video?: boolean;

  @Prop({ type: Boolean, required: false, default: false })
  is_qualified?: boolean;
}

@Schema()
export class OnboardingSteps {
  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  community_joined: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  pwa_added: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  onboarding_call_booked: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  has_followed_socials: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, default: false, required: false })
  has_enabled_notification?: boolean;
}

@Schema()
export class OnboardingRewards {
  @ApiProperty()
  @Prop({ type: Boolean, default: false, required: false })
  setup_video_credits_earned?: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, default: false, required: false })
  first_order_credits_earned?: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, default: false, required: false })
  kyc_credits_earned?: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, default: false, required: false })
  push_notification_credits_earned?: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, default: false, required: false })
  product_upload_credits_earned?: boolean;
}

@Schema({ timestamps: true })
export class User {
  public id: string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  name: string;

  @ApiProperty()
  @Prop({ type: String, required: true, unique: true })
  email: string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  password: string;

  @ApiProperty()
  @Prop({ type: String, required: true, unique: true })
  phone: string;

  // TODO specify plan relationship for a user
  @Prop({ type: String })
  plan: string;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  email_verified: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  phone_verified: boolean;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  avatar: string;

  @ApiProperty()
  @Prop({ type: Date, default: null })
  can_get_custom_link: Date | null;

  @ApiProperty()
  @Prop({ type: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Store' }] })
  stores: Store[];

  @ApiProperty({ type: () => User })
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Store' })
  primary_store: Store;

  @ApiProperty()
  subscription?: Subscription;

  @ApiProperty()
  @Prop({ type: OnboardingSteps, default: new OnboardingSteps() })
  onboarding_steps: OnboardingSteps;

  @ApiProperty()
  @Prop({ type: OnboardingSteps, default: new OnboardingSteps() })
  onboarding_rewards: OnboardingRewards;

  @Prop({ type: String })
  email_verification_token: string;

  @Prop({ type: String })
  phone_verification_token: string;

  @Prop({ type: String })
  withdrawal_verification_token: string;

  @Prop({ type: String })
  reset_password_token: string;

  @Prop({ type: Date })
  last_login: Date;

  @Prop({ type: Boolean })
  storesDisabled?: boolean;

  @Prop({ type: Boolean, default: true })
  auto_debit_wallets?: boolean;

  @Prop({ type: String })
  reference?: string;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  referred_by?: string;

  @Prop({ type: SourceAd, required: false })
  source_ad?: SourceAd;

  @Prop({ type: UserMeta, required: false })
  meta?: UserMeta;

  @ApiProperty()
  @Prop({ type: String, default: USER_TYPE.REGULAR })
  type: USER_TYPE;

  @ApiProperty({ enum: INTERNAL_ROLES })
  @Prop({
    type: String,
    enum: Object.values(INTERNAL_ROLES),
    required: false,
  })
  internal_dashboard_role?: string;
}

export const UserSchema = SchemaFactory.createForClass(User);
