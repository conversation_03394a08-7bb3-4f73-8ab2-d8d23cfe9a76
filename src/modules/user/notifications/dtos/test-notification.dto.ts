import { IsNotEmpty, IsObject, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { NOTIFICATION_TYPE } from '../notification.schema';

export class SendTestNotificationDto {
  @ApiProperty({ description: 'User ID to send notification to' })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({ description: 'Notification title' })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({ description: 'Notification message' })
  @IsString()
  @IsNotEmpty()
  message: string;

  @ApiProperty({ description: 'Path to navigate to when notification is clicked' })
  @IsString()
  @IsNotEmpty()
  path: string;

  @ApiProperty({ enum: NOTIFICATION_TYPE, description: 'Type of notification' })
  @IsString()
  @IsNotEmpty()
  type: NOTIFICATION_TYPE;

  @ApiProperty({ description: 'Additional data to include with the notification', required: false })
  @IsObject()
  @IsOptional()
  data?: Record<string, any>;
}
