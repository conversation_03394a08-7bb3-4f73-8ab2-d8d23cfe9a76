import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import {
  FirebaseSubscription,
  FirebaseSubscriptionSchema,
  Notification,
  NotificationSchema,
  PushNotificationSubscription,
  PushNotificationSubscriptionSchema,
} from './notification.schema';
import { NotificationService } from './notification.service';
import { NotificationController } from './notification.controller';
import { FirebaseRepository } from '../../../repositories/firebase.repository';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Notification.name, schema: NotificationSchema },
      { name: PushNotificationSubscription.name, schema: PushNotificationSubscriptionSchema },
      { name: FirebaseSubscription.name, schema: FirebaseSubscriptionSchema },
    ]),
  ],
  controllers: [NotificationController],
  providers: [
    NotificationService,
    {
      provide: FirebaseRepository,
      useFactory: (logger, configService) => {
        return new FirebaseRepository(logger, configService);
      },
      inject: ['Logger', 'ConfigService'],
    },
  ],
  exports: [NotificationService],
})
export class NotificationModule {}
