import { Body, Controller, Get, Param, Patch, Post, Query, Req, UseGuards } from '@nestjs/common';
import { NotificationService } from './notification.service';
import { ApiBearerAuth, ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../jwt/jwt-auth.guard';
import { COUNTRY_CODE } from '../../country/country.schema';
import { NOTIFICATION_TYPE } from './notification.schema';
import { ConfigService } from '@nestjs/config';
import { SendTestNotificationDto } from './dtos';

@ApiTags('notifications')
@Controller('notifications')
export class NotificationController {
  private readonly isDevelopment: boolean;

  constructor(
    private readonly notificationService: NotificationService,
    private readonly configService: ConfigService,
  ) {
    this.isDevelopment = this.configService.get('NODE_ENV') !== 'production';
  }

  @ApiOperation({ summary: 'Get notifications for current store' })
  @ApiResponse({ status: 200, description: 'Return all notifications for the current store' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'per_page', required: false, type: Number, description: 'Number of items per page' })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @Get()
  async getNotifications(@Req() req, @Query('page') page?: number, @Query('per_page') perPage?: number) {
    const storeId = req.user.store.id;
    return this.notificationService.getStoreNotifications(req.user.id, storeId, page || 1, perPage || 20);
  }

  @ApiOperation({ summary: 'Get count of unread notifications for current store' })
  @ApiResponse({ status: 200, description: 'Return count of unread notifications for the current store' })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @Get('unread/count')
  async getUnreadCount(@Req() req) {
    const storeId = req.user.store.id;
    return {
      count: await this.notificationService.getStoreUnreadCount(req.user.id, storeId),
    };
  }

  @ApiOperation({ summary: 'Mark a notification as read' })
  @ApiResponse({ status: 200, description: 'Notification marked as read' })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @Patch(':notificationId/read')
  async markAsRead(@Param('notificationId') notificationId: string) {
    return this.notificationService.markAsRead(notificationId);
  }

  @ApiOperation({ summary: 'Mark all notifications as read for current store' })
  @ApiResponse({ status: 200, description: 'All notifications marked as read for the current store' })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @Patch('read-all')
  async markAllAsRead(@Req() req) {
    const storeId = req.user.store.id;
    await this.notificationService.markAllStoreAsRead(req.user.id, storeId);
    return { success: true };
  }

  @ApiOperation({ summary: 'Save push notification subscription' })
  @ApiResponse({ status: 201, description: 'Subscription saved successfully' })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @Post('push-subscription')
  async savePushSubscription(
    @Req() req,
    @Body()
    body: {
      endpoint: string;
      public_key: string;
      private_key: string;
      country?: COUNTRY_CODE;
    },
  ) {
    return this.notificationService.savePushSubscription(req.user.id, body);
  }

  @ApiOperation({ summary: 'Save firebase subscription' })
  @ApiResponse({ status: 201, description: 'Firebase subscription saved successfully' })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @Post('firebase-subscription')
  async saveFirebaseSubscription(@Req() req, @Body() body: { fcm_token: string; country?: COUNTRY_CODE }) {
    return this.notificationService.saveFirebaseSubscription(req.user.id, body.fcm_token, body.country);
  }

  @ApiOperation({ summary: 'Send a test notification to a specific user' })
  @ApiResponse({ status: 201, description: 'Notification sent successfully' })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @Post('test-notification')
  async sendTestNotification(@Req() req, @Body() notificationDto: SendTestNotificationDto) {
    const storeId = req.user.store.id;
    const notification = await this.notificationService.sendNotification(notificationDto.userId, {
      title: notificationDto.title,
      message: notificationDto.message,
      path: notificationDto.path,
      type: notificationDto.type,
      data: notificationDto.data || {},
      store: storeId, // Include current store context
    });

    return {
      success: true,
      notification,
    };
  }

  @ApiOperation({ summary: 'Send a test notification (DEVELOPMENT ONLY)' })
  @ApiResponse({ status: 201, description: 'Notification sent successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden in production environment' })
  @Post('dev/test-notification')
  async sendDevTestNotification(@Body() notificationDto: SendTestNotificationDto & { storeId?: string }) {
    // Only allow this endpoint in development
    if (!this.isDevelopment) {
      return {
        success: false,
        message: 'This endpoint is only available in development environments',
      };
    }

    const notification = await this.notificationService.sendNotification(notificationDto.userId, {
      title: notificationDto.title,
      message: notificationDto.message,
      path: notificationDto.path,
      type: notificationDto.type,
      data: notificationDto.data || {},
      store: notificationDto.storeId, // Allow setting store context for dev testing
    });

    return {
      success: true,
      notification,
    };
  }
}
