import { InjectQueue, OnQueueActive, OnQueueCompleted, OnQueueFailed, Process, Processor } from '@nestjs/bull';
import { Job, Queue } from 'bull';
import { BrokerTransportService } from '../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { JOBS, QUEUES } from '../../enums/queues.enum';
import { ResendRepository } from '../../repositories/resend.repository';

export interface UserJob {
  type: JOBS.SEND_WELCOME_EMAILS;
  data: {
    user_name: string;
    name: string;
    email: string;
  };
}

@Processor(QUEUES.USER)
export class UserQueue {
  constructor(
    @InjectQueue(QUEUES.USER)
    private readonly queue: Queue<{
      user_name: string;
      name: string;
      email: string;
    }>,
    protected readonly brokerTransport: BrokerTransportService,
    private readonly resend: ResendRepository,
  ) {}

  @OnQueueActive()
  onActive(job: Job<UserJob>) {
    console.log(`Processing job ${job.id} of type ${job.name} with data ${job.data.data.user_name}...`);
  }

  @OnQueueFailed()
  onFailed(job: Job, error: Error) {
    console.log(`Failed to process job ${job.id} of type ${job.name} with data, error: ${error.message}`);
  }

  @OnQueueCompleted()
  onCompleted(job: Job, result: any) {
    console.log(`Job ${job.id} of type ${job.name} successfully completed, result: ${result}`);
    job.remove().catch(console.log);
  }

  @Process(QUEUES.USER)
  async send(job: Job<UserJob>) {
    switch (job.data.type) {
      case JOBS.SEND_WELCOME_EMAILS:
        await this.resend.sendEmail(BROKER_PATTERNS.MAIL.WELCOME_EMAIL, {
          to: job.data.data.email,
          subject: 'Welcome To Catlog 👋',
          data: {
            name: job.data.data.user_name.split(' ')[0],
          },
        });
        return;
        break;
      default:
      // DO NOTHING
    }
  }
}
