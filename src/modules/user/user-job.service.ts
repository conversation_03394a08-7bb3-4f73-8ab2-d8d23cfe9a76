import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { UserService } from './user.service';
import { formatPhoneNumber, isUsingProdDBLocally } from '../../utils';
import dayjs from 'dayjs';
import { getDocId } from '../../utils/functions';

export class Users<PERSON>ob extends UserService {
  // @Cron(CronExpression.EVERY_DAY_AT_2PM)
  // async sendSignupAutoCheckInJob() {
  //   this.logger.log('Running signup auto check-in job');

  //   const startOfYesterday = dayjs().subtract(1, 'day').startOf('day');
  //   const endOfYesterday = dayjs().subtract(1, 'day').endOf('day');

  //   const users = await this.userModel
  //     .find({
  //       createdAt: { $gte: startOfYesterday, $lte: endOfYesterday },
  //     })
  //     .exec();

  //   // Send check-in message to each user
  //   for (const user of users) {
  //     await this.whatsapp.sendSignupCheckInMessage(formatPhoneNumber(user.phone), { customerName: user.name });
  //   }
  // }

  @Cron(CronExpression.EVERY_WEEK)
  async deleteInactiveUsersFromCustomerIo() {
    if (isUsingProdDBLocally()) return;
    this.logger.log('Running signup auto check-in job');

    const twoMonthsAgo = new Date();
    twoMonthsAgo.setMonth(twoMonthsAgo.getMonth() - 6);

    const users = await this.userModel.find({
      last_login: { $lt: twoMonthsAgo },
      'meta.added_to_customer_io': true,
    });

    for (const user of users) {
      //delete users from customer.io
      await this.customerIo.deleteUser(getDocId(user));
    }
  }
}
