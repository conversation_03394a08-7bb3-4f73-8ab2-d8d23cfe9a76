import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { CURRENCIES } from '../../country/country.schema';
import mongoose, { Document, Types } from 'mongoose';
import { TRANSACTION_TYPE } from '../../wallets/wallet.schema';

export type CatlogCreditsDocument = CatlogCredits & Document;
export type CatlogCreditsTransactionsDocument = CatlogCreditsTransactions & Document;

export enum ONBOARDING_REWARDS_TIME_LIMITS {
  WATCH_SETUP_VIDEO = 1,
  UPLOAD_10_PRODUCTS = Number.MAX_SAFE_INTEGER,
  ENABLE_PUSH_NOTIFICATION = 3,
  COMPLETE_KYC = 5,
  FIRST_ORDER_WITH_PAYMENT = 7,
}
export enum ONBOARDING_REWARDS_NARRATION {
  WATCH_SETUP_VIDEO = 'Credits for watching setup video',
  UPLOAD_10_PRODUCTS = 'Credits for uploading 10 products',
  ENABLE_PUSH_NOTIFICATION = 'Credits for enabling push notification',
  COMPLETE_KYC = 'Credits for completing kyc',
  FIRST_ORDER_WITH_PAYMENT = 'Credits for first order with payment',
}

export class CatlogCreditsTxMeta {
  referral_code?: string;
  payment_id?: string;
  referred_user?: string;
  referred_by?: string;
  timestamp?: number;
  onboarding_step?: string;
  manual_credit_reference?: string;
  reversed_transaction_id?: string;
}

@Schema({ timestamps: true })
export class CatlogCredits {
  public id: string;

  @ApiProperty()
  @Prop({ type: Number, default: 0 })
  balance: number;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User', unique: true })
  owner: string;

  @ApiProperty()
  @Prop({ type: String })
  currency: CURRENCIES;
}

@Schema({ timestamps: true })
export class CatlogCreditsTransactions {
  public id: string;

  @ApiProperty()
  @Prop({ type: Number, required: true })
  amount: number;

  @ApiProperty()
  @Prop({ type: Number, required: true })
  balance_before: number;

  @ApiProperty()
  @Prop({ type: String })
  currency: CURRENCIES;

  @ApiProperty()
  @Prop({ enum: [TRANSACTION_TYPE.CREDIT, TRANSACTION_TYPE.DEBIT] })
  type: TRANSACTION_TYPE;

  @ApiProperty()
  @Prop({ type: String })
  narration: string;

  @ApiProperty()
  @Prop({ type: CatlogCreditsTxMeta })
  meta: CatlogCreditsTxMeta;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'CatlogCredits' })
  credit_wallet: string;
}

export class ReverseTransactionDto {
  @ApiProperty({ description: 'Transaction ID to reverse', example: '60f7b1b3e4b0a12345678901' })
  transactionId: string;

  @ApiProperty({ description: 'Reason for reversing the transaction', example: 'Admin reversal due to error' })
  reason: string;
}

export const CatlogCreditsSchema = SchemaFactory.createForClass(CatlogCredits);
export const CatlogCreditsTransactionsSchema = SchemaFactory.createForClass(CatlogCreditsTransactions);
