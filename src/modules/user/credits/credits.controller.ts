import { Body, Controller, Get, Post, Query, Req, UseGuards } from '@nestjs/common';
import { CatlogCreditsService } from './credits.service';
import { JwtAuthGuard } from '../../../jwt/jwt-auth.guard';
import { InternalApiJWTGuard } from '../../../guards/api.guard';
import { ApiExcludeEndpoint, ApiSecurity } from '@nestjs/swagger';
import { IRequest } from '../../../interfaces/request.interface';
import { PaginatedQueryDto } from '../../wallets/dtos/search.dto';
import { ManuallyCreditWalletDto } from '../../../models/dtos/UserDtos';
import { ReverseTransactionDto } from './credits.schema';

@Controller('users/credits')
export class CreditsController {
  constructor(private readonly creditsService: CatlogCreditsService) {}

  @Get('balance')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  async getStoreCredits(@Req() req: IRequest) {
    const data = await this.creditsService.getStoreCredits(req.user?.store?.id);

    return {
      message: 'Fetched store credits',
      data,
    };
  }

  @Get('/transactions')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  async getStoreCreditTransactions(
    @Req() req: IRequest,
    @Query('filter') filter: any,
    @Query() query: PaginatedQueryDto,
  ) {
    const data = await this.creditsService.getStoreTransactions(req.user?.store?.id, filter, query);

    return {
      message: 'Fetched credits transactions',
      ...data,
    };
  }

  @Post('convert-to-cash')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  async convertCreditsToCash(@Req() req: IRequest, @Body() body: { amount: number }) {
    const data = await this.creditsService.convertToCash(req.user?.store?.id, body.amount);

    return {
      message: 'Converted to cash',
      data,
    };
  }

  @Post('/manually-add-credits')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async manuallyAddCredits(@Body() body: ManuallyCreditWalletDto) {
    const res = await this.creditsService.manuallyAddCredits(body);

    return {
      message: 'Added credits to user',
      data: res,
    };
  }

  @Post('/migrate')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async migrateAllCredits() {
    const res = await this.creditsService.migrateAllCredits();

    return {
      message: 'Migrated credits',
      data: res,
    };
  }

  @Post('/reverse-transaction')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async reverseTransaction(@Body() body: ReverseTransactionDto) {
    const data = await this.creditsService.reverseTransaction(body.transactionId, body.reason);

    return {
      message: 'Transaction reversed successfully',
      data,
    };
  }
}
