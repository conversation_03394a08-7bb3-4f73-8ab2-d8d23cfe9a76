import { BadRequestException, Inject, Injectable, forwardRef } from '@nestjs/common';
import { InjectConnection, InjectModel } from '@nestjs/mongoose';
import { ONBOARDING_STEPS_REWARD_KEYS, ONBOARDING_STEPS_WITH_REWARDS, User, UserDocument } from '../user.schema';
import mongoose, { Model, PaginateModel, Types as mongooseTypes } from 'mongoose';
import {
  CatlogCredits,
  CatlogCreditsDocument,
  CatlogCreditsTransactions,
  CatlogCreditsTransactionsDocument,
  CatlogCreditsTxMeta,
  ONBOARDING_REWARDS_NARRATION,
  ONBOARDING_REWARDS_TIME_LIMITS,
} from './credits.schema';
import { COUNTRY_CODE, COUNTRY_CURRENCY_MAP, CURRENCIES, CURRENCY_COUNTRY_MAP } from '../../country/country.schema';
import { Store, StoreDocument } from '../../store/store.schema';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { TRANSACTION_CHANNELS, TRANSACTION_TYPE, Wallet, WalletDocument } from '../../wallets/wallet.schema';
import { Referrals, ReferralsDocument } from '../referrals/referrals.schema';
import { SearchTransactionsDto } from '../../wallets/dtos/search.dto';
import { PaginatedQueryDto } from '../../wallets/dtos/search.dto';
import { UserService } from '../user.service';
import { genChars, toCurrency } from '../../../utils';
import { Mutex } from 'async-mutex';
import { ManuallyCreditWalletDto } from '../../../models/dtos/UserDtos';
import { ApiGuardConfig } from '../../../config/types/api-guard.config';
import { generateOTP, getDocId, toKobo, toNaira } from '../../../utils/functions';
import { ConfigService } from '@nestjs/config';
import { CREDITS, ONBOARDING_CREDITS } from '../../../utils/constants';
import { ResendRepository } from '../../../repositories/resend.repository';
import dayjs from 'dayjs';

const phoneToCurrency: { [key: string]: CURRENCIES } = {
  '+234': CURRENCIES.NGN,
  '+233': CURRENCIES.GHC,
};

@Injectable()
export class CatlogCreditsService {
  private mutex = new Mutex();

  constructor(
    private readonly brokerTransport: BrokerTransportService,
    @InjectModel(CatlogCredits.name)
    public readonly creditsModel: Model<CatlogCreditsDocument>,
    @InjectModel(CatlogCreditsTransactions.name)
    private readonly creditsTrasactionsModel: PaginateModel<CatlogCreditsTransactionsDocument>,
    @InjectModel(Referrals.name)
    private readonly referralsModel: Model<ReferralsDocument>,
    @InjectModel(User.name)
    private readonly usersModel: Model<UserDocument>,
    @Inject(forwardRef(() => UserService))
    private readonly userService: UserService,
    @InjectConnection()
    protected readonly connection: mongoose.Connection,
    private config: ConfigService,
    private readonly resend: ResendRepository,
  ) {}

  async create(owner: string, country: string, balance?: number) {
    const credits = await this.creditsModel.create({
      owner,
      balance: balance || 0,
      currency: COUNTRY_CURRENCY_MAP[country],
    });

    await credits.save();

    return credits;
  }

  async getStoreTransactions(storeId: string, filter: SearchTransactionsDto, pagination: PaginatedQueryDto) {
    const credits = await this.getStoreCredits(storeId);

    const compoundFilter: any = {};

    if (filter?.search) {
      compoundFilter.narration = { $regex: filter.search, $options: 'i' };
    }

    if (filter?.type && filter.type !== 'all') {
      compoundFilter.type = filter.type;
    }

    const transactions = await this.creditsTrasactionsModel.paginate(
      {
        ...compoundFilter,
        credit_wallet: credits._id,
      },
      {
        sort: { createdAt: -1 },
        page: pagination.page || 1,
        limit: pagination.per_page || 50,
        lean: true,
      },
    );

    return {
      data: {
        store: storeId,
        transactions: transactions.docs,
      },
      page: transactions.page,
      next_page: transactions.nextPage,
      prev_page: transactions.prevPage,
      total: transactions.totalDocs,
      total_pages: transactions.totalPages,
      per_page: transactions.limit,
    };
  }

  async addCreditsToUser(
    user: string,
    amount: number,
    meta: CatlogCreditsTxMeta,
    narration: string,
    country: COUNTRY_CODE = COUNTRY_CODE.NG,
  ) {
    let newlyCreated = false;
    let credits = await this.creditsModel.findOne({ owner: user });
    // if (!credits) return { creditted: false, message: 'Credit wallet not found' };

    if (!credits) {
      credits = await this.create(user, country, 0);
      newlyCreated = true;
    }

    if (amount > 0) {
      const similarTransaction = await this.creditsTrasactionsModel.findOne({
        meta: meta,
        type: TRANSACTION_TYPE.CREDIT,
        credit_wallet: credits.id,
      });

      if (similarTransaction) return { creditted: false, message: 'Transaction previously processed', credits };

      const tx = await this.creditsTrasactionsModel.create({
        amount,
        currency: credits.currency,
        type: TRANSACTION_TYPE.CREDIT,
        narration: narration,
        meta,
        credit_wallet: credits.id,
        balance_before: credits.balance,
      });

      credits.balance += amount;
      await credits.save();
      await tx.save();

      if (!newlyCreated) {
        const userData = await this.usersModel.findById(user);

        await this.userService.sendPushNotification(
          '',
          {
            title: `${toCurrency(String(amount / 100), credits.currency)} has been added to your credit wallet 🤑`,
            message: narration,
            path: `/dashboard`,
          },
          false,
          user,
        );

        this.resend.sendEmail(BROKER_PATTERNS.MAIL.CREDITS_RECEIVED, {
          to: userData?.email,
          subject: `${toCurrency(String(amount / 100), credits.currency)} has been added to your credit wallet 🤑`,
          data: {
            name: userData.name.split(' ')[0],
            amount: toCurrency(String(amount / 100), credits.currency),
            balance: toCurrency(String(credits.balance / 100), credits.currency),
            reason: narration,
          },
        });
      }
    }

    return { creditted: true, message: 'Wallet creditted', credits };
  }

  async debitCreditsFromUser(user: string, amount: number) {
    const credits = await this.creditsModel.findOneAndUpdate(
      {
        owner: user,
        balance: {
          $gte: amount,
        },
      },
      {
        $inc: {
          balance: -amount,
        },
      },
    );
    if (!credits) return { debitted: false, message: 'Insufficient funds' };

    return {
      debitted: true,
      message: 'Wallet debitted',
    };
  }

  async reverseDebit(user: string, amount: number) {
    const credits = await this.creditsModel.findOneAndUpdate(
      {
        owner: user,
      },
      {
        $inc: {
          balance: amount,
        },
      },
    );

    return {
      debitted: true,
      message: 'Wallet debitted',
      wallet: credits,
    };
  }

  async recordCreditDebit(user: string, amount: number, meta: CatlogCreditsTxMeta, narration: string) {
    const credits = await this.creditsModel.findOne({ owner: user });
    if (!credits) return;

    const similarTransaction = await this.creditsTrasactionsModel.findOne({
      meta: meta,
      type: TRANSACTION_TYPE.DEBIT,
      credit_wallet: credits.id,
    });

    if (similarTransaction) return;

    const tx = await this.creditsTrasactionsModel.create({
      amount,
      currency: credits.currency,
      type: TRANSACTION_TYPE.DEBIT,
      narration,
      meta,
      credit_wallet: credits.id,
      balance_before: amount + credits.balance,
    });

    await tx.save();

    return tx.toJSON();
  }

  async chargeWallet(
    user: string,
    amount: number,
    narration: string,
    type: TRANSACTION_TYPE,
    meta: CatlogCreditsTxMeta,
    currency: CURRENCIES,
  ) {
    const release = await this.mutex.acquire();

    try {
      const wallet = await this.creditsModel.findOne({ owner: user });

      if (!wallet) {
        release();
        return { status: 400, message: 'No wallet found' };
      }

      const similarTransaction = await this.creditsTrasactionsModel.findOne({ meta: meta });

      if (similarTransaction) {
        release();
        return wallet; // Transaction already processed
      }

      const transaction = new this.creditsTrasactionsModel({
        amount,
        type,
        narration,
        balance_before: wallet.balance,
        credit_wallet: wallet._id,
        meta,
        currency,
      });

      if (type === TRANSACTION_TYPE.DEBIT && transaction.amount > wallet.balance) {
        release();
        throw new Error('Insufficient funds');
      }

      if (type === TRANSACTION_TYPE.DEBIT) wallet.balance -= amount;
      else if (type === TRANSACTION_TYPE.CREDIT) wallet.balance += amount;
      else {
        release();
        throw new Error('Invalid transaction type');
      }

      await wallet.save();
      await transaction.save();

      release();

      return wallet;
    } catch (error) {
      release();
      return { status: 500, message: error?.message, error: error?.message ?? 'Something went wrong' };
    }
  }

  async addCreditsForOnboardingSteps(step: ONBOARDING_STEPS_WITH_REWARDS, userId: string) {
    const user = await this.usersModel.findById(userId);

    if (!user) return { error: 'User not found' };

    if (user.onboarding_rewards[step]) return { error: 'User already earned reward' };

    const daysSinceSignup = dayjs().diff(dayjs((user as any)?.createdAt), 'days');

    if (daysSinceSignup > ONBOARDING_REWARDS_TIME_LIMITS[step]) return { error: 'Time limit has passed for reward' };

    const wallet = await this.creditsModel.findOne({ owner: getDocId(user) });
    const creditsToEarn = ONBOARDING_CREDITS[step][wallet.currency];

    if (!creditsToEarn) return { error: 'Currency not supported' };

    const credits = await this.addCreditsToUser(
      getDocId(user),
      creditsToEarn,
      { onboarding_step: step },
      ONBOARDING_REWARDS_NARRATION[step],
      COUNTRY_CURRENCY_MAP[wallet.currency],
    );

    if (!credits) return { error: 'Something went wrong' };

    user.onboarding_rewards = { ...(user.onboarding_rewards ?? {}), [ONBOARDING_STEPS_REWARD_KEYS[step]]: true };
    await user.save();

    return { credits: credits?.credits };
  }

  async getStoreCredits(storeId: string) {
    const store = await this.brokerTransport
      .send<any>(BROKER_PATTERNS.STORE.GET_STORE_LEAN, { filter: { _id: storeId }, select: { owner: 1 } })
      .toPromise();

    if (!store) {
      throw new BadRequestException('Store does not exist');
    }

    const credits = await this.creditsModel.findOne({ owner: store?.owner as string });

    if (!credits) return null;

    return credits;
  }

  async convertToCash(storeId: string, amount: number) {
    const store = await this.brokerTransport
      .send<any>(BROKER_PATTERNS.STORE.GET_STORE_LEAN, { filter: { _id: storeId }, select: { owner: 1 } })
      .toPromise();

    if (!store) {
      throw new BadRequestException('Store does not exist');
    }

    const credits = await this.creditsModel.findOne({ owner: store?.owner as string });

    if (!credits) throw new BadRequestException("Couldn't find credit wallet");

    const amountInKobo = toKobo(amount);

    if (amountInKobo < CREDITS.REFERRAL.COMMISION.CASHOUT[CURRENCY_COUNTRY_MAP[credits.currency]])
      throw new BadRequestException(
        `Minimum cash out amount is ${toCurrency(toNaira(amountInKobo), credits.currency)}`,
      );

    if (amountInKobo > credits.balance)
      throw new BadRequestException(
        `Insufficient credit balance, current balance is: ${toCurrency(toNaira(credits.balance), credits.currency)}`,
      );

    const wallet = await this.brokerTransport
      .send<Wallet>(BROKER_PATTERNS.WALLET.GET_WALLET, { storeId })
      .toPromise();

    if (!wallet) throw new BadRequestException('Please complete KYC to get a wallet');

    if (wallet.currency !== credits.currency)
      throw new BadRequestException('Please switch to a store with the same currency as your catlog credits');

    const conversionTimeStamp = Date.now();

    const chargedWallet = await this.chargeWallet(
      store?.owner,
      amountInKobo,
      'Withdrawal to cash',
      TRANSACTION_TYPE.DEBIT,
      { timestamp: conversionTimeStamp },
      credits.currency,
    );

    if (!chargedWallet || (chargedWallet as any)?.error)
      throw new BadRequestException('Something went wrong! Please try again');

    const amountToCredit = amountInKobo / 2;
    const credittedWallet = await this.brokerTransport
      .send<{
        success: boolean;
        wallet?: WalletDocument;
        message: string;
      }>(BROKER_PATTERNS.WALLET.CREDIT, {
        walletId: getDocId(wallet),
        amount: amountToCredit,
        channel: TRANSACTION_CHANNELS.CREDIT_CASHOUT,
        narration: `${toCurrency(toNaira(amountInKobo), credits.currency)} credits to cash`,
        fee: 0,
        meta: { manual_credit_reference: conversionTimeStamp.toString() },
        source: {
          name: 'Catlog',
          purpose: `Catlog Credit to Cash`,
          method: '',
        },
      })
      .toPromise();

    if (!credittedWallet?.success) {
      await this.chargeWallet(
        store?.owner,
        amountInKobo,
        '[REVERSAL] Withdrawal to cash',
        TRANSACTION_TYPE.CREDIT,
        { timestamp: conversionTimeStamp },
        credits.currency,
      );

      throw new BadRequestException('Something went wrong! Please try again');
    }

    return chargedWallet as CatlogCreditsDocument;
  }

  async getUserCredits(user: string) {
    const credits = await this.creditsModel.findOne({ owner: user });

    if (!credits) return;

    return credits;
  }

  async getCreditsFromStore(storeId: string) {
    const store = await this.brokerTransport
      .send(BROKER_PATTERNS.STORE.GET_STORE_LEAN, { filter: { _id: storeId }, select: { owner: 1 } })
      .toPromise();

    if (!store || !store?.owner) return null;

    const credits = await this.creditsModel.findOne({ owner: store?.owner });

    if (!credits) return;

    return credits;
  }

  async manuallyAddCredits(data: ManuallyCreditWalletDto) {
    const { amount, otp: userOtp, narration, email, country } = data;
    if (!userOtp) throw new BadRequestException('OTP is required');

    const apiGuardCfg = this.config.get<ApiGuardConfig>('apiGuardConfig');
    const otp = generateOTP(apiGuardCfg.otpSalt);

    if (userOtp !== otp) throw new BadRequestException('GET THE FUCK OFFFFF!!!! CALLING THE POLICE');

    const txReference = genChars(16);
    const user = await this.usersModel.findOne({ email });

    if (!user) throw new BadRequestException('No user');

    return await this.addCreditsToUser(
      getDocId(user),
      toKobo(amount),
      { manual_credit_reference: txReference },
      narration,
      country,
    );
  }

  async migrateAllCredits() {
    const stores = await this.brokerTransport
      .send<StoreDocument[]>(BROKER_PATTERNS.STORE.GET_STORES_LEAN, { filter: {}, select: { owner: 1, country: 1 } })
      .toPromise();

    // const owners = [...new Set(stores.map((s) => ({s.owner.toString()})))];

    // return owners;

    const savedOwnersLog = [];
    const reqs = [];

    let count = 0;

    for (const store of stores) {
      const owner = store.owner.toString();
      const creditsExist = savedOwnersLog.includes(owner);

      if (creditsExist) continue;

      savedOwnersLog.push(owner);
      reqs.push(this.create(owner, (store.country as COUNTRY_CODE) || COUNTRY_CODE.NG));
    }

    const res = await Promise.all(reqs);

    return res;
  }

  // Method to get total credits earned by a user in a specified date range
  async totalCreditsEarnedWrapData(ownerId: string, startDate: Date, endDate: Date): Promise<number> {
    startDate = dayjs(startDate).toDate();
    endDate = dayjs(endDate).toDate();
    // Fetch the wallet ID for the given owner
    const wallet = await this.creditsModel.findOne({ owner: ownerId }).exec();

    // If no wallet is found for the owner, return 0 credits
    if (!wallet) {
      return 0;
    }

    // Now, use the wallet's ID to filter the transactions
    const result = await this.creditsTrasactionsModel.aggregate([
      // Match transactions for the wallet
      {
        $match: {
          credit_wallet: mongoose.Types.ObjectId(wallet._id), // Use the wallet's ID
          type: 'credit', // Only credit transactions
          createdAt: { $gte: startDate, $lte: endDate }, // Filter by date range
        },
      },
      // Project the necessary fields
      {
        $project: {
          amount: 1,
          createdAt: 1,
        },
      },
      // Group by credit_wallet and sum the credits
      {
        $group: {
          _id: '$credit_wallet', // Group by the credit_wallet (wallet ID)
          totalCredits: { $sum: '$amount' }, // Sum the credit amounts
        },
      },
      // Project the final fields to include total credits
      {
        $project: {
          totalCredits: 1,
        },
      },
    ]);

    return result.length > 0 && result[0].length > 0 ? result[0].totalCredits : 0;
  }

  /**
   * Change the currency for a store's credits
   * @param storeId The ID of the store
   * @param newCurrency The new currency to set
   * @returns Info about the update
   */
  async changeCurrencyForStore(storeId: string, newCurrency: CURRENCIES) {
    try {
      // First find the store owner
      const store = await this.brokerTransport
        .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
        .toPromise();

      if (!store) {
        return { success: false, message: 'Store not found' };
      }

      const ownerId = getDocId(store.owner);

      // Find the credits for this owner
      const credits = await this.creditsModel.findOne({ owner: ownerId });

      if (!credits) {
        // No credits found, nothing to update
        return { success: true, message: 'No credits found for this user' };
      }

      // Update the currency
      credits.currency = newCurrency;
      await credits.save();

      return {
        success: true,
        message: 'Credits currency updated successfully',
        credits: {
          id: credits.id,
          balance: credits.balance,
          currency: credits.currency,
        },
      };
    } catch (error) {
      console.error('Error changing currency for store credits:', error);
      return { success: false, message: 'Failed to update credits currency' };
    }
  }

  async reverseTransaction(transactionId: string, reason: string) {
    // Find the original transaction
    const originalTransaction = await this.creditsTrasactionsModel.findById(transactionId);

    if (!originalTransaction) {
      throw new BadRequestException('Transaction not found');
    }

    // Check if transaction has already been reversed
    const existingReversal = await this.creditsTrasactionsModel.findOne({
      'meta.reversed_transaction_id': transactionId,
    });

    if (existingReversal) {
      throw new BadRequestException('Transaction has already been reversed');
    }

    // Get the credit wallet
    const credits = await this.creditsModel.findById(originalTransaction.credit_wallet);

    if (!credits) {
      throw new BadRequestException("Couldn't find credit wallet");
    }

    const timestamp = Date.now();

    // Determine the reverse transaction type
    const reverseType =
      originalTransaction.type === TRANSACTION_TYPE.CREDIT ? TRANSACTION_TYPE.DEBIT : TRANSACTION_TYPE.CREDIT;

    // For debit reversals, check if there are sufficient funds
    if (reverseType === TRANSACTION_TYPE.CREDIT || credits.balance >= originalTransaction.amount) {
      // Create the reverse transaction
      const reverseTransaction = await this.chargeWallet(
        credits.owner,
        originalTransaction.amount,
        `Reverse Transaction: ${reason} (Original: ${originalTransaction.narration})`,
        reverseType,
        {
          timestamp,
          reversed_transaction_id: transactionId,
          manual_credit_reference: `reverse-${timestamp}`,
        },
        credits.currency,
      );

      if (!reverseTransaction || (reverseTransaction as any)?.error) {
        throw new BadRequestException('Something went wrong! Please try again');
      }

      return {
        success: true,
        message: 'Transaction reversed successfully',
        original_transaction: {
          id: originalTransaction._id,
          type: originalTransaction.type,
          amount: toCurrency(String(originalTransaction.amount / 100), credits.currency),
          narration: originalTransaction.narration,
        },
        reverse_transaction: {
          type: reverseType,
          amount: toCurrency(String(originalTransaction.amount / 100), credits.currency),
          narration: `Reverse Transaction: ${reason} (Original: ${originalTransaction.narration})`,
        },
        new_balance: toCurrency(String((reverseTransaction as CatlogCreditsDocument).balance / 100), credits.currency),
      };
    } else {
      throw new BadRequestException('Insufficient funds in credit wallet to reverse this debit transaction');
    }
  }
}
