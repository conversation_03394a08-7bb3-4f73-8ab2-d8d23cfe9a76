import { Controller } from '@nestjs/common';
import { CatlogCreditsService } from './credits.service';
import { MessagePattern } from '@nestjs/microservices';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { SkipThrottle } from '@nestjs/throttler';
import { ONBOARDING_STEPS_WITH_REWARDS } from '../user.schema';

@SkipThrottle()
@Controller()
export class CatlogCreditsBroker {
  constructor(private readonly creditsService: CatlogCreditsService) {}

  @MessagePattern(BROKER_PATTERNS.USER.CREDITS.ADD_CREDITS)
  async addCreditsToUser({ user_id, amount, meta, narration }) {
    return this.creditsService.addCreditsToUser(user_id, amount, meta, narration);
  }

  @MessagePattern(BROKER_PATTERNS.USER.CREDITS.DEBIT_CREDITS)
  async debitCreditsFromUser({ user_id, amount }) {
    return await this.creditsService.debitCreditsFromUser(user_id, amount);
  }

  @MessagePattern(BROKER_PATTERNS.USER.CREDITS.REVERSE_DEBIT)
  async reverseDebit({ user_id, amount }) {
    return await this.creditsService.reverseDebit(user_id, amount);
  }

  @MessagePattern(BROKER_PATTERNS.USER.CREDITS.RECORD_DEBIT)
  async recordDebit({ user_id, amount, meta, narration }) {
    return await this.creditsService.recordCreditDebit(user_id, amount, meta, narration);
  }

  @MessagePattern(BROKER_PATTERNS.USER.CREDITS.CHARGE_CREDITS_WALLET)
  async chargeCreditsWallet({ user, amount, narration, type, meta, currency }) {
    return await this.creditsService.chargeWallet(user, amount, narration, type, meta, currency);
  }

  @MessagePattern(BROKER_PATTERNS.USER.CREDITS.GET_CREDITS)
  async getCredits({ user }) {
    return this.creditsService.getUserCredits(user);
  }

  @MessagePattern(BROKER_PATTERNS.USER.CREDITS.GET_CREDITS_FROM_STORE)
  async getCreditsFromStore(data) {
    return this.creditsService.getCreditsFromStore(data?.store);
  }

  @MessagePattern(BROKER_PATTERNS.USER.CREDITS.CREDITS_EARNED_WRAP_DATA)
  async getCreditEarnedWrapData(data: any) {
    return this.creditsService.totalCreditsEarnedWrapData(data?.ownerId, data?.startDate, data?.endDate);
  }

  @MessagePattern(BROKER_PATTERNS.USER.CREDITS.ADD_CREDITS_FOR_ONBOARDING_STEPS)
  async addCreditsForOnboardingSteps(data: { step: ONBOARDING_STEPS_WITH_REWARDS; userId: string }) {
    return this.creditsService.addCreditsForOnboardingSteps(data?.step, data?.userId);
  }
}
