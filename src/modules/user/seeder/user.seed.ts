import { Collection, Connection } from 'mongoose';
import { userMock } from '../../../tools/testdata';
import { ISeed } from '../../../interfaces/seed/seed.interface';

export class UserSeed implements ISeed {
  private readonly collection: Collection;

  constructor(db: Connection) {
    this.collection = db.collection('users');
  }

  async runDevSeed() {
    if (!(await this.collection.findOne({ _id: userMock._id }))) {
      return this.collection.insertOne(userMock);
    }
  }

  runProdSeed(): Promise<any> {
    return Promise.resolve(undefined);
  }
}
