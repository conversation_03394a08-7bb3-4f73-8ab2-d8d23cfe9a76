import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import mongoose, { Document } from 'mongoose';

class ReferredUser {
  @ApiProperty()
  @Prop({ type: <PERSON><PERSON><PERSON> })
  has_claimed: boolean;

  @ApiProperty()
  @Prop({ type: Number })
  times_claimed: number;

  @ApiProperty()
  @Prop({ type: String })
  user: string;

  @ApiProperty()
  @Prop({ type: Date })
  referred_on: Date;
}

export type ReferralsDocument = Referrals & Document;

@Schema({ timestamps: true })
export class Referrals {
  public id: string;
  public updatedAt?: Date;
  public createdAt?: Date;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User', unique: true })
  owner: string;

  @ApiProperty()
  @Prop({ type: String })
  referral_code: string;

  @ApiProperty()
  @Prop({ type: [{ has_claimed: <PERSON><PERSON><PERSON>, user: String, referred_on: Date, times_claimed: Number }] })
  referrals: ReferredUser[];
}

export const ReferralsSchema = SchemaFactory.createForClass(Referrals);
