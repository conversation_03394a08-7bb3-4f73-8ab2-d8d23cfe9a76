import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Referrals, ReferralsDocument } from './referrals.schema';
import mongoose, { FilterQuery, Model, PaginateModel } from 'mongoose';
import { User, UserDocument } from '../user.schema';
import { Store, StoreDocument } from '../../store/store.schema';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { UserService } from '../user.service';
import { PaginatedQueryDto } from '../../../models/dtos/PaginatedDto';
import { ResendRepository } from '../../../repositories/resend.repository';
import dayjs from 'dayjs';

function generateRandomPrepend() {
  const letters = 'abcdefghijklmnopqrstuvwxyz';
  const numbers = '0123456789';

  let r = [' ', ' ', ' '];
  r[0] = numbers[Math.floor(Math.random() * 10)];
  r[1] = numbers[Math.floor(Math.random() * 10)];
  r[2] = numbers[Math.floor(Math.random() * 10)];

  const letterPos = Math.floor(Math.random() * 3);
  r[letterPos] = letters[Math.floor(Math.random() * 25)];

  return r.join('');
}

//this has to be a service method because we need to check if code already exists
const createReferralCode = (name: string) => {
  let processedName = name.split(' ')[0].replace("'", '').toLowerCase();

  processedName += '-' + generateRandomPrepend();

  return processedName;
};

@Injectable()
export class ReferralsService {
  constructor(
    private readonly brokerTransport: BrokerTransportService,
    @InjectModel(Referrals.name)
    private readonly referralsModel: PaginateModel<ReferralsDocument>,
    @InjectModel(User.name)
    private readonly userModel: Model<UserDocument>,
    @Inject(forwardRef(() => UserService))
    private readonly userService: UserService,
    private readonly resend: ResendRepository,
  ) {}

  async create(owner: string, name?: string) {
    if (!name) {
      const userDocument = await this.userModel.findOne({ _id: owner });
      if (!userDocument) return;

      name = userDocument.name;
    }

    const referral = await this.referralsModel.create({
      owner,
      referral_code: createReferralCode(name),
      referrals: [],
    });

    await referral.save();

    return referral;
  }

  async isValidReferral(code: string) {
    const referral = await this.referralsModel.findOne({ referral_code: code.toLowerCase() });

    if (!referral) return { exists: false, user: null, referral: null };

    const user = await this.userModel.findById(referral.owner);

    return {
      exists: true,
      referral: referral.id,
      user: {
        id: user.id,
        name: user.name,
      },
    };
  }

  async userReferred(referral: string, data: { user: string }) {
    const referralData = await this.referralsModel.findByIdAndUpdate(referral, {
      $push: { referrals: { ...data, has_claimed: false, times_claimed: 0, referred_on: new Date() } },
    });

    const user = await this.userModel.findById(referralData.owner);
    const referredUser = await this.userModel.findById(data.user);

    await this.userService.sendPushNotification(
      '',
      {
        title: `You referred someone to Catlog 🎉`,
        message: `${
          referredUser?.name.split(' ')[0]
        } joined Catlog using your code. You'll receive some credits each month they subscribe for 3 months.`,
        path: `/dashboard`,
      },
      false,
      referralData.owner,
    );

    this.resend.sendEmail(BROKER_PATTERNS.MAIL.USER_REFERRED, {
      to: user?.email,
      subject: `${referredUser?.name.split(' ')[0]} joined Catlog with your referral code 🎉`,
      data: {
        name: user.name.split(' ')[0],
        user_name: referredUser?.name.split(' ')[0],
      },
    });

    return true;
  }

  async getReferrals(storeId: string) {
    const store = await this.brokerTransport
      .send<any>(BROKER_PATTERNS.STORE.GET_STORE_LEAN, { filter: { _id: storeId }, select: { owner: 1 } })
      .toPromise();

    if (!store) return {};

    const ref = (await this.referralsModel.findOne({ owner: store?.owner })).toJSON();
    const referrals = [];

    for (let index = 0; index < ref.referrals.length; index++) {
      const referral = ref.referrals[index];
      const user = (await this.userModel.findOne({ _id: referral.user }).select({ name: 1, createdAt: 1 })).toJSON();

      referrals.push({
        hasClaimed: referral.has_claimed,
        user: {
          name: user.name,
          date_joined: (user as any).createdAt,
        },
      });
    }

    const referralsData = {
      ...ref,
      referrals,
    };

    return referralsData;
  }

  async getUserReferrals(userId: string) {
    const ref = (await this.referralsModel.findOne({ owner: userId })).toJSON();
    // const referrals = [];

    // for (let index = 0; index < ref.referrals.length; index++) {
    //   const referral = ref.referrals[index];
    //   const user = (await this.userModel.findOne({ _id: referral.user }).select({ name: 1, createdAt: 1 })).toJSON();

    //   referrals.push({
    //     ...referral,
    //     user: {
    //       name: user.name,
    //       date_joined: (user as any).createdAt,
    //     },
    //   });
    // }

    // const referralsData = {
    //   ...ref,
    //   referrals,
    // };

    return ref;
  }

  async referralUsed(ref: string, userId: string) {
    const referral = await this.referralsModel.findOne({ referral_code: ref });
    if (!referral) return;

    const user = await this.userModel.findOne({ _id: userId });
    if (!user) return;

    referral.referrals.push({
      user: userId,
      has_claimed: false,
      times_claimed: 0,
      referred_on: new Date(),
    });

    referral.save();
  }

  async referralSubscribed(user: string, referredUser: string, timesClaimed: number) {
    const referral = (await this.referralsModel.findOne({ owner: user })).toJSON();
    if (!referral) return;

    const referrals = [...referral.referrals];
    const referredUserIndex = referral.referrals.findIndex((r) => r.user === referredUser);
    const referralData = referrals[referredUserIndex];

    referrals[referredUserIndex] = {
      ...referralData,
      has_claimed: true,
      times_claimed: (referralData.times_claimed ?? 0) + timesClaimed,
    };

    referral.referrals = referrals;
    await this.referralsModel.updateOne({ _id: referral.id }, { referrals });

    return referral;
  }

  async getReferralsLeaderboard() {
    const startDate = new Date('2023-11-01');
    const aggregate = await this.userModel.aggregate([
      {
        $match: {
          $and: [
            { referred_by: { $exists: true } },
            { referred_by: { $ne: null } },
            { createdAt: { $gte: startDate } },
          ],
        },
      },
      {
        $lookup: {
          from: 'subscriptions',
          localField: '_id',
          foreignField: 'owner',
          as: 'subscription',
        },
      },
      {
        $unwind: '$subscription',
      },
      {
        $lookup: {
          from: 'users',
          localField: 'referred_by',
          foreignField: '_id',
          as: 'referred_by',
        },
      },
      {
        $unwind: '$referred_by',
      },
      {
        $group: {
          _id: { id: '$referred_by._id', name: '$referred_by.name' },
          referrals: { $push: { user: '$_id', subscription: '$subscription' } },
        },
      },
      {
        $project: {
          _id: 0,
          name: '$_id.name',
          total_referrals: { $size: '$referrals' },
          total_subscribed_referrals: {
            $size: {
              $filter: {
                input: '$referrals',
                as: 'referral',
                cond: {
                  $and: ['$$referral.subscription.last_payment_reference'],
                },
              },
            },
          },
        },
      },
    ]);

    const leaderboardData = [...aggregate];

    leaderboardData.sort((obj1, obj2) => {
      if (obj1.total_subscribed_referrals !== obj2.total_subscribed_referrals) {
        return obj2.total_subscribed_referrals - obj1.total_subscribed_referrals;
      }
      return obj2.total_referrals - obj1.total_referrals;
    });

    return leaderboardData.map((a, i) => ({
      ...a,
      position: i + 1,
    }));
  }

  async migrateAllReferrals() {
    const users = await this.userModel.find({}, { _id: 1 }).lean();
    const reqs = [];

    for await (const user of users) {
      reqs.push(this.create(user._id.toString()));
    }

    const res = await Promise.all(reqs);

    return {
      users: res,
    };
  }

  async referralsWrapData(ownerId: string, startDate: Date, endDate: Date) {
    startDate = dayjs(startDate).toDate();
    endDate = dayjs(endDate).toDate();
    const result = await this.referralsModel.aggregate([
      // Match the ownerId
      { $match: { owner: new mongoose.Types.ObjectId(ownerId) } },

      // Unwind the referrals array to process each referral individually
      { $unwind: '$referrals' },

      // Match referrals within the date range
      {
        $match: {
          'referrals.referred_on': { $gte: startDate, $lte: endDate },
        },
      },

      // Group the results by the owner to get the count of referrals within the range
      {
        $group: {
          _id: '$owner', // Group by owner
          totalReferrals: { $sum: 1 }, // Increment count for each matching referral
        },
      },
    ]);

    // Return the total referrals count for the given ownerId or 0 if no match is found
    return result.length > 0 ? result[0].totalReferrals : 0;
  }
}
