import { Controller, Get, HttpCode, HttpStatus, Param, Post, Query, Req, UseGuards } from '@nestjs/common';
import { ReferralsService } from './referrals.service';
import { JwtAuthGuard } from '../../../jwt/jwt-auth.guard';
import { InternalApiJWTGuard } from '../../../guards/api.guard';
import { CREDITS } from '../../../utils/constants';
import { ApiExcludeEndpoint, ApiSecurity } from '@nestjs/swagger';
import { IRequest } from '../../../interfaces/request.interface';
import { PaginatedQueryDto } from '../../../models/dtos/PaginatedDto';

@Controller('users/referrals')
export class ReferralsController {
  constructor(private readonly referralsService: ReferralsService) {}

  @Get('')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  async getReferrals(@Req() req: IRequest) {
    const data = await this.referralsService.getReferrals(req.user?.store?.id);

    return {
      message: 'Fetched referrals',
      data,
    };
  }

  @Get('/check-validity')
  async isValidReferral(@Query('code') code: string) {
    const validity = await this.referralsService.isValidReferral(code);

    return {
      message: 'Validation done',
      data: validity,
    };
  }

  @Get('/leaderboard')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  async getReferralsLeaderboard() {
    const data = await this.referralsService.getReferralsLeaderboard();
    return {
      data,
      message: 'Fetched leaderboard data successfully',
    };
  }
  @Get('/rewards')
  @UseGuards(JwtAuthGuard)
  async getReferralRewards() {
    return {
      message: 'Rewards Fetched',
      data: CREDITS.REFERRAL.COMMISION,
    };
  }

  @Post('/migrate')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async migrateReferrals() {
    const res = await this.referralsService.migrateAllReferrals();

    return {
      message: 'Migrated referrals successfully',
      data: res,
    };
  }
}
