import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { AFFILIATE_TYPES } from './affiliates.schema';

export class CreateAffiliateDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({ enum: AFFILIATE_TYPES, default: AFFILIATE_TYPES.PERSON })
  @IsEnum(AFFILIATE_TYPES)
  @IsOptional()
  type?: AFFILIATE_TYPES;
}

export class UpdateAffiliateDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty()
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  phone?: string;

  @ApiProperty({ enum: AFFILIATE_TYPES })
  @IsEnum(AFFILIATE_TYPES)
  @IsOptional()
  type?: AFFILIATE_TYPES;
}
