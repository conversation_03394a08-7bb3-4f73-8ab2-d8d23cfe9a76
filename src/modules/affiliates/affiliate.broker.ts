import { Controller } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { FilterQuery, Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { AffiliatesService } from './affiliates.service';
import { Affiliate, AffiliateDocument } from './affiliates.schema';
import { CreateAffiliateDto } from './affiliates.dto';
import { SkipThrottle } from '@nestjs/throttler';

@SkipThrottle()
@Controller()
export class AffiliatesBroker {
  constructor(
    private readonly affiliateService: AffiliatesService,
    @InjectModel(Affiliate.name)
    private readonly affiliateModel: Model<AffiliateDocument>,
  ) {}

  @MessagePattern(BROKER_PATTERNS.AFFILIATE.CREATE_AFFILIATE)
  async createAffiliate({ data, store_id }: { data: CreateAffiliateDto; store_id: string }) {
    return await this.affiliateService.createAffiliate(data, store_id);
  }

  @MessagePattern(BROKER_PATTERNS.AFFILIATE.GET_AFFILIATE)
  async getAffiliate(filter: FilterQuery<Affiliate>) {
    return await this.affiliateModel.findOne(filter);
  }

  @MessagePattern(BROKER_PATTERNS.AFFILIATE.GET_AFFILIATES)
  async getAffiliates({ filterQuery, paginationQuery }: { filterQuery: FilterQuery<Affiliate>; paginationQuery: any }) {
    return await this.affiliateService.getAffiliates(filterQuery, paginationQuery);
  }

  @MessagePattern(BROKER_PATTERNS.AFFILIATE.UPDATE_ORDERS)
  async updateOrders(data: { affiliate_id: string; count: number }) {
    return await this.affiliateService.updateOrders(data.affiliate_id, data.count);
  }

  @MessagePattern(BROKER_PATTERNS.AFFILIATE.UPDATE_CUSTOMERS)
  async updateCustomers(data: { affiliate_id: string; count: number }) {
    return await this.affiliateService.updateCustomers(data.affiliate_id, data.count);
  }
}
