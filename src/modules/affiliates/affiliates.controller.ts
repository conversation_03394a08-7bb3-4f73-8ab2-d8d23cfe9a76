import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  Req,
  UseGuards,
  Patch,
  Delete,
  Put,
} from '@nestjs/common';
import { ApiOkResponse, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { InternalApiKeyGuard } from '../../guards/internal-api-key.guard';
import { PlanGuard } from '../../guards/plan.guard';
import { IRequest } from '../../interfaces/request.interface';
import { JwtAuthGuard } from '../../jwt/jwt-auth.guard';
import { SCOPES } from '../../utils/permissions.util';
import { PlanPermissions } from '../../decorators/permission.decorator';
import { CreateAffiliateDto, UpdateAffiliateDto } from './affiliates.dto';
import { AffiliatesService } from './affiliates.service';
import { PaginatedQueryDto } from '../../models/dtos/PaginatedDto';
import { Affiliate } from './affiliates.schema';
import { RoleGuard } from '../../guards/role.guard';

@ApiTags('Affiliates')
@Controller('affiliates')
export class AffiliatesController {
  constructor(private readonly affiliatesService: AffiliatesService) {}

  @Get()
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_AFFILIATES)
  @UseGuards(JwtAuthGuard, RoleGuard)
  async getAffiliates(@Query() query: PaginatedQueryDto, @Req() req: IRequest) {
    const filter = query.filter || {};
    filter.store = req.user.store.id;
    const data = await this.affiliatesService.getAffiliates(filter, query);
    return {
      data,
      message: 'Affiliates fetched successfully',
    };
  }

  @Get('analytics')
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_AFFILIATES)
  @UseGuards(JwtAuthGuard, RoleGuard)
  async getAffiliateAnalytics(@Req() req: IRequest) {
    const data = await this.affiliatesService.getAffiliateAnalytics(req.user.store.id);
    return {
      data,
      message: 'Affiliate analytics fetched successfully',
    };
  }

  @Get(':id')
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_AFFILIATES)
  @UseGuards(JwtAuthGuard, RoleGuard)
  async getAffiliate(@Param('id') id: string, @Req() req: IRequest) {
    const data = await this.affiliatesService.getAffiliate(id, req.user.store.id);
    return {
      data,
      message: 'Affiliate fetched successfully',
    };
  }

  @Get(':id/orders')
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_AFFILIATES)
  @UseGuards(JwtAuthGuard, RoleGuard)
  async getAffiliateOrders(@Param('id') id: string, @Req() req: IRequest) {
    const data = await this.affiliatesService.getAffiliateOrders(id, req.user.store.id);
    return {
      data,
      message: 'Affiliate orders fetched successfully',
    };
  }

  @Get(':id/customers')
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_AFFILIATES)
  @UseGuards(JwtAuthGuard, RoleGuard)
  async getAffiliateCustomers(@Param('id') id: string, @Req() req: IRequest) {
    const data = await this.affiliatesService.getAffiliateCustomers(id, req.user.store.id);
    return {
      data,
      message: 'Affiliate customers fetched successfully',
    };
  }

  @Post()
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_AFFILIATES)
  @UseGuards(JwtAuthGuard, RoleGuard)
  async createAffiliate(@Body() createAffiliateDto: CreateAffiliateDto, @Req() req: IRequest) {
    const data = await this.affiliatesService.createAffiliate(createAffiliateDto, req.user.store.id);
    return {
      data,
      message: 'Affiliate created successfully',
    };
  }

  @Put(':id')
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_AFFILIATES)
  @UseGuards(JwtAuthGuard, RoleGuard)
  async updateAffiliate(@Param('id') id: string, @Body() updateAffiliateDto: UpdateAffiliateDto, @Req() req: IRequest) {
    const data = await this.affiliatesService.updateAffiliate(id, updateAffiliateDto, req.user.store.id);
    return {
      data,
      message: 'Affiliate updated successfully',
    };
  }

  @Delete(':id')
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_AFFILIATES)
  @UseGuards(JwtAuthGuard, RoleGuard)
  async deleteAffiliate(@Param('id') id: string, @Req() req: IRequest) {
    const data = await this.affiliatesService.deleteAffiliate(id, req.user.store.id);
    return {
      data,
      message: 'Affiliate deleted successfully',
    };
  }
}
