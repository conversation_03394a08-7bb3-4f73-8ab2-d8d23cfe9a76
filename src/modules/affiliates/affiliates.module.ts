import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { jsonHookMongoosePlugin, setIdMongoosePlugin } from '../../mongoose-plugins';
import { SharedModule } from '../../shared.module';
import mongoosePaginate from 'mongoose-paginate-v2';
import { Affiliate, AffiliateSchema } from './affiliates.schema';
import { AffiliatesController } from './affiliates.controller';
import { AffiliatesService } from './affiliates.service';
import { AffiliatesBroker } from './affiliate.broker';

@Module({
  imports: [
    MongooseModule.forFeatureAsync([
      {
        name: Affiliate.name,
        useFactory: () => {
          AffiliateSchema.plugin(setIdMongoosePlugin());
          AffiliateSchema.plugin(jsonHookMongoosePlugin(['_id', '__v']));
          AffiliateSchema.plugin(mongoosePaginate);
          return AffiliateSchema;
        },
      },
    ]),
    SharedModule,
  ],
  controllers: [AffiliatesController, AffiliatesBroker],
  providers: [AffiliatesService],
})
export class AffiliatesModule {}
