import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Store } from '../store/store.schema';

export type AffiliateDocument = Affiliate & Document;

export enum AFFILIATE_TYPES {
  PERSON = 'PERSON',
  PLATFORM = 'PLATFORM',
}

@Schema({ timestamps: true })
export class Affiliate {
  _id: any;

  @ApiProperty()
  @Prop({ type: String, required: true })
  name: string;

  @ApiProperty()
  @Prop({ type: String, required: false })
  email?: string;

  @ApiProperty()
  @Prop({ type: String, required: false })
  phone?: string;

  @ApiProperty()
  @Prop({ type: String, required: true, unique: true })
  slug: string;

  @ApiProperty()
  @Prop({ type: Number, default: 0 })
  total_orders: number;

  @ApiProperty()
  @Prop({ type: Number, default: 0 })
  total_customers: number;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Store', required: true })
  store: string | Store;

  @ApiProperty()
  @Prop({
    type: String,
    enum: Object.values(AFFILIATE_TYPES),
    default: AFFILIATE_TYPES.PERSON,
  })
  type: AFFILIATE_TYPES = AFFILIATE_TYPES.PERSON;

  @ApiProperty()
  @Prop({ type: Number, default: 0, required: false })
  commission_rate?: number;

  @ApiProperty()
  @Prop({ type: Number, default: 0, required: false })
  total_commission?: number;
}

export const AffiliateSchema = SchemaFactory.createForClass(Affiliate);
