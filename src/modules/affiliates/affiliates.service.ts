import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, PaginateModel } from 'mongoose';
import { BrokerTransportService } from '../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { genChars, mapPaginatedResponse, mapPaginateQuery, sluggify } from '../../utils';
import { PaginatedQueryDto } from '../../models/dtos/PaginatedDto';
import { Affiliate, AffiliateDocument, AFFILIATE_TYPES } from './affiliates.schema';
import { CreateAffiliateDto, UpdateAffiliateDto } from './affiliates.dto';
import { ResendRepository } from '../../repositories/resend.repository';
import { AffiliateInviteProps } from '../../react-email/emails/utils/types';
import { Store } from '../store/store.schema';
import { normalizePhone } from '../../react-email/emails/utils/functions';
@Injectable()
export class AffiliatesService {
  constructor(
    @InjectModel(Affiliate.name)
    private readonly affiliateModel: PaginateModel<AffiliateDocument>,
    protected readonly brokerTransport: BrokerTransportService,
    private readonly resend: ResendRepository,
  ) {}

  async createAffiliate(data: CreateAffiliateDto, store_id: string) {
    const slug = await this.generateAffiliateSlug(data.name);

    const affiliate = await this.affiliateModel.create({
      ...data,
      slug,
      store: store_id,
      total_orders: 0,
      total_customers: 0,
      type: data.type || AFFILIATE_TYPES.PERSON,
    });

    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE_LEAN, {
        filter: { _id: store_id },
      })
      .toPromise();

    if (!store) {
      throw new NotFoundException('Store not found');
    }

    if (data.email) {
      const payload = {
        to: data.email,
        subject: `You are now an affiliate for ${store.name} 🎉`,
        data: {
          name: data.name,
          store_name: store.name,
          preview_text: 'You are now an affiliate',
          invite_link: `https://${store.slug}.catlog.shop?ref=${slug}`,
          store_phone: normalizePhone(store.phone),
        } as AffiliateInviteProps,
      };
      await this.resend.sendEmail(BROKER_PATTERNS.MAIL.AFFILIATE_INVITE, payload);
    }

    return affiliate;
  }

  async getAffiliate(affiliate_id: string, store_id: string) {
    const affiliate = await this.affiliateModel.findById(affiliate_id);

    if (!affiliate) {
      throw new NotFoundException('Affiliate not found');
    }

    if (affiliate.store.toString() !== store_id) {
      throw new NotFoundException('Affiliate not found');
    }

    return affiliate;
  }

  async getAffiliateBySlug(slug: string, store_id: string) {
    const affiliate = await this.affiliateModel.findOne({ slug });

    if (!affiliate) {
      throw new NotFoundException('Affiliate not found');
    }

    if (affiliate.store.toString() !== store_id) {
      throw new NotFoundException('Affiliate not found');
    }

    return affiliate;
  }

  async getAffiliates(filterQuery: FilterQuery<Affiliate>, paginationQuery: PaginatedQueryDto) {
    const result = await this.affiliateModel.paginate(filterQuery, {
      ...mapPaginateQuery(paginationQuery),
    });

    return {
      data: result.docs,
      ...mapPaginatedResponse(result),
    };
  }

  async updateOrders(affiliate_id: string, count: number = 1) {
    const affiliate = await this.affiliateModel.findById(affiliate_id);

    if (!affiliate) {
      throw new NotFoundException('Affiliate not found');
    }

    affiliate.total_orders += count;
    await affiliate.save();

    return affiliate;
  }

  async updateCustomers(affiliate_id: string, count: number = 1) {
    const affiliate = await this.affiliateModel.findById(affiliate_id);

    if (!affiliate) {
      throw new NotFoundException('Affiliate not found');
    }

    affiliate.total_customers += count;
    await affiliate.save();

    return affiliate;
  }

  async getAffiliateAnalytics(store_id: string) {
    const affiliates = await this.affiliateModel.find({ store: store_id });

    const total_affiliates = affiliates.length;
    const total_orders = affiliates.reduce((sum, affiliate) => sum + affiliate.total_orders, 0);
    const total_customers = affiliates.reduce((sum, affiliate) => sum + affiliate.total_customers, 0);

    const top_affiliate =
      affiliates.length > 0
        ? affiliates.reduce((prev, current) => (prev.total_orders > current.total_orders ? prev : current))
        : null;

    return {
      total_affiliates,
      total_orders,
      total_customers,
      top_affiliate: top_affiliate && top_affiliate?.total_orders > 0 ? top_affiliate.name : null,
    };
  }

  async getAffiliateOrders(affiliate_id: string, store_id: string) {
    // First verify the affiliate belongs to the store
    const affiliate = await this.getAffiliate(affiliate_id, store_id);

    // Use the broker to get all orders for this affiliate
    const orders = await this.brokerTransport
      .send(BROKER_PATTERNS.ORDER.GET_ALL, { affiliate: affiliate_id, is_paid: true })
      .toPromise();

    return orders;
  }

  async getAffiliateCustomers(affiliate_id: string, store_id: string) {
    // First verify the affiliate belongs to the store
    const affiliate = await this.getAffiliate(affiliate_id, store_id);

    // Use the broker to get all customers for this affiliate
    const customers = await this.brokerTransport
      .send(BROKER_PATTERNS.ORDER.GET_CUSTOMERS, {
        filter: { 'meta.affiliate': affiliate_id },
        select: {},
      })
      .toPromise();

    return customers;
  }

  async updateAffiliate(id: string, updateAffiliateDto: UpdateAffiliateDto, store_id: string) {
    let affiliate = await this.getAffiliate(id, store_id);

    if (!affiliate) {
      throw new NotFoundException('Affiliate not found');
    }

    affiliate = await this.affiliateModel.findByIdAndUpdate(id, updateAffiliateDto, { new: true });

    return affiliate;
  }

  async deleteAffiliate(id: string, store_id: string) {
    const affiliate = await this.getAffiliate(id, store_id);

    if (!affiliate) {
      throw new NotFoundException('Affiliate not found');
    }

    await affiliate.deleteOne();

    return affiliate;
  }

  private async generateAffiliateSlug(name: string): Promise<string> {
    const baseSlug = sluggify(name);
    let slug = baseSlug;
    let counter = 1;

    while (await this.affiliateModel.findOne({ slug })) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    return slug;
  }
}
