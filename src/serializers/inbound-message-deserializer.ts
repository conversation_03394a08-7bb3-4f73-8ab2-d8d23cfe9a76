import { Logger } from '@nestjs/common';
import {
  ConsumerDeserializer,
  IncomingEvent,
  IncomingRequest,
} from '@nestjs/microservices';

export class InboundMessageDeserializer implements ConsumerDeserializer {
  private readonly logger = new Logger('InboundMessageDeserializer');

  deserialize(
    value: any,
    options?: Record<string, any>,
  ): IncomingRequest | IncomingEvent {
    console.log(value, options);
    this.logger.verbose(
      `<<-- deserializing inbound message:\n${JSON.stringify(
        value,
      )}\n\twith options: ${options}`,
    );
    return value;
  }
}
