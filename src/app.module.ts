import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import { MongodbConfig } from './config/types/mongodb.config';
import {
  BrokerTransportConfiguration,
  JwtConfiguration,
  MongoDbConfiguration,
  RedisConfiguration,
  ThrottleConfiguration,
  HostApiConfiguration,
} from './config/configuration';
import { UserModule } from './modules/user/user.module';
import { SharedModule } from './shared.module';
import { StoreModule } from './modules/store/store.module';
import { ReceiptModule } from './modules/receipts/receipts.module';
import { PlanModule } from './modules/plan/plan.module';
import { ItemModule } from './modules/item/item.module';
import { CartModule } from './modules/cart/cart.module';
import { FileController } from './file/file.controller';
import { AnalyticModule } from './modules/analytic/analytic.module';
import { PaymentModule } from './modules/payment/payment.module';
import { ScheduleModule } from '@nestjs/schedule';
import { OrdersModule } from './modules/orders/orders.module';
import { InvoiceModule } from './modules/invoice/invoice.module';
import { CountryModule } from './modules/country/country.module';
import { WalletModule } from './modules/wallets/wallet.module';
import { WebhookModule } from './modules/webhooks/webhook.module';
import { PaymentMethodModule } from './modules/paymentmethod/paymentmethod.module';
import { SubscriptionModule } from './modules/subscription/subscription.module';
import { WebsocketModule } from './modules/websocket/websocket.module';

import { UtilsModule } from './modules/utils/utils.module';
import { DeliveryModule } from './modules/deliveries/deliveries.module';
import { AdminConfigModule } from './modules/adminconfig/adminconfig.module';
import { WhatsappChatbotModule } from './modules/whatsapp-bot/whatsapp-bot.module';
import { BullModule } from '@nestjs/bull';
import { TwitterBotModule } from './modules/twitter/twitter.module';
import { UrlShortenerModule } from './modules/url-shortener/url-shortener.module';
import { isProduction } from './utils';
import { SharedServicesModule } from './modules/shared/shared-services.module';
import { ThrottlerModule, ThrottlerGuard } from '@nestjs/throttler';
import { APP_GUARD } from '@nestjs/core';
import { CustomThrottlerGuard } from './guards/custom-throttler-guard';
import { ThrottleConfig } from './config/types/throttle.config';
import { IpAddressModule } from './modules/ip-address/ip-address.module';
import { AffiliatesModule } from './modules/affiliates/affiliates.module';
import { SSEModule } from './modules/sse/sse.module';
@Module({
  imports: [
    ConfigModule.forRoot({
      load: [
        MongoDbConfiguration,
        BrokerTransportConfiguration,
        RedisConfiguration,
        JwtConfiguration,
        ThrottleConfiguration,
        HostApiConfiguration,
      ],
    }),
    SharedModule,
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        const mongoDbConfig = configService.get<MongodbConfig>('mongodbConfiguration');
        return {
          useUnifiedTopology: true,
          useNewUrlParser: true,
          useFindAndModify: true,
          useCreateIndex: true,
          uri: mongoDbConfig.uri,
        };
      },
    }),
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (config: ConfigService) => [
        {
          ttl: config.get<ThrottleConfig>('throttleConfiguration').ttl,
          limit: config.get<ThrottleConfig>('throttleConfiguration').limit,
        },
      ],
    }),
    SharedServicesModule,
    StoreModule,
    UserModule,
    PlanModule,
    ItemModule,
    CartModule,
    AnalyticModule,
    PaymentModule,
    OrdersModule,
    CountryModule,
    WalletModule,
    InvoiceModule,
    WebhookModule,
    PaymentMethodModule,
    SubscriptionModule,
    WebsocketModule,
    // TwitterBotModule,
    UrlShortenerModule,
    ReceiptModule,
    UtilsModule,
    DeliveryModule,
    AdminConfigModule,
    WhatsappChatbotModule,
    TwitterBotModule,
    UrlShortenerModule,
    IpAddressModule,
    AffiliatesModule,
    SSEModule,
  ],
  controllers: [AppController, FileController],
  providers: [
    AppService,
    {
      provide: APP_GUARD,
      useClass: CustomThrottlerGuard,
    },
  ],
})
export class AppModule {}
