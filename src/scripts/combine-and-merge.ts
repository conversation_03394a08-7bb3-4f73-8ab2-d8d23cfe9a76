import mongoose from 'mongoose';
import { isObjectId } from '../utils';

(async () => {
  const oldDb = await mongoose.createConnection('');
  const newDb = await mongoose.createConnection('');
  const currentDb = await mongoose.createConnection('');

  const collections = [
    'analytics',
    // 'cards',
    // 'carts',
    // 'customers',
    // 'items',
    // 'orders',
    // 'paymentmethods',
    // 'payments',
    // 'plans',
    // 'stores',
    // 'subscriptions',
    // 'users',
  ];

  for (const collection of collections) {
    const oldCollection = oldDb.collection(collection);
    const newCollection = newDb.collection(collection);
    const currentCollection = currentDb.collection(collection);

    const oldDocuments = await oldCollection.find({}).toArray();

    await currentCollection.insertMany(oldDocuments);

    const newDocuments = await newCollection.find({}).toArray();
    let i = 0;

    console.log('Loading...');
    for (const newDocument of newDocuments) {
      await currentCollection.replaceOne(
        {
          _id: isObjectId(newDocument._id)
            ? mongoose.Types.ObjectId(newDocument._id)
            : newDocument._id,
        },
        newDocument,
        { upsert: true },
      );
      i++;
      // console.log('LOADING... ', i);
    }
    console.log('DONE: ', collection);
  }
})();
