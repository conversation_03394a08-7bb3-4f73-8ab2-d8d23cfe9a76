import mongoose from 'mongoose';

(async () => {
  await mongoose.connect('');
  const { connection } = mongoose;
  const cartsCollection = await connection.useDb('').collection('carts');

  const carts = await cartsCollection
    .aggregate([
      {
        $lookup: {
          from: 'items',
          localField: 'items.item_id',
          foreignField: '_id',
          as: 'items_populated',
        },
      },

      {
        $addFields: {
          'items.populated': '$items_populated',
        },
      },

      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m', date: '$created_at' } },
          items: { $push: '$items' },
          carts: { $sum: 1 },
        },
      },
    ])
    .toArray();

  let totalAmount = 0;
  const totalCartsByMonth = carts
    .map((cart) => {
      const total = cart.items.flat().reduce(
        (accumulator, item) => {
          const resolvedItem = item.populated.find((subItem) => subItem._id.toString() === item.item_id.toString());

          if (!resolvedItem) {
            return accumulator;
          }

          accumulator.price += resolvedItem.price * item.quantity;
          accumulator.items += 1;

          return accumulator;
        },
        { price: 0, items: 0 },
      );
      return {
        month: cart._id,
        total_carts: cart.carts,
        total_price: total.price,
        total_items: total.items,
      };
    })
    .map((cart) => {
      totalAmount += cart.total_price;
      cart.total_price = cart.total_price.toLocaleString();
      return cart;
    });
})();
