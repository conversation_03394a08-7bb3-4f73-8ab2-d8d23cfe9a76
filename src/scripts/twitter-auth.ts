import { EUploadMimeType, TwitterApi } from 'twitter-api-v2';
import readline from 'readline';
/*
api<PERSON><PERSON> sxKVEJKNlyraaqJwRaXTwZIyy
api secret EpHzoX9bMgepfbRzWTeABgXRrmwnTHS2Zx7vgBvNFmnXfQBEJr
bearer AAAAAAAAAAAAAAAAAAAAAFdcgwEAAAAA%2FDrPU0%2BetSyHXlrm2JeSdzeUlRM%3DAcK6LagcCgaXCuxnzC7VvRyKShnfKQUAt9JPXVzIlgcJDSfVSg
access token 940638921872101377-6gMY1A6dzzfnHbXkV5pcloP6EBtpzWd
access token secret 1TjBRQOXNHz1UrtKQX3Y9f7sOH1Bg2WgoihlYXq2gq8Dz
*/

/*
apiKey *************************
apiSecret t9QOiVJhkGQsEvoybulHJTpP03191vkzArIgiFdtQRI60JcDXy
bearer AAAAAAAAAAAAAAAAAAAAAKdwgwEAAAAAuaNqLgTLc3BJy2hquVeRb0bHd3M%3D5nIdrjZ3cbl5thqGJzpgIkbuVXgDnynf2DKO9NAsloglcRwvEf
*/

/*
app key  *************************
app secret wwbxdiOV5asdONSoFULvXxXdzzRe03KhL5QuQQwlY0mZvk36DN

axxess token 940638921872101377-t0MaOfmc6S8SD7urVQm5OknsJ3ivBZQ
access secret e0drFHeEuBsDQLlArIvnYKUT9NWGYjKfEM4xT75UFt9QG
*/
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

async function main() {
  let client = new TwitterApi({
    clientId: 'UGwwTllMVW52cHRQZTRlZ0xvUnk6MTpjaQ',
    clientSecret: 'gBzMrWjouErQD00Md58hNNqLvP3jU48yXHemVVP2V2WnvewyiN',
    // clientId: '*************************',
    // clientSecret: 't9QOiVJhkGQsEvoybulHJTpP03191vkzArIgiFdtQRI60JcDXy'
    // accessToken: '1084194062343770113-Or2FZZf7iEBLfr08jzbgIvroDjA4bm',
    // accessSecret: 'UAbJOOjRuGYiVfcY4CTDxFUspmNSrVZ1yhoQ7IuPSuDof'
  });

  const { url, codeVerifier, state } = client.generateOAuth2AuthLink(
    'https://webhook.site/8b468f4b-1eac-4246-b238-7ef551fa6107',
    {
      scope: ['tweet.write', 'tweet.read', 'users.read', 'offline.access'],
    },
  );

  const answer: string = await new Promise((res) => {
    rl.question('code: ', (answer) => {
      res(answer);
    });
  });

  rl.close();
  const { accessToken: token, client: userClient } = await client.loginWithOAuth2({
    code: answer,
    codeVerifier,
    redirectUri: 'https://webhook.site/8b468f4b-1eac-4246-b238-7ef551fa6107',
  });

  try {
    let id = '';
    const user = new TwitterApi(token);
    try {
      const client = new TwitterApi({
        appKey: '*************************',
        appSecret: 'YgDKm9YAHFtvCWjqB53JzRcVe7ciK0WuiWpXhUY35L9UZPTqG2',
        // clientId: '*************************',
        // clientSecret: 't9QOiVJhkGQsEvoybulHJTpP03191vkzArIgiFdtQRI60JcDXy'
        accessToken: '1084194062343770113-Or2FZZf7iEBLfr08jzbgIvroDjA4bm',
        accessSecret: 'UAbJOOjRuGYiVfcY4CTDxFUspmNSrVZ1yhoQ7IuPSuDof',
      });
      id = await client.v1.uploadMedia('./tweetimg.jpg', { mimeType: EUploadMimeType.Jpeg });
      id = await userClient.v1.uploadMedia('./tweetimg.jpg', { mimeType: EUploadMimeType.Jpeg });
    } catch (err) {
      console.log('UPLOAD', err, err.data);
      return;
    }

    await user.v2.tweet({
      text: 'Sample text',
      media: {
        media_ids: [id],
      },
    });
  } catch (err) {
    console.log('Error', err, err.data);
  }
}

async function main2() {
  try {
    const client = new TwitterApi({
      appKey: '*************************',
      appSecret: 'wwbxdiOV5asdONSoFULvXxXdzzRe03KhL5QuQQwlY0mZvk36DN',

      accessToken: '940638921872101377-t0MaOfmc6S8SD7urVQm5OknsJ3ivBZQ',
      accessSecret: 'e0drFHeEuBsDQLlArIvnYKUT9NWGYjKfEM4xT75UFt9QG',
    });

    const id = await client.v1.uploadMedia('./tweetimg.jpg', { mimeType: EUploadMimeType.Jpeg });
    console.log(id);
  } catch (err) {
    console.log('Error', err, err.data);
  }
}

async function main3() {
  let client = new TwitterApi(
    'AAAAAAAAAAAAAAAAAAAAAFdcgwEAAAAA%2FDrPU0%2BetSyHXlrm2JeSdzeUlRM%3DAcK6LagcCgaXCuxnzC7VvRyKShnfKQUAt9JPXVzIlgcJDSfVSg',
  );

  try {
    const { url } = await client.generateAuthLink('https://webhook.site/8b468f4b-1eac-4246-b238-7ef551fa6107');

    console.log(url);
  } catch (e) {
    console.log(e.data);
  }
}

async function main4() {
  let client = new TwitterApi({
    appKey: '*************************',
    appSecret: 'wwbxdiOV5asdONSoFULvXxXdzzRe03KhL5QuQQwlY0mZvk36DN',

    accessToken: '940638921872101377-t0MaOfmc6S8SD7urVQm5OknsJ3ivBZQ',
    accessSecret: 'e0drFHeEuBsDQLlArIvnYKUT9NWGYjKfEM4xT75UFt9QG',
  });

  try {
    const tweet = await client.v2.tweet({
      text: 'Automated tweet with image',
      media: {
        media_ids: ['1567985933185650688'],
      },
    }); // 1567985933185650688

    console.log(tweet);
  } catch (e) {
    console.log(e.data);
  }
}

async function main5() {
  let client = new TwitterApi({
    appKey: '*************************',
    appSecret: 'YgDKm9YAHFtvCWjqB53JzRcVe7ciK0WuiWpXhUY35L9UZPTqG2',
    // clientId: '*************************',
    // clientSecret: 't9QOiVJhkGQsEvoybulHJTpP03191vkzArIgiFdtQRI60JcDXy'
    // accessToken: '1084194062343770113-Or2FZZf7iEBLfr08jzbgIvroDjA4bm',
    // accessSecret: 'UAbJOOjRuGYiVfcY4CTDxFUspmNSrVZ1yhoQ7IuPSuDof'
  });

  // By default, oauth/authenticate are used for auth links, you can change with linkMode
  // property in second parameter to 'authorize' to use oauth/authorize
  const authLink = await client
    .generateAuthLink('https://webhook.site/8b468f4b-1eac-4246-b238-7ef551fa6107', { linkMode: 'authorize' })
    .catch((e) => console.log('ERROR AUTH', e, e.data));

  console.log(authLink);

  if (!authLink) return;

  const authVerifier: string = await new Promise((res) => {
    rl.question('verifier: ', (answer) => {
      res(answer);
    });
  });

  const authToken: string = await new Promise((res) => {
    rl.question('token: ', (answer) => {
      res(answer);
    });
  });

  client = new TwitterApi({
    appKey: '*************************',
    appSecret: 'YgDKm9YAHFtvCWjqB53JzRcVe7ciK0WuiWpXhUY35L9UZPTqG2',
    // clientId: '*************************',
    // clientSecret: 't9QOiVJhkGQsEvoybulHJTpP03191vkzArIgiFdtQRI60JcDXy'
    accessToken: authToken,
    accessSecret: authLink.oauth_token_secret,
  });

  rl.close();

  console.log(authVerifier, authToken);

  const { client: userClient, accessToken: token, accessSecret: secret } = await client.login(authVerifier);
  console.log(token, secret);
  try {
    let id = '';
    try {
      // const client = new TwitterApi({
      //     appKey: '*************************',
      //     appSecret: 'YgDKm9YAHFtvCWjqB53JzRcVe7ciK0WuiWpXhUY35L9UZPTqG2',
      //     // clientId: '*************************',
      //     // clientSecret: 't9QOiVJhkGQsEvoybulHJTpP03191vkzArIgiFdtQRI60JcDXy'
      //     accessToken: '1084194062343770113-Or2FZZf7iEBLfr08jzbgIvroDjA4bm',
      //     accessSecret: 'UAbJOOjRuGYiVfcY4CTDxFUspmNSrVZ1yhoQ7IuPSuDof'
      // });
      id = await userClient.v1.uploadMedia('./tweetimg.jpg', { mimeType: EUploadMimeType.Jpeg });
      console.log(id);
    } catch (err) {
      console.log('UPLOAD', err, err.data);
      return;
    }

    await userClient.v1.tweet('sample text', {
      media_ids: [id],
    });
  } catch (err) {
    console.log('Error', err, err.data);
  }
}

main5();
