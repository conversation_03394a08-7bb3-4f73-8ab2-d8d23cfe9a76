import dotenv from 'dotenv';
import mongoose from 'mongoose';

import JSON_FILE from '../tools/scale-test-data';

(async () => {
  dotenv.config();
  await mongoose.connect(
    process.env.MONGODB_URI,
    {
      useUnifiedTopology: true,
      useNewUrlParser: true,
      useFindAndModify: true,
      useCreateIndex: true,
    },
    async (err) => {
      if (err) return console.error(err);
    },
  );

  const conn = mongoose.connection;

  if (!conn) return;

  const db = conn;

  const collections = Object.getOwnPropertyNames(JSON_FILE);

  for (const collection of collections) {
    const col = db.collection(collection);
    const objects = JSON_FILE[collection];

    for (const object of objects) {
      try {
        await col.insertOne(object);
      } catch (e) {
        console.error(`Failed adding ${collection} object: ${e.message}`);
      }
    }
  }
})()
  .then(() => process.exit())
  .catch(console.error);
