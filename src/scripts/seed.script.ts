import { UserSeed } from '../modules/user/seeder/user.seed';
import mongoose from 'mongoose';
import { StoreSeed } from '../modules/store/seed/store.seed';
import { ItemSeed } from '../modules/item/seed/item.seed';
import { PlanSeed } from '../modules/plan/seed/plan.seed';
import { SubscriptionSeed } from '../modules/payment/seed/subscription.seed';

export default (async () => {
  await mongoose.connect(process.env.MONGODB_URI);
  const { connection } = mongoose;

  if (process.env.NODE_ENV != 'production') {
    mongoose.set('debug', true);
    const seeders = [UserSeed, StoreSeed, ItemSeed, PlanSeed, SubscriptionSeed];
    await Promise.all(
      seeders
        .map((seed) => new seed(connection))
        .map(async (seed) => await seed.runDevSeed()),
    );
  } else {
    const seeders = [UserSeed, StoreSeed, ItemSeed, PlanSeed, SubscriptionSeed];
    await Promise.all(
      seeders
        .map((seed) => new seed(connection))
        .map(async (seed) => await seed.runProdSeed()),
    );
  }
  setImmediate(() => {
    process.exit(0);
  });
})();
