import dotenv from "dotenv";
import mongoose from "mongoose";

import J<PERSON><PERSON>_FILE from "../tools/setup-data"

(async () => {
    dotenv.config();
    await mongoose.connect(process.env.MONGODB_URI, {
        useUnifiedTopology: true,
        useNewUrlParser: true,
        useFindAndModify: true,
        useCreateIndex: true
    }, async (err) => {
        if (err) return console.error(err);
    });

    const conn = mongoose.connection

    if (!conn) return;

    const db = conn;
    
    const collections = Object.getOwnPropertyNames(JSON_FILE)

    for (const collection of collections) {
        const col = db.collection(collection);
        const objects = JSON_FILE[collection];

        console.log(`Seeding "${collection}"`)
        for (const object of objects) {
            try {
                await col.insertOne(object)
                console.log("Inserted", object)
            } catch (e) {
                console.error(`Failed adding ${collection} object: ${e.message}`)
            }
        }
    }
})().then(() => process.exit()).catch(console.error)