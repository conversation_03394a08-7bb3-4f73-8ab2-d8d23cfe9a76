import mongoose from 'mongoose';

(async () => {
  await mongoose.connect('');
  const { connection } = mongoose;
  const storesCollection = await connection
    .useDb('beta-db')
    .collection('stores');

  const stores = await storesCollection.find({}).toArray();
  await Promise.all(
    stores.map(async (store) => {
      const slugs = (store.slugs || [])
        .map((slug) => (slug ? slug.toLowerCase() : null))
        .filter((slug) => slug)
        .filter((slug, i, slugs) => slugs.indexOf(slug) == i);

      const slug = (store.slug || '').toLowerCase();
      const primary_slug = (store.primary_slug || '').toLowerCase();

      const update = {
        slug,
        slugs,
        primary_slug,
      };

      await storesCollection.findOneAndUpdate(
        {
          _id: mongoose.Types.ObjectId(store._id),
        },
        { $set: update },
      );
      console.log('done');
    }),
  );
  console.log('Finally done');
})();
