import mongoose from 'mongoose';
const trackFieldsHistoryPlugin = (fields: string[]) => {
  return (schema: mongoose.Schema) => {
    ['findOneAndUpdate', 'updateOne', 'update', 'findByIdAndUpdate', 'updateOne'].forEach((method) => {
      schema.pre(method, async function () {
        await saveDiffs(this, fields);
      });
    });
  };
};

const saveDiffs = async (queryObject, fields: string[]) => {
  const results = await queryObject.find(queryObject._conditions);
  for (let result of results) {
    saveDiffHistory(queryObject, result, fields);
  }
};

const saveDiffHistory = (queryObject, currentObject, fields: string[]) => {
  const queryUpdate = queryObject.getUpdate();
  let queryUpdateKeys = Object.keys(queryUpdate);

  for (let key of queryUpdateKeys) {
    if (key.startsWith('$')) {
      queryUpdateKeys = queryUpdateKeys.concat(Object.keys(queryUpdate[key]));
    }
  }

  if (!queryUpdateKeys.some((k) => fields.some((f) => f === k))) return;

  queryObject._update.meta = {
    ...(currentObject.meta ?? {}),
    history: {
      ...(currentObject.meta?.history ?? {}),
      ...pick(currentObject, fields),
    },
  };
};

function pick(obj, keys: any[]) {
  const ret: any = {};
  keys.forEach((key) => {
    if (key in obj) ret[key] = obj[key];
  });
  return ret;
}

export default trackFieldsHistoryPlugin;
