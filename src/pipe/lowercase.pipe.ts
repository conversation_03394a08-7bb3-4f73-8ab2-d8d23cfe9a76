import { Injectable, PipeTransform } from '@nestjs/common';
import { isArray, isObject, isString } from 'class-validator';

@Injectable()
export class LowercasePipe implements PipeTransform {
  private propertiesToConvert: Record<string, any> = {};

  constructor(propertiesToConvert: string[] = []) {
    propertiesToConvert.forEach((prop) => {
      this.propertiesToConvert[prop] = true;
    });
  }

  /**
   * Runs a deep nested check to convert inputs to lowercase
   * @param obj
   * @private
   */
  private convertPropertiesToLowerCase(obj: Record<string, any> | Record<string, any>[]) {
    // return obj;
    if (!isArray(obj) && !isObject(obj)) {
      return obj;
    }

    if (!isArray(obj)) {
      for (const prop in obj) {
        if (
          !this.propertiesToConvert[prop] &&
          !isObject(obj[prop] && !isArray(obj[prop]))
          //|| /^[A-Z]+$/.test(prop) //regex is to ensure that properties that are all capitalized shouldnt be converted to lowercase
        ) {
          continue;
        }
        if (!isObject(obj[prop]) && isString(obj[prop])) {
          obj[prop] = obj[prop].toLowerCase();
          continue;
        }
        this.convertPropertiesToLowerCase(obj[prop]);
      }
      return obj;
    }

    for (let i = 0; i < obj.length; i++) {
      this.convertPropertiesToLowerCase(obj[i]);
    }

    return obj;
  }

  transform(value: any, metadata: any): any {
    return metadata.type === 'body' || metadata.type === 'query' ? this.convertPropertiesToLowerCase(value) : value;
  }
}
