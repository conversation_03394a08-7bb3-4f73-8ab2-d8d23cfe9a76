import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import Request from 'express';

@Injectable()
export class InternalApiKeyGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const [req]: [Request] = context.getArgs();
    const apiKey =
      req.headers['x-internal-api-key'] || req.headers['internal-api-key'];
    return apiKey && apiKey === process.env.INTERNAL_API_KEY;
  }
}
