import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Reflector } from '@nestjs/core';
import { ApiGuardConfig } from '../config/types/api-guard.config';
import { createHmac } from 'crypto';
import { INTERNAL_DASHBOARD_PERMISSIONS_KEY } from '../decorators/permission.decorator';
import { InternalDashboardPermissions, INTERNAL_ROLES, SCOPES } from '../utils/permissions.util';
import { UserDocument } from '../modules/user/user.schema';
import { JwtService } from '@nestjs/jwt';
import { BrokerTransportService } from '../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../enums/broker.enum';

/**
 * Helper class to handle an internal dashboard user
 */
class InternalDashboardUser {
  id: string;
  role: string;
  permissions: string[];

  constructor(user: UserDocument) {
    this.id = user._id?.toString() || user.id;
    this.role = user.internal_dashboard_role;
    this.permissions = InternalDashboardPermissions[this.role] || [];
  }

  hasPermission(permission: string): boolean {
    return this.permissions.includes(permission);
  }

  hasPermissions(permissions: string[]): boolean {
    return permissions.every((permission) => this.hasPermission(permission));
  }
}

@Injectable()
export class InternalApiJWTGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private config: ConfigService,
    private brokerTransport: BrokerTransportService,
    private jwtService: JwtService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const { user } = request;
    const apiGuardCfg = this.config.get<ApiGuardConfig>('apiGuardConfig');

    // Get the requested path for endpoint identification
    const req = context.switchToHttp().getRequest();
    const path = req.route?.path;

    // Check for required permissions
    const requiredPermissions = this.reflector.getAllAndOverride<string[]>(INTERNAL_DASHBOARD_PERMISSIONS_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // Legacy check - for the main admin account using apiGuardConfig
    const key = apiGuardCfg.username + ':' + apiGuardCfg.password;
    const hash = createHmac('sha256', key).update(apiGuardCfg.rn).digest('hex');

    // If the user is the main admin user from config, they have all permissions
    if (hash === user?.id) {
      // Ensure user has SUPER_ADMIN role and all permissions
      const allInternalPermissions = Object.values(SCOPES.INTERNAL_DASHBOARD);
      request.user = {
        ...user,
        internalRole: INTERNAL_ROLES.SUPER_ADMIN,
        permissions: allInternalPermissions,
      };

      return true;
    }

    // Special case for test endpoints - require a valid token
    if (path === '/users/internal/test' || path === '/users/internal/roles') {
      // Just require any valid token
      if (user && user.type === 'internal') {
        return true;
      }
    }

    // Special case for admin test endpoint - require admin permissions
    if (path === '/users/internal/admin-test') {
      if (user && user.permissions && Array.isArray(user.permissions)) {
        const hasPermissions = user.permissions.some(
          (perm) => perm === SCOPES.INTERNAL_DASHBOARD.MANAGE_DASHBOARD_USERS,
        );
        if (hasPermissions) {
          return true;
        }
      }
    }

    // For users with internal dashboard roles
    if (user && user.internalRole) {
      // No specific permissions required
      if (!requiredPermissions || requiredPermissions.length === 0) {
        return true;
      }

      // Check if user has the required permissions
      if (user.permissions && Array.isArray(user.permissions)) {
        const hasPermissions = user.permissions.some((perm) => requiredPermissions.includes(perm));
        return hasPermissions;
      }
    }

    // For users identified by userId - resolve their permissions using broker transport
    if (user && user.userId) {
      try {
        // The UserBroker.getUser expects a direct mongoose.FilterQuery object
        const userObj = await this.brokerTransport
          .send(BROKER_PATTERNS.USER.GET_USER, { _id: user.userId })
          .toPromise();

        if (!userObj) {
          throw new UnauthorizedException('User not found or invalid');
        }

        const dbUser = userObj as UserDocument;

        if (!dbUser.internal_dashboard_role) {
          throw new UnauthorizedException('User has no dashboard role');
        }

        const dashboardUser = new InternalDashboardUser(dbUser);

        // No specific permissions required
        if (!requiredPermissions || requiredPermissions.length === 0) {
          // Attach user info for subsequent requests
          request.user = {
            id: dashboardUser.id,
            internalRole: dashboardUser.role,
            permissions: dashboardUser.permissions,
          };

          return true;
        }

        // Check for required permissions
        if (dashboardUser.hasPermissions(requiredPermissions)) {
          // Attach user info for subsequent requests
          request.user = {
            id: dashboardUser.id,
            internalRole: dashboardUser.role,
            permissions: dashboardUser.permissions,
          };

          return true;
        }

        throw new UnauthorizedException('Insufficient permissions');
      } catch (error) {
        if (error instanceof UnauthorizedException) {
          throw error;
        }
        throw new UnauthorizedException('Error validating user permissions');
      }
    }

    // No valid authentication or permissions required
    return false;
  }
}
