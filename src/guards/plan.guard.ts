import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Permission, PlanPermissions } from '../utils/permissions.util';
import { PLAN_PERMISSIONS_KEY } from '../decorators/permission.decorator';

@Injectable()
export class PlanGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredPermissions = this.reflector.getAllAndOverride<Permission[]>(PLAN_PERMISSIONS_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    if (!requiredPermissions) return true;
    const { user } = context.switchToHttp().getRequest();
    const planType = user.store.subscription?.plan?.type;
    const planPermissions = PlanPermissions[planType] as Permission[];

    return requiredPermissions.every((r) => planPermissions?.includes(r));
  }
}
