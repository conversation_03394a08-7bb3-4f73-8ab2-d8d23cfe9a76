import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { CountryPermissons, Permission, PlanPermissions } from '../utils/permissions.util';
import { COUNTRY_PERMISSIONS_KEY, PLAN_PERMISSIONS_KEY } from '../decorators/permission.decorator';


@Injectable()
export class CountryGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredPermissions = this.reflector.getAllAndOverride<Permission[]>(
      COUNTRY_PERMISSIONS_KEY,
      [context.getHandler(), context.getClass()],
    );
    if (!requiredPermissions) return true;
    const { user } = context.switchToHttp().getRequest();
    const countryPermissions = CountryPermissons[user.store.country] as Permission[];

    return requiredPermissions.every((r) => countryPermissions?.includes(r));
  }
}
