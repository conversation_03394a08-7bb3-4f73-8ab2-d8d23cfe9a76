import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import Axios, { AxiosInstance } from 'axios';
import { WhatsappConfig } from '../config/types/whatsapp.config';
import { BotContext } from '../interfaces/whatsapp.interface';
import { removeNullUndefinedFromObject } from '../modules/whatsapp-bot/utils/functions.utils';
import { sluggify } from '../utils';
import { registerErrors } from '../utils/errors.util';
import { DELIVERY_INFO_FLOW_SECRET, FLOW_MESSAGE_VERSION } from '../utils/wabot-constants';

export enum WHATSAPP_TEMPLATES {
  VERIFICATION_TOKEN = 'catlog_verification',
  SUBSCRIPTION_EXPIRY = 'subscription_expiry',
  FREE_TRIAL_EXPIRY = 'free_trial_expired',
  AUTO_DELIVERY_FAILED = 'auto_delivery_failed',
  AUTO_DELIVERY_BOOKED = 'auto_delivery_booked',

  //BOT TEMPLATES
  ORDER_CONFIRMATION = 'confirm_payment_request',
  CONFIRM_DELIVERY_ORDER_WITH_FLOW = 'confirm_delivery_order_with_flow',
  CONFIRM_PICKUP_ORDER_WITH_FLOW = 'confirm_pickup_order_with_flow',
  AUTO_CANCELLATION_MESSAGE = 'order_cancelled_seller_notification',
  AUTO_SIGNUP_CHECKIN_MESSAGE = 'welcome_to_catlog',
  AUTO_DELIVERY_CANCELLATION_MESSAGE = 'auto_delivery_cancelled',
  AUTO_CUSTOMER_CHECK_IN = 'auto_customer_check_in',
  STORE_OPENED_REMINDER = 'store_opened_reminder',
}

export enum MESSAGE_TYPES {
  INTERACTIVE = 'interactive',
  TEXT = 'text',
  TEMPLATE = 'template',
  CONTACTS = 'contacts',
  LOCATION = 'location',
  BUTTON = 'button',
  IMAGE = 'image',
  DOCUMENT = 'document',
}
export enum HEADER_TYPES {
  IMAGE = 'image',
  TEXT = 'text',
  VIDEO = 'video',
  DOCUMENT = 'document',
}
export interface InteractiveMessageParams {
  flow?: {
    flow_token?: string;
    flow_id: string;
    flow_cta: string;
    flow_action: 'navigate' | 'data_exchange';
    flow_message_version?: string;
    flow_action_payload: {
      screen: string;
      data: any;
    };
  };
  type: 'list' | 'button' | 'cta_url' | 'flow' | 'location_request_message';
  header?: string;
  header_type?: HEADER_TYPES;
  header_data?: any;
  footer?: string;
  body?: string;
  buttons?: string[];
  list?: {
    list_cta: string;
    sections: {
      title: string;
      rows: {
        title: string;
        description: string;
        id: string;
      }[];
    }[];
  };
  cta?: {
    display_text?: string;
    url?: string;
  };
}
export interface ContactMessageParams {
  url?: string;
  phones: [
    {
      phone: string;
      wa_id: string;
    },
  ];
  name: string;
}

export interface MediaMessageParams {
  link: string;
  filename?: string;
  caption?: string;
}

export interface LocationMessageParams {
  location: string;
  latitude: string;
  name: string;
  address: string;
}

@Injectable()
export class WhatsappBusinessApiRepository {
  private config: WhatsappConfig;
  private axios: AxiosInstance;

  constructor(configService: ConfigService, private readonly jwtService: JwtService) {
    this.config = configService.get<WhatsappConfig>('whatsappConfiguration');

    this.axios = Axios.create({
      baseURL: 'https://graph.facebook.com/v17.0',
      headers: {
        Authorization: `Bearer ${this.config?.token}`,
        'Content-Type': 'application/json',
      },
    });
  }

  private async send(to: string, from: string, data: { type: MESSAGE_TYPES } & { [key in MESSAGE_TYPES]?: any }) {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        to: to,
        ...data,
      };

      const res = await this.axios.post(`/${from}/messages`, payload);
      return { data: res.data?.messages };
    } catch (e) {
      const error = e?.response ?? e;
      console.log(error);
      registerErrors(error);
      return { error: error };
    }
  }

  async sendMediaMessage(
    ctx: BotContext,
    params: MediaMessageParams,
    type: MESSAGE_TYPES.DOCUMENT | MESSAGE_TYPES.IMAGE,
  ) {
    return this.send(ctx.destinationPhone, ctx.botPhoneId, {
      type,
      [type]: {
        ...params,
      },
    });
  }

  async sendTemplatedMessage(data: {
    from: string;
    to: string;
    templateName: string;
    parameters?: string[];
    buttons?: { sub_type?: string; parameters: string[] }[];
    language?: string;
    flowActionData?: any;
    headers?: { type: HEADER_TYPES; data: any }[];
  }) {
    const {
      from,
      to,
      templateName,
      parameters = [],
      buttons = [],
      language = 'en',
      flowActionData,
      headers = [],
    } = data;

    return await this.send(to, from, {
      type: MESSAGE_TYPES.TEMPLATE,
      template: {
        name: templateName,
        language: {
          code: language,
        },
        components: [
          headers.length > 0
            ? { type: 'header', parameters: headers.map((h) => ({ type: h.type, [h.type]: h.data })) }
            : null,
          parameters?.length > 0
            ? {
                type: 'body',
                parameters: parameters.map((p) => ({
                  type: 'text',
                  text: p,
                })),
              }
            : null,
          ...buttons?.map((b, i) => ({
            type: 'button',
            sub_type: b?.sub_type ?? 'url',
            index: i,
            parameters: b?.parameters.map((p) =>
              flowActionData ? { type: 'action', action: flowActionData } : { type: 'text', text: p },
            ),
          })),
        ].filter((v) => !!v),
      },
    });
  }

  async sendTextMessage(ctx: BotContext, message: string) {
    const urlRegex = /(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/i;
    const hasLink = urlRegex.test(message);

    return this.send(ctx.destinationPhone, ctx.botPhoneId, {
      type: MESSAGE_TYPES.TEXT,
      text: {
        body: message,
        preview_url: hasLink,
      },
    });
  }

  async sendContactMessage(ctx: BotContext, params: ContactMessageParams) {
    return this.send(ctx.destinationPhone, ctx.botPhoneId, {
      type: MESSAGE_TYPES.CONTACTS,
      contacts: [
        {
          urls: [
            {
              url: params.url,
              type: 'WORK',
            },
          ],
          phones: JSON.stringify(params.phones.map((p) => ({ ...p, type: 'WORK' }))),
          name: {
            formatted_name: params.name,
            first_name: params.name,
          },
        },
      ],
    });
  }

  async sendLocationMessage(ctx: BotContext, params: LocationMessageParams) {
    return this.send(ctx.destinationPhone, ctx.botPhoneId, {
      type: MESSAGE_TYPES.LOCATION,
      location: {
        ...params,
      },
    });
  }

  async sendInteractiveMessage(ctx: BotContext, params: InteractiveMessageParams) {
    const { type, header, header_type, header_data, footer, body, buttons, list, cta, flow } = params;
    let action = {};

    switch (type) {
      case 'list':
        action = {
          button: list?.list_cta ?? '',
          sections: (list?.sections ?? []).map((s) => ({
            title: s.title,
            rows: s.rows.map((r) => ({
              title: r.title,
              description: r.description,
              id: r.id,
            })),
          })),
        };
        break;
      case 'button':
        action = {
          buttons: (buttons ?? []).map((b) => ({
            type: 'reply',
            reply: {
              id: sluggify(b),
              title: b,
            },
          })),
        };
        break;
      case 'cta_url':
        action = {
          name: 'cta_url',
          parameters: {
            display_text: cta?.display_text,
            url: cta?.url,
          },
        };
        break;
      case 'flow':
        flow.flow_token =
          flow.flow_token ?? this.jwtService.sign({ FLOW_SECRET: DELIVERY_INFO_FLOW_SECRET }, { expiresIn: '1d' });
        action = {
          name: 'flow',
          parameters: {
            flow_message_version: flow.flow_message_version ?? FLOW_MESSAGE_VERSION,
            ...flow,
          },
        };
        break;
      case 'location_request_message':
        action = {
          name: 'send_location',
        };
        break;
    }

    return this.send(ctx.destinationPhone, ctx.botPhoneId, {
      type: MESSAGE_TYPES.INTERACTIVE,
      interactive: removeNullUndefinedFromObject({
        //WA sometimes misbehaves when header or footer is empty string
        type,
        header: header
          ? {
              type: header_type ?? 'text',
              text: header ?? '',
              [header_type]: header_data,
            }
          : undefined,
        body: body
          ? {
              text: body,
            }
          : undefined,
        footer: footer
          ? {
              text: footer,
            }
          : undefined,
        action: action,
      }),
    });
  }
}
