import { Injectable, Logger } from '@nestjs/common';
import { StripeConfig } from '../config/types/stripe.config';
import { ConfigService } from '@nestjs/config';
import { Errorable } from '../utils/types';
import Axios, { AxiosInstance } from 'axios';

export interface StripeCheckoutSession {
  id: string; // Unique identifier for the session
  object: 'checkout.session'; // Object type
  customer?: string; // Customer ID
  payment_status: 'paid' | 'unpaid' | 'no_payment_required';
  status: 'open' | 'complete' | 'expired';
  amount_total?: number; // Total amount in cents
  currency?: string; // Currency (e.g., 'usd')
  payment_intent?: string; // Payment Intent ID
  mode?: 'payment' | 'subscription' | 'setup'; // Checkout mode
  success_url: string; // URL to redirect on success
  cancel_url: string; // URL to redirect on cancel
  url?: string; // Hosted Checkout session URL (optional)
}

interface BalanceTransaction {
  id: string; // Unique identifier for the transaction
  object: string; // Always "balance_transaction"
  amount: number; // Gross amount in the smallest currency unit (e.g., cents for USD)
  currency: string; // Currency code (e.g., "usd")
  description: string | null; // Description of the transaction
  fee: number; // Total fees in the smallest currency unit
  fee_details: Array<{
    amount: number; // Fee amount in the smallest currency unit
    application: string | null; // ID of the application that collected the fee (if any)
    currency: string; // Currency of the fee
    description: string; // Description of the fee
    type: string; // Type of fee (e.g., "stripe_fee", "application_fee")
  }>;
  net: number; // Net amount (gross - fees) in the smallest currency unit
  created: number; // Timestamp when the transaction was created
  available_on: number; // Timestamp when the funds are available for use
  livemode: boolean; // Whether the transaction occurred in live mode
  source: string | null; // ID of the source object (e.g., charge, refund)
  status: string; // Status of the transaction (e.g., "pending", "available")
  type: string; // Type of transaction (e.g., "charge", "refund", "payout", "adjustment")
  exchange_rate: number | null; // Exchange rate used if currency conversion occurred
}

export interface StripeSuccessfulChargeData {
  id: string;
  amount: number;
  reference: string;
  fee: number;
  settled: number;
}

export interface StripePaymentInitiationData {
  // totalAmount: number;
  fee: number;
  amount: number;
  reference: string;
  description: string;
}

@Injectable()
export class StripeRepository {
  private readonly config: StripeConfig;
  private readonly axios: AxiosInstance;

  constructor(config: ConfigService, private readonly logger: Logger) {
    this.config = config.get<StripeConfig>('stripeConfiguration');
    this.axios = Axios.create({
      baseURL: 'https://api.stripe.com/v1',
      headers: {
        Authorization: `Bearer ${this.config.secretKey}`,
      },
    });
    // this.stripe = new Stripe(this.stripeConfig.secretKey);
  }

  async createCheckoutSession(payload: StripePaymentInitiationData): Errorable<StripeCheckoutSession> {
    console.log('Creating checkout session:', payload);
    try {
      const response = await this.axios.post(
        '/checkout/sessions',
        new URLSearchParams({
          'line_items[0][price_data][currency]': 'usd',
          'line_items[0][price_data][product_data][name]': payload.description,
          'line_items[0][price_data][unit_amount]': payload.amount.toString(),
          'line_items[0][quantity]': '1',
          'line_items[1][price_data][currency]': 'usd',
          'line_items[1][price_data][product_data][name]': 'Payment processing fee',
          'line_items[1][price_data][unit_amount]': payload.fee.toString(),
          'line_items[1][quantity]': '1',
          mode: 'payment',
          success_url: process.env.CATLOG_WWW + '/payment-successful',
          'payment_intent_data[metadata][payment_reference]': payload.reference,
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      );

      return { data: response.data };
    } catch (error) {
      // console.log('STRIPE INITIATION ERROR:', error);
      this.logger.error('STRIPE INITIATION ERROR:', error.response.data);
      return { error: error };
    }
  }

  async fetchBalanceTransaction(balanceTransactionId): Errorable<BalanceTransaction> {
    try {
      const response = await this.axios.get(`/balance_transactions/${balanceTransactionId}`);

      console.log('Balance Transaction Details:', response.data);
      return { data: response.data };
    } catch (error) {
      // console.error('Error fetching balance transaction:', error.response?.data || error.message);
      console.error('Error fetching balance transaction:', error.response?.data || error.message);
      this.logger.error('STRIPE INITIATION ERROR:', error.response?.data || error.message);
      return { error };
    }
  }
}
