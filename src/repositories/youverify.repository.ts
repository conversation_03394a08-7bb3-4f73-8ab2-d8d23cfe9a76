import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Axios, { AxiosInstance } from 'axios';
import { YouVerifyApiConfig } from '../config/types/youverify.config';
import { Errorable } from '../utils/types';

const YV_MERCHANT_ID = '471335';

export interface YouVerifyPassportResponse {
  id: string;
  parentId?: any;
  status: string;
  reason?: any;
  dataValidation: boolean;
  selfieValidation: boolean;
  firstName: string;
  middleName: string;
  lastName: string;
  expiredDate: Date;
  notifyWhenIdExpire: boolean;
  image: string;
  signature?: any;
  issuedAt: string;
  issuedDate: Date;
  mobile?: any;
  dateOfBirth: string;
  isConsent: boolean;
  idNumber: string;
  businessId: string;
  type: string;
  gender: string;
  requestedAt: Date;
  requestedById: string;
  country: string;
  createdAt: Date;
  lastModifiedAt: Date;
}

export interface YouVerifyCompareImagesResponse {
  id: string;
  parentId?: any;
  status: string;
  reason: string;
  selfieValidation: boolean;
  isConsent: boolean;
  idNumber: string;
  businessId: string;
  type: string;
  requestedAt: Date;
  requestedById: string;
  createdAt: Date;
  lastModifiedAt: Date;
  imageComparison: {
    confidenceLevel: number;
    threshold: number;
    match: boolean;
    image1: string;
    image2: string;
  };
}

export interface YouVerifyVNINResponse {
  id: string;
  parentId?: any;
  status: string;
  reason?: any;
  dataValidation: boolean;
  selfieValidation: boolean;
  firstName: string;
  middleName: string;
  lastName: string;
  image: string;
  mobile: string;
  mobileIntFormat: string;
  enterpriseCode: number;
  vNINUserId: string;
  dateOfBirth: string;
  vNIN: string;
  isConsent: boolean;
  idNumber: string;
  businessId: string;
  type: string;
  allValidationPassed: boolean;
  gender: string;
  requestedAt: Date;
  requestedById: string;
  country: string;
  createdAt: Date;
  lastModifiedAt: Date;
}

export interface YouVerifyResponse<T> {
  success: boolean;
  statusCode: number;
  message: string;
  data: T;
  links: any[];
}

@Injectable()
export class YouVerifyRepository {
  private config: YouVerifyApiConfig;
  private axios: AxiosInstance;
  constructor(configService: ConfigService) {
    this.config = configService.get<YouVerifyApiConfig>('youVerifyConfiguration');
    this.axios = Axios.create({
      baseURL: this.config.apiUrl,
      headers: {
        token: this.config.apiKey,
      },
    });
  }

  async compareImages(link1: string, link2: string) {
    try {
      const { data } = await this.axios.post<YouVerifyResponse<YouVerifyCompareImagesResponse>>(
        '/v2/api/identity/compare-image',
        {
          image1: link1,
          image2: link2,
          isSubjectConsent: true,
        },
      );

      if (!data.success) {
        return { error: data.message };
      }

      return { data: data.data };
    } catch (e) {
      console.log('COMPARING ID IMAGES ERROR');
      console.log(e);
      return { error: e };
    }
  }

  async verifyLicense(documentNumber: string, imageUrl: string) {
    try {
      const { data } = await this.axios.post<YouVerifyResponse<YouVerifyPassportResponse>>(
        '/v2/api/identity/ng/drivers-license',
        {
          id: documentNumber,
          validations: {
            selfie: {
              image: imageUrl,
            },
          },
          isSubjectConsent: true,
        },
      );

      if (!data.success) {
        return { error: data.message };
      }

      return { data: data.data };
    } catch (e) {
      return { error: e.error.response };
    }
  }

  async verifyPassport(documentNumber: string, lastName: string, imageUrl: string) {
    try {
      const { data } = await this.axios.post<YouVerifyResponse<YouVerifyPassportResponse>>(
        '/v2/api/identity/ng/passport',
        {
          id: documentNumber,
          lastName,
          validations: {
            selfie: {
              image: imageUrl,
            },
          },
          isSubjectConsent: true,
        },
      );

      if (!data.success) {
        return { error: data.message };
      }

      return { data: data.data };
    } catch (e) {
      return { error: e.response };
    }
  }

  async verifyVnin(vnin: string, image?: string): Errorable<YouVerifyVNINResponse> {
    try {
      const { data } = await this.axios.post<YouVerifyResponse<YouVerifyVNINResponse>>('/v2/api/identity/ng/vnin', {
        id: vnin,
        isSubjectConsent: true,
        ...(image ? { validations: { selfie: { image: 'data:image/jpeg;base64,' + image } } } : {}),
      });

      if (!data.success) {
        return { error: JSON.stringify(data) };
      }

      data.data.image = data.data.image.split('data:image/jpeg;base64,')[1];

      return { data: data.data };
    } catch (e) {
      return { error: e.response };
    }
  }
}
