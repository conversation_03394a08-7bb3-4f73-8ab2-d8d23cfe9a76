import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Axios, { AxiosInstance } from 'axios';
import { registerErrors } from '../utils/errors.util';
import { Errorable } from '../utils/types';
import { extractErrorDetails } from '../utils/axios-errors';
import { CacheService } from '../modules/shared/cache/cache.service';
import { ShaqExpressConfig } from '../config/types/shaq-express.config';

/**
 * /trips/pricing - POST
 * The request body for "Check Available Pricing"
 */
export interface ShaqPricingRequest {
  package_type: string; // required
  delivery_notes?: string; // optional
  pickup_phone_number: string; // required
  pickup_lng: string; // required
  pickup_lat: string; // required
  reference: string; // required (your unique identifier)
  amount_paid: string; // required
  drop_offs: {
    lng: string;
    lat: string;
    phone: string;
  }[];
}

/**
 * /trips/pricing - POST
 * The response body for "Check Available Pricing"
 */
export interface ShaqPricingResponse {
  message: string; // e.g. "Trip created"
  data: {
    tracking_number: string;
    client_reference: string; // same as `reference` passed in
    status: string | null; // from the docs, it might be null initially
    prices: {
      id: number;
      delivery_type: string; // e.g. "Normal", "Express"
      vehicle_type: string; // e.g. "Motor", "Car"
      charge: number;
      is_available: boolean;
      expiration: string; // e.g. ISO string: "2025-01-13T15:52:48.000000Z"
    }[];
  };
}

/**
 * /trips/book - POST
 * The request body for "Book A Trip"
 */
export interface ShaqBookTripRequest {
  tracking_number: string; // must be from the pricing result
  price_id: number; // must be from one of the "prices" array above
}

/**
 * /trips/book - POST
 * The response body for "Book A Trip"
 */
export interface ShaqBookTripResponse {
  message: string;
  data: {
    tracking_number: string;
    client_reference: string;
    tracking_url: string;
    amount: number;
    status: string;
  };
}

/**
 * /trips/cancel - POST
 * The request body for "Cancel A Trip"
 */
export interface ShaqCancelTripRequest {
  tracking_number: string;
}

/**
 * /trips/cancel - POST
 * The response body for "Cancel A Trip"
 */
export interface ShaqCancelTripResponse {
  message: string;
  data: {
    tracking_number: string;
    client_reference: string;
    amount: number;
    status: string;
  };
}

/**
 * /trips/status - POST
 * The request body for "Check Trip Status"
 */
export interface ShaqTripStatusRequest {
  tracking_number: string;
}

/**
 * /trips/status - POST
 * The response body for "Check Trip Status"
 */
export interface ShaqTripStatusResponse {
  message: string;
  data: {
    tracking_number: string;
    client_reference: string;
    amount: number;
    status: string;
  };
}

/**
 * Repository for interacting with ShaQ Express API
 */
@Injectable()
export class ShaqExpressRepository {
  private readonly axios: AxiosInstance;
  private readonly config: ShaqExpressConfig;

  constructor(private readonly configService: ConfigService, private readonly logger: Logger) {
    // Adjust to however you load your ShaQ config
    this.config = this.configService.get<ShaqExpressConfig>('shaqExpressConfiguration');

    // Create a custom Axios instance
    this.axios = Axios.create({
      baseURL: this.config.api_url,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this.config.api_token}`,
      },
    });
  }

  /**
   * Check available pricing for a trip
   * POST /trips/pricing
   */
  async checkPricing(payload: ShaqPricingRequest): Promise<Errorable<ShaqPricingResponse>> {
    try {
      const res = await this.axios.post<ShaqPricingResponse>('/trips/pricing', payload);
      return { data: res.data, error: null };
    } catch (err) {
      const error = extractErrorDetails(err);
      this.logger.error(error);
      registerErrors(error);
      return {
        error:
          error?.details?.data?.description ?? error?.details?.message ?? error?.message ?? 'Something went wrong!',
      };
    }
  }

  /**
   * Book a trip
   * POST /trips/book
   */
  async bookTrip(payload: ShaqBookTripRequest): Promise<Errorable<ShaqBookTripResponse>> {
    try {
      const res = await this.axios.post<ShaqBookTripResponse>('/trips/book', payload);

      if (res.status === 205) {
        //handle rate expired error
        return {
          error: 'Rate has expired, please contact support',
        };
      }

      return { data: res.data, error: null };
    } catch (err) {
      const error = extractErrorDetails(err);
      this.logger.error(error);
      registerErrors(error);
      return {
        error:
          error?.details?.data?.description ?? error?.details?.message ?? error?.message ?? 'Something went wrong!',
      };
    }
  }

  /**
   * Cancel a trip
   * POST /trips/cancel
   */
  async cancelTrip(payload: ShaqCancelTripRequest): Promise<Errorable<ShaqCancelTripResponse>> {
    try {
      const res = await this.axios.post<ShaqCancelTripResponse>('/trips/cancel', payload);
      return { data: res.data, error: null };
    } catch (err) {
      const error = extractErrorDetails(err);
      this.logger.error(error);
      registerErrors(error);
      return {
        error:
          error?.details?.data?.description ?? error?.details?.message ?? error?.message ?? 'Something went wrong!',
      };
    }
  }

  /**
   * Check the status of a trip
   * POST /trips/status
   */
  async checkTripStatus(payload: ShaqTripStatusRequest): Promise<Errorable<ShaqTripStatusResponse>> {
    try {
      const res = await this.axios.post<ShaqTripStatusResponse>('/trips/status', payload);
      return { data: res.data, error: null };
    } catch (err) {
      const error = extractErrorDetails(err);
      this.logger.error(error);
      registerErrors(error);
      return {
        error:
          error?.details?.data?.description ?? error?.details?.message ?? error?.message ?? 'Something went wrong!',
      };
    }
  }
}
