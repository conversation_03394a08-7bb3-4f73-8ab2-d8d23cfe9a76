import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { Errorable } from '../utils/types';
import { HostApiConfig } from '../config/types/host-api.config';

/**
 * Repository for interacting with the host-side API
 * that provides server functionality outside the Docker container
 */
@Injectable()
export class HostApiRepository {
  private readonly axios: AxiosInstance;
  private readonly baseUrl: string;
  private readonly apiKey: string;

  constructor(private readonly configService: ConfigService, private readonly logger: Logger) {
    const hostApiConfig = this.configService.get<HostApiConfig>('hostApiConfiguration');

    if (!hostApiConfig) {
      throw new Error('Host API configuration is not defined');
    }

    if (!hostApiConfig.baseUrl) {
      throw new Error('Host API baseUrl is not defined');
    }

    if (!hostApiConfig.apiKey) {
      throw new Error('Host API apiKey is not defined');
    }

    this.baseUrl = hostApiConfig.baseUrl;
    this.apiKey = hostApiConfig.apiKey;

    this.axios = axios.create({
      baseURL: this.baseUrl,
      headers: {
        Authorization: `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
    });
  }

  /**
   * Generate SSL certificate for a domain
   * @param domain The domain to generate an SSL certificate for
   * @returns Result of the SSL generation process
   */
  async generateSslCertificate(domain: string): Errorable<{ success: boolean; message: string; details?: string }> {
    try {
      const response = await this.axios.post('/ssl-generator', { domain });
      return { data: response.data };
    } catch (error) {
      this.logger.error(`Error generating SSL certificate for ${domain}: ${error.message}`, error.stack);
      return {
        error: error.response?.data || {
          success: false,
          message: error.message,
        },
      };
    }
  }

  /**
   * Verify domain ownership via DNS records
   * @param domain The domain to verify
   * @param verificationCode The verification code to check for in DNS
   * @returns Result of the domain verification process
   */
  async verifyDomainOwnership(
    domain: string,
    verificationCode: string,
  ): Errorable<{
    success: boolean;
    message: string;
    verified: boolean;
  }> {
    try {
      const response = await this.axios.post('/domain-verification', {
        domain,
        verificationCode,
      });
      return { data: response.data };
    } catch (error) {
      this.logger.error(`Error verifying domain ${domain}: ${error.message}`, error.stack);
      return {
        error: error.response?.data || {
          success: false,
          message: error.message,
          verified: false,
        },
      };
    }
  }

  /**
   * Delete a domain's SSL certificate and Nginx configuration from the host server
   * @param domain The domain to delete
   * @returns Result of the domain deletion process
   */
  async deleteDomain(
    domain: string,
  ): Errorable<{
    success: boolean;
    message: string;
  }> {
    try {
      const response = await this.axios.delete('/delete-domain', {
        data: { domain },
      });
      return { data: response.data };
    } catch (error) {
      this.logger.error(`Error deleting domain ${domain}: ${error.message}`, error.stack);
      return {
        error: error.response?.data || {
          success: false,
          message: error.message,
        },
      };
    }
  }

  /**
   * Check the health status of the host service
   * @returns Health check result
   */
  async checkHealth(): Errorable<{ status: string; timestamp: string }> {
    try {
      const response = await this.axios.get('/health');
      return { data: response.data };
    } catch (error) {
      this.logger.error(`Health check failed: ${error.message}`, error.stack);
      return { error: error.response?.data || 'Service unhealthy' };
    }
  }
}
