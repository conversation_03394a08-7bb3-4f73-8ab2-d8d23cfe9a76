import { Injectable, Logger } from '@nestjs/common';
import { ZillaConfig } from '../config/types/zilla.config';
import { ConfigService } from '@nestjs/config';
import Axios, { AxiosInstance } from 'axios';
import { registerErrors } from '../utils/errors.util';
import { Errorable } from '../utils/types';

export enum ZILLA_PAYMENT_TYPES {
  ONE_OFF = 'ONE_OFF',
  RECURRING = 'RECURRING',
}

export type ZillaResponse<T> = {
  message: string;
  errorCode?: string;
  data: T;
  meta?: any;
};

export interface IZillaPurchaseOrderResponse {
  id: string;
  createdAt: Date;
  merchantOutletId: string;
  amount: number;
  clientOrderReference: string;
  title: string;
  productCategory: string;
  type: string;
  reusableCount: number;
  usedCount: number;
  status: string;
  customerHandle?: any;
  customerId?: any;
  redirectUrl: string;
  orderSource: string;
  createdByPrincipalId: string;
  completedAt?: any;
  orderCode: string;
  paymentLink: string;
}

export interface IZillaPurchaseOrder {
  amount: number;
  clientOrderReference: string;
  title: string;
  type?: ZILLA_PAYMENT_TYPES;
}

@Injectable()
export class ZillaRepository {
  private readonly zillaConfig: ZillaConfig;
  private readonly axios: AxiosInstance;

  constructor(config: ConfigService, private readonly logger: Logger) {
    this.zillaConfig = config.get<ZillaConfig>('zillaConfiguration');
    this.axios = Axios.create({
      baseURL: this.zillaConfig.apiUrl,
    });
    this.logger.setContext('zilla.respository.ts');
  }

  private async getAuth(): Errorable<string> {
    try {
      const { data } = await this.axios.post<{ data: { token: string } }>('/auth/sa', {
        publicKey: this.zillaConfig.publicKey,
        secretKey: this.zillaConfig.secretKey,
      });

      return { data: data.data.token };
    } catch (err) {
      this.logger.error(err.response.data);
      registerErrors(err.response.data);

      return { error: err.response };
    }
  }

  async generatePaymentLink(payload: IZillaPurchaseOrder): Errorable<IZillaPurchaseOrderResponse> {
    try {
      const authenticated = await this.getAuth();

      if (authenticated.error) {
        throw new Error(authenticated.error);
      }

      const { data } = await this.axios.post<ZillaResponse<IZillaPurchaseOrderResponse>>('/purchase-order', payload, {
        headers: {
          Authorization: 'Bearer ' + authenticated.data,
        },
      });

      return { data: data.data };
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);

      return { error: err };
    }
  }
}
