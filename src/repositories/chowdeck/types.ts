export interface CreateChowdeckMerchantPayload {
  reference: string;
  email: string;
  first_name: string;
  last_name: string;
  phone: string;
  name: string;
  support_contact: string;
  support_email: string;
  minimum_delivery_time: number;
  cover_images: CoverImage[];
  description: string;
  tags: string[];
  bank_code: string;
  account_number: string;
  account_name: string;
  address: {
    latitude: number;
    longitude: number;
  };
  available_hours: {
    default: Hours;
    monday?: Hours;
    tuesday?: Hours;
    wednesday?: Hours;
    thursday?: Hours;
    friday?: Hours;
    saturday?: Hours;
    sunday?: Hours;
  };
}

export interface CreateDeliveryPayload {
  destination_contact: {
    name: string;
    phone: string;
    email?: string;
    country_code: string;
  };
  source_contact: {
    name: string;
    phone: string;
    email?: string;
    country_code: string;
  };
  customer_delivery_note?: string;
  customer_vendor_note?: string;
  fee_id: number;
  item_type: string;
  user_action: string;
  reference?: string;
  estimated_order_amount?: number;
}

export interface GetDeliveryFeesPayload {
  source_address: {
    latitude: string;
    longitude: string;
  };
  destination_address: {
    latitude: string;
    longitude: string;
  };
  estimated_order_amount?: number;
}

export interface ChowdeckMerchant {
  id: number;
  primary_user: number;
  name: string;
  reference: string;
  description: string;
  support_contact: string;
  support_email: string;
  logo_url: string;
  current_domain: string;
  cover_images: CoverImage[];
  slug: string;
  currency: string;
  minimum_order_amount: number;
  is_active: boolean;
  location: Location | null;
  offers_pickup: boolean;
  offers_delivery: boolean;
  vendor_class: string;
  vendor_type: string;
  registration_status: RegistrationStatus;
  minimum_delivery_time: number;
  maximum_delivery_time: number;
  metadata: any | null;
  created_at: string;
  updated_at: string;
  likes: number;
  available_hours: AvailableHours;
  notification_types: string;
  onboarded_by: number;
  total_rating: number;
  number_of_rating: number;
  average_rating: string;
  preorder_configuration: any;
  settlement_schedule: any | null;
  charge_percent: string;
  flat_fee_per_item: number;
  is_open: boolean;
  parent_vendor: any | null;
  has_pin: boolean;
  setting: any | null;
  service_charge_percent: string;
  service_charge_cap: number;
  is_closed_by_operations: boolean;
  activation_status: string;
  webhook_url: string;
  coordinate: Coordinate;
  pretty_name: string;
  address_id: number;
  city: string;
  tags: string[];
  discounts: any[];
  opening_time_text: any | null;
  can_pre_order: boolean;
  current_is_open_state: boolean;
}

interface CoverImage {
  path: string;
}

interface Location {
  // Define if there are specific fields for location
}

interface RegistrationStatus {
  basic_information: number;
  bank_information: number;
  incorporation_information: number;
}

interface AvailableHours {
  default: Hours;
  [key: string]: Hours | undefined; // Flexible for any day of the week
}

interface Coordinate {
  x: number;
  y: number;
}

export interface Hours {
  opening: number;
  closing: number;
}

export interface ChowdeckItem {
  id: number;
  group_id: number;
  rank: number;
  name: string;
  popular_name: string;
  description: string;
  in_stock: boolean;
  is_published: boolean;
  is_active: boolean;
  price: number;
  currency: string;
  price_description: string;
  created_at: string;
  updated_at: string;
  container_type_id: any;
  menu_group_ids: any;
  reference: string;
  volume_per_portion: number;
  maximum_quantity: any;
  maximum_quantity_as_side: number;
  size_description: any;
  menu_sub_category_id: any;
  listing_price_factor: string;
  container_name: any;
  container_price: any;
  container_description: any;
  container_volume: any;
  tags: ChowdeckTag[];
  category: ChowdeckCategory;
  images: Image[];
  modifiers?: ChowdeckItemModifier[];
}

export interface ChowdeckModifierItem {
  id: number;
  name: string;
  price: number;
  menu_group_id: number;
  is_active: boolean;
  menu_id: number | null;
  in_stock: boolean;
  listing_price_factor: number | null;
  mark_up_price: number | null;
}

export interface ChowdeckItemModifier {
  id: number;
  name: string;
  is_published: boolean;
  minimum_selection: number;
  maximum_selection: number;
  maximum_free_selection: number;
  has_quantity: boolean;
  items: ChowdeckModifierItem[];
}

export interface ChowdeckTag {
  name: string;
  id: number;
}

export interface ChowdeckCategory {
  id: number;
  name: string;
  is_published: boolean;
  is_general: boolean;
  rank?: number;
  reference?: string;
  food_count?: number;
}

export interface Image {
  path: string;
  rank: number;
}
