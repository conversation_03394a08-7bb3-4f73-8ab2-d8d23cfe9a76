import dayjs from 'dayjs';
import { Address } from '../../modules/deliveries/deliveries.schema';
import { Store } from '../../modules/store/store.schema';
import { User } from '../../modules/user/user.schema';
import { Account } from '../../modules/wallets/wallet.account.schema';
import { formatPhoneNumber, sluggify } from '../../utils';
import { ChowdeckItemModifier, ChowdeckModifierItem, CreateChowdeckMerchantPayload, Hours } from './types';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import { ItemVariants } from '../../modules/item/item.schema';
import Mongoose from 'mongoose';

export function parseTimeRangeToHours(timeRange: string): Hours | null {
  const times = timeRange.split('-');

  if (times.length !== 2) {
    console.error("Time range format is incorrect. Expected format: 'HH:mmAM-PM-HH:mmAM-PM'.");
    return null;
  }

  dayjs.extend(customParseFormat);

  const openingTime = dayjs(times[0].trim(), 'hh:mmA');
  const closingTime = dayjs(times[1].trim(), 'hh:mmA');

  // Validate time parsing
  if (!openingTime.isValid() || !closingTime.isValid()) {
    console.error('Invalid time provided in the time range.');
    return null;
  }

  return {
    opening: openingTime.hour() * 100 + openingTime.minute(),
    closing: closingTime.hour() * 100 + closingTime.minute(),
  };
}

export function catlogStoreToChowdeckMerchant(
  storeOwner: User,
  store: Store,
  storeAccount: Account,
  storeAddress: Address,
): CreateChowdeckMerchantPayload {
  const userNameSplit = storeOwner.name.split(' ');

  if (!storeAccount || !storeAddress) return null;

  return {
    reference: sluggify(store.name) + '-' + store.id,
    email: storeOwner.email,
    first_name: userNameSplit[0],
    last_name: userNameSplit[1],
    phone: formatPhoneNumber(store.phone),
    name: store.name,
    support_contact: storeOwner.phone,
    support_email: storeOwner.email,
    minimum_delivery_time: 0,
    cover_images: [
      {
        path: store?.hero_image ?? '',
      },
    ],
    description: store.description,
    tags: store?.business_category?.product_types ?? [],
    bank_code: storeAccount.bank_code,
    account_number: storeAccount.account_number,
    account_name: storeAccount.account_name,
    address: {
      latitude: storeAddress.latitude,
      longitude: storeAddress.longitude,
    },
    available_hours: {
      default: parseTimeRangeToHours(store?.configuration.hours.monday),
      monday: parseTimeRangeToHours(store?.configuration.hours.monday),
      tuesday: parseTimeRangeToHours(store?.configuration.hours.tuesday),
      wednesday: parseTimeRangeToHours(store?.configuration.hours.wednesday),
      thursday: parseTimeRangeToHours(store?.configuration.hours.thursday),
      friday: parseTimeRangeToHours(store?.configuration.hours.friday),
      saturday: parseTimeRangeToHours(store?.configuration.hours.saturday),
      sunday: parseTimeRangeToHours(store?.configuration.hours.sunday),
    },
  };
}

export function chowdeckItemModifiersToProductOptions(modifiers: ChowdeckItemModifier[]): ItemVariants {
  if (!modifiers || modifiers.length < 1) return { type: 'custom', options: [] };

  // Helper function to get all combinations
  function getCombinations(
    arrays: ChowdeckModifierItem[][],
    index = 0,
    prefix: ChowdeckModifierItem[] = [],
  ): ChowdeckModifierItem[][] {
    if (index === arrays.length) return [prefix];
    const result: ChowdeckModifierItem[][] = [];
    arrays[index].forEach((item) => {
      result.push(...getCombinations(arrays, index + 1, [...prefix, item]));
    });
    return result;
  }

  // Extract the items from each modifier group
  const items = modifiers.map((group) => group.items);
  const allCombinations = getCombinations(items);

  // Map combinations to the desired format
  const options = allCombinations.map((combination) => {
    const values: { [key: string]: string } = {};
    let price = 0;
    let is_available = true;

    combination.forEach((item) => {
      const group = modifiers.find((group) => group.id === item.menu_group_id);
      if (group) {
        values[group.name] = item.name;
        price += item.price;
        is_available = is_available && item.is_active && item.in_stock;
      }
    });

    return {
      values,
      price,
      is_available,
      _id: Mongoose.Types.ObjectId(),
    };
  });

  return {
    type: 'custom',
    options,
  };
}
