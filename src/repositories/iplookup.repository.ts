import { Injectable, Logger } from '@nestjs/common';
import Axios, { AxiosInstance } from 'axios';
import { Errorable } from '../utils/types';

@Injectable()
export class IPLookUpRepository {
  private axios: AxiosInstance;

  constructor(private readonly logger: Logger) {
    this.axios = Axios.create({
      baseURL: 'https://api.iplocation.net',
      headers: { 'Content-Type': 'application/json' },
    });
  }

  async getIPInfo(ip: string): Errorable<any> {
    try {
      const response = await this.axios.get(`/?ip=${ip}`);

      if (response?.data?.response_code === '200') {
        return { data: response.data };
      }

      return { error: response?.data?.response_message ?? 'Failed to retrieve IP information' };
    } catch (err) {
      this.logger.error(err);
      return { error: err.response?.data?.response_message ?? 'An error occurred during the IP lookup' };
    }
  }
}
