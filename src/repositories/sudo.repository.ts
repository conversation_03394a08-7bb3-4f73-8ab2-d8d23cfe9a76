import { Injectable, Logger } from '@nestjs/common';
import Axios, { AxiosInstance } from 'axios';
import { ConfigService } from '@nestjs/config';
import { Errorable } from '../utils/types';
import { registerErrors } from '../utils/errors.util';
import { extractErrorDetails } from '../utils/axios-errors';
import { SudoConfig } from '../config/types/sudo.config';
import { CacheService } from '../modules/shared/cache/cache.service';

interface SudoOAuthTokenResponse {
  access_token: string;
  client_id: string;
  token_type: string;
  expires_in: number;
  refresh_token: string;
  ibs_client_id: string;
  ibs_user_id: string;
}

export interface CreateVirtualAccountRequest {
  validFor: number;
  settlementAccount: {
    bankCode: string;
    accountNumber: string;
  };
  amountControl: 'Fixed' | 'Exact' | 'OverPayment';
  callbackUrl: string;
  amount?: number;
  externalReference: string;
}

export interface VirtualAccountResponse {
  _id: string;
  client: string;
  bankCode: string;
  accountNumber: string;
  accountName: string;
  currencyCode: string;
  bvn: string;
  validFor: number;
  externalReference: string;
  amountControl: string;
  amount: number;
  expiryDate: string;
  callbackUrl: string;
  settlementAccount: {
    _id: string;
    bankCode: string;
    accountNumber: string;
  };
  status: string;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface NameEnquiryRequest {
  bankCode: string;
  accountNumber: string;
}

export interface NameEnquiryResponse {
  responseCode: string;
  responseMessage: string;
  sessionId: string;
  bankCode: string;
  accountNumber: string;
  accountName: string;
  kycLevel: string;
  bvn: string;
}

export interface TransferRequest {
  saveBeneficiary: boolean;
  nameEnquiryReference: string;
  debitAccountNumber: string;
  beneficiaryBankCode: string;
  beneficiaryAccountNumber: string;
  amount: number;
  narration: string;
  paymentReference?: string;
}

export interface TransferResponse {
  queued: boolean;
  _id: string;
  client: string;
  account: string;
  type: string;
  sessionId: string;
  nameEnquiryReference: string;
  paymentReference: string;
  mandateReference: null;
  isReversed: boolean;
  reversalReference: null;
  provider: string;
  providerChannel: string;
  providerChannelCode: string;
  destinationInstitutionCode: string;
  creditAccountName: string;
  creditAccountNumber: string;
  creditBankVerificationNumber: null;
  creditKYCLevel: string;
  debitAccountName: string;
  debitAccountNumber: string;
  debitBankVerificationNumber: null;
  debitKYCLevel: string;
  transactionLocation: string;
  narration: string;
  amount: number;
  fees: number;
  vat: number;
  stampDuty: number;
  responseCode: null;
  responseMessage: null;
  status: string;
  recurringTransfer: null;
  isDeleted: boolean;
  createdAt: string;
  createdBy: string;
  updatedAt: string;
  __v: number;
}

export interface VirtualAccountTransferWebhook {
  type: string;
  data: {
    _id: string;
    client: string;
    virtualAccount: string;
    sessionId: string;
    nameEnquiryReference: string;
    paymentReference: string;
    isReversed: boolean;
    reversalReference: string;
    provider: string;
    providerChannel: string;
    providerChannelCode: string;
    destinationInstitutionCode: string;
    creditAccountName: string;
    creditAccountNumber: string;
    creditBankVerificationNumber: string | null;
    creditKYCLevel: string;
    debitAccountName: string;
    debitAccountNumber: string;
    debitBankVerificationNumber: string | null;
    debitKYCLevel: string;
    transactionLocation: string;
    narration: string;
    amount: number;
    fees: number;
    vat: number;
    stampDuty: number;
    responseCode: string;
    responseMessage: string;
    status: string;
    isDeleted: boolean;
    createdAt: string;
    declinedAt: string;
    updatedAt: string;
    __v: number;
  };
}

export interface SudoApiResponse<T> {
  statusCode: number;
  responseCode?: string;
  message: string;
  data: T;
}

export interface CreatePaymentVirtualAccountRequest {
  validFor: number;
  settlementAccount: {
    bankCode: string;
    accountNumber: string;
  };
  callbackUrl: string;
  amount: number;
  externalReference: string;
  customerName?: string;
  customerEmail?: string;
  customerPhone?: string;
  meta?: Record<string, any>;
}

export interface VirtualAccountPaymentWebhook {
  type: string;
  data: {
    _id: string;
    client: string;
    virtualAccount: string;
    sessionId: string;
    nameEnquiryReference: string;
    paymentReference: string;
    isReversed: boolean;
    reversalReference: string;
    provider: string;
    providerChannel: string;
    providerChannelCode: string;
    destinationInstitutionCode: string;
    creditAccountName: string;
    creditAccountNumber: string;
    creditBankVerificationNumber: string | null;
    creditKYCLevel: string;
    debitAccountName: string;
    debitAccountNumber: string;
    debitBankVerificationNumber: string | null;
    debitKYCLevel: string;
    transactionLocation: string;
    narration: string;
    amount: number;
    fees: number;
    vat: number;
    stampDuty: number;
    responseCode: string;
    responseMessage: string;
    status: string;
    isDeleted: boolean;
    createdAt: string;
    declinedAt: string;
    updatedAt: string;
    __v: number;
  };
}

@Injectable()
export class SudoRepository {
  private axios: AxiosInstance;
  private config: SudoConfig;
  private readonly tokenCacheKey = 'sudo_oauth_token';
  private readonly clientIdCacheKey = 'sudo_ibs_client_id';

  constructor(
    private readonly configService: ConfigService,
    private readonly logger: Logger,
    private readonly cacheService: CacheService,
  ) {
    this.config = configService.get<SudoConfig>('sudoConfiguration');

    this.axios = Axios.create({
      baseURL: this.config.base_url,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  // Method to get OAuth token
  async getOAuthToken(): Promise<SudoOAuthTokenResponse> {
    const payload = {
      grant_type: 'client_credentials',
      client_assertion_type: 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer',
      client_assertion: this.config.client_assertion,
      client_id: this.config.client_id,
    };

    try {
      const res = await this.axios.post<SudoOAuthTokenResponse>('/oauth2/token', payload);
      return res?.data;
    } catch (err) {
      const error = extractErrorDetails(err);
      this.logger.error('Failed to get OAuth token from Sudo');
      this.logger.log(error);
      registerErrors(error);
      throw new Error(error?.message);
    }
  }

  // Method to get token from cache or auth endpoint
  async getCachedOrNewToken(): Promise<{ token: string; ibsClientId: string }> {
    // Check if the token exists in the cache
    const cachedToken = await this.cacheService.get<string>(this.tokenCacheKey);
    const cachedIbsClientId = await this.cacheService.get<string>(this.clientIdCacheKey);

    if (cachedToken && cachedIbsClientId) {
      return { token: cachedToken, ibsClientId: cachedIbsClientId };
    }

    // If no cached token, fetch a new one
    const tokenResponse = await this.getOAuthToken();

    // Set the token in Redis with TTL matching its expiry time
    await this.cacheService.setWithLock(this.tokenCacheKey, tokenResponse.access_token, tokenResponse.expires_in);
    await this.cacheService.setWithLock(this.clientIdCacheKey, tokenResponse.ibs_client_id, tokenResponse.expires_in);

    return { token: tokenResponse.access_token, ibsClientId: tokenResponse.ibs_client_id };
  }

  // Create virtual account
  async createVirtualAccount(request: CreateVirtualAccountRequest): Promise<Errorable<VirtualAccountResponse>> {
    try {
      const { token, ibsClientId } = await this.getCachedOrNewToken();

      const res = await this.axios.post<SudoApiResponse<VirtualAccountResponse>>('/virtual-accounts', request, {
        headers: {
          Authorization: `Bearer ${token}`,
          ClientID: ibsClientId || this.config.ibs_client_id,
        },
      });

      return { data: res.data.data };
    } catch (err) {
      const error = extractErrorDetails(err);
      this.logger.error('Failed to create virtual account on Sudo');
      this.logger.log(error);
      registerErrors(error);
      return { error: error?.message };
    }
  }

  // Name inquiry for bank account
  async nameEnquiry(request: NameEnquiryRequest): Promise<Errorable<NameEnquiryResponse>> {
    try {
      const { token, ibsClientId } = await this.getCachedOrNewToken();

      const res = await this.axios.post<SudoApiResponse<NameEnquiryResponse>>('/transfers/name-enquiry', request, {
        headers: {
          Authorization: `Bearer ${token}`,
          ClientID: ibsClientId || this.config.ibs_client_id,
        },
      });

      console.log('NAME INQUIRY RESPONSE', res.data);

      return { data: res.data.data };
    } catch (err) {
      const error = extractErrorDetails(err);
      this.logger.error('Failed to perform name inquiry on Sudo');
      this.logger.log(error);
      registerErrors(error);
      return { error: error?.message };
    }
  }

  // Transfer funds
  async transfer(request: TransferRequest): Promise<Errorable<TransferResponse>> {
    try {
      const { token, ibsClientId } = await this.getCachedOrNewToken();
      console.log('TRANSFER REQUEST', request);

      const res = await this.axios.post<SudoApiResponse<TransferResponse>>('/transfers', request, {
        headers: {
          Authorization: `Bearer ${token}`,
          ClientID: ibsClientId || this.config.ibs_client_id,
        },
      });

      console.log('TRANSFER RESPONSE', res.data);

      return { data: res.data.data };
    } catch (err) {
      const error = extractErrorDetails(err);
      this.logger.error('Failed to perform transfer on Sudo');
      this.logger.log(error);
      registerErrors(error);
      return { error: error?.message };
    }
  }

  // Initiate a withdrawal transfer
  async initiateWithdrawal({
    amount,
    accountNumber,
    bankCode,
    narration,
    reference,
  }: {
    amount: number;
    accountNumber: string;
    bankCode: string;
    narration: string;
    reference: string;
  }): Promise<Errorable<TransferResponse>> {
    try {
      // Step 1: Get name inquiry for the account
      const nameInquiryResult = await this.nameEnquiry({
        bankCode,
        accountNumber,
      });

      if (nameInquiryResult.error || !nameInquiryResult.data) {
        return { error: nameInquiryResult.error || 'Name inquiry failed' };
      }

      // Step 2: Initiate the transfer
      const transferResult = await this.transfer({
        saveBeneficiary: true,
        nameEnquiryReference: nameInquiryResult.data.sessionId,
        debitAccountNumber: this.config.debit_account_number, // Settlement account number
        beneficiaryBankCode: bankCode,
        beneficiaryAccountNumber: accountNumber,
        amount: amount,
        narration: narration,
        paymentReference: reference,
      });

      if (transferResult.error || !transferResult.data) {
        return { error: transferResult.error || 'Transfer initiation failed' };
      }

      return transferResult;
    } catch (err) {
      const error = extractErrorDetails(err);
      this.logger.error('Failed to initiate withdrawal on Sudo');
      this.logger.log(error);
      registerErrors(error);
      return { error: error?.message };
    }
  }

  // Get bank list
  async getBanks(): Promise<Errorable<any[]>> {
    try {
      const { token, ibsClientId } = await this.getCachedOrNewToken();

      const res = await this.axios.get<SudoApiResponse<any[]>>('/transfers/banks', {
        headers: {
          Authorization: `Bearer ${token}`,
          ClientID: ibsClientId || this.config.ibs_client_id,
        },
      });

      return { data: res.data.data };
    } catch (err) {
      const error = extractErrorDetails(err);
      this.logger.error('Failed to get banks from Sudo');
      this.logger.log(error);
      registerErrors(error);
      return { error: error?.message };
    }
  }

  // Get virtual account details
  async getVirtualAccount(accountId: string): Promise<Errorable<VirtualAccountResponse>> {
    try {
      const { token, ibsClientId } = await this.getCachedOrNewToken();

      const res = await this.axios.get<SudoApiResponse<VirtualAccountResponse>>(`/virtual-accounts/${accountId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          ClientID: ibsClientId || this.config.ibs_client_id,
        },
      });

      return { data: res.data.data };
    } catch (err) {
      const error = extractErrorDetails(err);
      this.logger.error('Failed to get virtual account details from Sudo');
      this.logger.log(error);
      registerErrors(error);
      return { error: error?.message };
    }
  }

  // Create payment virtual account (for receiving public payments)
  async createPaymentVirtualAccount(
    request: CreatePaymentVirtualAccountRequest,
  ): Promise<Errorable<VirtualAccountResponse>> {
    try {
      const { token, ibsClientId } = await this.getCachedOrNewToken();

      // Build the request payload for payment virtual account
      const payload = {
        validFor: request.validFor,
        settlementAccount: request.settlementAccount,
        amountControl: 'Fixed', // For payment accounts, we typically use Fixed amount
        callbackUrl: request.callbackUrl,
        amount: request.amount,
        externalReference: request.externalReference,
        meta: request.meta || {}, // Store additional payment info
      };

      const res = await this.axios.post<SudoApiResponse<VirtualAccountResponse>>('/virtual-accounts', payload, {
        headers: {
          Authorization: `Bearer ${token}`,
          ClientID: ibsClientId || this.config.ibs_client_id,
        },
      });

      console.log('CREATE PAYMENT VIRTUAL ACCOUNT RESPONSE', res.data);

      return { data: res.data.data };
    } catch (err) {
      const error = extractErrorDetails(err);
      this.logger.error('Failed to create payment virtual account on Sudo');
      this.logger.log(error);
      registerErrors(error);
      return { error: error?.message };
    }
  }

  // Verify a webhook signature from Sudo
  verifyWebhookSignature(requestBody: any, signature: string): boolean {
    // Note: Implement according to Sudo's webhook signature verification method
    // This is a placeholder - you would need to get the actual verification method from Sudo
    try {
      // For now, return true as a placeholder
      // In production, you should implement proper signature verification
      return true;
    } catch (error) {
      this.logger.error('Failed to verify Sudo webhook signature');
      this.logger.log(error);
      return false;
    }
  }

  // Process a webhook from Sudo
  async processWebhookPayload(body: VirtualAccountPaymentWebhook): Promise<boolean> {
    try {
      const { type, data } = body;

      // Ensure it's a virtual account transfer webhook
      if (type !== 'virtualAccount.transfer') {
        this.logger.warn(`Received unknown webhook type: ${type}`);
        return false;
      }

      // Verify the payment is successful
      if (data.status !== 'Completed' || data.responseCode !== '00') {
        this.logger.warn(`Payment not completed: ${data.status}, code: ${data.responseCode}`);
        return false;
      }

      return true;
    } catch (err) {
      const error = extractErrorDetails(err);
      this.logger.error('Failed to process Sudo webhook');
      this.logger.log(error);
      registerErrors(error);
      return false;
    }
  }
}
