import { Injectable } from '@nestjs/common';
import Axios, { AxiosInstance } from 'axios';
import { Errorable } from '../utils/types';

interface PdfResquest {
  page_url: string;
  page_width: number;
  page_height?: number;
  scale?: number;
  page_margin?: {
    top?: number;
    bottom?: number;
    left?: number;
    right?: number;
  };
}

@Injectable()
export class WebServicesRepository {
  private axios: AxiosInstance;

  constructor() {
    this.axios = Axios.create({
      baseURL: process.env.WEB_SERVICES_URL,
      responseType: 'arraybuffer',
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  async generatePdf(payload: PdfResquest): Errorable<any> {
    try {
      const r = await this.axios.post('pdf', {
        ...payload,
      });

      return { data: r.data };
    } catch (err) {
      return { error: err };
    }
  }
}
