import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Axios, { AxiosInstance } from 'axios';
import { DojahConfig } from '../config/types/dojah.config';
import { Exclusive } from '../utils/types';

export type Error<T = any> = { error: T };
export type Entity<T = any> = { entity: T };

export type SelfieVerification = {
  confidence_value: number;
  match: boolean;
  photoId_image_blurry: boolean;
  selfie_image_blurry: boolean;
  selfie_glare: boolean;
  photoId_glare: boolean;
  age_range: string;
  sunglasses: boolean;
};

export type DocumentAnalysis = {
  status: {
    overall_status: number;
    document_images: string;
    text: string;
    document_type: string;
    expiry: string;
  };
  document_type: {
    document_name: string;
    document_country_name: string;
    document_country_code: string;
    document_type_id: number;
    document_year?: any;
  };
  document_images: {
    Portrait: string;
    Barcode: string;
    'Document front side': string;
  };
  text_data: {
    field_name: string;
    field_type: number;
    status: number;
    value: string;
  }[];
};

export type VNINLookup = {
  vnin: string;
  firstname: string;
  middlename: string;
  surname: string;
  user_id: string;
  gender: string;
  mobile: string;
  dateOfBirth: string;
  photo: string;
};

export type VNINFacialComparison = {
  vnin: string;
  firstname: string;
  middlename: string;
  surname: string;
  userid: string;
  gender: string;
  telephoneno: string;
  birthdate: string;
  photo: string;
  selfie_verification: {
    confidence_value: number;
    match: number;
  };
};

export type BVNLookup = {
  bvn: string;
  first_name: string;
  last_name: string;
  middle_name: string;
  gender: string;
  date_of_birth: string;
  phone_number1: string;
  image: string;
  email: string;
  enrollment_bank: string;
  enrollment_branch: string;
  level_of_account: string;
  lga_of_origin: string;
  lga_of_residence: string;
  marital_status: string;
  name_on_card: string;
  nationality: string;
  nin: string;
  phone_number2: string;
  registration_date: string;
  residential_address: string;
  state_of_origin: string;
  state_of_residence: string;
  title: string;
  watch_listed: string;
};

@Injectable()
export class DojahRepository {
  private readonly dojahConfig: DojahConfig;
  private readonly axios: AxiosInstance;

  constructor(private readonly configService: ConfigService, private readonly logger: Logger) {
    this.dojahConfig = configService.get<DojahConfig>('dojahConfiguration');
    this.axios = Axios.create({
      baseURL: this.dojahConfig.apiUrl,
      headers: {
        'content-type': 'application/json',
        Authorization: this.dojahConfig.apiKey,
        AppId: this.dojahConfig.appId,
      },
    });
  }

  async vninLookup(nin: string): Promise<Exclusive<Entity<VNINLookup>, Error>> {
    // try {
    return new Promise((resolve, reject) =>
      this.axios
        .get('kyc/vnin', {
          params: { vnin: nin },
        })
        .then((res) => resolve(res.data))
        .catch(function (error) {
          if (error.response) {
            // The request was made and the server responded with a status code
            // that falls out of the range of 2xx
            console.log('VNIN RESPONSE ERROR');
            console.log(error.response);
            // console.log(error?.response?.data?.error);
            reject({ error: error?.response?.data?.error });
          } else if (error.request) {
            // The request was made but no response was received
            // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
            // http.ClientRequest in node.js
            console.log(error.request);
          } else {
            // Something happened in setting up the request that triggered an Error
            console.log('Error', error.message);
          }

          reject({ error });
        }),
    );
  }

  async vninFacialVerification(nin: string, image: string): Promise<Exclusive<Entity<VNINFacialComparison>, Error>> {
    // try {
    return new Promise((resolve, reject) =>
      this.axios
        .post('kyc/vnin/verify', { vnin: nin, selfie_image: image })
        .then((res) => resolve(res.data))
        .catch(function (error) {
          if (error.response) {
            // The request was made and the server responded with a status code
            // that falls out of the range of 2xx
            console.log('VNIN RESPONSE ERROR');
            console.log(error.response);
            // console.log(error?.response?.data?.error);
            reject({ error: error?.response?.data?.error });
          } else if (error.request) {
            // The request was made but no response was received
            // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
            // http.ClientRequest in node.js
            console.log(error.request);
          } else {
            // Something happened in setting up the request that triggered an Error
            console.log('Error', error.message);
          }

          reject({ error });
        }),
    );
  }

  async lookupBvn(bvn: string): Promise<Exclusive<Entity<BVNLookup>, Error>> {
    try {
      const response = await this.axios.get('kyc/bvn/advance', {
        params: { bvn },
      });
      return response.data;
    } catch (error) {
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        this.logger.error('BVN RESPONSE ERROR');
        this.logger.error(error.response);
        throw new Error(error?.response?.data?.error);
      } else if (error.request) {
        // The request was made but no response was received
        // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
        // http.ClientRequest in node.js
        this.logger.error(error.request);
      } else {
        // Something happened in setting up the request that triggered an Error
        this.logger.error('Error', error.message);
      }

      throw error;
    }
  }

  async verifyOtherId(
    photoId: string,
    selfieImage: string,
    firstName: string,
    lastName: string,
  ): Promise<Exclusive<Entity, Error>> {
    try {
      const res = await this.axios.post('kyc/photoid/verify', {
        selfie_image: selfieImage,
        photoid_image: photoId,
        first_name: firstName,
        last_name: lastName,
      });

      return res.data;
    } catch (err) {
      console.log('VERIFYING ID ERROR');
      console.error(err);
      return { error: err ?? true };
    }
  }

  async verifySelfie(selfieImage: string, photoId: string): Promise<Exclusive<Entity<SelfieVerification>, Error>> {
    try {
      const res = await this.axios.post<Entity<SelfieVerification>>('kyc/photoid/verify', {
        selfie_image: selfieImage,
        photoid_image: photoId,
      });

      return res.data;
    } catch (err) {
      console.error(err.response);
      return { error: err.response };
    }
  }

  async documentAnalysis(documentBase64: string): Promise<Exclusive<Entity<DocumentAnalysis>, Error>> {
    try {
      const res = await this.axios.post<Entity<DocumentAnalysis>>('document/analysis', {
        input_type: 'base64',
        images: documentBase64,
      });

      return res.data;
    } catch (err) {
      console.error(err.response);
      return { error: err.response };
    }
  }
}
