import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Axios, { AxiosInstance } from 'axios';
import { Errorable } from '../utils/types';
import { FlutterWaveConfig } from '../config/types/flutterwave.config';
import { stripUndefinedAndNull } from '../utils/functions';
import { extractErrorDetails } from '../utils/axios-errors';
import { registerErrors } from '../utils/errors.util';
import { StartbuttonConfig } from '../config/types/startbutton.config';
import { CURRENCIES, CURRENCY_COUNTRY_MAP } from '../modules/country/country.schema';
import { IResolveAccountResponse } from './paystack.repository';
import { COUNTRY_CODE_NAME_MAP } from '../utils/constants';

type Response<T> = { data: T };

interface InitiatePaymentData {
  reference: string;
  amount: number;
  currency: CURRENCIES;
}

interface StartbuttonInitiateTransferParams {
  account_name: string;
  account_number: string;
  bank_code: string;
  currency: CURRENCIES;
  amount: number;
  reference: string;
}

interface InitiatePayementResponse {
  success: boolean;
  message: string;
  data: string;
}

export interface WithdrawalBank {
  name: string;
  code: string;
  id: number;
}

export interface BanksResponse {
  success: boolean;
  message: string;
  data: WithdrawalBank[];
}

@Injectable()
export class StartbuttonRepository {
  private config: StartbuttonConfig;
  private axios: AxiosInstance;

  constructor(readonly configService: ConfigService, private readonly logger: Logger) {
    this.config = configService.get<StartbuttonConfig>('startbuttonConfiguration');

    this.axios = Axios.create({
      baseURL: this.config.baseUrl,
      headers: {
        Authorization: 'Bearer ' + this.config.publicKey,
      },
    });
  }

  async initiatePayment(payload: InitiatePaymentData): Errorable<InitiatePayementResponse> {
    try {
      const reqData = {
        ...payload,
        email: '<EMAIL>',
        redirectUrl: process.env.CATLOG_WWW + '/payment-successful',
      };

      const res = await this.axios.post<InitiatePayementResponse>('/transaction/initialize', reqData);

      return { data: res?.data };
    } catch (err) {
      const error = extractErrorDetails(err);

      this.logger.log(error);
      registerErrors(error);
      return { error: error?.message };
    }
  }

  async getBanks(payload: { currency: CURRENCIES }): Errorable<BanksResponse> {
    try {
      const res = await this.axios.get<BanksResponse>(
        `/bank/list/${payload.currency}?type=${currenyBankTypeMap[payload.currency]}`,
      );

      return { data: res?.data };
    } catch (err) {
      const error = extractErrorDetails(err);
      this.logger.log(error);
      registerErrors(error);
      return { error: error?.message };
    }
  }

  async resolveAccountNumber(account_number: string, bank_code: string): Errorable<IResolveAccountResponse> {
    try {
      const res = await this.axios.get<IResolveAccountResponse>(
        `/bank/verify?bankCode=${bank_code}&accountNumber=${account_number}`,
        { headers: { Authorization: 'Bearer ' + this.config.privateKey } },
      );

      return { data: res.data };
    } catch (err) {
      this.logger.log(err.data, 'paystack account lookup failed');
      const error = extractErrorDetails(err);
      this.logger.log(error);
      return { error: error.message };
    }
  }

  async initiateTransfer(payload: StartbuttonInitiateTransferParams): Errorable<InitiatePayementResponse> {
    try {
      const initiationData = getTransferInitiationData(payload);

      if (!initiationData) {
        throw new Error('Transfers to this currency are not supported');
      }

      const res = await this.axios.post<any>('/transaction/transfer', initiationData, {
        headers: { Authorization: 'Bearer ' + this.config.privateKey },
      });

      return { data: res?.data };
    } catch (err) {
      const error = extractErrorDetails(err);

      this.logger.log(error);
      registerErrors(error);
      return { error: error?.message };
    }
  }
}

const getTransferInitiationData = (payload: StartbuttonInitiateTransferParams) => {
  const commons = {
    amount: payload.amount,
    currency: payload.currency,
    country: COUNTRY_CODE_NAME_MAP[CURRENCY_COUNTRY_MAP[payload.currency]].name,
    transactionReference: payload.reference,
  };

  if (payload.currency === CURRENCIES.ZAR) {
    return {
      ...commons,
      bankCode: payload.bank_code,
      accountNumber: payload.account_number,
      accountName: payload.account_name,
    };
  }

  if (payload.currency === CURRENCIES.KES) {
    return {
      ...commons,
      MNO: payload.bank_code,
      msisdn: payload.account_number,
    };
  }

  return null;
};

const currenyBankTypeMap = {
  [CURRENCIES.GHC]: 'mobile_money',
  [CURRENCIES.NGN]: 'bank',
  [CURRENCIES.KES]: 'mobile_money',
  [CURRENCIES.ZAR]: 'bank',
};
