import { Injectable, Logger } from '@nestjs/common';
import { FirebaseConfig } from '../config/types/firebase.config';
import { ConfigService } from '@nestjs/config';
import * as admin from 'firebase-admin';

@Injectable()
export class FirebaseRepository {
  private config: FirebaseConfig;

  constructor(private readonly logger: Logger, configService: ConfigService) {
    try {
      this.config = configService.get<FirebaseConfig>('firebaseConfiguration');

      if (!this.config) {
        this.logger.error('Firebase configuration is missing');
        return;
      }

      if (!admin.apps.length && this.config.project_id && this.config.client_email && this.config.private_key) {
        const privateKey = this.config.private_key.replace(/\\n/g, '\n').replace(/\n/g, '\n').replace(/""/g, '');

        admin.initializeApp({
          credential: admin.credential.cert({
            projectId: this.config.project_id,
            clientEmail: this.config.client_email,
            privateKey: privateKey,
          }),
        });

        // Verify the initialization
        admin
          .app()
          .options.credential.getAccessToken()
          .then(() => {
            this.logger.log('Firebase Admin SDK initialized successfully');
          })
          .catch((error) => {
            this.logger.error(`Firebase Admin SDK initialization verification failed: ${error.message}`);
          });
      }
    } catch (error) {
      this.logger.error(`Failed to initialize Firebase: ${error.message}`);
    }
  }

  /**
   * Sends a notification to a single device using FCM.
   * @param fcmToken The FCM token of the target device.
   * @param payload The notification payload.
   * @returns An object indicating the status of the send operation.
   */
  async sendToDevice(
    fcmToken: string,
    payload: { title: string; body: string; path: string },
  ): Promise<{ status: string; error?: string }> {
    try {
      if (!admin.apps.length) {
        throw new Error('Firebase Admin SDK not initialized');
      }

      const message: admin.messaging.Message = {
        token: fcmToken,
        notification: {
          title: payload.title,
          body: payload.body,
        },
        data: {
          path: payload.path,
        },
        android: {
          priority: 'high',
        },
        apns: {
          payload: {
            aps: {
              contentAvailable: true,
              priority: 10,
            },
          },
        },
      };

      const response = await admin.messaging().send(message);
      this.logger.log('Successfully sent message:', response);
      return { status: 'fulfilled' };
    } catch (error: any) {
      this.logger.error(`Failed to send Firebase notification: ${error.message}`);
      return { status: 'rejected', error: error.message };
    }
  }

  /**
   * Sends notifications to multiple devices.
   * @param fcmTokens An array of FCM tokens.
   * @param payload The notification payload.
   * @returns An array of status objects for each send operation and a list of invalid tokens.
   */
  async sendToDevices(
    fcmTokens: string[],
    payload: { title: string; body: string; path: string; data?: Record<string, any> },
  ): Promise<{ results: { status: string; error?: string }[]; invalidTokens: string[] }> {
    if (!admin.apps.length) {
      return {
        results: fcmTokens.map(() => ({
          status: 'rejected',
          error: 'Firebase Admin SDK not initialized',
        })),
        invalidTokens: [],
      };
    }

    const messages: admin.messaging.Message[] = fcmTokens.map((token) => ({
      token,
      notification: {
        title: payload.title,
        body: payload.body,
      },
      data: {
        path: payload.path,
        ...(payload.data || {}),
      },
      android: {
        priority: 'high',
        notification: {
          priority: 'high',
          sound: 'default',
        },
      },
      apns: {
        headers: {
          'apns-priority': '10',
          'apns-push-type': 'alert',
        },
        payload: {
          aps: {
            alert: {
              title: payload.title,
              body: payload.body,
            },
            sound: 'default',
            badge: 1,
            'content-available': 1,
            'mutable-content': 1,
            priority: 10,
          },
          path: payload.path,
          ...(payload.data || {}),
        },
      },
    }));

    try {
      // Send messages in batches of 500 (FCM limit)
      const batchSize = 500;
      const results: { status: string; error?: string }[] = [];
      const invalidTokens: string[] = [];

      for (let i = 0; i < messages.length; i += batchSize) {
        const batch = messages.slice(i, i + batchSize);
        try {
          const batchResponses = await admin.messaging().sendEach(batch);

          const batchResults = batchResponses.responses.map((resp, idx) => {
            if (resp.success) {
              return { status: 'fulfilled' };
            } else {
              const token = fcmTokens[i + idx];
              const error = resp.error?.message;

              // Log detailed error information
              this.logger.error(
                `Failed to send to ${token}: ${error}. Error details: ${JSON.stringify({
                  error: resp.error,
                  token,
                  messagePayload: batch[idx],
                })}`,
              );

              // Collect invalid tokens
              if (
                error?.includes('registration-token-not-registered') ||
                error?.includes('invalid-registration-token') ||
                error?.includes('invalid-argument') ||
                error?.includes('not-registered')
              ) {
                invalidTokens.push(token);
              }

              return { status: 'rejected', error };
            }
          });

          results.push(...batchResults);
        } catch (batchError: any) {
          this.logger.error(`Batch send failed: ${batchError.message}`);
          const batchResults = batch.map(() => ({
            status: 'rejected',
            error: batchError.message,
          }));
          results.push(...batchResults);
        }
      }

      return { results, invalidTokens };
    } catch (error: any) {
      this.logger.error(`Failed to send Firebase notifications: ${error.message}`);
      return {
        results: fcmTokens.map(() => ({
          status: 'rejected',
          error: error.message,
        })),
        invalidTokens: [],
      };
    }
  }
}
