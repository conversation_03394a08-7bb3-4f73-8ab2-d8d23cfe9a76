import { Injectable, Logger } from '@nestjs/common';
// import { BrokerTransportService } from '../broker/broker-transport.service';
// import Axios, { AxiosInstance } from 'axios'; // Remove Axios
import { ConfigService } from '@nestjs/config';
import { registerErrors } from '../utils/errors.util';
import { Errorable } from '../utils/types';
import { OpenaiConfig } from '../config/types/openai.config';
import { Configuration, OpenAIApi, CreateChatCompletionRequest, CreateEmbeddingRequest } from 'openai'; // Import SDK components

export interface GptResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  usage: Usage;
  choices: Choice[];
}

interface Choice {
  message: Message;
  finish_reason: string;
  index: number;
}

interface Message {
  role: string;
  content: string;
}

interface Usage {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
}
@Injectable()
export class OpenaiRepository {
  private config: OpenaiConfig;
  private readonly openai: OpenAIApi;
  private readonly logger = new Logger(OpenaiRepository.name);
  private readonly basicModel = 'gpt-3.5-turbo-0125'; // Keep model names
  private readonly advancedModel = 'gpt-4-turbo'; // Keep model names
  // private axios: AxiosInstance; // Remove Axios instance

  constructor(
    configService: ConfigService,
    // private readonly brokerTransport: BrokerTransportService,
  ) {
    this.config = configService.get<OpenaiConfig>('openaiConfiguration');
    if (!this.config?.api_key) {
      this.logger.warn('OpenAI API key is not configured. OpenAI features will be disabled.');
    } else {
      const configuration = new Configuration({
        apiKey: this.config.api_key,
      });
      this.openai = new OpenAIApi(configuration);
    }
    // Remove Axios initialization
  }

  // Restore the method with SDK implementation
  async getJsonResponseFromPrompt(
    promptTemplate: string,
    data: string,
    useAdvancedModel = false,
  ): Promise<Errorable<any>> {
    // Keep Errorable return type for compatibility
    if (!this.openai) {
      this.logger.warn('OpenAI client not initialized. Cannot get JSON response.');
      return { error: 'OpenAI client not initialized' };
    }
    try {
      const model = useAdvancedModel ? this.advancedModel : this.basicModel;
      const messages: CreateChatCompletionRequest['messages'] = [
        {
          role: 'system',
          content: `You are a helpful assistant for a commerce platform. Answer questions as truthfully as possible, and if you're unsure of the answer, say "Sorry, I don't know". Respond ONLY with valid JSON.`,
        },
        { role: 'user', content: `${promptTemplate}. '${data}'` },
      ];

      const response = await this.openai.createChatCompletion({
        model,
        messages,
        temperature: 0.1,
      });

      const content = response.data.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No content received from OpenAI completion.');
      }

      // Attempt to parse the JSON response from the content string
      try {
        const jsonResponse = JSON.parse(content);
        // Return the parsed JSON, potentially wrapped if the original method returned the whole Axios response
        // For simplicity here, returning the parsed JSON directly.
        // Adjust if the original method returned more complex structure.
        return jsonResponse; // Directly return the parsed JSON object
      } catch (parseError) {
        this.logger.error('Failed to parse JSON response from OpenAI:', content, parseError);
        throw new Error('Invalid JSON received from OpenAI.');
      }
    } catch (error) {
      this.logger.error('Error getting JSON response from prompt:', error?.response?.data || error.message);
      registerErrors(error.response);
      // Match the expected error return format if possible
      return { error: error?.response?.data?.error?.message || error.message || 'Unknown OpenAI error' };
    }
  }

  async generateEmbedding(input: string, model: string = 'text-embedding-ada-002'): Promise<number[] | null> {
    if (!this.openai) {
      this.logger.warn('OpenAI client not initialized. Cannot generate embedding.');
      return null;
    }
    try {
      const request: CreateEmbeddingRequest = { model, input };
      const response = await this.openai.createEmbedding(request);
      return response.data.data[0].embedding;
    } catch (error) {
      this.logger.error('Error generating embedding:', error?.response?.data || error.message);
      registerErrors(error.response);
      // Depending on how you want to handle errors, you might throw or return null/empty array
      return null;
    }
  }

  async createChatCompletion(
    messages: CreateChatCompletionRequest['messages'],
    useAdvancedModel = false,
    options: Partial<CreateChatCompletionRequest> = {},
  ): Promise<string | null> {
    if (!this.openai) {
      this.logger.warn('OpenAI client not initialized. Cannot create chat completion.');
      return null;
    }
    try {
      const model = useAdvancedModel ? this.advancedModel : this.basicModel;
      const response = await this.openai.createChatCompletion({
        model,
        messages,
        temperature: 0.1,
        ...options, // Allow passing additional options
      });
      return response.data.choices[0]?.message?.content?.trim() || null;
    } catch (error) {
      this.logger.error('Error creating chat completion:', error?.response?.data || error.message);
      registerErrors(error.response);
      return null;
    }
  }

  async generateTextEmbedding(item: { name: string; description: string; variants?: any }): Promise<number[] | null> {
    if (!this.openai) {
      this.logger.warn('OpenAI client not initialized. Cannot generate embedding.');
      return null;
    }

    // Construct a string representation of the item for embedding
    let text = `${item.name || ''} ${item.description || ''}`;

    // Include variant information if available
    if (item.variants?.options?.length > 0) {
      text += ' Variants: ';
      for (const option of item.variants.options) {
        if (option.values) {
          const values = typeof option.values === 'object' ? Object.values(option.values).join(' ') : option.values;
          text += values + ' ';
        }
      }
    }

    text = text.trim();
    if (!text) {
      this.logger.warn('No text content available for embedding generation.');
      return null;
    }

    return this.generateEmbedding(text);
  }
}
