import { Injectable, Logger } from '@nestjs/common';
import { KoraPayConfig } from '../config/types/korapay.config';
import Axios, { AxiosInstance } from 'axios';
import { ConfigService } from '@nestjs/config';
import { Errorable } from '../utils/types';
import { registerErrors } from '../utils/errors.util';
import { extractErrorDetails } from '../utils/axios-errors';

interface Customer {
  name: string;
  email: string;
}

interface BankAccount {
  account_name: string;
  account_number: string;
  bank_name: string;
  bank_code: string;
  expiry_date_in_utc: string;
}

interface CreateKpOneTimeAccount {
  account_name: string;
  amount: number;
  currency: string;
  reference: string;
  customer: Customer;
  merchant_bears_cost: boolean;
}
interface Data {
  currency: string;
  amount: number;
  amount_expected: number;
  fee: number;
  vat: number;
  reference: string;
  payment_reference: string;
  status: string;
  narration: string;
  merchant_bears_cost: boolean;
  bank_account: BankAccount;
  customer: Customer;
}

export interface CreateKpOneTimeAccountResponse {
  status: boolean;
  message: string;
  data: Data;
}

@Injectable()
export class KoraPayRepository {
  private config: KoraPayConfig;
  private axios: AxiosInstance;

  constructor(readonly configService: ConfigService, private readonly logger: Logger) {
    this.config = configService.get<KoraPayConfig>('korapayConfiguration');
    this.axios = Axios.create({
      baseURL: 'https://api.korapay.com',
      headers: {
        Authorization: 'Bearer ' + this.config.privateKey,
      },
    });
  }

  async createOneTimeVirtualAccount(info: CreateKpOneTimeAccount): Errorable<CreateKpOneTimeAccountResponse> {
    try {
      const res = await this.axios.post<CreateKpOneTimeAccountResponse>('merchant/api/v1/charges/bank-transfer', info);

      return { data: res?.data };
    } catch (err) {
      console.log('KP_ACC_CREATION_ERROR', err);

      const error = extractErrorDetails(err);

      this.logger.log(error);
      registerErrors(error);
      return { error: error?.message };
    }
  }
}
