import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Aws from 'aws-sdk';
import sharp from 'sharp';
import { S3Config } from '../config/types/s3.config';

export enum FileTypeEnum {
  ITEMS = 'items',
  KYC = 'kyc',
  STORES = 'stores',
  HIGHLIGHTS = 'highlights',
}

@Injectable()
export class S3Repository {
  path: string = '/';
  private readonly client: Aws.S3;
  private readonly config: S3Config;

  constructor(private readonly configService: ConfigService) {
    this.config = this.configService.get<S3Config>('s3Configuration');

    this.client = new Aws.S3({
      region: this.config.S3_REGION,
      accessKeyId: this.config.S3_ACCESS_KEY,
      secretAccessKey: this.config.S3_SECRET_KEY,
    });
  }

  withPath(path: string) {
    const newS3 = new S3Repository(this.configService);
    newS3.path = path;

    return newS3;
  }

  async uploadBuffer(buf: Buffer, name: string, contentType: string, expires: Date = undefined, path?) {
    const fullPath = `${path || ''}${name}`;
    return this.client
      .upload({
        Bucket: this.config.S3_BUCKET_NAME,
        Body: buf,
        ContentType: contentType,
        Key: fullPath,
        ACL: 'public-read',
        Expires: expires,
      })
      .promise();
  }

  async uploadB64(
    file: string,
    name: string,
    expires: Date = undefined,
    path?: string,
    contentType: string = 'image/jpeg',
  ) {
    const fullPath = `${path || ''}${name}`;
    return this.client
      .upload({
        Bucket: this.config.S3_BUCKET_NAME,
        Body: Buffer.from(file, 'base64'),
        ContentType: contentType,
        Key: fullPath,
        ACL: 'public-read',
        Expires: expires,
      })
      .promise();
  }

  async upload(file: Express.Multer.File, expires: Date = undefined, path?) {
    const fullPath = `${path || ''}${file.filename}`;
    return this.client
      .upload({
        Bucket: this.config.S3_BUCKET_NAME,
        Body: file.buffer,
        ContentType: file.mimetype,
        Key: fullPath,
        ACL: 'public-read',
        Expires: expires,
      })
      .promise();
  }

  async resizeImage(imagePath: string, size: { width: number; height: number }): Promise<Buffer> {
    // const imageKey = imagePath.replace(/^.*[\\\/]/, ''); // Extract file name from path
    const imageKey = imagePath.replace(/^.*\\/, '');
    const s3Params = {
      Bucket: this.config.S3_BUCKET_NAME,
      Key: `${imageKey}`,
    };

    try {
      const imageObject = await this.client.getObject(s3Params).promise();
      const resizedImage = await sharp(imageObject.Body as Buffer)
        .resize(size.width, size.height)
        .toBuffer();

      return resizedImage;
    } catch (error) {
      console.log({ error });
      throw error;
    }
  }
}
