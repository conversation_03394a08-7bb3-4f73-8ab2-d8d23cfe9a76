import { Injectable, Logger } from '@nestjs/common';
import { ZillaConfig } from '../config/types/zilla.config';
import { ConfigService } from '@nestjs/config';
import Axios, { AxiosInstance } from 'axios';
import { registerErrors } from '../utils/errors.util';
import { ShipbubbleConfig } from '../config/types/shipbubble.config';
import { Errorable } from '../utils/types';
import { DeliveryDocument, DeliveryItem, DeliveryPackageDimensions } from '../modules/deliveries/deliveries.schema';
import { ReCaptchaConfig } from '../config/types/recaptcha.config';

export interface VerificationResponse {
  success: boolean;
  challenge_ts: Date;
  hostname: string;
  'error-codes'?: any[];
}

@Injectable()
export class ReCaptchaRepository {
  private config: ReCaptchaConfig;
  private readonly axios: AxiosInstance;

  constructor(configService: ConfigService, private readonly logger: Logger) {
    this.config = configService.get<ReCaptchaConfig>('reCaptchaConfiguration');
    this.axios = Axios.create({
      baseURL: this.config?.apiUrl,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
  }

  async verifyUserToken(token: string): Errorable<VerificationResponse> {
    try {
      const res = await this.axios.post('/siteverify', `secret=${this.config.secretKey}&response=${token}`);
      return res;
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);
      return { error: err.response?.data?.["error-codes"] };
    }
  }
}
