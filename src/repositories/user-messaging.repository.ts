import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Axios, { AxiosInstance } from 'axios';
import { BrokerTransportService } from '../broker/broker-transport.service';
import { WhatsappConfig } from '../config/types/whatsapp.config';
import { BROKER_PATTERNS } from '../enums/broker.enum';
import { HEADER_TYPES, WHATSAPP_TEMPLATES, WhatsappBusinessApiRepository } from './whatsapp.repository';

enum MESSAGE_CHANNELS {
  SMS = 'sms',
  WHATSAPP = 'whatsapp',
}

export const WHATSAPP_PHONE_IDS = {
  MAIN: process?.env.NODE_ENV === 'production' ? '104383212767626' : '110190245216781',
};

const MESSAGE_CHANNEL: MESSAGE_CHANNELS = MESSAGE_CHANNELS.WHATSAPP;

// type MessageMap<T> = {[key: string]: {T: (to: string, data:)}}

const SubscriptionExpiryTemplate = (variables: SubscriptionExpiryMessageVariables) => `
Hi ${variables.customerName}, your subscription to the ${variables.planName} plan on Catlog expired 6 days ago, we're unable to automatically renew your subscription, so please go ahead to ${process.env.CATLOG_WWW}/app/dashboard?renew_plan=true to make payments. 

Please note that if your if your subscription is not renewed by tomorrow you will be rolled back to the Starter plan.
`;

const OrderFulfilledMessageTemplate = (variables: OrderFulfilledMessageVariables) => `
Hi ${variables.customerName}, Your order ${variables.orderId} with ${variables.storeName}, has been fulfilled.
`;

const OrderCancelledMessageTemplate = (variables: OrderCancelledMessageVariables) => `
Hi ${variables.customerName}, Your order ${variables.orderId} with ${variables.storeName}, has been cancelled.
`;

const OrderProcessingMessageTemplate = (variables: OrderProcessingMessageVariables) => `
Hi ${variables.customerName}, Your order ${variables.orderId} with ${variables.storeName}, has been confirmed is now processing.
`;

const OrderCreatedMessageTemplate = (variables: OrderCreatedMessageVariables) => `
Hi ${variables.storeName}, you just received an order from ${variables.customerName}, you can check it out & confirm it here ${process.env.CATLOG_WWW}/orders/${variables.orderId}?forCustomer=true\. If ${variables.customerName} has not reached out to you yet, you can reach out to them on: ${variables.customerPhone}
`;

const OrderStatusChangedMessageTemplate = (status: string, variables: OrderStatusChangedMessageVariables) => `
Hi ${variables.customerName}, your order status just changed to *${status}*, you can check it out here ${process.env.CATLOG_WWW}/orders/${variables.orderId}?forCustomer=true\. If ${variables.storeName} has not reached out to you yet, you can reach out to them on: ${variables.storePhone}
`;

const VerificationTokenMessageTemplate = (token: string) => `
Your Catlog verification code is ${token}.
`;

const BvnVerificationMessageTemplate = (variables: BvnVerificationMessageVariables) => `
Hi ${variables.name}, your BVN verification code is ${variables.code}, please send an <NAME_EMAIL> if you did not initiate this request.
`;

type BvnVerificationMessageVariables = {
  name: string;
  code: string;
};

type SubscriptionExpiryMessageVariables = {
  customerName: string;
  planName: string;
  type: 'free_trial' | 'renewal';
};

type SignupCheckInMessageVariables = {
  customerName: string;
};

type OrderCreatedMessageVariables = {
  storeName: string;
  orderId: string;
  customerName: string;
  customerPhone: string;
};

type OrderFulfilledMessageVariables = {
  storeName: string;
  orderId: string;
  customerName: string;
};

type OrderProcessingMessageVariables = OrderFulfilledMessageVariables;
type OrderCancelledMessageVariables = OrderProcessingMessageVariables;

type OrderStatusChangedMessageVariables = {
  storeName: string;
  orderId: string;
  url: string;
  customerName: string;
  storePhone: string;
};

@Injectable()
export class UserMessagingRepository {
  private config: WhatsappConfig;
  private axios: AxiosInstance;

  constructor(
    private readonly logger: Logger,
    private readonly configService: ConfigService,
    private readonly brokerTransport: BrokerTransportService,
    private readonly whatsappBusinessApi: WhatsappBusinessApiRepository,
  ) {
    this.config = configService.get<WhatsappConfig>('whatsappConfiguration');

    this.axios = Axios.create({
      baseURL: 'https://graph.facebook.com/v17.0',
      headers: {
        Authorization: `Bearer ${this.config?.token}`,
        'Content-Type': 'application/json',
      },
    });
  }

  async sendSubscriptionExpiryMessage(
    to: string,
    variables: SubscriptionExpiryMessageVariables,
    mode: MESSAGE_CHANNELS = MESSAGE_CHANNEL,
  ) {
    const { customerName, planName } = variables;
    switch (mode) {
      case 'sms':
        this.smsSubscriptionExpiredMessage(to, variables);
        break;

      case 'whatsapp':
        this.sendSubscriptionExpiryToWhatsapp(to, variables.type, { customerName, planName });
        break;

      default:
        break;
    }
  }

  async sendOrderFulfilledMessage(
    to: string,
    variables: OrderFulfilledMessageVariables,
    mode: MESSAGE_CHANNELS = MESSAGE_CHANNEL,
  ) {
    switch (mode) {
      case 'sms':
        this.smsOrderFulfilledMessage(to, variables);
        break;

      case 'whatsapp':
        this.whatsappOrderFulfilledMessage(to, variables);
        break;

      default:
        break;
    }
  }

  async sendOrderProcessingMessage(
    to: string,
    variables: OrderProcessingMessageVariables,
    mode: MESSAGE_CHANNELS = MESSAGE_CHANNEL,
  ) {
    switch (mode) {
      case 'sms':
        this.smsOrderProcessingMessage(to, variables);
        break;

      case 'whatsapp':
        this.whatsappOrderProcessingMessage(to, variables);
        break;

      default:
        break;
    }
  }

  async sendOrderCancelledMessage(
    to: string,
    variables: OrderCancelledMessageVariables,
    mode: MESSAGE_CHANNELS = MESSAGE_CHANNEL,
  ) {
    switch (mode) {
      case 'sms':
        this.smsOrderCancelledMessage(to, variables);
        break;

      case 'whatsapp':
        this.whatsappOrderCancelledMessage(to, variables);
        break;

      default:
        break;
    }
  }

  async sendOrderCreatedMessage(
    to: string,
    variables: OrderCreatedMessageVariables,
    mode: MESSAGE_CHANNELS = MESSAGE_CHANNEL,
  ) {
    switch (mode) {
      case 'sms':
        this.smsOrderCreatedMessage(to, variables);
        break;

      case 'whatsapp':
        this.whatsappOrderCreatedMessage(to, variables);
        break;

      default:
        break;
    }
  }

  async sendOrderStatusChangedMessage(
    to: string,
    status: string,
    variables: OrderStatusChangedMessageVariables,
    mode: MESSAGE_CHANNELS = MESSAGE_CHANNEL,
  ) {
    switch (mode) {
      case 'sms':
        this.smsOrderStatusChangedMessage(to, status, variables);
        break;

      case 'whatsapp':
        this.whatsappOrderStatusChangedMessage(to, status, variables);
        break;

      default:
        break;
    }
  }

  async sendVerificationTokenMessage(to: string, token: string, mode: MESSAGE_CHANNELS = MESSAGE_CHANNEL) {
    switch (mode) {
      case 'sms':
        this.smsVerificationTokenMessage(to, token);
        break;

      case 'whatsapp':
        await this.sendTokenToWhatsapp(to, token);
        break;

      default:
        break;
    }
  }

  async sendBvnVerificationTokenMessage(
    to: string,
    data: BvnVerificationMessageVariables,
    mode: MESSAGE_CHANNELS = MESSAGE_CHANNEL,
  ) {
    switch (mode) {
      case 'sms':
        this.smsBvnVerificationTokenMessage(to, data);
        break;

      case 'whatsapp':
        // this.whatsappBvnVerificationTokenMessage(to, data);
        await this.sendTokenToWhatsapp(to, data.code);
        break;

      default:
        break;
    }
  }

  // async sendSignupCheckInMessage(
  //   to: string,
  //   variables: SignupCheckInMessageVariables,
  //   mode: MESSAGE_CHANNELS = MESSAGE_CHANNEL,
  // ) {
  //   const firstName = variables.customerName.split(' ')[0];

  //   switch (mode) {
  //     case 'whatsapp':
  //       await this.whatsappSignupCheckInMessage(to, { name: firstName });
  //     default:
  //       break;
  //   }
  // }

  private async whatsappSubscriptionExpiredMessage(to: string, variables: SubscriptionExpiryMessageVariables) {
    await this.brokerTransport
      .emit(BROKER_PATTERNS.MESSAGING.WHATSAPP, {
        type: '6c0c56a9-8d47-40bf-922b-c6e8505f5e06',
        data: {
          1: variables.customerName,
          2: variables.planName + ' plan',
          3: '6 days ago',
          4: `${process.env.CATLOG_WWW}/app/dashboard?renew_plan=true`,
          5: 'by tomorrow',
        },
        to: to,
      })
      .toPromise();
  }

  private async smsSubscriptionExpiredMessage(to: string, variables: SubscriptionExpiryMessageVariables) {
    await this.brokerTransport
      .emit(BROKER_PATTERNS.MESSAGING.SMS, {
        data: {
          sms: SubscriptionExpiryTemplate(variables),
        },
        to: to,
      })
      .toPromise();
  }

  private async whatsappOrderFulfilledMessage(to: string, variables: OrderFulfilledMessageVariables) {
    // Hi {{1}}, Your order {{2}} with {{3}}, has been fulfilled.
    await this.brokerTransport
      .emit(BROKER_PATTERNS.MESSAGING.WHATSAPP, {
        type: 'ee6cd57c-f3cd-45f2-9ce1-7de9a3e6d59d',
        data: {
          1: variables.customerName,
          2: `${process.env.CATLOG_WWW}/orders/${variables.orderId}?forCustomer=true`,
          3: variables.storeName,
        },
        to: to,
      })
      .toPromise();
  }

  private async smsOrderFulfilledMessage(to: string, variables: OrderFulfilledMessageVariables) {
    await this.brokerTransport
      .emit(BROKER_PATTERNS.MESSAGING.SMS, {
        data: {
          sms: OrderFulfilledMessageTemplate(variables),
        },
        to: to,
      })
      .toPromise();
  }

  private async whatsappOrderProcessingMessage(to: string, variables: OrderProcessingMessageVariables) {
    // Hi {{1}}, Your order {{2}} with {{3}}, has been confirmed and is now processing.
    await this.brokerTransport
      .emit(BROKER_PATTERNS.MESSAGING.WHATSAPP, {
        type: 'cbdc0e4d-ee88-4e73-9149-4243cbab1798',
        data: {
          1: variables.customerName,
          2: `${process.env.CATLOG_WWW}/orders/${variables.orderId}?forCustomer=true`,
          3: variables.storeName,
        },
        to: to,
      })
      .toPromise();
  }

  private async smsOrderProcessingMessage(to: string, variables: OrderProcessingMessageVariables) {
    await this.brokerTransport
      .emit(BROKER_PATTERNS.MESSAGING.SMS, {
        data: {
          sms: OrderProcessingMessageTemplate(variables),
        },
        to: to,
      })
      .toPromise();
  }

  private async whatsappOrderCancelledMessage(to: string, variables: OrderCancelledMessageVariables) {
    // Hi {{1}}, Your order {{2}} with {{3}}, has been cancelled.
    await this.brokerTransport
      .emit(BROKER_PATTERNS.MESSAGING.WHATSAPP, {
        type: '73f5bec5-c5cb-4378-bdd1-d9e09d65109c',
        data: {
          1: variables.customerName,
          2: `${process.env.CATLOG_WWW}/orders/${variables.orderId}?forCustomer=true`,
          3: variables.storeName,
        },
        to: to,
      })
      .toPromise();
  }

  private async smsOrderCancelledMessage(to: string, variables: OrderCancelledMessageVariables) {
    await this.brokerTransport
      .emit(BROKER_PATTERNS.MESSAGING.SMS, {
        data: {
          sms: OrderCancelledMessageTemplate(variables),
        },
        to: to,
      })
      .toPromise();
  }

  private async smsOrderCreatedMessage(to: string, variables: OrderCreatedMessageVariables) {
    await this.brokerTransport
      .emit(BROKER_PATTERNS.MESSAGING.SMS, {
        type: '6b749ee8-db04-4d16-bcbe-b0dbeda4871a',
        data: {
          1: variables.storeName,
          2: variables.customerName,
          3: `${process.env.CATLOG_WWW}/orders/${variables.orderId}?forCustomer=true`,
          4: variables.customerName,
          5: variables.customerPhone,
        },
        to: to,
      })
      .toPromise();
  }

  private async whatsappOrderCreatedMessage(to: string, variables: OrderCreatedMessageVariables) {
    await this.brokerTransport
      .emit(BROKER_PATTERNS.MESSAGING.WHATSAPP, {
        data: {
          sms: OrderCreatedMessageTemplate(variables),
        },
        to: to,
      })
      .toPromise();
  }

  private async whatsappOrderStatusChangedMessage(
    to: string,
    status: string,
    variables: OrderStatusChangedMessageVariables,
  ) {
    await this.brokerTransport
      .emit(BROKER_PATTERNS.MESSAGING.WHATSAPP, {
        type: 'a11b43d4-6b8b-4715-a0c0-fa957ec43964',
        data: {
          1: variables.customerName,
          2: status,
          3: variables.url,
          4: variables.storeName,
          5: variables.storePhone,
        },
        to: to,
      })
      .toPromise();
  }

  private async smsOrderStatusChangedMessage(
    to: string,
    status: string,
    variables: OrderStatusChangedMessageVariables,
  ) {
    await this.brokerTransport
      .emit(BROKER_PATTERNS.MESSAGING.SMS, {
        data: {
          sms: OrderStatusChangedMessageTemplate(status, variables),
        },
        to: to,
      })
      .toPromise();
  }

  private async whatsappVerificationTokenMessage(to: string, token: string) {
    await this.brokerTransport
      .emit(BROKER_PATTERNS.MESSAGING.WHATSAPP, {
        type: '2c8e8bc8-6cc3-4db1-81f7-8815fc70c030',
        data: {
          1: token,
        },
        to: to,
      })
      .toPromise();
  }

  private async sendTokenToWhatsapp(to: string, token: string) {
    return await this.whatsappBusinessApi.sendTemplatedMessage({
      from: WHATSAPP_PHONE_IDS.MAIN,
      to,
      templateName: WHATSAPP_TEMPLATES.VERIFICATION_TOKEN,
      parameters: [token],
      buttons: [{ sub_type: 'url', parameters: [token] }],
    });
  }

  private async sendSubscriptionExpiryToWhatsapp(
    to: string,
    type: 'free_trial' | 'renewal',
    data: { customerName: string; planName: string },
  ) {
    const renewalLink = 'https://app.catlog.shop/dashboard?renew=true';

    return await this.whatsappBusinessApi.sendTemplatedMessage({
      from: WHATSAPP_PHONE_IDS.MAIN,
      to,
      templateName:
        type === 'free_trial' ? WHATSAPP_TEMPLATES.FREE_TRIAL_EXPIRY : WHATSAPP_TEMPLATES.SUBSCRIPTION_EXPIRY,
      parameters: [data.customerName, data.planName, renewalLink],
      buttons: [],
    });
  }

  // private async whatsappSignupCheckInMessage(to: string, data: { name: string }) {
  //   return await this.whatsappBusinessApi.sendTemplatedMessage({
  //     from: WHATSAPP_PHONE_IDS.MAIN,
  //     to,
  //     templateName: WHATSAPP_TEMPLATES.AUTO_SIGNUP_CHECKIN_MESSAGE,
  //     parameters: [data.name],
  //     headers: [
  //       {
  //         type: HEADER_TYPES.VIDEO,
  //         data: {
  //           link: 'https://res.cloudinary.com/catlog/video/upload/v1725835035/loom-video.mp4',
  //         },
  //       },
  //     ],
  //   });
  // }

  private async smsVerificationTokenMessage(to: string, token: string) {
    await this.brokerTransport
      .emit(BROKER_PATTERNS.MESSAGING.SMS, {
        data: {
          sms: VerificationTokenMessageTemplate(token),
        },
        to: to,
      })
      .toPromise();
  }

  private async whatsappBvnVerificationTokenMessage(to: string, data: BvnVerificationMessageVariables) {
    await this.brokerTransport
      .emit(BROKER_PATTERNS.MESSAGING.WHATSAPP, {
        type: '4f527d17-f023-446b-b2c7-22eb4556ced3',
        data: {
          1: data.name,
          2: data.code,
        },
        to: to,
      })
      .toPromise();
  }

  private async smsBvnVerificationTokenMessage(to: string, data: BvnVerificationMessageVariables) {
    await this.brokerTransport
      .emit(BROKER_PATTERNS.MESSAGING.SMS, {
        data: {
          sms: BvnVerificationMessageTemplate(data),
        },
        to: to,
      })
      .toPromise();
  }
}
