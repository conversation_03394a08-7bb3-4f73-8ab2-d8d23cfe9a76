import { InjectQueue, OnQueueActive, OnQueueCompleted, OnQueueFailed, Process, Processor } from '@nestjs/bull';
import { Injectable, Logger, PreconditionFailedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { render } from '@react-email/render';
import { Job, Queue } from 'bull';
import { Resend } from 'resend';
import { ResendConfig } from '../config/types/resend.config';
import { BROKER_PATTERNS } from '../enums/broker.enum';
import { QUEUES } from '../enums/queues.enum';
import { extractErrorDetails } from '../utils/axios-errors';
import { registerErrors } from '../utils/errors.util';

interface MailJob {
  type: string;
  payload: {
    to: string;
    subject: string;
    attachment?: string;
    attachmentName?: string;
    scheduledAt?: string;
    data: Record<string, any>;
  };
}

@Injectable()
@Processor(QUEUES.MAIL)
export class ResendRepository {
  private config: ResendConfig;
  private resend: Resend;

  constructor(
    private readonly logger: Logger,
    configService: ConfigService,
    @InjectQueue(QUEUES.MAIL)
    protected readonly mailQueue: Queue,
  ) {
    this.config = configService.get<ResendConfig>('resendConfiguration');

    if (this.config.isTesting) return;
    this.resend = new Resend(this.config.apiKey);
  }

  @OnQueueActive()
  onActive(job: Job<any>) {
    console.log(`Processing job ${job.id} of type ${job.name} with data ${job.data.name}...`);
  }

  @OnQueueFailed()
  onFailed(job: Job, error: Error) {
    console.log(`Failed to process job ${job.id} of type ${job.name} with data, error: ${error.message}`);
  }

  @OnQueueCompleted()
  onCompleted(job: Job, result: any) {
    console.log(`Job ${job.id} of type ${job.name} successfully completed, result: ${result}`);
  }

  @Process(QUEUES.MAIL)
  protected async sendQueuedMail(job: Job<MailJob>) {
    const { type, payload } = job.data;

    try {
      const emailName = type.replace('MAIL.', '').toLowerCase();
      const Component = (await require(`../react-email/emails/${emailName}`))?.default;

      if (Component !== undefined) {
        const html = await render(Component(payload.data));
        // console.log(html);
        const res = await this.resend.emails.send({
          from: 'Catlog <<EMAIL>>',
          to: payload.to,
          subject: payload.subject,
          scheduledAt: payload?.scheduledAt ?? undefined,
          html: html,
        });

        return { data: res?.data };
      } else {
        throw new PreconditionFailedException('Email not found');
      }
    } catch (err) {
      const error = extractErrorDetails(err);

      this.logger.error(error);
      registerErrors(error);
      return { error: error?.message };
    }
  }

  async sendEmail(
    type: typeof BROKER_PATTERNS['MAIL'][keyof typeof BROKER_PATTERNS['MAIL']],
    payload: MailJob['payload'],
  ) {
    await this.mailQueue.add(
      QUEUES.MAIL,
      {
        type,
        payload,
      },
      { delay: 1000 },
    );
  }
}
