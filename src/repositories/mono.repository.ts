import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Axios, { AxiosInstance } from 'axios';
import { MonoConfig } from '../config/types/mono.config';
import { Kyc } from '../modules/store/kyc/kyc.schema';
import { Errorable } from '../utils/types';

export class BvnResponse {
  title: string;
  full_name: string;
  first_name: string;
  middle_name: string;
  last_name: string;
  email: string;
  gender: string;
  dob: string;
  phone: string;
  alternate_phone: string;
  avatar: string;
  country: string;
  state_of_origin: string;
  state_of_residence: string;
  lga_of_origin: string;
  lga_of_residence: string;
  address_line_1: string;
  address_line_2: string;
  address_line_3: string;
  marital_status: string;
  nin: string;
  nationality: string;
  registration_date: string;
  enrollment_bank: string;
  enrollment_branch: string;
  account_level: string;
  watchlisted: boolean;

  //new structure
  firstName: string;
  middleName: string;
  lastName: string;
  registrationDate: string;
  alternatePhone: string;
  stateOfOrigin: string;
  stateOfResidence: string;
  addressLine1: string;
  addressLine2: string;
  addressLine3: string;
  maritalStatus: string;
  watchListed: string;

  //updated structure
  phone_number: string;
  phone_number_2: string;
}

type Error = { error: string };
export type VirtualAccountResponse = {
  bank_name: string;
  bank_code: string;
  account_number: string;
  account_name: string;
  type: string;
  account_holder: string;
};

export type DirectPayInitiateResponse = {
  id: string;
  type: string;
  amount: number;
  description: string;
  reference: string;
  payment_link: string;
  created_at: Date;
  updated_at: Date;
};

export type InitiateBVNLookupResponse = {
  session_id: string;
  methods: { method: 'email' | 'phone'; hint: string }[];
};

export type BVNLookupOTPResponse = {
  message: string;
};

// export type BVNLookupOTPResponse = {
//   message: string;
// };

@Injectable()
export class MonoRepository {
  private readonly monoConfig: MonoConfig;
  private readonly axios: AxiosInstance;

  constructor(config: ConfigService, private readonly logger: Logger) {
    this.monoConfig = config.get<MonoConfig>('monoConfiguration');
    this.axios = Axios.create({
      baseURL: 'https://api.withmono.com/',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        'mono-sec-key': this.monoConfig.apikey,
      },
    });
    this.logger.setContext('mono.respository.ts');
  }

  async lookupBvn(bvn: string): Errorable<BvnResponse> {
    try {
      const { data } = await this.axios.post<{ data: BvnResponse }>('/v2/lookup/bvn', {
        bvn,
      });

      return { data: data.data };
    } catch (err) {
      return { error: err?.error?.response ?? 'true' };
    }
  }

  async createAccountHolder(document: Kyc): Errorable<string> {
    try {
      const payload = {
        entity: document.entity,
        first_name: document.first_name,
        last_name: document.last_name,
        bvn: document.bvn,
        phone: document.phone,
        address: {
          address_line1: document.address.address_line1,
          lga: document.address.lga,
          state: document.address.state,
          city: document.address.city,
        },
        identity: {
          type: document.identity.type,
          number: document.identity.number,
        },
      };

      const { data } = await this.axios.post('/issuing/v1/accountholders', payload);

      return { data: data.data.id };
    } catch (err) {
      return { error: err.error.response };
    }
  }

  async createSubAccountHolder(
    mainAccount: string,
    firstName: string,
    lastName: string,
    phone: string,
    dob: string,
  ): Errorable<string> {
    try {
      const payload = {
        main_account: mainAccount,
        first_name: firstName,
        last_name: lastName,
        phone: phone,
        dob: {
          date: dob,
        },
      };

      const { data } = await this.axios.post('/issuing/v1/accountholders', payload);

      return { data: data.data.id };
    } catch (err) {
      return { error: err.error.response };
    }
  }

  async createVirtualAccount(account: string, provider: string): Errorable<string> {
    try {
      const { data } = await this.axios.post('/issuing/v1/virtualaccounts', {
        account_holder: account,
        account_type: 'deposit',
        disposable: false,
        provider,
      });

      return { data: data.data.id };
    } catch (err) {
      return { error: err.error.response };
    }
  }

  async fetchVirtualAccount(
    account: string,
  ): Promise<
    ({ error?: string; data: VirtualAccountResponse } | { error: string; data?: VirtualAccountResponse }) & {
      status: string;
    }
  > {
    try {
      const { data } = await this.axios.get<{
        data: VirtualAccountResponse;
        status: string;
      }>('/issuing/v1/virtualaccounts/' + account);

      return { data: data.data, status: data.status };
    } catch (err) {
      return { error: err.error.response, status: '' };
    }
  }

  async initiatePayment(amount: number, reference: string, description: string): Errorable<DirectPayInitiateResponse> {
    try {
      const { data } = await this.axios.post<DirectPayInitiateResponse>('/v1/payments/initiate', {
        amount,
        reference,
        description,
        type: 'onetime-debit',
      });

      return { data };
    } catch (e) {
      return { error: e };
    }
  }

  async initiateBVNLookup(bvn: string): Errorable<InitiateBVNLookupResponse> {
    try {
      const { data } = await this.axios.post<InitiateBVNLookupResponse>('v2/lookup/bvn/initiate', {
        bvn,
      });

      return { data };
    } catch (e) {
      return { error: e };
    }
  }
}
