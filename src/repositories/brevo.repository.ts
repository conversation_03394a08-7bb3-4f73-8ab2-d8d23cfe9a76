import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { BrevoConfig } from '../config/types/brevo.config';
import { KycStatus } from '../modules/store/kyc/kyc.schema';
import * as BrevoSdk from '@getbrevo/brevo';

@Injectable()
export class BrevoRepository {
  private readonly brevoConfig: BrevoConfig;
  private readonly apiInstance: BrevoSdk.ContactsApi;
  private readonly listIds: number[];

  constructor(private readonly config: ConfigService) {
    this.brevoConfig = this.config.get<BrevoConfig>('brevoConfiguration');
    this.apiInstance = new BrevoSdk.ContactsApi();
    this.apiInstance.setApiKey(BrevoSdk.ContactsApiApiKeys.apiKey, this.brevoConfig.apiKey);

    this.listIds = [Number.parseInt(this.brevoConfig.listId)];
  }

  createUser(email: string, firstName: string, lastName: string, country: string, products: number = 0) {
    if (this.brevoConfig.isTesting) return new Promise((res) => res(undefined));

    try {
      const contact = new BrevoSdk.CreateContact();
      contact.email = email;
      contact.listIds = this.listIds;
      contact.attributes = {
        FIRSTNAME: firstName ?? '',
        LASTNAME: lastName ?? '',
        PRODUCTS_COUNT: products,
        COUNTRY: country,
      };
      return this.apiInstance.createContact(contact);
    } catch (e) {
      console.log('BREVO CREATE ERROR : ', e);
      return;
    }
  }

  addUserProducts(email: string, products: number) {
    if (this.brevoConfig.isTesting) return new Promise((res) => res(undefined));

    try {
      const update = new BrevoSdk.UpdateContact();
      update.attributes = { PRODUCTS_COUNT: products };
      update.listIds = this.listIds;
      return this.apiInstance.updateContact(encodeURI(email), update);
    } catch (e) {
      console.log('BREVO UPDATE ERROR:', e);
      return;
    }
  }

  updateUserCountry(email: string, country: string) {
    if (this.brevoConfig.isTesting) return new Promise((res) => res(undefined));

    try {
      const update = new BrevoSdk.UpdateContact();
      update.attributes = {
        COUNTRY: country,
      };
      update.listIds = this.listIds;
      return this.apiInstance.updateContact(encodeURI(email), update);
    } catch (e) {
      console.log('BREVO UPDATE ERROR:', e);
      return;
    }
  }

  generalUpdate(
    email: string,
    data: { FIRSTNAME?: string; LASTNAME?: string; COUNTRY?: string; KYCSTATUS?: string; PRODUCTS_COUNT?: number },
  ) {
    if (this.brevoConfig.isTesting) return new Promise((res) => res(undefined));

    try {
      const update = new BrevoSdk.UpdateContact();
      update.attributes = {
        ...data,
      };
      update.listIds = this.listIds;
      return this.apiInstance.updateContact(encodeURI(email), update);
    } catch (e) {
      console.log('BREVO UPDATE ERROR:', e);
      return;
    }
  }

  kycStatusUpdate(email: string, status: KycStatus) {
    try {
      const update = new BrevoSdk.UpdateContact();
      update.listIds = this.listIds;
      update.attributes = {
        KYC_STATUS: status,
      };
      return this.apiInstance.updateContact(encodeURI(email), update);
    } catch (e) {
      console.log('BREVO UPDATE ERROR:', e);
      return;
    }
  }

  updateEmail(oldEmail: string, newEmail: string) {
    if (this.brevoConfig.isTesting) return new Promise((res) => res(undefined));
    try {
      const update = new BrevoSdk.UpdateContact();
      update.listIds = this.listIds;
      update.attributes = {
        EMAIL: newEmail,
      };
      return this.apiInstance.updateContact(encodeURI(oldEmail), update);
    } catch (e) {
      console.log('BREVO UPDATE ERROR:', e);
      return;
    }
  }

  async userExists(email: string): Promise<any> {
    if (this.brevoConfig.isTesting) return new Promise((res) => res(undefined));

    try {
      const res = await this.apiInstance.getContactInfo(encodeURI(email));
      return res;
    } catch (e) {
      return false;
    }
  }
}
