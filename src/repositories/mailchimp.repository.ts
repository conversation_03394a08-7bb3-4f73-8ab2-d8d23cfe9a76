import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import mailchimpClient from '@mailchimp/mailchimp_marketing';
import { MailchimpConfig } from '../config/types/mailchimp.config';
import md5 from 'md5';
import { isProduction } from '../utils/index';
import { KycStatus } from '../modules/store/kyc/kyc.schema';

interface IAddListMember {
  email_address: string;
  merge_fields: {
    FNAME: string;
    LNAME: string;
    DJOINED: any;
  };
}

export interface MailchimpUser extends mailchimpClient.MembersSuccessResponse {}

@Injectable()
export class MailchimpRepository {
  private readonly mailchimpConfig: MailchimpConfig;
  private readonly client = mailchimpClient;

  constructor(private readonly config: ConfigService) {
    this.mailchimpConfig = config.get<MailchimpConfig>('mailchimpConfiguration');
    this.client.setConfig({
      apiKey: this.mailchimpConfig.apiKey,
      server: this.mailchimpConfig.server,
    });
  }

  createUser(email: string, firstName: string, lastName: string, country: string, products: number = 0) {
    if (this.mailchimpConfig.isTesting) return new Promise((res) => res(undefined));

    try {
      return this.client.lists.addListMember(this.mailchimpConfig.listId, {
        email_address: email,
        merge_fields: {
          FNAME: firstName || 'None', // idk, just in case
          LNAME: lastName || 'None',
          PRODUCTS: products,
          COUNTRY: country,
        },
        tags: ['signed up', 'customer', isProduction() ? 'production' : 'development'],
        status: 'subscribed',
      });
    } catch (e) {
      console.log('MAILCHIMP CREATE ERROR : ', e);
      return;
    }
  }

  addUserProducts(email: string, products: number) {
    if (this.mailchimpConfig.isTesting) return new Promise((res) => res(undefined));

    try {
      return this.client.lists.updateListMember(this.mailchimpConfig.listId, md5(email), {
        merge_fields: {
          PRODUCTS: products,
        },
      });
    } catch (e) {
      console.log('error:', e);
      return;
    }
  }

  updateUserCountry(email: string, country: string) {
    if (this.mailchimpConfig.isTesting) return new Promise((res) => res(undefined));

    try {
      return this.client.lists.updateListMember(this.mailchimpConfig.listId, md5(email), {
        merge_fields: {
          COUNTRY: country,
        },
      });
    } catch (e) {
      console.log('error:', e);
      return;
    }
  }

  generalUpdate(email: string, data: { COUNTRY?: string; KYCSTATUS?: string; PRODUCTS?: number }) {
    if (this.mailchimpConfig.isTesting) return new Promise((res) => res(undefined));

    try {
      return this.client.lists.updateListMember(this.mailchimpConfig.listId, md5(email), {
        merge_fields: {
          ...data,
        },
        status: 'subscribed',
      });
    } catch (e) {
      console.log('error:', e);
      return;
    }
  }

  kycStatusUpdate(email: string, status: KycStatus) {
    try {
      return this.client.lists.updateListMember(this.mailchimpConfig.listId, md5(email), {
        merge_fields: {
          KYCSTATUS: status,
        },
      });
    } catch (e) {
      console.log('error:', e);
      return;
    }
  }

  updateEmail(oldEmail: string, newEmail: string) {
    if (this.mailchimpConfig.isTesting) return new Promise((res) => res(undefined));

    return this.client.lists.updateListMember(this.mailchimpConfig.listId, md5(oldEmail), {
      email_address: newEmail,
    });
  }

  async userExists(email: string): Promise<boolean | mailchimpClient.MembersSuccessResponse | undefined> {
    if (this.mailchimpConfig.isTesting) return new Promise((res) => res(undefined));

    try {
      const r = (await this.client.lists.getListMember(
        this.mailchimpConfig.listId,
        md5(email),
      )) as mailchimpClient.MembersSuccessResponse;
      return r;
    } catch (e) {
      console.log('MAILCHIMP LOOKUP ERROR: ', e);
      return false;
    }
  }

  updateTags(email: string) {
    if (this.mailchimpConfig.isTesting) return new Promise((res) => res(undefined));

    try {
      return this.client.lists.updateListMemberTags(this.mailchimpConfig.listId, md5(email), {
        tags: [
          {
            name: 'production',
            status: isProduction() ? 'active' : 'inactive',
          },
          {
            name: 'development',
            status: !isProduction() ? 'active' : 'inactive',
          },
          {
            name: 'signed up',
            status: 'inactive',
          },
          {
            name: 'customer',
            status: 'inactive',
          },
        ],
      });
    } catch (e) {
      console.log('error:', e);
    }
  }
}
