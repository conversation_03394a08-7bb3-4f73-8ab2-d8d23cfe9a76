import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PaystackConfig } from '../config/types/paystack.config';
import Axios, { AxiosInstance } from 'axios';
import { registerErrors } from '../utils/errors.util';
import { Errorable } from '../utils/types';
import { COUNTRY_CODE, CURRENCIES } from '../modules/country/country.schema';
import { extractErrorDetails } from '../utils/axios-errors';

export interface PaystackVerificationStatus {
  status: boolean;
  message: string;
  data: TransactionData;
}

interface TransactionData {
  id: number;
  domain: string;
  status: string;
  reference: string;
  receipt_number: string | null;
  amount: number;
  message: string | null;
  gateway_response: string;
  paid_at: string;
  created_at: string;
  channel: string;
  currency: string;
  ip_address: string;
  metadata: any;
  log: Log;
  fees: number;
  fees_split: any;
  authorization: Authorization;
  customer: Customer;
  plan: any;
  split: any;
  order_id: string | null;
  paidAt: string;
  createdAt: string;
  requested_amount: number;
  pos_transaction_data: any;
  source: any;
  fees_breakdown: any;
  connect: any;
  transaction_date: string;
  plan_object: any;
  subaccount: any;
}

interface Log {
  start_time: number;
  time_spent: number;
  attempts: number;
  errors: number;
  success: boolean;
  mobile: boolean;
  input: any[];
  history: LogHistory[];
}

interface LogHistory {
  type: string;
  message: string;
  time: number;
}

interface Authorization {
  authorization_code: string;
  bin: string;
  last4: string;
  exp_month: string;
  exp_year: string;
  channel: string;
  card_type: string;
  bank: string;
  country_code: string;
  brand: string;
  reusable: boolean;
  signature: string;
  account_name: string | null;
}

interface Customer {
  id: number;
  first_name: string | null;
  last_name: string | null;
  email: string;
  customer_code: string;
  phone: string | null;
  metadata: any;
  risk_action: string;
  international_format_phone: string | null;
}

interface IChargeAuthorization {
  amount: number;
  email: string;
  authorization_code: string;
  reference: string;
  currency: CURRENCIES;
}

type Response<T> = { data: T };

export interface ITransferResponse {
  reference: string;
  integration: number;
  domain: string;
  amount: number;
  currency: string;
  source: string;
  reason: string;
  recipient: number;
  status: string;
  transfer_code: string;
  id: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface IPaystackBank {
  name: string;
  slug: string;
  code: string;
  longcode: string;
  gateway?: any;
  pay_with_bank: boolean;
  active: boolean;
  is_deleted?: any;
  country: string;
  currency: string;
  type: string;
  id: number;
  image?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IResolveAccountResponse {
  account_number: string;
  account_name: string;
  bank_id: number;
}

@Injectable()
export class PaystackRepository {
  private readonly paystackConfig: PaystackConfig;
  private readonly axios: (currency: CURRENCIES) => AxiosInstance;
  constructor(private readonly logger: Logger, configService: ConfigService) {
    this.paystackConfig = configService.get<PaystackConfig>('paystackConfiguration');

    this.axios = (currency: CURRENCIES) =>
      Axios.create({
        baseURL: 'https://api.paystack.co',
        headers: {
          Authorization: `Bearer ${this.getPrivateKey(currency) ?? this.paystackConfig.privateKey}`,
          'Content-Type': 'application/json',
        },
      });
  }

  async verifyTransaction(reference: string, currency: CURRENCIES): Errorable<TransactionData> {
    try {
      const {
        data: { data },
      } = await this.axios(currency).get(`/transaction/verify/${reference}`);

      return { data };
    } catch (err) {
      this.logger.log(err.data, 'paystack verification failed');
      const error = extractErrorDetails(err);
      return { error: error.message };
    }
  }

  async resolveAccountNumber(
    account_number: string,
    bank_code: string,
    currency: CURRENCIES,
  ): Errorable<IResolveAccountResponse> {
    try {
      const { data } = await this.axios(currency).get<Response<IResolveAccountResponse>>(
        `/bank/resolve?account_number=${account_number}&bank_code=${bank_code}`,
      );
      return { data: data.data };
    } catch (err) {
      this.logger.log(err.data, 'paystack account lookup failed');
      const error = extractErrorDetails(err);
      return { error: error.message };
    }
  }

  async getBanks(currency: CURRENCIES = CURRENCIES.NGN): Errorable<IPaystackBank[]> {
    try {
      let query = `?currency=${currency}`;

      if (currency === CURRENCIES?.GHC || currency === CURRENCIES?.KES) {
        query += `&type=mobile_money`;
      }

      const { data } = await this.axios(currency).get<Response<IPaystackBank[]>>('/bank' + query);
      return { data: data.data };
    } catch (err) {
      this.logger.log(err.data, 'paystack get banks failed');
      const error = extractErrorDetails(err);
      return { error: error.message };
    }
  }

  async createRecipient(
    account_name: string,
    account_number: string,
    bank_code: string,
    currency: CURRENCIES = CURRENCIES.NGN,
  ): Errorable<string> {
    try {
      const { data } = await this.axios(currency).post('/transferrecipient', {
        type: this.getRecipientType(currency),
        name: account_name,
        account_number: account_number,
        bank_code: bank_code,
        currency: currency,
      });

      return { data: data.data.recipient_code };
    } catch (err) {
      this.logger.log(err.data, 'paystack create recipients failed');
      const error = extractErrorDetails(err);
      return { error: error.message };
    }
  }

  async singleTransfer(
    amount: number,
    recipient_code: string,
    reason: string,
    currency: CURRENCIES = CURRENCIES.NGN,
    ref: string,
  ): Errorable<ITransferResponse> {
    try {
      const { data } = await this.axios(currency).post<Response<ITransferResponse>>('/transfer', {
        source: 'balance',
        reason: reason,
        amount: amount,
        recipient: recipient_code,
        reference: ref,
      });

      this.logger.log(data, 'PAYSTACK SINGLE TRANSFER DATA');

      return { data: data.data };
    } catch (err) {
      this.logger.log(err, 'PAYSTACK SINGLE TRANSFER ERROR');
      this.logger.log(err?.response, 'paystack single transfer failed');
      const error = extractErrorDetails(err);
      return { error: error.message, status: err?.response?.status || 500 };
    }
  }

  async chargeAuthorization(payload: IChargeAuthorization, currency: CURRENCIES) {
    try {
      const {
        data: { data },
      } = await this.axios(currency).post('/transaction/charge_authorization', payload);
      if (data.status === 'success') {
        return data;
      }

      this.logger.warn({ tile: 'Paystack charge failed,', ...data });

      return null;
    } catch (err) {
      // this.logger.error(err.response.data);
      const error = extractErrorDetails(err);

      this.logger.error(error);
      registerErrors(err);
      return false;
    }
  }

  getPrivateKey(currency: CURRENCIES) {
    switch (currency) {
      case CURRENCIES.NGN:
        return this.paystackConfig.privateKey;
      case CURRENCIES.GHC:
        return this.paystackConfig.ghPrivatekey;
      case CURRENCIES.ZAR:
        return this.paystackConfig.zaPrivatekey;
      default:
        return this.paystackConfig.privateKey;
    }
  }

  getPublicKey(currency: CURRENCIES) {
    switch (currency) {
      case CURRENCIES.NGN:
        return this.paystackConfig.publicKey;
      case CURRENCIES.GHC:
        return this.paystackConfig.ghPublickey;
      case CURRENCIES.ZAR:
        return this.paystackConfig.zaPublickey;
      default:
        return this.paystackConfig.publicKey;
    }
  }

  getRecipientType(currency: CURRENCIES) {
    switch (currency) {
      case CURRENCIES.NGN:
        return 'nuban';
      case CURRENCIES.GHC:
        return 'mobile_money';
      case CURRENCIES.ZAR:
        return 'basa';
      case CURRENCIES.KES:
        return 'mobile_money';
      default:
        return 'nuban';
    }
  }
}
