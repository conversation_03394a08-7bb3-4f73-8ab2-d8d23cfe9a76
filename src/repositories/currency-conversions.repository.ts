import { Injectable, Logger } from '@nestjs/common';
import Axios, { AxiosInstance } from 'axios';
import { Errorable } from '../utils/types';
import { CURRENCIES } from '../modules/country/country.schema';
import { OrderItem } from '../modules/orders/order.schema';
import { BrokerTransportService } from '../broker/broker-transport.service';
import { getExchangeRates } from '../utils/functions';
import { CurrencyRates } from '../modules/wallets/currency-conversion/currency-rate.schema';
import { BROKER_PATTERNS } from '../enums/broker.enum';

@Injectable()
export class CurrencyConversionRepository {
  constructor(private readonly brokerTransport: BrokerTransportService) {}

  convertToCurrency(
    amount: number,
    currency: CURRENCIES,
    rates: { [key: string]: number },
    markups: { [key: string]: number },
  ): number {
    const conversionRate = rates && rates[currency] !== undefined ? rates[currency] : 1;
    const markup = markups?.[currency] || 0;

    // Scale calculations to avoid floating-point issues
    const scale = 100;

    // Calculate the amount with markup
    const amountWithMarkup = Math.round(amount * (1 + markup / 100) * scale);
    const convertedAmount = Math.round(amountWithMarkup * conversionRate) / scale;

    return convertedAmount;
  }

  convertItemsToCurrency(items: OrderItem[], convertCurrency: (amount: number) => number) {
    return items.map((i) => {
      return {
        ...i,
        snapshot: {
          ...i.snapshot,
          price: convertCurrency(i.snapshot.price),
          discount_price: i.snapshot.discount_price ? convertCurrency(i.snapshot.discount_price) : undefined,
        },
        variant: i?.variant
          ? {
              ...i.variant,
              price: convertCurrency(i?.variant?.price),
              discount_price: i?.variant?.discount_price ? convertCurrency(i?.variant?.discount_price) : undefined,
            }
          : undefined,
        // variant: i.var
      };
    });
  }

  async getExchangeRates(ratesId: string, currencyOptions: CURRENCIES[], defaultCurrency: CURRENCIES) {
    const hasMultipleCurrencies =
      currencyOptions.length > 1 || (currencyOptions.length === 1 && currencyOptions[0] !== defaultCurrency);

    const rates = hasMultipleCurrencies
      ? ratesId
        ? await this.brokerTransport.send<CurrencyRates>(BROKER_PATTERNS.WALLET.GET_EXCHANGE_RATE, ratesId).toPromise()
        : await this.brokerTransport.send<CurrencyRates>(BROKER_PATTERNS.WALLET.GET_EXCHANGE_RATES, {}).toPromise()
      : { rates: { [defaultCurrency]: 1 } };

    if (!rates) return null;

    const exchangeRates = getExchangeRates(rates.rates, defaultCurrency, currencyOptions);

    return exchangeRates;
  }
}
