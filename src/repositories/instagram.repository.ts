import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { default as Axios, AxiosInstance, default as axios } from 'axios';
import { BrokerTransportService } from '../broker/broker-transport.service';
import { InstagramConfig } from '../config/types/instagram.config';
import { registerErrors } from '../utils/errors.util';
import { paramsFromObject } from '../utils/functions';
import { Errorable } from '../utils/types';
import { extractErrorDetails } from '../utils/axios-errors';

export interface InstagramMedia {
  id: string;
  media_type: string;
  media_url: string;
  permalink?: string;
  caption?: string;
  thumbnail_url?: string;
}

export interface MediaItem {
  id: string;
  media_type: 'IMAGE' | 'VIDEO';
  media_url: string;
  thumbnail_url?: string;
  post_id?: string;
}

export interface MediaPickerResponse {
  images: any[];
  videos: any[];
  paging: any;
}

@Injectable()
export class InstagramRepository {
  private config: InstagramConfig;
  private readonly axios: AxiosInstance;

  constructor(
    private readonly logger: Logger,
    configService: ConfigService,
    private readonly brokerTransport: BrokerTransportService,
  ) {
    this.config = configService.get<InstagramConfig>('instagramConfiguration');
    this.axios = Axios.create({
      baseURL: 'https://graph.instagram.com/',
    });
  }

  async getAccessToken(access_code: string, redirect_uri: string): Errorable<any> {
    try {
      const data = {
        client_id: this.config.app_key,
        client_secret: this.config.app_secret,
        grant_type: 'authorization_code',
        redirect_uri,
        code: access_code,
      };

      const form = new URLSearchParams();
      Object.keys(data).forEach((k) => form.append(k, data[k]));

      const shortLivedTokenRes = await axios.post('https://api.instagram.com/oauth/access_token', form, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      });

      const params = paramsFromObject({
        grant_type: 'ig_exchange_token',
        client_secret: this.config.app_secret,
        access_token: shortLivedTokenRes.data?.access_token,
      });

      const res = await this.axios.get(`access_token?${params}`);

      return res;
    } catch (err) {
      return { error: err.response?.data?.error_message };
    }
  }

  async refreshLongLivedAccessToken(access_token: string): Errorable<any> {
    try {
      const params = paramsFromObject({
        grant_type: 'ig_refresh_token',
        access_token,
      });

      const res = await this.axios.get(`refresh_access_token?${params}`);
      return res;
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);
      return { error: err.response?.data?.error_message };
    }
  }

  async getMedia(payload: {
    access_token: string;
    pagination?: {
      next?: string;
      previous?: string;
      limit?: number;
    };
  }) {
    try {
      const params = paramsFromObject({
        fields: 'id,caption,media_type,media_url,thumbnail_url,permalink,children',
        access_token: payload.access_token,
        before: payload.pagination?.previous,
        after: payload.pagination?.next,
        limit: payload.pagination?.limit,
      });

      const res = await this.axios.get(`me/media?${params}`);
      return res.data;
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);
      return { error: err.response?.data?.error_message };
    }
  }

  async getAlbumMedia(payload: { access_token: string; media_id: string }): Errorable<any> {
    try {
      const params = paramsFromObject({
        fields: 'id,media_type,media_url,thumbnail_url',
        access_token: payload.access_token,
      });

      const res = await this.axios.get(`${payload.media_id}/children?${params}`);
      return res.data;
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);
      return { error: err.response?.data?.error_message };
    }
  }

  async getInstagramUser(access_token: string): Errorable<any> {
    try {
      const params = paramsFromObject({
        fields: 'id,media_count,username,account_type',
        access_token: access_token,
      });

      const res = await this.axios.get(`me?${params}`);
      return res.data;
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);
      return { error: err.response?.data?.error_message };
    }
  }

  async getAllMediaFromPosts(payload: {
    access_token: string;
    pagination?: {
      next?: string;
      previous?: string;
      limit?: number;
    };
  }): Errorable<MediaPickerResponse> {
    try {
      const postsResponse = await this.getMedia(payload);

      if (postsResponse.error) {
        return { error: postsResponse.error };
      }

      const posts = postsResponse.data || [];
      const paging = postsResponse?.paging;
      const allMedia: MediaItem[] = [];

      for (const post of posts) {
        if (post.media_type === 'CAROUSEL_ALBUM') {
          const albumResponse = await this.getAlbumMedia({
            access_token: payload.access_token,
            media_id: post.id,
          });

          if (!albumResponse.error && albumResponse.data) {
            const albumData = albumResponse.data as any;
            const albumMediaItems = albumData?.data || [];

            const albumMedia = albumMediaItems
              .filter((media: any) => media.media_type === 'IMAGE' || media.media_type === 'VIDEO')
              .map((media: any) => ({
                id: media.id,
                media_type: media.media_type as 'IMAGE' | 'VIDEO',
                media_url: media.media_url,
                thumbnail_url: media.thumbnail_url,
                post_id: post.id,
              }));

            allMedia.push(...albumMedia);
          }
        } else if (post.media_type === 'IMAGE' || post.media_type === 'VIDEO') {
          allMedia.push({
            id: post.id,
            media_type: post.media_type as 'IMAGE' | 'VIDEO',
            media_url: post.media_url,
            thumbnail_url: post.thumbnail_url,
            post_id: post.id,
          });
        }
      }

      const images = allMedia
        .filter((media) => media.media_type === 'IMAGE')
        .map((m) => ({ media_url: m.media_url, thumbnail: m.media_url }));
      const videos = allMedia
        .filter((media) => media.media_type === 'VIDEO')
        .map((m) => ({ media_url: m.media_url, thumbnail: m.thumbnail_url }));

      const response: MediaPickerResponse = {
        images,
        videos,
        paging,
      };

      return { data: response };
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);
      return { error: err.response?.data?.error_message || 'Failed to fetch media from posts' };
    }
  }
}
