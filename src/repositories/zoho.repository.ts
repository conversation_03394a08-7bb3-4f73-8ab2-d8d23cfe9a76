import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Axios, { AxiosInstance } from 'axios';
import { registerErrors } from '../utils/errors.util';
import { extractErrorDetails } from '../utils/axios-errors';
import { CacheService } from '../modules/shared/cache/cache.service';
import { ONE_HOUR, ONE_MINUTE_IN_SECONDS } from '../utils/constants';
import { ZohoConfig } from '../config/types/zoho.config';
import { PLAN_TYPE, PLANS } from '../enums/plan.enum';
import { COUNTRY_CODE } from '../modules/country/country.schema';
import { Errorable } from '../utils/types';
import { getAppEnv } from '../utils';
import { SUBSCRIPTION_STATUS } from '../enums/payment.enum';
import dayjs from 'dayjs';
import { BrokerTransportService } from '../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../enums/broker.enum';
import { BUSINESS_TYPES } from '../modules/store/store.schema';

interface BiginContactParams {
  First_Name: string;
  Last_Name: string;
  Email: string;
  Phone: string;
  Store_Name: string;
  Store_Link: string;
  Selected_Plan: {
    id: string;
  };
  Payment_Due_Date: string;
  Signup_Date: string;
  Country: string;
  Business_Type: BUSINESS_TYPES;
  Business_Category: string;
  Monthly_Orders: string;
  No_of_Payments: number;
  Total_Payments: number;
  On_Free_Trial: string;
  Subscription_Status: string;
  User_Currency: string;
  Has_Taken_Order_With_Payment?: string;
}

interface BiginPipelineParams {
  User_Name?: string;
  Amount?: number;
  Owner?: {
    id: string;
  };
  Sub_Pipeline?: string;
  Stage?: string;
  Country?: string;
  Contact_Name?: {
    id: string;
  };
  Closing_Date?: string;
  Note?: string;
  Store_Link?: string;
  Selected_Plan?: {
    id: string;
  };
  Signup_Date?: string;
  Store_Name?: string;
  Created_Time?: string;
  No_Of_Payments?: number;
  Total_Payments?: number;
  User_Currency?: string;
  Business_Type?: string;
  Business_Category?: string;
  Monthly_Orders?: string;
  Modified_Time?: string;
  On_Free_Trial?: string;
  Subscription_Status?: string;
  Last_Activity_Time?: string;
  Pipeline?: {
    id: string;
  };
  Currency?: string;
  Payments_Made?: number;
  Deal_Name?: string;
  Phone?: string;
  Whatsapp_Link?: string;
  Email?: string;
  Has_Taken_Order_With_Payment?: string;
}

@Injectable()
export class ZohoRepository {
  private config: ZohoConfig;
  private readonly axios: AxiosInstance;
  private readonly tokenCacheKey = 'zoho_access_token'; // Key to store the token in Redis

  constructor(
    configService: ConfigService,
    private readonly logger: Logger,
    private readonly cacheService: CacheService, // Inject the cache service
    private readonly brokerTransport: BrokerTransportService,
  ) {
    this.config = configService.get<ZohoConfig>('zohoConfiguration');
    this.axios = Axios.create({
      baseURL: 'https://www.zohoapis.com/bigin/v2',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Axios request interceptor to attach the auth token to each request
    this.axios.interceptors.request.use(
      async (config) => {
        const authToken = await this.getCachedOrNewToken();
        config.headers['Authorization'] = `Zoho-oauthtoken ${authToken}`;
        return config;
      },
      (error) => {
        return Promise.reject(error);
      },
    );
  }

  // Method to authenticate and get a new auth token
  async getAccessToken(): Promise<any> {
    const payload = {
      refresh_token: this.config.refreshToken,
      client_id: this.config.clientId,
      client_secret: this.config.clientSecret,
      grant_type: 'refresh_token',
    };

    try {
      const res = await Axios.post(
        `https://accounts.zoho.com/oauth/v2/token`,
        {},
        {
          params: payload,
        },
      );
      return res.data;
    } catch (err) {
      const error = extractErrorDetails(err);
      this.logger.log(error);
      registerErrors(error);
      throw new Error(error?.message);
    }
  }

  // Method to get token from cache or fetch a new one
  async getCachedOrNewToken(): Promise<string> {
    // Check if the token exists in the cache
    const cachedToken = await this.cacheService.get<string>(this.tokenCacheKey);

    if (cachedToken) {
      return cachedToken;
    }

    // If no cached token, fetch a new one
    const authResponse = await this.getAccessToken();
    const authToken = authResponse?.access_token;

    // Set a fixed TTL of 1 hours (in seconds)
    const ttl = ONE_MINUTE_IN_SECONDS * 45; // 45 minutes in seconds

    // Set the token in Redis with TTL
    await this.cacheService.setWithLock(this.tokenCacheKey, authToken, ttl);

    return authToken;
  }

  async createOrUpdateContact(
    contact: BiginContactParams,
    meta: {
      status: SUBSCRIPTION_STATUS;
      plan: PLAN_TYPE;
      planPrice: number;
      userId: string;
      userJustPaid: boolean;
      paidUpfront: boolean;
      biginContactId: string;
    },
  ): Promise<Errorable<any>> {
    if (getAppEnv() !== 'production') return { error: 'Not in production' };

    try {
      const lastName = !!contact?.Last_Name ? contact.Last_Name : '(No Last Name)';
      const contactId = meta.biginContactId;
      let res;

      console.log('Contact ID', contactId);
      console.log('Email', contact.Email);

      if (contactId) {
        res = await this.axios.put(`/Contacts/${contactId}`, {
          data: [
            {
              ...contact,
              Owner: { id: countryUsers[contact.Country] },
              Last_Name: lastName,
            },
          ],
        });
      } else {
        res = await this.axios.post('/Contacts/upsert', {
          data: [
            {
              ...contact,
              Owner: { id: countryUsers[contact.Country] },
              Last_Name: lastName,
              duplicate_check_fields: ['Email'],
            },
          ],
        });
      }

      const data = res?.data?.data?.[0];

      if (data?.details?.id) {
        const matchingPipelineDeals = await this.axios.get(
          '/Pipelines/search?criteria=(Contact_Name.id:equals:' + data.details.id + ')',
        );
        const deal = matchingPipelineDeals?.data?.data?.[0];

        const dealStage = deal ? deal?.Stage : null;
        let pipelineStage = null;

        if (meta.paidUpfront) {
          pipelineStage = PipelineStages.PAID_UPFRONT;
        } else if (meta.userJustPaid) {
          pipelineStage = PipelineStages.ACTIVE_SUBSCRIBER;
        } else if (
          (dealStage && dealStage === PipelineStages.UNQUALIFIED_USER) ||
          (!dealStage &&
            [BUSINESS_TYPES.DIGITAL, BUSINESS_TYPES.SERVICE, BUSINESS_TYPES.PROPERTIES].includes(contact.Business_Type)) //automatically move user to unqualified if they don't sell physical products
        ) {
          console.log("Stopped Update because deal is in 'Unqualified User' stage");
          pipelineStage = PipelineStages.UNQUALIFIED_USER;
        } else if (
          meta.status !== SUBSCRIPTION_STATUS.IN_ACTIVE &&
          (dealStage === PipelineStages.NEGOTIATION || dealStage === PipelineStages.QUALIFIED_FREE_TRIAL)
        ) {
          pipelineStage = dealStage;
        } else {
          pipelineStage = getPiplelineStage(meta.status, meta.plan, contact.No_of_Payments, contact.Payment_Due_Date);
        }

        const piplelineData: BiginPipelineParams = {
          User_Name: contact.First_Name + ' ' + contact.Last_Name,
          Deal_Name: contact.First_Name + ' ' + contact.Last_Name,
          Amount: meta.planPrice,
          Owner: { id: countryUsers[contact.Country] },
          Sub_Pipeline: 'Onboarding Pipeline Standard',
          Stage: pipelineStage,
          Country: contact.Country,
          Contact_Name: { id: data.details.id },
          Closing_Date: contact.Payment_Due_Date,
          Store_Link: contact.Store_Link,
          Selected_Plan: { id: contact.Selected_Plan.id },
          Store_Name: contact.Store_Name,
          Created_Time: contact.Signup_Date,
          No_Of_Payments: contact.No_of_Payments,
          Total_Payments: contact.Total_Payments,
          User_Currency: contact.User_Currency,
          Business_Type: contact.Business_Type,
          Business_Category: contact.Business_Category,
          Monthly_Orders: contact.Monthly_Orders,
          Modified_Time: contact.Signup_Date,
          On_Free_Trial: contact.On_Free_Trial,
          Subscription_Status: contact.Subscription_Status,
          Last_Activity_Time: contact.Signup_Date,
          Pipeline: { id: ONBOARDING_PIPELINE_ID },
          Currency: contact.User_Currency,
          Payments_Made: contact.Total_Payments,
          Signup_Date: contact.Signup_Date,
          Phone: contact.Phone,
          Whatsapp_Link: `https://wa.me/${contact.Phone.replace('+', '')}`,
          Email: contact.Email,
          Has_Taken_Order_With_Payment: contact.Has_Taken_Order_With_Payment,
        };

        const piplelineRes = await this.axios.post('/Pipelines/upsert', {
          data: [{ ...piplelineData, duplicate_check_fields: ['Contact_Name'] }],
        });

        this.updateUserRecord(meta.userId, data.details.id);
      }

      return { error: null, data: res.data };
    } catch (err) {
      const error = extractErrorDetails(err);
      this.logger.error(error);
      registerErrors(error);
      return {
        error:
          error?.details?.data?.description ?? error?.details?.message ?? error?.message ?? 'Something went wrong!',
      };
    }
  }

  async updateUserRecord(userId, recordId) {
    await this.brokerTransport.send(BROKER_PATTERNS.PAYMENT.UPDATE_BIGIN_RECORD_ID, { userId, recordId }).toPromise();
  }
}

export const planProductMap = {
  [PLANS.BASIC]: {
    Monthly: {
      [COUNTRY_CODE.GH]: 'BA_MO_GH',
      [COUNTRY_CODE.NG]: 'BA_MO_NG',
      [COUNTRY_CODE.KE]: 'BA_MO_KE',
      [COUNTRY_CODE.ZA]: 'BA_MO_ZA',
    },
    Quaterly: {
      [COUNTRY_CODE.GH]: 'BA_QA_GH',
      [COUNTRY_CODE.NG]: 'BA_QA_NG',
      [COUNTRY_CODE.KE]: 'BA_QA_KE',
      [COUNTRY_CODE.ZA]: 'BA_QA_ZA',
    },
    'Bi-Annually': {
      [COUNTRY_CODE.GH]: 'BA_BA_GH',
      [COUNTRY_CODE.NG]: 'BA_BA_NG',
      [COUNTRY_CODE.KE]: 'BA_BA_KE',
      [COUNTRY_CODE.ZA]: 'BA_BA_ZA',
    },
    Yearly: {
      [COUNTRY_CODE.GH]: 'BA_YR_GH',
      [COUNTRY_CODE.NG]: 'BA_YR_NG',
      [COUNTRY_CODE.KE]: 'BA_YR_KE',
      [COUNTRY_CODE.ZA]: 'BA_YR_ZA',
    },
  },
  [PLANS.BUSINESS_PLUS]: {
    Monthly: {
      [COUNTRY_CODE.GH]: 'BU_MO_GH',
      [COUNTRY_CODE.NG]: 'BU_MO_NG',
      [COUNTRY_CODE.KE]: 'BU_MO_KE',
      [COUNTRY_CODE.ZA]: 'BU_MO_ZA',
    },
    Quaterly: {
      [COUNTRY_CODE.GH]: 'BU_QA_GH',
      [COUNTRY_CODE.NG]: 'BU_QA_NG',
      [COUNTRY_CODE.KE]: 'BU_QA_KE',
      [COUNTRY_CODE.ZA]: 'BU_QA_ZA',
    },
    'Bi-Annually': {
      [COUNTRY_CODE.GH]: 'BU_BA_GH',
      [COUNTRY_CODE.NG]: 'BU_BA_NG',
      [COUNTRY_CODE.KE]: 'BU_BA_KE',
      [COUNTRY_CODE.ZA]: 'BU_BA_ZA',
    },
    Yearly: {
      [COUNTRY_CODE.GH]: 'BU_YR_GH',
      [COUNTRY_CODE.NG]: 'BU_YR_NG',
      [COUNTRY_CODE.KE]: 'BU_YR_KE',
      [COUNTRY_CODE.ZA]: 'BU_YR_ZA',
    },
  },
};

export const otherPlansMap = {
  [PLANS.STARTER]: 'FREE',
  [PLANS.KITCHEN]: 'KITCHEN',
};

export const productCodeToIdMap = {
  BA_MO_NG: '6557491000000565044',
  BA_MO_GH: '6557491000000565045',
  BA_MO_ZA: '6557491000000565046',
  BA_MO_KE: '6557491000000565047',
  BA_QA_NG: '6557491000000565048',
  BA_QA_GH: '6557491000000565049',
  BA_QA_ZA: '6557491000000565050',
  BA_QA_KE: '6557491000000565051',
  BA_BA_NG: '6557491000000565052',
  BA_BA_GH: '6557491000000565053',
  BA_BA_ZA: '6557491000000565054',
  BA_BA_KE: '6557491000000565055',
  BU_MO_NG: '6557491000000565056',
  BU_MO_GH: '6557491000000565057',
  BU_MO_ZA: '6557491000000565058',
  BU_MO_KE: '6557491000000565059',
  BU_QA_NG: '6557491000000565060',
  BU_QA_GH: '6557491000000565061',
  BU_QA_ZA: '6557491000000565062',
  BU_QA_KE: '6557491000000565063',
  BU_BA_NG: '6557491000000565064',
  BU_BA_GH: '6557491000000565065',
  BU_BA_ZA: '6557491000000565066',
  BU_BA_KE: '6557491000000565067',
  FREE: '6557491000000565068',
  KITCHEN: '6557491000000565069',
};

const ONBOARDING_PIPELINE_ID = '6557491000000504347';

enum PipelineStages {
  FREE_TRIAL = 'Free Trial',
  PAID_UPFRONT = 'Paid Upfront',
  ACTIVE_SUBSCRIBER = 'Active Subscriber',
  UNQUALIFIED_USER = 'Unqualified User',
  NEGOTIATION = 'Negotiation',
  QUALIFIED_FREE_TRIAL = 'Qualified Free Trial',
  SUBSCRIPTION_DUE = 'Subscription Due',
  RECENTLY_DROPPED_OFF = 'Recently Dropped Off',
  LOST_PAYING_USER = 'Lost Has Payments',
  LOST_NO_PAYMENTS = 'Lost No Payments',
}

const getPiplelineStage = (
  status: SUBSCRIPTION_STATUS,
  plan: PLAN_TYPE,
  noOfPayments: number,
  paymentDueDate: string,
) => {
  const userIsActiveSubscriber =
    status === SUBSCRIPTION_STATUS.ACTIVE &&
    plan !== PLAN_TYPE.STARTER &&
    noOfPayments > 0 &&
    dayjs(paymentDueDate).diff(dayjs(), 'days') > 2; //check the date calculation properly
  if (userIsActiveSubscriber) return PipelineStages.ACTIVE_SUBSCRIBER;

  const freeTrialUser =
    status === SUBSCRIPTION_STATUS.ACTIVE &&
    plan !== PLAN_TYPE.STARTER &&
    noOfPayments === 0 &&
    dayjs(paymentDueDate).diff(dayjs(), 'days') > 2; //check the date calculation properly
  if (freeTrialUser) return PipelineStages.FREE_TRIAL;

  const userHasPaymentDue =
    plan !== PLAN_TYPE.STARTER &&
    (dayjs(paymentDueDate).diff(dayjs(), 'days') <= 2 || status === SUBSCRIPTION_STATUS.IN_ACTIVE); //check the date calculation properly
  if (userHasPaymentDue) return PipelineStages.SUBSCRIPTION_DUE;

  const recentlyDroppedOff = plan === PLAN_TYPE.STARTER && dayjs(paymentDueDate).diff(dayjs(), 'days') >= -14; //check the date calculation properly
  if (recentlyDroppedOff) return PipelineStages.RECENTLY_DROPPED_OFF;

  const lostPayingUser =
    plan === PLAN_TYPE.STARTER && noOfPayments > 0 && dayjs(paymentDueDate).diff(dayjs(), 'days') < -14; //check the date calculation properly
  if (lostPayingUser) return PipelineStages.LOST_PAYING_USER;

  const lostFreeTrialUser =
    plan === PLAN_TYPE.STARTER && noOfPayments === 0 && dayjs(paymentDueDate).diff(dayjs(), 'days') < -14; //check the date calculation properly
  if (lostFreeTrialUser) return PipelineStages.LOST_NO_PAYMENTS;
};

const users = {
  RIDWAN: '6557491000000504083',
  EVANS: '6557491000000504096',
  CHIAMAKA: '6557491000001655026',
};

const countryUsers = {
  [COUNTRY_CODE.GH]: users.EVANS,
  [COUNTRY_CODE.NG]: users.RIDWAN,
  [COUNTRY_CODE.KE]: users.EVANS,
  [COUNTRY_CODE.ZA]: users.CHIAMAKA,
};
