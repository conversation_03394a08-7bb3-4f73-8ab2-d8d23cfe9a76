import { Injectable, Logger } from '@nestjs/common';
import Axios, { AxiosInstance } from 'axios';
import { BlocHQConfig } from '../config/types/bloc.config';
import { ConfigService } from '@nestjs/config';
import { Errorable } from '../utils/types';
import { extractErrorDetails } from '../utils/axios-errors';
import { registerErrors } from '../utils/errors.util';

interface IBlocVerifyAccountResponse {
  account_name: string;
  account_number: string;
}

@Injectable()
export class BlocHQRepository {
  private config: BlocHQConfig;
  private axios: AxiosInstance;

  constructor(private readonly logger: Logger, configService: ConfigService) {
    this.config = configService.get<BlocHQConfig>('blochqConfiguration');
    this.axios = Axios.create({
      baseURL: 'https://api.blochq.io/v1/',
      headers: {
        Authorization: `Bearer ${this.config.privateKey}`,
        'Content-Type': 'application/json',
      },
    });
  }

  async resolveAccount(accountNumber: string, bankCode: string): Errorable<IBlocVerifyAccountResponse> {
    try {
      const r = await this.axios.get('/resolve-account', {
        params: {
          account_number: accountNumber,
          bank_code: bankCode,
        },
      });

      return { data: r.data.data };
    } catch (e) {
      return { error: e };
    }
  }

  async singleTransfer(
    amount: number,
    account_number: string,
    bank_code: string,
    ref: string,
    reason: string,
  ): Errorable<any> {
    try {
      const r = await this.axios.post('/transfers/balance', {
        amount,
        account_number,
        bank_code,
        narration: reason,
        reference: ref,
      });

      return { data: r.data.data };
    } catch (err) {
      return { error: err.response || err, status: err?.response?.status || 500 };
    }
  }

  async createCollectionAccount(amount: number): Errorable<any> {
    try {
      const r = await this.axios.post('/accounts/collections', {
        preferred_bank: 'Providus',
        alias: 'Pay',
        collection_rules: {
          frequency: 1,
          amount,
        },
      });

      return { data: r.data.data };
    } catch (err) {
      const error = extractErrorDetails(err);

      this.logger.log(error);
      registerErrors(error);
      return { error: error?.message };
    }
  }
}
