import { Injectable, Logger } from '@nestjs/common';
import Axios, { AxiosInstance } from 'axios';
import { ConfigService } from '@nestjs/config';
import { Errorable } from '../utils/types';
import { registerErrors } from '../utils/errors.util';
import { extractErrorDetails } from '../utils/axios-errors';
import { ZeepayConfig } from '../config/types/zeepay.config';
import { CacheService } from '../modules/shared/cache/cache.service';
import { ZEEPAY_CHANNEL_MAP } from '../enums/payment.enum';

interface ZeepayOAuthTokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  refresh_token: string;
}

export interface ZeepayDebitWalletRequest {
  customerName: string;
  mno: string;
  amount: number;
  msisdn: string;
  description: string;
  reference: string;
  callback_url?: string;
}

export interface ZeepayWebhookPayload {
  zeepay_id: number;
  reference: string;
  status: string;
  message: string;
  gateway_id?: string;
  amount?: number;
  fee_charged?: number;
  amount_settled?: number;
}

export interface DebitWalletResponse {
  code: number;
  zeepay_id: number;
  amount: number;
  message: string;
}

export interface AmountDetails {
  cc: number;
  rate: string;
  send: number;
  receive: number;
  send_local: number;
  send_currency: string;
  receive_currency: string;
}

export interface SenderDetails {
  no: string;
  name: string;
  type: number;
}

export interface RecipientDetails {
  no: number;
  name: string;
  type: number;
}

export interface Transaction {
  id: number;
  merchant_id: string;
  type: string;
  medium: string;
  reference: string;
  external_reference: string | null;
  zeepay_id: string;
  gateway_id: string | null;
  amount: number;
  amount_details: AmountDetails;
  medium_details: any; // replace `any` with a more specific type if you know it
  status: number;
  status_message: string;
  sender_details: SenderDetails;
  recipient_details: RecipientDetails;
  reason: string | null;
  description: string;
  extra: string;
  api: number;
  callback: string;
  created_at: string; // ISO 8601 timestamp
  updated_at: string; // ISO 8601 timestamp
  proxy_merchant_id: string | null;
}

export interface ZeepayWebhookV2Payload {
  transaction: Transaction;
}

@Injectable()
export class ZeepayRepository {
  private axios: AxiosInstance;
  private useV2Apis: boolean;
  private config: ZeepayConfig;
  private readonly tokenCacheKey = 'zeepay_oauth_token'; // Key to store the token in Redis

  constructor(
    private readonly configService: ConfigService,
    private readonly logger: Logger,
    private readonly cacheService: CacheService, // Inject the cache service
  ) {
    this.config = configService.get<ZeepayConfig>('zeepayConfiguration');
    this.useV2Apis = this.config.version === 'V2';

    this.axios = Axios.create({
      baseURL: this.useV2Apis ? this.config.base_url_v2 : this.config.base_url,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  // Method to get OAuth token
  async getOAuthToken(): Promise<ZeepayOAuthTokenResponse> {
    const payload = {
      grant_type: 'password',
      client_id: this.useV2Apis ? this.config.client_id_v2 : this.config.client_id,
      client_secret: this.useV2Apis ? this.config.client_secret_v2 : this.config.client_secret,
      username: this.useV2Apis ? this.config.username_v2 : this.config.username,
      password: this.useV2Apis ? this.config.password_v2 : this.config.password,
    };

    try {
      const res = await this.axios.post<ZeepayOAuthTokenResponse>('/oauth/token', new URLSearchParams(payload), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      return res?.data;
    } catch (err) {
      const error = extractErrorDetails(err);
      this.logger.log(error);
      registerErrors(error);
      throw new Error(error?.message);
    }
  }

  // Method to get token from cache or auth endpoint
  async getCachedOrNewToken(): Promise<string> {
    // Check if the token exists in the cache
    const cachedToken = await this.cacheService.get<{ access_token: string; version: string }>(this.tokenCacheKey);

    if (typeof cachedToken === 'object' && cachedToken?.version === this.config.version) {
      return cachedToken.access_token;
    }

    // If no cached token, fetch a new one
    const newTokenResponse = await this.getOAuthToken();

    // Set the token in Redis with TTL matching its expiry time
    await this.cacheService.setWithLock(
      this.tokenCacheKey,
      {
        access_token: newTokenResponse.access_token,
        version: this.config.version,
      },
      newTokenResponse.expires_in,
    );

    return newTokenResponse.access_token;
  }

  async debitWallet(info: ZeepayDebitWalletRequest): Promise<Errorable<DebitWalletResponse>> {
    const accessToken = await this.getCachedOrNewToken();

    if (this.useV2Apis) {
      return this.debitWalletV2(info, accessToken);
    }
    return this.debitWalletV1(info, accessToken);
  }

  // Debit wallet method with token caching logic
  async debitWalletV1(info: ZeepayDebitWalletRequest, accessToken: string): Promise<Errorable<DebitWalletResponse>> {
    try {
      // Get the OAuth token (either from cache or a new one)

      // Call the debit wallet API
      info.callback_url = process.env.CATLOG_API + '/webhooks/zeepay';
      const res = await this.axios.post<DebitWalletResponse>(
        '/api/custom/transactions/tech-maxx/wallets/debit-wallet',
        info,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        },
      );

      return { data: res?.data };
    } catch (err) {
      const error = extractErrorDetails(err);
      this.logger.log(error);
      registerErrors(error);
      return { error: error?.message };
    }
  }

  async debitWalletV2(info: ZeepayDebitWalletRequest, accessToken: string): Promise<Errorable<DebitWalletResponse>> {
    try {
      const payload = {
        name: info.customerName,
        phone_number: info.msisdn,
        channel: ZEEPAY_CHANNEL_MAP[info.mno],
        extra: info.reference,
        amount: info.amount,
        callback_url: 'https://eu.getconvoy.cloud/ingest/eXxLsv7AUEuTXhO8',
      };

      const res = await this.axios.post<DebitWalletResponse>('/api/pay', new URLSearchParams(payload as any), {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      const data = res?.data as any;

      if (data.type === 'error') {
        throw new Error(data.message);
      } else {
        return { data: data };
      }
    } catch (err) {
      const error = extractErrorDetails(err);
      this.logger.log(error);
      registerErrors(error);
      throw new Error(error?.message);
    }
  }
}
