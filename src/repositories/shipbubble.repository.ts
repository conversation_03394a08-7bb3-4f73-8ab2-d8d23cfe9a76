import { Injectable, Logger } from '@nestjs/common';
import { ZillaConfig } from '../config/types/zilla.config';
import { ConfigService } from '@nestjs/config';
import Axios, { AxiosInstance } from 'axios';
import { registerErrors } from '../utils/errors.util';
import { ShipbubbleConfig } from '../config/types/shipbubble.config';
import { Errorable } from '../utils/types';
import { DeliveryDocument, DeliveryItem, DeliveryPackageDimensions } from '../modules/deliveries/deliveries.schema';
import { extractErrorDetails } from '../utils/axios-errors';

export interface ValidateAddressResponse {
  address_code: number;
  name: string;
  email: string;
  phone: string;
  stree?: string;
  street_no?: string;
  address: string;
  formatted_address: string;
  country: string;
  country_code: string;
  city: string;
  city_code: string;
  state: string;
  state_code: string;
  postal_code: string;
  latitude: number;
  longitude: number;
}

export interface SbCourier {
  name: string;
  pin_image: string;
  service_code: string;
  origin_country: string;
  international: boolean;
  domestic: boolean;
  on_demand: boolean;
  status: string;
  package_categories: { id: number }[];
}

@Injectable()
export class ShipbubbleRepository {
  private config: ShipbubbleConfig;
  private readonly axios: AxiosInstance;

  constructor(configService: ConfigService, private readonly logger: Logger) {
    this.config = configService.get<ShipbubbleConfig>('shipbubbleConfiguration');
    this.axios = Axios.create({
      baseURL: this.config?.apiUrl,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this.config.apiKey}`,
      },
    });
  }

  async getPackageCategories(): Errorable<any> {
    try {
      const res = await this.axios.get('/shipping/labels/categories');
      return res.data.data;
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);
      return { error: err.response?.data?.message };
    }
  }

  async getPackageDimensions(): Errorable<any> {
    try {
      const res = await this.axios.get('/shipping/labels/boxes');
      return res.data.data;
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);
      return { error: err.response?.data?.message };
    }
  }

  async getCouriers(): Errorable<SbCourier[]> {
    try {
      const res = await this.axios.get('/shipping/couriers');
      return { error: null, data: res.data.data };
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);
      return { error: err.response?.data?.message };
    }
  }

  async validateAddress(payload: {
    name: string;
    email: string;
    phone: string;
    address: string;
  }): Errorable<ValidateAddressResponse> {
    try {
      const res = await this.axios.post('/shipping/address/validate', payload);
      return res.data;
    } catch (err) {
      const error = extractErrorDetails(err);

      // console.log(error);
      this.logger.error(error);
      registerErrors(error);

      //throw error if API is down so users can still place orders
      if (err?.response?.status > 422) {
        throw new Error(err?.response?.data);
      }

      return { error: err.response?.data?.message };
    }
  }

  async getShippingRates(payload: {
    sender_address_code: number;
    receiver_address_code: number;
    pickup_date: string;
    category_id: number;
    package_items: DeliveryItem[];
    package_dimension: DeliveryPackageDimensions;
    service_type: string;
    delivery_instructions: string;
  }): Errorable<any> {
    try {
      const res = await this.axios.post('/shipping/fetch_rates', {
        ...payload,
        reciever_address_code: payload.receiver_address_code,
        pickup_date: payload.pickup_date.split('T')[0],
      });
      return res.data;
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);
      return { error: err.response?.data?.message };
    }
  }

  async cancelDelivery(payload: { order_id: string }): Errorable<any> {
    try {
      const res = await this.axios.post(`/shipping/labels/cancel/${payload.order_id}`);
      return res;
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);
      return { error: err.response?.data?.message };
    }
  }

  async initiateDelivery(payload: { request_token: string; service_code: string; courier_id: string }): Errorable<any> {
    try {
      const res = await this.axios.post('/shipping/labels', {
        ...payload,
      });

      return res.data;
    } catch (err) {
      const error = extractErrorDetails(err);

      this.logger.error(error);
      registerErrors(error);
      return { error: error?.details?.message ?? error?.message };
    }
  }
}
