import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Axios, { AxiosInstance } from 'axios';
import { Errorable } from '../utils/types';
import { FlutterWaveConfig } from '../config/types/flutterwave.config';
import { stripUndefinedAndNull } from '../utils/functions';
import { extractErrorDetails } from '../utils/axios-errors';
import { registerErrors } from '../utils/errors.util';

type Response<T> = { data: T };

interface CreateFwOneTimeAccount {
  email: string;
  tx_ref: string;
  narration: string;
  amount: number;
  firstname?: string;
  lastname?: string;
  phonenumber?: string;
}

interface OneTimeVirtualAccountResponse {
  response_code: string;
  response_message: string;
  flw_ref: string;
  order_ref: string;
  account_number: string;
  frequency: string | number;
  bank_name: string;
  created_at: string;
  expiry_date: string;
  note: string;
  amount: string;
}

@Injectable()
export class FlutterWaveRepository {
  private config: FlutterWaveConfig;
  private axios: AxiosInstance;

  constructor(readonly configService: ConfigService, private readonly logger: Logger) {
    this.config = configService.get<FlutterWaveConfig>('flutterwaveConfiguration');
    this.axios = Axios.create({
      baseURL: 'https://api.flutterwave.com',
      headers: {
        Authorization: 'Bearer ' + this.config.privateKey,
      },
    });
  }

  async createOneTimeVirtualAccount(info: CreateFwOneTimeAccount): Errorable<OneTimeVirtualAccountResponse> {
    try {
      //removing undefined because firstname & lastname could be null
      //appending _PMCKDU_1 to reference to test transactions because FW uses that to trigger a test transfer
      const reqData = stripUndefinedAndNull({
        ...info,
        frequency: 1,
        is_permanent: false,
        tx_ref: process.env.NODE_ENV === 'production' ? info.tx_ref : info.tx_ref + '_PMCKDU_3',
      });

      const res = await this.axios.post<{ data: OneTimeVirtualAccountResponse }>(
        '/v3/virtual-account-numbers',
        reqData,
      );

      return { data: res?.data?.data };
    } catch (err) {
      console.log('FLW_ACC_CREATION_ERROR', err);

      const error = extractErrorDetails(err);

      this.logger.log(error);
      registerErrors(error);
      return { error: error?.message };
    }
  }
}
