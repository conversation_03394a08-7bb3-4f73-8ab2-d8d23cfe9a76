import { BadGatewayException, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Axios, { AxiosInstance } from 'axios';
import { SquadConfig } from '../config/types/squad.config';
import { Errorable } from '../utils/types';
import * as crypto from 'crypto';
import { extractErrorDetails } from '../utils/axios-errors';
import { cleanString, generateCombinedName } from '../utils/functions';

type Response<T> = { data: T };

interface SquadCreateBusinessAccount {
  name: string;
  store_name: string;
  phone: string;
  bvn: string;
  customer_identifier: string;
}

interface SquadCreateVirtualAccount {
  name: string;
  store_name: string;
  phone: string;
  dateOfBirth: string;
  bvn: string;
  gender: '2' | '1';
  address: string;
  customer_identifier: string;
}

interface VirtualAccountResponse {
  first_name: string;
  last_name: string;
  bank_code: string;
  virtual_account_number: string;
  beneficiary_account: string;
  customer_identifier: string;
  created_at: Date;
  updated_at: Date;
}

export interface TransactionData {
  transaction_reference: string;
  virtual_account_number: string;
  principal_amount: string;
  settled_amount: string;
  fee_charged: string;
  transaction_date: string;
  transaction_indicator: string;
  remarks: string;
  currency: string;
  alerted_merchant: boolean;
  merchant_settlement_date: string;
  sender_name: string;
  session_id: string;
  frozen_transaction: any;
  customer: {
    customer_identifier: string;
  };
  virtual_account: {
    account_type: string;
  };
}

export interface SquadTxWebhookData {
  id: string;
  payload: {
    hash: string;
    transaction_reference: string;
    virtual_account_number: string;
    principal_amount: string;
    settled_amount: string;
    fee_charged: string;
    transaction_date: string;
    transaction_indicator: string;
    remarks: string;
    currency: string;
    alerted_merchant: boolean;
    merchant_settlement_date: string;
    sender_name: string;
    session_id: string;
    frozen_transaction: any;
    customer_identifier: string;
    virtual_account: {
      account_type: string;
    };
  };
}

interface TransactionsResponse<T> {
  status: number;
  success: boolean;
  message: string;
  data: { rows: T[] };
}

interface AccountLookupParams {
  account_number: string;
  bank_code: string;
}

interface AccountLookupResponse {
  data: {
    account_name: string;
    account_number: string;
  };
}

interface InitiateTransferParams {
  remark: string;
  bank_code: string;
  amount: number;
  account_number: string;
  transaction_reference: string;
  account_name: string;
}

interface PayoutInitiationResponse {
  status?: number;
  message?: string;
  data?: {
    transaction_reference: string;
    response_description: string;
    currency_id: string;
    amount: string;
    nip_transaction_reference: string;
    account_number: string;
    account_name: string;
    destination_institution_name: string;
  };
}

interface PayoutRequeryParams {
  transaction_reference: string;
}

@Injectable()
export class SquadRepository {
  private config: SquadConfig;
  private axios: AxiosInstance;

  constructor(readonly configService: ConfigService, private readonly logger: Logger) {
    this.config = configService.get<SquadConfig>('squadConfiguration');
    this.axios = Axios.create({
      baseURL: this.config.apiUrl,
      headers: {
        Authorization: 'Bearer ' + this.config.privateKey,
      },
    });
  }

  async createVirtualAccount(info: SquadCreateVirtualAccount): Errorable<VirtualAccountResponse> {
    try {
      const res = await this.axios.post<{ data: VirtualAccountResponse }>('/virtual-account', {
        first_name: info.store_name.replace(/&/g, 'and'),
        last_name: info.name + ' - Catlog',
        dob: info.dateOfBirth,
        bvn: info.bvn,
        gender: info.gender,
        address: info.address,
        customer_identifier: info.customer_identifier,
        mobile_num: info.phone.startsWith('234') ? '0' + info.phone.replace('234', '') : info.phone,
      });

      return { data: res.data.data };
    } catch (error) {
      console.log('ACC_CREATION_ERROR', error);
      return { error: error };
    }
  }

  async createBusinessAccount(info: SquadCreateBusinessAccount): Errorable<VirtualAccountResponse> {
    const account_name = generateCombinedName(info.store_name, info.name);
    const data = {
      business_name: cleanString(account_name, true),
      bvn: info.bvn,
      customer_identifier: info.customer_identifier,
      mobile_num: info.phone.startsWith('234') ? '0' + info.phone.replace('234', '') : info.phone,
    };

    try {
      const res = await this.axios.post<{ data: VirtualAccountResponse }>('/virtual-account/business', data);

      return { data: res.data.data };
    } catch (error) {
      const errorDetails = extractErrorDetails(error);
      console.log('ACC_CREATION_REQ_DATA', data);
      console.log('ACC_CREATION_ERROR', errorDetails?.details);
      return { error: errorDetails?.message };
    }
  }

  async getTransactionsByDateRange(startDate: string, endDate: string): Errorable<TransactionData[]> {
    try {
      const { data } = await this.axios.get<TransactionsResponse<TransactionData>>(
        `/virtual-account/merchant/transactions/all?startDate=${startDate}&endDate=${endDate}&perPage=200`,
      );
      return { data: data?.data?.rows };
    } catch (err) {
      this.logger.log(err?.data, 'Fetching squad transactions failed');
      return { error: err };
    }
  }

  async getTransactionsByReference(reference: string): Errorable<TransactionData[]> {
    try {
      const { data } = await this.axios.get<TransactionsResponse<TransactionData>>(
        `/virtual-account/merchant/transactions/all?transactionReference=${reference}`,
      );
      return { data: data?.data?.rows };
    } catch (err) {
      this.logger.log(err?.data, 'Fetching squad transactions failed');
      return { error: err };
    }
  }

  async getFailedWebhooks(): Errorable<SquadTxWebhookData[]> {
    try {
      const { data } = await this.axios.get<TransactionsResponse<SquadTxWebhookData>>(`/virtual-account/webhook/logs`);
      return { data: data?.data?.rows };
    } catch (err) {
      this.logger.log(err?.data, 'Fetching failed webhooks');
      return { error: err };
    }
  }

  async deleteTxFromFailedWebhooks(ref): Errorable<any> {
    try {
      const { data } = await this.axios.delete<any>(`/virtual-account/webhook/logs/${ref}`);
      return { data: data?.data };
    } catch (err) {
      this.logger.log(err?.data, 'Fetching delete record');
      return { error: err };
    }
  }

  async triggerSquadWebhook(data: any): Errorable<any> {
    try {
      const hash = crypto.createHmac('sha512', this.config.privateKey).update(JSON.stringify(data)).digest('hex');

      const res = await this.axios.post<{ data: VirtualAccountResponse }>(
        'https://api.catlog.shop/webhooks/squad',
        data,
        {
          headers: {
            'x-squad-signature': hash,
          },
        },
      );

      return { data: 'webhook triggered' };
    } catch (error) {
      console.log('FAILED TO TRIGGER SQUAD WEBHOOK', error);
      return { error: error };
    }
  }

  async mockTransfer(data: { account_number: string; amount: number }): Errorable<any> {
    try {
      const res = await this.axios.post<{ data: VirtualAccountResponse }>('/virtual-account/simulate/payment', {
        virtual_account_number: data.account_number,
        amount: String(data.amount),
      });

      return { data: res.data.data };
    } catch (error) {
      console.log('MOCK_PAYMENT_ERROR', error);
      return { error: error };
    }
  }

  async lookupAccount(data: AccountLookupParams): Errorable<AccountLookupResponse> {
    try {
      const res = await this.axios.post<{ data: AccountLookupResponse }>('/payout/account/lookup', data);

      return { data: res.data.data };
    } catch (error) {
      console.log('ACCOUNT_LOOKUP_ERROR', error);
      return { error: error };
    }
  }

  async initiateTransfer(data: InitiateTransferParams): Errorable<PayoutInitiationResponse> {
    try {
      const res = await this.axios.post<PayoutInitiationResponse>('/payout/transfer', {
        ...data,
        amount: String(data.amount),
        currency_id: 'NGN',
        transaction_reference: this.config.merchantID + '_' + data.transaction_reference,
      });

      return { data: res.data };
    } catch (error) {
      console.log('INITIATE_TRANSFER_ERROR', error);
      const errorData = extractErrorDetails(error);
      return { error: errorData.message, status: 424, data: error?.response?.data };
    }
  }

  async requeryTransfer(data: PayoutRequeryParams): Errorable<PayoutInitiationResponse> {
    try {
      const res = await this.axios.post<PayoutInitiationResponse>('/payout/requery', {
        transaction_reference: this.config.merchantID + '_' + data.transaction_reference,
      });

      return { data: res.data };
    } catch (error) {
      console.log('REQUERY_TRANSFER_ERROR', error);
      const errorData = extractErrorDetails(error);
      return { error: errorData.message, status: error?.response?.status, data: error?.response?.data };
    }
  }
}
