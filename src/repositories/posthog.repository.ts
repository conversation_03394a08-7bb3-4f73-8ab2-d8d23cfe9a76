import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Axios, { AxiosInstance } from 'axios';
import { PosthogConfig } from '../config/types/posthog.config';
import { registerErrors } from '../utils/errors.util';
import { Errorable } from '../utils/types';

export interface EventPayload<T> {
  event: string;
  distinct_id: string;
  timestamp: Date;
  properties: T;
}
export interface GetEventsPayload {
  distinct_id: string;
  filters?: Record<string, string>[];
}

export interface ChowbotEventProps {
  is_production: boolean;
  is_session_event: boolean;
  session_id?: string;
  customer?: { name: string; id: string; phone: string };
  store?: { name: string; id: string };
  botMeta: {
    id: string;
    phone: string;
  };
  order?: string;
  message?: string;
  isUserMessage?: boolean;
}

@Injectable()
export class PosthogRepository {
  private config: PosthogConfig;
  private readonly axios: AxiosInstance;

  constructor(configService: ConfigService, private readonly logger: Logger) {
    this.config = configService.get<PosthogConfig>('posthogConfiguration');
    this.axios = Axios.create({
      baseURL: 'https://app.posthog.com',
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  async captureChowbotEvent(payload: EventPayload<ChowbotEventProps>): Errorable<any> {
    try {
      const res = await this.axios.post('/capture/', {
        ...payload,
        api_key: this.config.publicKeys.chowbot,
      });

      return res;
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);
      return { error: err.response?.data?.message };
    }
  }

  async getChowbotEvents({ distinct_id, filters = [] }: GetEventsPayload): Errorable<any> {
    try {
      const res = await this.axios.get(`/api/projects/${this.config.projectIds.chowbot}/events/`, {
        headers: {
          Authorization: `Bearer ${this.config.privateKeys.chowbot}`,
        },
        params: {
          distinct_id,
          properties: { type: 'AND', values: [...filters] },
        },
      });

      return res;
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);
      return { error: err.response?.data?.message };
    }
  }
}
