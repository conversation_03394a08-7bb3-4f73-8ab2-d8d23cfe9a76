import { Injectable, Logger } from '@nestjs/common';
import Axios, { AxiosInstance } from 'axios';
import { ConfigService } from '@nestjs/config';
import { registerErrors } from '../utils/errors.util';
import { Errorable } from '../utils/types';
import { GoogleMapsConfig } from '../config/types/maps.google.config';

@Injectable()
export class GoogleMapsRepository {
  private config: GoogleMapsConfig;
  private axios: AxiosInstance;

  constructor(private readonly logger: Logger, configService: ConfigService) {
    this.config = configService.get<GoogleMapsConfig>('googleMapsConfiguration');
    this.axios = Axios.create({
      baseURL: 'https://maps.googleapis.com/maps/api',
      headers: { 'Content-Type': 'application/json' },
    });
  }

  async getAddressFromLatLong(latitude: number, longitude: number): Errorable<any> {
    try {
      const response = await this.axios.get(
        `/geocode/json?latlng=${latitude},${longitude}&key=${this.config.publicKey}`,
      );

      if (response?.data?.results[0]?.formatted_address) {
        return { data: response?.data?.results[0]?.formatted_address };
      }

      if (response?.data?.plus_code?.compound_code) {
        return { data: response?.data?.plus_code?.compound_code };
      }

      return { error: response?.data?.error_message ?? 'No address found' };
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);
      return { error: err.response?.data?.error_message };
    }
  }

  async lookupAddress(address: string): Errorable<any> {
    try {
      const response = await this.axios.get(`/geocode/json?address=${address}&key=${this.config.publicKey}`);

      if (response?.data?.results[0]?.formatted_address) {
        return { data: response?.data?.results[0]?.formatted_address };
      }

      if (response?.data?.plus_code?.compound_code) {
        return { data: response?.data?.plus_code?.compound_code };
      }

      return { error: response?.data?.error_message ?? 'No address found' };
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);
      return { error: err.response?.data?.error_message };
    }
  }

  async lookupAddressFullResult(address: string): Errorable<any> {
    try {
      const response = await this.axios.get(`/geocode/json?address=${address}&key=${this.config.publicKey}`);

      if (response?.data?.results[0]?.formatted_address) {
        return { data: response?.data };
      }

      return { error: response?.data?.error_message ?? 'No address found' };
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);
      return { error: err.response?.data?.error_message };
    }
  }
}
