import { Injectable, Logger } from '@nestjs/common';
import Axios, { AxiosInstance } from 'axios';
import { FincraConfig } from '../config/types/fincra.config';
import { ConfigService } from '@nestjs/config';
import { Errorable } from '../utils/types';
import { extractErrorDetails } from '../utils/axios-errors';

export interface IGenerateQuotePayload {
  sourceCurrency: string;
  destinationCurrency: string;
  amount: string;
}

export interface IGenerateQuoteResponse {
  success: boolean;
  message: string;
  data: {
    sourceCurrency: string;
    destinationCurrency: string;
    sourceAmount: number;
    destinationAmount: number;
    action: string;
    transactionType: string;
    fee: number;
    initialAmount: number;
    quotedAmount: number;
    rate: number;
    amountToCharge: number;
    amountToReceive: number;
    reference: string;
    expireAt: string;
    settlementDate: string;
    originalRate?: number;
  };
}

export interface IInitiateConversionPayload {
  quoteReference: string;
  customerReference: string;
}

export interface IInitiateConversionResponse {
  success: boolean;
  message: string;
  data: {
    _id: number | string;
    id: number | string;
    reference: string;
    customerReference: string;
  };
}

export interface IVerifyConversionResponse {
  success: boolean;
  message: string;
  data: {
    id: number;
    _id: string;
    amountCharged: number;
    amountReceived: number;
    sourceCurrency: string;
    destinationCurrency: string;
    sourceFee: number;
    destinationFee: number;
    settlementDate: string;
    settledAt: string;
    sourceExchangeRate: number;
    destinationExchangeRate: number;
    status: string;
    reference: string;
    isSubAccount: number;
    description: string | null;
    settlementDestination: string;
    customerReference: string;
    message: string;
    createdAt: string;
    updatedAt: string;
  };
}

@Injectable()
export class FincraRepository {
  private fincraConfig: FincraConfig;
  private readonly axios: AxiosInstance;
  private readonly apiKey: string;
  private readonly businessId: string;

  constructor(config: ConfigService, private readonly logger: Logger) {
    this.fincraConfig = config.get<FincraConfig>('fincraConfiguration');

    this.apiKey = this.fincraConfig.secretKey || '';
    this.businessId = this.fincraConfig.businessId || '';

    this.axios = Axios.create({
      baseURL: this.fincraConfig.baseUrl,
      headers: {
        'api-key': this.apiKey,
        'Content-Type': 'application/json',
      },
    });
  }

  async generateQuote(payload: IGenerateQuotePayload): Errorable<IGenerateQuoteResponse> {
    try {
      const fullPayload = {
        ...payload,
        action: 'send',
        transactionType: 'conversion',
        feeBearer: 'business',
        paymentDestination: 'fliqpay_wallet',
        beneficiaryType: 'individual',
        delay: false,
        business: this.businessId,
      };

      const { data } = await this.axios.post<IGenerateQuoteResponse>('quotes/generate', fullPayload);

      this.logger.log(data, 'Fincra generate quote success');
      return { data };
    } catch (err) {
      const errorDetails = extractErrorDetails(err);
      this.logger.error(errorDetails, 'Fincra generate quote failed');
      return {
        error: errorDetails.message,
      };
    }
  }

  async initiateCurrencyConversion(payload: IInitiateConversionPayload): Errorable<IInitiateConversionResponse> {
    try {
      const fullPayload = {
        ...payload,
        business: this.businessId,
      };
      const { data } = await this.axios.post<IInitiateConversionResponse>('conversions/initiate', fullPayload);

      this.logger.log(data, 'Fincra initiate currency conversion success');
      return { data };
    } catch (err) {
      const errorDetails = extractErrorDetails(err);
      this.logger.error(errorDetails, 'Fincra initiate currency conversion failed');
      return {
        error: errorDetails.details,
      };
    }
  }

  async verifyConversionStatus(conversionReference: string): Errorable<IVerifyConversionResponse> {
    try {
      const { data } = await this.axios.get<IVerifyConversionResponse>(`conversions/reference/${conversionReference}`);

      this.logger.log(data, 'Fincra verify conversion status success');
      return { data };
    } catch (err) {
      const errorDetails = extractErrorDetails(err);
      this.logger.error(errorDetails, 'Fincra verify conversion status failed');
      return {
        error: errorDetails.message,
      };
    }
  }
}
