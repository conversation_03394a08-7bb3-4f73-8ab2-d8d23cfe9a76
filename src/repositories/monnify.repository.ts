import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Axios, { AxiosInstance } from 'axios';
import { MonnifyConfig } from '../config/types/monnify.config';
import { Errorable } from '../utils/types';
import { extractErrorDetails } from '../utils/axios-errors';
import { registerErrors } from '../utils/errors.util';

type TransactionInitPayload = {
  amount: number;
  customerName: string;
  customerEmail: string;
  paymentDescription: string;
  paymentReference: string;
};

type BankTransferInitPayload = {
  transactionReference: string;
  bankCode?: string;
};

interface BankTransferInitResponseBody {
  accountNumber: string;
  accountName: string;
  bankName: string;
  bankCode: number;
  accountDurationSeconds: number;
  ussdPayment: string;
  requestTime: Date;
  transactionReference: string;
  paymentReference: string;
  amount: number;
  fee: number;
  totalPayable: number;
}

interface TransactionResponseBody {
  transactionReference: string;
  paymentReference: string;
  merchantName: string;
  apiKey: string;
  enabledPaymentMethod: string[];
  checkoutUrl: string;
}

export interface MonnifyResponse<T> {
  requestSuccessful: boolean;
  responseMessage: string;
  responseCode: string;
  responseBody: T;
}

@Injectable()
export class MonnifyRespository {
  private readonly monnifyConfig: MonnifyConfig;
  private readonly axios: AxiosInstance;

  constructor(private readonly logger: Logger, configService: ConfigService) {
    this.monnifyConfig = configService.get<MonnifyConfig>('monnifyConfiguration');

    this.axios = Axios.create({
      baseURL: this.monnifyConfig.apiUrl,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  async initiateTransaction(payload: TransactionInitPayload): Errorable<TransactionResponseBody> {
    try {
      const { data } = await this.axios.post<MonnifyResponse<TransactionResponseBody>>(
        '/merchant/transactions/init-transaction',
        {
          amount: payload.amount,
          customerName: payload.customerName,
          customerEmail: payload.customerEmail,
          paymentDescription: payload.paymentDescription,
          currencyCode: 'NGN',
          contractCode: this.monnifyConfig.contractCode,
          paymentMethods: ['CARD', 'ACCOUNT_TRANSFER'],
          redirectUrl: '',
          paymentReference: payload.paymentReference,
        },
        {
          headers: {
            authorization: 'Bearer ' + (await this.getAuth()),
          },
        },
      );
      return { data: data.responseBody };
    } catch (err) {
      console.log('INITIATING MONNIFY PAYMENT ERROR');
      console.log(err);
      return { error: err };
    }
  }

  async bankTransfer(payload: BankTransferInitPayload): Errorable<BankTransferInitResponseBody> {
    try {
      const authCode = await this.getAuth();

      const { data } = await this.axios.post<MonnifyResponse<BankTransferInitResponseBody>>(
        '/merchant/bank-transfer/init-payment',
        payload,
        {
          headers: {
            authorization: 'Bearer ' + authCode,
          },
        },
      );

      return { data: data.responseBody };
    } catch (err) {
      console.log('INITIATING MONNIFY TRANSFER ERROR');
      // console.log(err);

      const error = extractErrorDetails(err);

      this.logger.log(error);
      registerErrors(error);
      return { error: error?.message };
    }
  }

  private async getAuth(): Promise<string> {
    const { data } = await this.axios.post<{
      responseBody: { accessToken: string };
    }>(
      '/auth/login',
      {},
      {
        headers: {
          authorization:
            'Basic ' + Buffer.from(this.monnifyConfig.apiKey + ':' + this.monnifyConfig.secretKey).toString('base64'),
        },
      },
    );

    return data.responseBody.accessToken;
  }
}
