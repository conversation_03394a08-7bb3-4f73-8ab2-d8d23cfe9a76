import { SetMetadata } from '@nestjs/common';
import { Permission } from '../utils/permissions.util';

export const ROLE_PERMISSIONS_KEY = 'ROLE_PERMISSIONS';
export const PLAN_PERMISSIONS_KEY = 'PLAN_PERMISSIONS';
export const COUNTRY_PERMISSIONS_KEY = 'COUNTRY_PERMISSIONS';
export const INTERNAL_DASHBOARD_PERMISSIONS_KEY = 'INTERNAL_DASHBOARD_PERMISSIONS';

export const RolePermissions = (...permissions: Permission[]) => SetMetadata(ROLE_PERMISSIONS_KEY, permissions);
export const PlanPermissions = (...permissions: Permission[]) => SetMetadata(PLAN_PERMISSIONS_KEY, permissions);
export const CountryPermissons = (...permissions: Permission[]) => SetMetadata(COUNTRY_PERMISSIONS_KEY, permissions);
export const InternalDashboardPermissions = (...permissions: string[]) =>
  SetMetadata(INTERNAL_DASHBOARD_PERMISSIONS_KEY, permissions);
