import { CacheModule, Global, Logger, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { RedisConfig } from './config/types/redis.config';
import * as redisStore from 'cache-manager-redis-store';
import type { RedisClientOptions } from 'redis';
import { RavenModule } from 'nest-raven';
import {
  ApiGuardConfiguration,
  BrokerTransportConfiguration,
  DojahConfiguration,
  JwtConfiguration,
  MailchimpConfiguiration,
  MongoDbConfiguration,
  MonnifyConfiguration,
  MonoConfiguration,
  PaystackConfiguration,
  PushNotificationConfiguration,
  FirebaseConfiguration,
  RedisConfiguration,
  S3Configuration,
  SquadConfiguration,
  TwitterApiConfiguration,
  YouVerifyApiConfiguration,
  ZillaConfiguration,
  ThepeerConfiguration,
  SlackWebhookConfiguration,
  ShipBubbleConfiguration,
  BlockHQConfiguration,
  WhatsappConfiguration,
  InstagramConfiguration,
  OpenaiConfiguration,
  ReCaptchaConfiguration,
  BrevoConfiguiration,
  FlutterWaveConfiguration,
  KoraPayConfiguration,
  PosthogConfiguration,
  ChowdeckConfiguration,
  GoogleMapsConfig,
  ResendConfiguiration,
  ZeepayConfiguration,
  PayazaConfiguration,
  FincraConfiguration,
  StartbuttonConfiguration,
  FezDeliveryConfiguration,
  StripeConfiguration,
  LeatherbackConfiguration,
  CustomerIoConfiguration,
  ZohoConfiguration,
  ShaqExpressConfiguration,
  MetaConfiguration,
  Go54Configuration,
  DomainnameapiConfiguration,
  SudoConfiguration,
} from './config/configuration';
import { JwtModule, JwtService } from '@nestjs/jwt';
import { JwtStrategy } from './jwt/jwt.strategy';
import { JwtConfig } from './config/types/jwt.config';
import { BrokerTransportService } from './broker/broker-transport.service';
import { NatsOptions } from '@nestjs/microservices';
import { S3Repository } from './repositories/s3.repositories';
import { PaystackRepository } from './repositories/paystack.repository';
import { ZillaRepository } from './repositories/zilla.repository';
import { MailchimpRepository } from './repositories/mailchimp.repository';
import { MonoRepository } from './repositories/mono.repository';
import { DojahRepository } from './repositories/dojah.repository';
import { UserMessagingRepository } from './repositories/user-messaging.repository';
import { MonnifyRespository } from './repositories/monnify.repository';
import { BullModule } from '@nestjs/bull';
import { YouVerifyRepository } from './repositories/youverify.repository';
import { SquadRepository } from './repositories/squad.repository';
import { SlackRepository } from './repositories/slack.respository';
import { ShipbubbleRepository } from './repositories/shipbubble.repository';
import { BlocHQRepository } from './repositories/bloc.repository';
import { InstagramRepository } from './repositories/instagram.repository';
import { OpenaiRepository } from './repositories/openai.respository';
import { ReCaptchaRepository } from './repositories/recaptcha.repository';
import { WhatsappBusinessApiRepository } from './repositories/whatsapp.repository';
import { BrevoRepository } from './repositories/brevo.repository';
import { FlutterWaveRepository } from './repositories/flutterwave.repository';
import { PosthogRepository } from './repositories/posthog.repository';
import { ChowdeckRepository } from './repositories/chowdeck/index.repository';
import { WebServicesRepository } from './repositories/webservices.repository';
import { GoogleMapsRepository } from './repositories/maps.google.repository';
import { KoraPayRepository } from './repositories/korapay.repository';
import { ResendRepository } from './repositories/resend.repository';
import { ZeepayRepository } from './repositories/zeepay.repository';
import { SharedServicesModule } from './modules/shared/shared-services.module';
import { CacheService } from './modules/shared/cache/cache.service';
import { IPLookUpRepository } from './repositories/iplookup.repository';
import { PayazaRepository } from './repositories/payaza.repository';
import { FincraRepository } from './repositories/fincra.repository';
import { StartbuttonRepository } from './repositories/startbutton.repository';
import { FezDeliveryRepository } from './repositories/fez-delivery.repository';
import { StripeRepository } from './repositories/stripe.repository';
import { CustomerIoRepository } from './repositories/customer-io.repository';
import { ZohoRepository } from './repositories/zoho.repository';
import { CurrencyConversionRepository } from './repositories/currency-conversions.repository';
import { ShaqExpressRepository } from './repositories/shaq-express.repository';
import { QUEUES } from './enums/queues.enum';
import { HostApiRepository } from './repositories/host-api.repository';
import { Go54Repository } from './repositories/go54.repository';
import { DomainnameapiRepository } from './repositories/domainnameapi.repository';
import { InternalApiJWTGuard } from './guards/api.guard';
import { APP_GUARD } from '@nestjs/core';
import { Reflector } from '@nestjs/core';
import { SudoRepository } from './repositories/sudo.repository';
import { FirebaseRepository } from './repositories/firebase.repository';

@Global()
@Module({
  imports: [
    BullModule.registerQueue({ name: QUEUES.MAIL }),
    BullModule.forRoot({
      redis:
        process.env.USE_MAILCHIMP_API == 'false'
          ? {
              host: 'localhost',
              port: 6379,
            }
          : {
              host: process.env.REDIS_HOST,
              port: Number(process.env.REDIS_PORT) || 6379,
              db: Number(process.env.REDIS_DB) || 0,
              password: process.env.REDIS_PASSWORD ?? undefined,
            },
    }),
    ConfigModule.forRoot({
      load: [
        BrokerTransportConfiguration,
        RedisConfiguration,
        MongoDbConfiguration,
        JwtConfiguration,
        S3Configuration,
        PaystackConfiguration,
        ZillaConfiguration,
        MailchimpConfiguiration,
        MonoConfiguration,
        DojahConfiguration,
        MonnifyConfiguration,
        TwitterApiConfiguration,
        ApiGuardConfiguration,
        YouVerifyApiConfiguration,
        SquadConfiguration,
        PushNotificationConfiguration,
        FirebaseConfiguration,
        ThepeerConfiguration,
        SlackWebhookConfiguration,
        ShipBubbleConfiguration,
        BlockHQConfiguration,
        WhatsappConfiguration,
        InstagramConfiguration,
        OpenaiConfiguration,
        ReCaptchaConfiguration,
        BrevoConfiguiration,
        FlutterWaveConfiguration,
        StartbuttonConfiguration,
        KoraPayConfiguration,
        PosthogConfiguration,
        ChowdeckConfiguration,
        GoogleMapsConfig,
        ResendConfiguiration,
        ZeepayConfiguration,
        PayazaConfiguration,
        FincraConfiguration,
        StripeConfiguration,
        LeatherbackConfiguration,
        FezDeliveryConfiguration,
        ShaqExpressConfiguration,
        CustomerIoConfiguration,
        ZohoConfiguration,
        MetaConfiguration,
        Go54Configuration,
        DomainnameapiConfiguration,
        SudoConfiguration,
      ],
    }),
    CacheModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        const password = configService.get<RedisConfig>('redisConfiguration').redisPass;
        const config = {
          name: 'redis',
          store: redisStore,
          host: configService.get<RedisConfig>('redisConfiguration').redisHost,
          port: configService.get<RedisConfig>('redisConfiguration').redisPort,
          database: configService.get<RedisConfig>('redisConfiguration').redisDb,
        } as RedisClientOptions;

        if (password) {
          config.password = password;
        } else {
          (config as any).no_ready_check = true;
        }
        return config;
      },
    }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        const jwtConfig = configService.get<JwtConfig>('jwtConfiguration');
        return {
          secret: jwtConfig.secret,
          signOptions: { expiresIn: jwtConfig.expiresIn },
        };
      },
    }),
    RavenModule,
  ],
  providers: [
    JwtStrategy,
    Logger,
    {
      provide: BrokerTransportService,
      useFactory: (configService: ConfigService) => {
        const brokerConfig = configService.get<NatsOptions>('brokerTransportConfiguration');
        return new BrokerTransportService(brokerConfig.options);
      },
      inject: [ConfigService],
    },
    {
      provide: InternalApiJWTGuard,
      useFactory: (
        reflector: Reflector,
        configService: ConfigService,
        brokerTransportService: BrokerTransportService,
        jwtService: JwtService,
      ) => {
        return new InternalApiJWTGuard(reflector, configService, brokerTransportService, jwtService);
      },
      inject: [Reflector, ConfigService, BrokerTransportService, JwtService],
    },
    CacheService,
    S3Repository,
    PaystackRepository,
    ZillaRepository,
    MailchimpRepository,
    MonoRepository,
    DojahRepository,
    UserMessagingRepository,
    MonnifyRespository,
    YouVerifyRepository,
    SquadRepository,
    SlackRepository,
    ShipbubbleRepository,
    BlocHQRepository,
    InstagramRepository,
    OpenaiRepository,
    ReCaptchaRepository,
    WhatsappBusinessApiRepository,
    BrevoRepository,
    FlutterWaveRepository,
    StartbuttonRepository,
    StripeRepository,
    KoraPayRepository,
    PosthogRepository,
    ChowdeckRepository,
    WebServicesRepository,
    GoogleMapsRepository,
    ResendRepository,
    ZeepayRepository,
    IPLookUpRepository,
    PayazaRepository,
    FincraRepository,
    FezDeliveryRepository,
    ShaqExpressRepository,
    CustomerIoRepository,
    ZohoRepository,
    CurrencyConversionRepository,
    HostApiRepository,
    Go54Repository,
    DomainnameapiRepository,
    SudoRepository,
    FirebaseRepository,
  ],
  exports: [
    ConfigModule,
    CacheModule,
    JwtModule,
    RavenModule,
    Logger,
    BrokerTransportService,
    CacheService,
    S3Repository,
    MailchimpRepository,
    PaystackRepository,
    ZillaRepository,
    MonoRepository,
    DojahRepository,
    UserMessagingRepository,
    MonnifyRespository,
    YouVerifyRepository,
    SquadRepository,
    SlackRepository,
    ShipbubbleRepository,
    BlocHQRepository,
    InstagramRepository,
    OpenaiRepository,
    ReCaptchaRepository,
    WhatsappBusinessApiRepository,
    BrevoRepository,
    FlutterWaveRepository,
    StartbuttonRepository,
    StripeRepository,
    KoraPayRepository,
    PosthogRepository,
    ChowdeckRepository,
    WebServicesRepository,
    GoogleMapsRepository,
    ResendRepository,
    ZeepayRepository,
    IPLookUpRepository,
    PayazaRepository,
    FincraRepository,
    FezDeliveryRepository,
    ShaqExpressRepository,
    CustomerIoRepository,
    ZohoRepository,
    CurrencyConversionRepository,
    HostApiRepository,
    Go54Repository,
    DomainnameapiRepository,
    InternalApiJWTGuard,
    SudoRepository,
    FirebaseRepository,
  ],
})
export class SharedModule {}
