version: '3.8'
services:
  # catlog-api:
  #   build:
  #     context: .
  #   restart: always
  #   environment:
  #     - MONGODB_URI=mongodb://host.docker.internal:27017/catlog
  #     - NATS_URL=nats://catlog-nats:4222
  #     - NATS_USERNAME=catlog-api
  #     - NATS_PASSWORD=catlog-password
  #     - REDIS_HOST=catlog-redis
  #     - REDIS_TTL=90
  #     - REDIS_PORT=6379
  #     - REDIS_DB=0
  #     - JWT_SECRET=name
  #     - JWT_EXPIRY=3600000
  #   depends_on:
  #     - catlog-nats
  #     - catlog-redis
  #   ports:
  #     - '4040:4000'
  #   networks:
  #     - catlog

  catlog-nats:
    image: nats:2.8.4-alpine
    ports:
      - '4222:4222'
    networks:
      - catlog
    command:
      - '-js'
      - '-DV'

  catlog-redis:
    image: redis:7.0.2
    restart: always
    ports:
      - '6379:6379'
    networks:
      - catlog
    command: redis-server --appendonly yes

networks:
  catlog:
    driver: bridge
