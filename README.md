# Catlog Api
This is the catlog api written with nest. A few things are required to get started, `brew`, `mongodb` and `docker`.

The best way to go about this is in two stages, setting up other requirements before installing the nodejs dependencies.

If you already have them installed, move on to [installing node related things](#node-stuff)

- ***Install [Docker](https://docs.docker.com/desktop/mac/install/)***
  
    After installing the application, open a new terminal window and navigate to the respository and run `docker-compose up`.
    This fetches the required containers and runs them, after this first run, they can be started from the docker app itself.

- ***Install [MongoDB](https://www.mongodb.com/docs/manual/tutorial/install-mongodb-on-os-x/#installing-mongodb-5.0-edition-edition)***

    Follow the instructions in the link above to setup mongodb on your computer _TODO: Add instructions for replica sets_

- ***Install [The Mail & SMS Server](https://github.com/catlog-shop/mailing)*** Optional
  
    This requires the [GO](https://go.dev) programming language which can be installed with `brew install golang`.
    
    And run with `go run .`

## Node Stuff

- ***Install dependencies***

````
yarn install
````

- ***Setup local DB***

````
yarn run local:setupdb
````

- ***Start app***
````
yarn run start:dev
````

## Foot Notes

A .env file is required, which is not distributed in the source code for security reasons. They are always changing and updating, until a decentralized way of managing this is developed, please ask for up to date env files when you run into an error.
