/**
 * Create a vector search index for item embeddings in MongoDB
 *
 * Usage:
 * 1. Make sure MongoDB 7.0+ is being used (required for vector search)
 * 2. Run with: node scripts/create-vector-index.js
 */

require('dotenv').config();
const { MongoClient } = require('mongodb');

async function createVectorIndex() {
  const uri = process.env.MONGODB_URI;

  if (!uri) {
    console.error('MONGODB_URI environment variable is not set');
    process.exit(1);
  }

  const client = new MongoClient(uri);

  try {
    await client.connect();
    console.log('Connected to MongoDB');

    // Extract database name from connection string
    const dbName = uri.split('/').pop().split('?')[0];
    const db = client.db(dbName);

    // Create the vector search index on the items collection
    const indexName = 'embedding_index';
    const indexExists = await db.collection('items').indexExists(indexName);

    if (indexExists) {
      console.log(`Vector search index ${indexName} already exists.`);
    } else {
      const indexDefinition = {
        name: indexName,
        definition: {
          mappings: {
            dynamic: true,
            fields: {
              embedding: {
                dimensions: 1536, // OpenAI's text-embedding-ada-002 dimensions
                similarity: 'cosine',
                type: 'knnVector',
              },
            },
          },
        },
      };

      await db.collection('items').createSearchIndex(indexDefinition);
      console.log(`Vector search index ${indexName} created successfully!`);
    }

    console.log('Vector search is now enabled for item embeddings.');
    console.log('The ItemRecommendationsService will automatically use vector search for similarity queries.');
  } catch (error) {
    console.error('Error creating vector search index:', error);

    if (error.message.includes('Atlas Search is not enabled')) {
      console.log('\nNOTE: If you are using MongoDB Atlas:');
      console.log('1. Make sure you have an M10 or higher cluster');
      console.log('2. Enable Atlas Search for your cluster from the Atlas dashboard');
      console.log('3. Run this script again after Atlas Search is enabled');
    } else if (error.message.includes('version') || error.message.includes('command not found')) {
      console.log('\nNOTE: Vector search requires MongoDB 7.0 or later');
      console.log('The fallback manual calculation will be used automatically if vector search is not available.');
    }
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

createVectorIndex().catch(console.error);
