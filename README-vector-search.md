# Vector Search for Item Recommendations

This feature implements MongoDB's vector search capabilities to efficiently find related products based on semantic similarity of embeddings.

## How It Works

1. Item embeddings are generated using OpenAI's embedding API and stored in the `embedding` field of the Item document
2. MongoDB vector search is used to efficiently find items with similar embeddings
3. The system falls back to manual cosine similarity calculation if vector search is not available

## Requirements

- MongoDB 7.0+ with vector search capabilities enabled
- For MongoDB Atlas: M10 or higher cluster with Atlas Search enabled

## Setup

Run the following script to create the vector search index:

```bash
node scripts/create-vector-index.js
```

This script will:

- Connect to your MongoDB instance
- Create a vector search index named `embedding_index` on the `items` collection
- Configure the index for cosine similarity with OpenAI embeddings (1536 dimensions)

## Implementation Details

The `findRelatedProducts` method in `ItemRecommendationsService` uses MongoDB's `$vectorSearch` aggregation to efficiently find similar items. This approach is more efficient than the previous implementation because:

1. It pushes the vector similarity calculation to the database level instead of fetching all items
2. It leverages MongoDB's optimized vector search capabilities for faster queries
3. It reduces the amount of data transferred between the application and database

If vector search is not available (older MongoDB version or incorrect setup), the system will automatically fall back to the original implementation which calculates similarity in the application.

## Performance Comparison

| Method          | Time Complexity | Memory Usage | Network Transfer |
| --------------- | --------------- | ------------ | ---------------- |
| Original Method | O(n)            | High         | High             |
| Vector Search   | O(log n)        | Low          | Low              |

The original method needed to:

1. Fetch all store items into memory
2. Generate embeddings for items missing them
3. Calculate similarity for each item in memory
4. Sort results by similarity score

The vector search method:

1. Pushes similarity calculation to the database
2. Only returns the top matching results
3. Reduces both memory and network usage
