const download = require('image-downloader');

const images = [
  {
    image: 'https://d38v990enafbk6.cloudfront.net/kuda.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/9psb.png',
    code: '120001',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/abbey_mortgage_bank.png',
    code: '801',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/access.png',
    code: '044',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/access.png',
    code: '063',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/accion_mfb.png',
    code: '602',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/bainescredit_mfb.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/bowen_mfb.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/carbon.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/cellulant.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/changan_rts_mfb.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/citibank.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/contech_global.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/corestep_mfb.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/coronation.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/ebarcs_mfb.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/ecobank.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/ecobank.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/ekondo_mfb.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/Eyowo.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/fcmb.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/ffs_mfb.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/fidelity.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/fbn-quest',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/fsdh.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/globus_bank.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/gomoney.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/gtb.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/heritage_bank.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/jaiz.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/jubilee_life.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/KCMB_MFB.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/KEGOW_CHAMSMOBILE.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/keystone.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/kredimoney.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/living_trust_mortgage_bank.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/lotus_bank.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/mainstreet_mfb.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/manny_mfb.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/Mint.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/moniepoint.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/Paycom_Opay.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/paga.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/Palmpay.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/parkway_readycash.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/parallex_bank.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/PATRICKGOLD_MICROFINANCE_BANK.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/PayAttitude_Online.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/polaris.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/providus.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/rand_merchant_bank.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/rubies.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/safe_haven_mfb.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/seedvest.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/sparkle.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/spectrum_mfb.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/stanbic.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/stanbic.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/standard_chartered.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/Stellas.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/sterling.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/suntrust.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/taj_bank.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/Tanadi.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/tangerine_money.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/titan_paystack.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/titan_trust_bank.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/union.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/uba.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/unity_bank.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/UZONDU_MICROFINANCE_BANK.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/VFD_MFB.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/wema.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/zenith.png',
    code: '',
  },
  {
    image: 'https://d38v990enafbk6.cloudfront.net/Konga_Pay.png',
    code: '',
  },
];

// images.map((image) => {
//   const options = {
//     url: image.image,
//     dest: '/Users/<USER>/Documents/code/catlog/catlog-api/bank-logos/', // will be saved to /path/to/dest/image.jpg
//   };

//   download
//     .image(options)
//     .then(({ filename }) => {
//       console.log('Saved to', filename); // saved to /path/to/dest/image.jpg
//     })
//     .catch((err) => console.error(err));
// });
